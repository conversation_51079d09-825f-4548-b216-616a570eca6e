version: '3.8'
services:
  artemis:
    image: ecr.prod.de.eu-west-1.de.aws.vgthosting.net/delivery-engine-docker-artemis
    environment:
      - ANONYMOUS_LOGIN=true
    ports:
      - 61616:61616
      - 8161:8161
  wiremock-v2:
    image: ecr.prod.de.eu-west-1.de.aws.vgthosting.net/delivery-engine-docker-wiremock2
    command: --global-response-templating
    ports:
      - 8080:8080
    volumes:
      - ./wiremock-v2/stub:/home/<USER>
  db:
    image: ecr.prod.de.eu-west-1.de.aws.vgthosting.net/delivery-engine-docker-oracle
    ports:
      - 1521:1521/tcp
    shm_size: 1g
    volumes:
      - type: bind
        source: ./create-test-user.sql
        target: /docker-entrypoint-initdb.d/create-test-user.sql
  influxdb:
    image: artifactory.sharedservices.prod.euw1.vg-cs.net/docker-public/influxdb:1.8.6
    ports:
      - 8086:8086

  zoo:
    image: artifactory.sharedservices.prod.euw1.vg-cs.net/docker-public/zookeeper
    ports:
      - "2181:2181/tcp"
    restart: always
