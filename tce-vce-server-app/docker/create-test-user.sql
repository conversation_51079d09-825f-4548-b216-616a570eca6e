ALTER SESSION SET "_ORACLE_SCRIPT"=true;

ALTER SYSTEM SET sessions=1248 SCOPE=SPFILE;
ALTER SYSTEM SET processes=1000 SCOPE=SPFILE;

CREATE TABLESPACE TEST_TABLE_TABLESPACE
  DATAFILE 'TEST_TABLE_TABLESPACE.dat'
  SIZE 10M AUTOEXTEND ON;

CREATE TEMPORARY TABLESPACE TEST_TABLESPACE_TEMP
  TEMPFILE 'TEST_TABLESPACE_TEMP.dat'
  SIZE 5M AUTOEXTEND ON;

CREATE USER test_user
  IDENTIFIED BY test_user
  DEFAULT TABLESPACE TEST_TABLE_TABLESPACE
  TEMPORARY TABLESPACE TEST_TABLESPACE_TEMP
  QUOTA UNLIMITED ON TEST_TABLE_TABLESPACE;

GRANT ALL PRIVILEGES TO test_user;
