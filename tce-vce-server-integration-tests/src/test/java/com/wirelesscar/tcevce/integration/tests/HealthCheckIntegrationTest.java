package com.wirelesscar.tcevce.integration.tests;

import org.apache.curator.test.TestingServer;
import org.junit.jupiter.api.Test;
import org.springframework.context.ConfigurableApplicationContext;

import com.volvo.tisp.vc.tcp.server.lib.server.TcpServer;
import com.volvo.tisp.vc.tcp.server.lib.server.TcpServerBuilder;
import com.wirelesscar.tcevce.impl.conf.AppConfig;
import com.wirelesscar.tcevce.integration.tests.util.EmbeddedActiveMqWrapper;
import com.wirelesscar.tcevce.integration.tests.util.IntegrationTestHelper;
import com.wirelesscar.tcevce.integration.tests.util.RestUtil;
import com.wirelesscar.tcevce.integration.tests.util.WireMockServerWrapper;

class HealthCheckIntegrationTest {
  @Test
  void integrationTest() throws Exception {
    try (EmbeddedActiveMqWrapper embeddedActiveMqWrapper = IntegrationTestHelper.createAndStartEmbeddedActiveMqWrapper();
        WireMockServerWrapper wireMockServerWrapper = IntegrationTestHelper.createAndStartWireMockServer();
        TcpServer tcpServer = new TcpServerBuilder().buildAndStart();
        TestingServer testingServer = IntegrationTestHelper.createTestingServer()) {
      IntegrationTestHelper.setupConfig(wireMockServerWrapper, tcpServer, testingServer);
      IntegrationTestHelper.stubInfluxdb(wireMockServerWrapper);

      try (ConfigurableApplicationContext configurableApplicationContext = IntegrationTestHelper.runSpringApplication(AppConfig.class)) {
        RestUtil.verifyOkHttpResponse("http://localhost:20597/actuator/health");
        RestUtil.verifyOkHttpResponse("http://localhost:20597/actuator/health/ping");
      }
    }
  }
}
