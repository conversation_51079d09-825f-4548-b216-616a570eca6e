package com.wirelesscar.tcevce.integration.tests;

import java.io.IOException;

import org.apache.curator.test.TestingServer;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.context.ConfigurableApplicationContext;

import com.github.tomakehurst.wiremock.client.WireMock;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Msisdn;
import com.volvo.tisp.vc.tcp.server.lib.model.HostInfo;
import com.volvo.tisp.vc.tcp.server.lib.server.TcpServer;
import com.volvo.tisp.vc.tcp.server.lib.server.TcpServerBuilder;
import com.wirelesscar.caretrack.dh.caretrack.protocol.ASNException;
import com.wirelesscar.tce.module.api.Message;
import com.wirelesscar.tce.module.api.MetaData;
import com.wirelesscar.tce.module.api.TransportType;
import com.wirelesscar.tcevce.impl.conf.AppConfig;
import com.wirelesscar.tcevce.integration.tests.util.DataUtil;
import com.wirelesscar.tcevce.integration.tests.util.DeviceDetailedEntryUtil;
import com.wirelesscar.tcevce.integration.tests.util.EmbeddedActiveMqWrapper;
import com.wirelesscar.tcevce.integration.tests.util.IntegrationTestHelper;
import com.wirelesscar.tcevce.integration.tests.util.MessageUtils;
import com.wirelesscar.tcevce.integration.tests.util.WireMockServerWrapper;

class MoSmsIntegrationTest {
  private static Message createMessage(Msisdn msisdn, boolean encrypted) throws ASNException {
    Message message = new Message();

    message.setPayload(MessageUtils.createAndEncodeAsn1Payload(encrypted));
    message.setMessageId(MessageUtils.MESSAGE_ID);
    message.setProperty(MetaData.SMPP_SOURCE_ADDRESS.name(), msisdn.toString());
    message.setProperty(MetaData.TRANSPORT_TYPE.name(), TransportType.SMS.name());

    return message;
  }

  private static void receiveAndVerifyMoAckMessage(TcpServer tcpServer) throws ClassNotFoundException, IOException, ASNException {
    Message message = MessageUtils.receiveMessageViaTcpSocket(tcpServer);

    Assertions.assertAll(
        () -> Assertions.assertEquals("true", message.getProperty(MetaData.ACK)),
        () -> Assertions.assertEquals(DataUtil.HANDLE.toString(), message.getProperty(MetaData.HANDLE)),
        () -> Assertions.assertNull(message.getProperty(MetaData.SMPP_SOURCE_ADDRESS))
    );

    MessageUtils.decodeAsn1PayloadAndVerifyMoAck(message.getPayload());
  }

  @Test
  void integrationTest() throws Exception {
    try (EmbeddedActiveMqWrapper embeddedActiveMqWrapper = IntegrationTestHelper.createAndStartEmbeddedActiveMqWrapper();
        WireMockServerWrapper wireMockServerWrapper = IntegrationTestHelper.createAndStartWireMockServer();
        TcpServer tcpServer = new TcpServerBuilder().buildAndStart();
        TestingServer testingServer = IntegrationTestHelper.createTestingServer()) {
      WireMock.configureFor(wireMockServerWrapper.getWireMockServer().port());

      IntegrationTestHelper.setupConfig(wireMockServerWrapper, tcpServer, testingServer);
      IntegrationTestHelper.stubInfluxdb(wireMockServerWrapper);
      IntegrationTestHelper.stubSubRepo();
      IntegrationTestHelper.stubConrepoNoContentLookupByMsisdn(wireMockServerWrapper, DataUtil.MSISDN);

      try (ConfigurableApplicationContext configurableApplicationContext = IntegrationTestHelper.runSpringApplication(AppConfig.class)) {
        Message message = createMessage(DataUtil.MSISDN, false);
        final HostInfo hostInfo = new HostInfo("localhost", 20587);
        MessageUtils.sendMessageOverTcp(message, hostInfo);
        MessageUtils.verifyMoAckNoVehiclesFound(tcpServer);
        MessageUtils.verifyMoMessageIsNotPublished();

        IntegrationTestHelper.stubConrepoOkLookupByMsisdn(wireMockServerWrapper, DeviceDetailedEntryUtil.createDeviceDetailedEntry());
        MessageUtils.sendMessageOverTcp(message, hostInfo);
        receiveAndVerifyMoAckMessage(tcpServer);
        MessageUtils.verifyMoMessageIsPublished(DataUtil.HANDLE, DataUtil.VPI.toString());
      }
    }
  }
}
