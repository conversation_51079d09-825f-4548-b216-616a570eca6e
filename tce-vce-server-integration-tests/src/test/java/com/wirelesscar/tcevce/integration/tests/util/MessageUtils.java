package com.wirelesscar.tcevce.integration.tests.util;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;
import java.time.Duration;
import java.util.Optional;
import java.util.Random;

import org.junit.jupiter.api.Assertions;

import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.volvo.tisp.vc.main.utils.lib.type.ImmutableByteArray;
import com.volvo.tisp.vc.tcp.server.lib.model.HostInfo;
import com.volvo.tisp.vc.tcp.server.lib.model.SendTcpMessage;
import com.volvo.tisp.vc.tcp.server.lib.sender.TcpSender;
import com.volvo.tisp.vc.tcp.server.lib.server.SocketWrapper;
import com.volvo.tisp.vc.tcp.server.lib.server.TcpServer;
import com.wirelesscar.caretrack.dh.caretrack.protocol.ASNException;
import com.wirelesscar.caretrack.dh.caretrack.protocol.ASNValue;
import com.wirelesscar.caretrack.dh.caretrack.protocol.InstantMachineData2;
import com.wirelesscar.caretrack.dh.caretrack.protocol.InstantMachineData2Element;
import com.wirelesscar.caretrack.dh.caretrack.protocol.MessageContent;
import com.wirelesscar.caretrack.dh.caretrack.protocol.MessageType;
import com.wirelesscar.caretrack.dh.caretrack.protocol.PERStream;
import com.wirelesscar.caretrack.dh.caretrack.protocol.Position;
import com.wirelesscar.caretrack.dh.caretrack.protocol.RunTimeValue;
import com.wirelesscar.caretrack.dh.caretrack.protocol.StandardMessageCount;
import com.wirelesscar.caretrack.dh.caretrack.protocol.TransportHeader;
import com.wirelesscar.tce.api.v2.MoMessage;
import com.wirelesscar.tce.module.api.Message;
import com.wirelesscar.tce.module.api.MessageStatus;
import com.wirelesscar.tce.module.api.MetaData;
import com.wirelesscar.tce.module.api.SmsDataCoding;
import com.wirelesscar.tce.module.api.TransportType;
import com.wirelesscar.tcevce.wecu.device.info.database.model.Handle;
import com.wirelesscar.tcevce.wecu.device.info.database.model.SatelliteId;

public final class MessageUtils {
  public static final Duration ACCEPT_DURATION = Duration.ofSeconds(3);
  public static final String MESSAGE_ID = "someMessageId";
  public static final String SATELLITE_MESSAGE_ID = "satelliteMessageId";

  private MessageUtils() {
    throw new IllegalStateException();
  }

  public static byte[] createAndEncodeAsn1Payload(boolean encrypted) throws ASNException {
    MessageType messageType = new MessageType();
    messageType.setInstantMachineData2(createInstantMachineData2());
    com.wirelesscar.caretrack.dh.caretrack.protocol.Message message = new com.wirelesscar.caretrack.dh.caretrack.protocol.Message();
    message.setSize(1);
    message.set(0, messageType);

    MessageContent messageContent = new MessageContent();
    messageContent.setData(message);

    TransportHeader transportHeader = createTransportHeader(encrypted);

    // Create a new encoded header
    PERStream perStream = new PERStream((int) transportHeader.encodedSize());
    perStream.startEncoding();
    transportHeader.encode(perStream);
    perStream.alignOnByte();
    byte[] headerBuffer = perStream.getBuffer();
    byte[] contentPayload = toRawValue(messageContent);
    byte[] payload = new byte[headerBuffer.length + contentPayload.length];

    // Append the header to old message content
    System.arraycopy(headerBuffer, 0, payload, 0, headerBuffer.length);
    System.arraycopy(contentPayload, 0, payload, headerBuffer.length, contentPayload.length);

    return payload;
  }

  public static Message createMoSatelliteMessage(SatelliteId satelliteId, boolean encrypted) throws ASNException {
    Message message = new Message();

    message.setMessageId(MESSAGE_ID);
    message.setPayload(createAndEncodeAsn1Payload(encrypted));
    message.setProperty(MetaData.SATELLITE_SOURCE_ADDRESS.name(), satelliteId.toString());
    message.setProperty(MetaData.TRANSPORT_TYPE.name(), TransportType.SATELLITE.name());
    message.setProperty(MetaData.SATELLITE_MESSAGE_ID.name(), SATELLITE_MESSAGE_ID);

    return message;
  }

  public static void createMtAckMessageAndReplyViaTcpSocket(Message message) throws IOException, ASNException {
    sendBackMtAckViaTcpSocket(createMtAckMessage(message));
  }

  public static void decodeAsn1PayloadAndVerifyMoAck(byte[] payload) throws ASNException {
    TransportHeader transportHeader = new TransportHeader();
    transportHeader.decode(new PERStream(payload));

    assertTrue(transportHeader.getMobileOriginated());

    final StandardMessageCount standardMessageCount = transportHeader.getMessageCount().getStandard();

    assertEquals(0, standardMessageCount.getIndexOfCurrentPacket());
    assertEquals(1, standardMessageCount.getTotalNumberOfPackets());
  }

  public static Message deserializeMessage(InputStream inputStream) throws IOException, ClassNotFoundException {
    Validate.notNull(inputStream, "inputStream");

    try (ObjectInputStream objectInputStream = new ObjectInputStream(inputStream)) {
      return (Message) objectInputStream.readObject();
    }
  }

  public static Message receiveAndVerifyMtMessageViaTcpSocket(TcpServer tcpServer, Vpi expectedVpi, Handle expectedHandle) throws Exception {
    return receiveAndVerifyMtMessageViaTcpSocket(tcpServer, expectedVpi.toString(), expectedHandle.toString());
  }

  public static Message receiveAndVerifyMtMessageViaTcpSocket(TcpServer tcpServer, Handle expectedHandle) throws Exception {
    return receiveAndVerifyMtMessageViaTcpSocket(tcpServer, expectedHandle.toString(), expectedHandle.toString());
  }

  public static Message receiveMessageViaTcpSocket(TcpServer tcpServer) throws IOException, ClassNotFoundException {
    try (SocketWrapper socketWrapper = tcpServer.accept(ACCEPT_DURATION).get()) {
      return deserializeMessage(socketWrapper.getInputStream());
    }
  }

  public static void sendMessageOverTcp(Message message, HostInfo hostInfo) throws IOException {
    SendTcpMessage sendTcpMessage = new SendTcpMessage(hostInfo, ImmutableByteArray.of(MessageUtils.serializeMessage(message)));
    TcpSender.sendAndClose(sendTcpMessage);
  }

  public static void verifyMoAckNoVehiclesFound(TcpServer tcpServer) throws Exception {
    Message message = receiveMessageViaTcpSocket(tcpServer);

    Assertions.assertEquals(Boolean.TRUE.toString(), message.getProperty(MetaData.ACK));
    Assertions.assertEquals(Boolean.TRUE.toString(), message.getProperty(MetaData.VEHICLE_NOT_FOUND));
    Assertions.assertNull(message.getProperty(MetaData.HANDLE));
  }

  public static void verifyMoMessageIsNotPublished() throws Exception {
    Optional<MoMessage> optionalMoMessage = JmsUtil.receiveMoMessage();
    Assertions.assertTrue(optionalMoMessage.isEmpty());
  }

  public static void verifyMoMessageIsPublished(Handle expectedHandle, String expectedVehicleId) throws Exception {
    MoMessage moMessage = JmsUtil.receiveMoMessage().get();
    Assertions.assertEquals(expectedHandle.toString(), moMessage.getHandle());
    Assertions.assertEquals(expectedVehicleId, moMessage.getVehiclePlatformId());
  }

  public static void verifySequenceNumber(Message message, long expectedSequenceNumber) throws ASNException {
    PERStream perStream = new PERStream(message.getPayload());
    TransportHeader transportHeader = new TransportHeader();
    transportHeader.decode(perStream);

    Assertions.assertEquals(expectedSequenceNumber, transportHeader.getSequenceNumber());
  }

  private static InstantMachineData2 createInstantMachineData2() throws ASNException {
    InstantMachineData2 instantMachineData2 = new InstantMachineData2();

    int internalSize = 50;
    instantMachineData2.setSize(internalSize);

    for (int i = 0; i < internalSize; ++i) {
      Position position = new Position();
      position.setLongitude(56);
      position.setLatitude(67);

      final int runtimeSize = 5;

      InstantMachineData2Element instantMachineData2Element = new InstantMachineData2Element();
      instantMachineData2Element.setMachineHours(new Random().nextInt(50_000));
      instantMachineData2Element.setNetwork("192.168.11.111");
      instantMachineData2Element.setPosition(position);
      instantMachineData2Element.getRunTimeList().setSize(runtimeSize);

      for (int j = 0; j < runtimeSize; ++j) {
        RunTimeValue runTimeValue = new RunTimeValue();
        runTimeValue.setUint16Val(j + 1L);
        instantMachineData2Element.setRunTimeList(j, runTimeValue);
      }

      instantMachineData2Element.setSpeed(new Random().nextInt(100));
      // from ASN.1: seconds since 2000-01-01 00:00 UTC
      int timestampInSecondsFor20000101 = 946_684_800;
      instantMachineData2Element.setTimeStamp(System.currentTimeMillis() / 1_000 - timestampInSecondsFor20000101);

      instantMachineData2.set(i, instantMachineData2Element);
    }

    return instantMachineData2;
  }

  private static Message createMtAckMessage(Message message) throws ASNException {
    PERStream perStream = new PERStream(message.getPayload());
    TransportHeader transportHeader = new TransportHeader();
    transportHeader.decode(perStream);
    return createMtAckMessage(message, transportHeader.getSequenceNumber());
  }

  private static Message createMtAckMessage(Message message, long sequenceNumber) throws ASNException {
    MessageContent messageContent = new MessageContent();
    messageContent.setAck();

    Message mtAckMessage = Message.createMessage();
    mtAckMessage.setMessageId(message.getMessageId());
    mtAckMessage.setVehicleID(message.getVehicleID());
    mtAckMessage.addProperties(message.getProperties());
    mtAckMessage.setProperty(MetaData.ACK, "true");
    mtAckMessage.setProperty(MetaData.MOBILE_ORIGINATED, "true");
    mtAckMessage.setProperty(MetaData.SMS_DATA_CODING, SmsDataCoding.BINARY.toString());
    setSwappedAddress(mtAckMessage, message);
    mtAckMessage.setProperty(MetaData.TRANSPORT_TYPE, message.getProperty(MetaData.TRANSPORT_TYPE));
    mtAckMessage.setPayload(createMtAckPayload(messageContent, sequenceNumber));

    return mtAckMessage;
  }

  private static byte[] createMtAckPayload(MessageContent messageContent, long sequenceNumber) throws ASNException {
    TransportHeader transportHeader = new TransportHeader();
    transportHeader.getMessageCount().setStandard();
    transportHeader.getMessageCount().getStandard().setIndexOfCurrentPacket(0);
    transportHeader.getMessageCount().getStandard().setTotalNumberOfPackets(1);
    transportHeader.setMobileOriginated(true);
    transportHeader.setSequenceNumber(sequenceNumber);
    transportHeader.setSkipAck(false);
    transportHeader.setEncrypted(false);

    PERStream perStream = new PERStream();
    transportHeader.encode(perStream);
    perStream.alignOnByte();
    messageContent.encode(perStream);
    return perStream.getBuffer();
  }

  private static TransportHeader createTransportHeader(boolean encrypted) throws ASNException {
    TransportHeader transportHeader = new TransportHeader();

    transportHeader.setSequenceNumber(new Random().nextInt(15));
    transportHeader.setSkipAck(false);
    transportHeader.setEncrypted(encrypted);
    transportHeader.setMobileOriginated(true);
    transportHeader.getMessageCount().setStandard();
    transportHeader.getMessageCount().getStandard().setIndexOfCurrentPacket(0);
    transportHeader.getMessageCount().getStandard().setTotalNumberOfPackets(1);

    return transportHeader;
  }

  private static Message receiveAndVerifyMtMessageViaTcpSocket(TcpServer tcpServer, String expectedVpi, String expectedHandle) throws Exception {
    Message message = receiveMessageViaTcpSocket(tcpServer);

    Assertions.assertEquals(Boolean.TRUE.toString(), message.getProperty(MetaData.MT));
    Assertions.assertEquals(MessageStatus.NONE, message.getStatus());
    Assertions.assertEquals(expectedVpi, message.getVehicleID());
    Assertions.assertEquals(expectedHandle, message.getProperty(MetaData.HANDLE));

    return message;
  }

  private static void sendBackMtAckViaTcpSocket(Message message) throws IOException {
    SendTcpMessage sendTcpMessage = new SendTcpMessage(new HostInfo("127.0.0.1", 20_587), ImmutableByteArray.of(serializeMessage(message)));
    TcpSender.sendAndClose(sendTcpMessage);
  }

  private static byte[] serializeMessage(Message message) throws IOException {
    Validate.notNull(message, "message");

    try (ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream()) {
      try (ObjectOutputStream objectOutputStream = new ObjectOutputStream(byteArrayOutputStream)) {
        objectOutputStream.writeObject(message);
      }

      return byteArrayOutputStream.toByteArray();
    }
  }

  private static void setSwappedAddress(Message swappedAddrMessage, Message message) {
    String sourceAddress = message.getProperty(MetaData.SMPP_SOURCE_ADDRESS);
    swappedAddrMessage.setProperty(MetaData.SMPP_SOURCE_ADDRESS, message.getProperty(MetaData.SMPP_DEST_ADDRESS));
    swappedAddrMessage.setProperty(MetaData.SMPP_DEST_ADDRESS, sourceAddress);
  }

  private static byte[] toRawValue(ASNValue asnValue) throws ASNException {
    int contentSize = (int) (asnValue.encodedSize() / 8) + 1;
    PERStream perStream = new PERStream(contentSize);
    asnValue.encode(perStream);
    perStream.alignOnByte();
    return perStream.getBuffer();
  }
}
