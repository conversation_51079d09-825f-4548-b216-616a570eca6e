package com.wirelesscar.tcevce.integration.tests;

import java.time.Duration;

import org.apache.curator.test.TestingServer;
import org.junit.jupiter.api.Test;
import org.springframework.context.ConfigurableApplicationContext;

import com.github.tomakehurst.wiremock.client.WireMock;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Ipv4Address;
import com.volvo.tisp.vc.tcp.server.lib.server.TcpServer;
import com.volvo.tisp.vc.tcp.server.lib.server.TcpServerBuilder;
import com.volvo.tisp.vc.udp.server.lib.model.HostInfo;
import com.volvo.tisp.vc.udp.server.lib.model.ReceivedUdpMessage;
import com.volvo.tisp.vc.udp.server.lib.server.SimpleUdpServer;
import com.volvo.tisp.vc.udp.server.lib.server.SimpleUdpServerBuilder;
import com.wirelesscar.caretrack.dh.caretrack.protocol.ASNException;
import com.wirelesscar.conrepo.api.v1.DeviceDetailedEntry;
import com.wirelesscar.tce.module.api.Message;
import com.wirelesscar.tce.module.api.MetaData;
import com.wirelesscar.tce.module.api.TransportType;
import com.wirelesscar.tcevce.impl.conf.AppConfig;
import com.wirelesscar.tcevce.integration.tests.util.DataUtil;
import com.wirelesscar.tcevce.integration.tests.util.DeviceDetailedEntryUtil;
import com.wirelesscar.tcevce.integration.tests.util.EmbeddedActiveMqWrapper;
import com.wirelesscar.tcevce.integration.tests.util.IntegrationTestHelper;
import com.wirelesscar.tcevce.integration.tests.util.MessageUtils;
import com.wirelesscar.tcevce.integration.tests.util.WireMockServerWrapper;

class MoUdpIntegrationTest {
  private static Message createMessage(HostInfo hostInfo) throws ASNException {
    return createMessage(hostInfo, false);
  }

  private static Message createMessage(HostInfo hostInfo, boolean encrypted) throws ASNException {
    Message message = new Message();

    message.setPayload(MessageUtils.createAndEncodeAsn1Payload(encrypted));
    message.setMessageId(MessageUtils.MESSAGE_ID);
    message.setProperty(MetaData.IP_SRC_ADDRESS.name(), hostInfo.getHostAddress());
    message.setProperty(MetaData.IP_SRC_PORT.name(), String.valueOf(hostInfo.getPort()));
    message.setProperty(MetaData.TRANSPORT_TYPE.name(), TransportType.UDP.name());

    return message;
  }

  private static void verifyMoAckReceived(SimpleUdpServer simpleUdpServer) throws InterruptedException, ASNException {
    ReceivedUdpMessage receivedUdpMessage = simpleUdpServer.receive(Duration.ofSeconds(3)).get();

    MessageUtils.decodeAsn1PayloadAndVerifyMoAck(receivedUdpMessage.getPayload().toByteArray());
  }

  @Test
  void integrationTest() throws Exception {
    try (EmbeddedActiveMqWrapper embeddedActiveMqWrapper = IntegrationTestHelper.createAndStartEmbeddedActiveMqWrapper();
        WireMockServerWrapper wireMockServerWrapper = IntegrationTestHelper.createAndStartWireMockServer();
        TcpServer tcpServer = new TcpServerBuilder().buildAndStart();
        SimpleUdpServer simpleUdpServer = new SimpleUdpServerBuilder().buildAndStart();
        TestingServer testingServer = IntegrationTestHelper.createTestingServer()) {
      WireMock.configureFor(wireMockServerWrapper.getWireMockServer().port());
      final HostInfo udpHostInfo = simpleUdpServer.getHostInfo();

      IntegrationTestHelper.setupConfig(wireMockServerWrapper, tcpServer, testingServer);
      IntegrationTestHelper.stubInfluxdb(wireMockServerWrapper);
      IntegrationTestHelper.stubSubRepo();
      DeviceDetailedEntry deviceDetailedEntry = DeviceDetailedEntryUtil.createDeviceDetailedEntry(Ipv4Address.ofString(udpHostInfo.getHostAddress()));

      try (ConfigurableApplicationContext configurableApplicationContext = IntegrationTestHelper.runSpringApplication(AppConfig.class)) {
        Message message = createMessage(udpHostInfo);
        com.volvo.tisp.vc.tcp.server.lib.model.HostInfo tcpHostInfo = new com.volvo.tisp.vc.tcp.server.lib.model.HostInfo("localhost", 20585);

        IntegrationTestHelper.stubConrepoNoContentLookupByIp(wireMockServerWrapper, Ipv4Address.ofString(deviceDetailedEntry.getSimEntry().getIp()));
        MessageUtils.sendMessageOverTcp(message, tcpHostInfo);
        verifyMoAckReceived(simpleUdpServer);
        MessageUtils.verifyMoMessageIsNotPublished();

        IntegrationTestHelper.stubConrepoOkLookupByIp(wireMockServerWrapper, deviceDetailedEntry);
        MessageUtils.sendMessageOverTcp(message, tcpHostInfo);
        verifyMoAckReceived(simpleUdpServer);
        MessageUtils.verifyMoMessageIsPublished(DataUtil.HANDLE, DataUtil.VPI.toString());
      }
    }
  }
}
