package com.wirelesscar.tcevce.integration.tests.util;

import org.junit.jupiter.api.Assertions;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

public final class RestUtil {
  private RestUtil() {
    throw new IllegalArgumentException();
  }

  public static RestTemplate createRestTemplate() {
    RestTemplate restTemplate = new RestTemplate();
    restTemplate.setErrorHandler(NoOpResponseErrorHandler.INSTANCE);
    return restTemplate;
  }

  public static void verifyOkHttpResponse(String url) {
    ResponseEntity<String> responseEntity = RestUtil.createRestTemplate()
        .exchange(url, HttpMethod.GET, null, String.class);
    Assertions.assertEquals(HttpStatus.OK, responseEntity.getStatusCode(), responseEntity::getBody);
  }
}
