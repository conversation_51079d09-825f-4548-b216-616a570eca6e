package com.wirelesscar.tcevce.integration.tests;

import java.sql.SQLException;
import java.util.List;

import org.apache.curator.test.TestingServer;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.context.ConfigurableApplicationContext;

import com.github.tomakehurst.wiremock.client.WireMock;
import com.volvo.tisp.framework.context.TispContext;
import com.volvo.tisp.vc.tcp.server.lib.server.TcpServer;
import com.volvo.tisp.vc.tcp.server.lib.server.TcpServerBuilder;
import com.wirelesscar.tce.db.common.db.api.MessageFields;
import com.wirelesscar.tce.module.api.Message;
import com.wirelesscar.tce.module.api.MetaData;
import com.wirelesscar.tcevce.impl.conf.AppConfig;
import com.wirelesscar.tcevce.integration.tests.util.DataUtil;
import com.wirelesscar.tcevce.integration.tests.util.IntegrationTestHelper;
import com.wirelesscar.tcevce.integration.tests.util.WireMockServerWrapper;
import com.wirelesscar.tcevce.module.scheduler.db.impl.ScheduledMessagePersisterImpl;
import com.wirelesscar.tcevce.module.split.ack.service.ScheduledMessageDatabaseService;

class ScheduledMessageDatabaseServiceIntegrationTest {
  private static final String VEHICLE_ID = "1a";

  private static void checkMessage(Message activeMessage) {
    Assertions.assertEquals(VEHICLE_ID, activeMessage.getVehicleID());
    Assertions.assertEquals(DataUtil.SEQUENCE_NUMBER.toString(), activeMessage.getProperty(MetaData.SEQUENCE_NUMBER));
    Assertions.assertEquals("a", activeMessage.getProperty(MessageFields.status.toString()));
  }

  private static Message createMessage() {
    Message message = new Message("1", null);
    message.setProperty(MetaData.SEQUENCE_NUMBER, DataUtil.SEQUENCE_NUMBER.toString());
    message.setVehicleID(VEHICLE_ID);

    return message;
  }

  private static void persistMessage(Message message, ScheduledMessagePersisterImpl scheduledMessagePersister) {
    TispContext.runInContext(() -> {
      try {
        scheduledMessagePersister.persist(message);
      } catch (SQLException e) {
        throw new IllegalStateException(e);
      }
    });
  }

  @Test
  void integrationTest() throws Exception {
    try (WireMockServerWrapper wireMockServerWrapper = IntegrationTestHelper.createAndStartWireMockServer();
        TcpServer tcpServer = new TcpServerBuilder().buildAndStart();
        TestingServer testingServer = IntegrationTestHelper.createTestingServer()) {
      WireMock.configureFor(wireMockServerWrapper.getWireMockServer().port());

      IntegrationTestHelper.setupConfig(wireMockServerWrapper, tcpServer, testingServer);
      IntegrationTestHelper.stubInfluxdb(wireMockServerWrapper);

      try (ConfigurableApplicationContext configurableApplicationContext = IntegrationTestHelper.runSpringApplication(AppConfig.class)) {
        ScheduledMessageDatabaseService scheduledMessageDatabaseService = configurableApplicationContext.getBean(ScheduledMessageDatabaseService.class);

        Assertions.assertEquals(0, scheduledMessageDatabaseService.findActiveMessagesWithSequenceNumberByVehicleId(VEHICLE_ID).size());

        persistMessage(createMessage(), new ScheduledMessagePersisterImpl());

        List<Message> activeMessages = scheduledMessageDatabaseService.findActiveMessagesWithSequenceNumberByVehicleId(VEHICLE_ID);
        Assertions.assertEquals(1, activeMessages.size());
        checkMessage(activeMessages.get(0));
      }
    }
  }
}
