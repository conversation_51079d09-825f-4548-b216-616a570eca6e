package com.wirelesscar.tcevce.integration.tests;

import javax.sql.DataSource;

import org.apache.curator.test.TestingServer;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.context.ConfigurableApplicationContext;

import com.github.tomakehurst.wiremock.client.WireMock;
import com.volvo.tisp.vc.tcp.server.lib.server.TcpServer;
import com.volvo.tisp.vc.tcp.server.lib.server.TcpServerBuilder;
import com.wirelesscar.tce.module.api.Message;
import com.wirelesscar.tcevce.impl.conf.AppConfig;
import com.wirelesscar.tcevce.integration.tests.util.DataUtil;
import com.wirelesscar.tcevce.integration.tests.util.EmbeddedActiveMqWrapper;
import com.wirelesscar.tcevce.integration.tests.util.IntegrationTestHelper;
import com.wirelesscar.tcevce.integration.tests.util.JmsUtil;
import com.wirelesscar.tcevce.integration.tests.util.MessageUtils;
import com.wirelesscar.tcevce.integration.tests.util.WireMockServerWrapper;
import com.wirelesscar.tcevce.wecu.device.info.database.api.DeviceInfoWriterFactory;

class MtIntegrationTestWithHandle {
  @Test
  void integrationTest() throws Exception {
    try (EmbeddedActiveMqWrapper embeddedActiveMqWrapper = IntegrationTestHelper.createAndStartEmbeddedActiveMqWrapper();
        WireMockServerWrapper wireMockServerWrapper = IntegrationTestHelper.createAndStartWireMockServer();
        TcpServer tcpServer = new TcpServerBuilder().buildAndStart();
        TestingServer testingServer = IntegrationTestHelper.createTestingServer()) {
      WireMock.configureFor(wireMockServerWrapper.getWireMockServer().port());

      IntegrationTestHelper.setupConfig(wireMockServerWrapper, tcpServer, testingServer);
      IntegrationTestHelper.stubInfluxdb(wireMockServerWrapper);
      IntegrationTestHelper.stubSubRepo();

      try (ConfigurableApplicationContext configurableApplicationContext = IntegrationTestHelper.runSpringApplication(AppConfig.class)) {
        DataUtil.insertSendSchemasInDatabase(configurableApplicationContext.getBean(DataSource.class));

        DataUtil.createAndInsertDeviceInfoWithoutVpi(configurableApplicationContext.getBean(DeviceInfoWriterFactory.class));

        JmsUtil.sendMtMessage(DataUtil.createMtMessageWithoutVpi());
        Message message1 = MessageUtils.receiveAndVerifyMtMessageViaTcpSocket(tcpServer, DataUtil.HANDLE);
        MessageUtils.verifySequenceNumber(message1, 0);
        MessageUtils.createMtAckMessageAndReplyViaTcpSocket(message1);
        DataUtil.checkMtStatusMessageWithoutVpi(JmsUtil.receiveMtStatusMessage().get());

        JmsUtil.sendMtMessage(DataUtil.createMtMessageWithoutVpi());
        Message message2 = MessageUtils.receiveAndVerifyMtMessageViaTcpSocket(tcpServer, DataUtil.HANDLE);
        MessageUtils.verifySequenceNumber(message2, 1);
        MessageUtils.createMtAckMessageAndReplyViaTcpSocket(message2);
        DataUtil.checkMtStatusMessageWithoutVpi(JmsUtil.receiveMtStatusMessage().get());

        Assertions.assertTrue(JmsUtil.receiveMtStatusMessage().isEmpty());
      }
    }
  }
}
