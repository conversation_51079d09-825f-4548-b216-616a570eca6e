package com.wirelesscar.tcevce.integration.tests;

import javax.sql.DataSource;

import org.apache.curator.test.TestingServer;
import org.junit.jupiter.api.Test;
import org.springframework.context.ConfigurableApplicationContext;

import com.github.tomakehurst.wiremock.client.WireMock;
import com.volvo.tisp.vc.tcp.server.lib.server.TcpServer;
import com.volvo.tisp.vc.tcp.server.lib.server.TcpServerBuilder;
import com.wirelesscar.tce.module.api.Message;
import com.wirelesscar.tcevce.impl.conf.AppConfig;
import com.wirelesscar.tcevce.integration.tests.util.DataUtil;
import com.wirelesscar.tcevce.integration.tests.util.DeviceDetailedEntryUtil;
import com.wirelesscar.tcevce.integration.tests.util.EmbeddedActiveMqWrapper;
import com.wirelesscar.tcevce.integration.tests.util.IntegrationTestHelper;
import com.wirelesscar.tcevce.integration.tests.util.JmsUtil;
import com.wirelesscar.tcevce.integration.tests.util.MessageUtils;
import com.wirelesscar.tcevce.integration.tests.util.WireMockServerWrapper;
import com.wirelesscar.tcevce.wecu.device.info.database.api.DeviceInfoWriterFactory;

class MtLookupDeviceIntegrationTest {
  @Test
  void integrationTest() throws Exception {
    try (EmbeddedActiveMqWrapper embeddedActiveMqWrapper = IntegrationTestHelper.createAndStartEmbeddedActiveMqWrapper();
        WireMockServerWrapper wireMockServerWrapper = IntegrationTestHelper.createAndStartWireMockServer();
        TcpServer tcpServer = new TcpServerBuilder().buildAndStart();
        TestingServer testingServer = IntegrationTestHelper.createTestingServer()) {
      WireMock.configureFor(wireMockServerWrapper.getWireMockServer().port());

      IntegrationTestHelper.setupConfig(wireMockServerWrapper, tcpServer, testingServer);
      IntegrationTestHelper.stubInfluxdb(wireMockServerWrapper);
      IntegrationTestHelper.stubConrepoOkLookupByVehiclePlatformId(wireMockServerWrapper, DeviceDetailedEntryUtil.createDeviceDetailedEntry());
      IntegrationTestHelper.stubSubRepo();

      try (ConfigurableApplicationContext configurableApplicationContext = IntegrationTestHelper.runSpringApplication(AppConfig.class)) {
        DataUtil.insertSendSchemasInDatabase(configurableApplicationContext.getBean(DataSource.class));

        DataUtil.createAndInsertDeviceInfo(configurableApplicationContext.getBean(DeviceInfoWriterFactory.class));

        JmsUtil.sendMtMessage(DataUtil.createMtMessageWithVpi());
        Message message = MessageUtils.receiveAndVerifyMtMessageViaTcpSocket(tcpServer, DataUtil.VPI, DataUtil.HANDLE);

        MessageUtils.createMtAckMessageAndReplyViaTcpSocket(message);
        DataUtil.checkMtStatusMessageWithVpi(JmsUtil.receiveMtStatusMessage().get());
      }
    }
  }
}
