package com.wirelesscar.tcevce.integration.tests.util;

import java.io.Closeable;

import org.apache.activemq.artemis.core.server.embedded.EmbeddedActiveMQ;

import com.volvo.tisp.vc.main.utils.lib.Validate;

public class EmbeddedActiveMqWrapper implements Closeable {
  private final EmbeddedActiveMQ embeddedActiveMQ;

  public EmbeddedActiveMqWrapper(EmbeddedActiveMQ embeddedActiveMQ) {
    Validate.notNull(embeddedActiveMQ, "embeddedActiveMQ");

    this.embeddedActiveMQ = embeddedActiveMQ;
  }

  @Override
  public void close() {
    try {
      embeddedActiveMQ.stop();
    } catch (RuntimeException e) {
      throw e;
    } catch (Exception e) {
      throw new IllegalStateException(e);
    }
  }
}
