package com.wirelesscar.tcevce.integration.tests.util;

import java.io.IOException;
import java.time.Duration;
import java.util.Enumeration;
import java.util.Optional;

import jakarta.jms.Connection;
import jakarta.jms.ConnectionFactory;
import jakarta.jms.DeliveryMode;
import jakarta.jms.Destination;
import jakarta.jms.JMSException;
import jakarta.jms.Message;
import jakarta.jms.MessageConsumer;
import jakarta.jms.MessageProducer;
import jakarta.jms.Queue;
import jakarta.jms.QueueBrowser;
import jakarta.jms.Session;
import jakarta.jms.TextMessage;
import jakarta.xml.bind.JAXBException;

import org.apache.activemq.artemis.jms.client.ActiveMQConnectionFactory;
import org.awaitility.Awaitility;

import com.volvo.tisp.framework.jms.TispJmsHeader;
import com.wirelesscar.conrepo.MessageTypes;
import com.wirelesscar.conrepo.api.v1.ActivationNotifyEventMessage;
import com.wirelesscar.tce.api.v2.MoMessage;
import com.wirelesscar.tce.api.v2.MtMessage;
import com.wirelesscar.tce.api.v2.MtStatusMessage;
import com.wirelesscar.tce.client.opus.MessageTypesJms;
import com.wirelesscar.tcevce.connectivityrepo.api.message.jms.ConrepoNotifyJmsController;

public final class JmsUtil {
  public static final String AMQ_URL = "tcp://localhost:61616";
  private static final String DEVICE_NOTIFY_IN_QUEUE_NAME = "LOCAL.LOCAL.LOCAL.TCEVCE." + ConrepoNotifyJmsController.CONREPO1_NOTIFY_IN_QUEUE;
  private static final Duration RECEIVE_DURATION = Duration.ofSeconds(3);

  private JmsUtil() {
    throw new IllegalStateException();
  }

  public static void publishActivationNotifyEventMessage(ActivationNotifyEventMessage activationNotifyEventMessage)
      throws JAXBException, IOException, JMSException {
    sendRequest(activationNotifyEventMessage, DEVICE_NOTIFY_IN_QUEUE_NAME, MessageTypes.ACTIVATION_NOTIFY_MESSAGE_TYPE, MessageTypes.VERSION_1_0);
  }

  public static Optional<MoMessage> receiveMoMessage() throws JMSException, JAXBException {
    return consumeAndUnmarshalMessage("LOCAL.LOCAL.LOCAL.TCEVCE.MO.MESSAGE.IN", RECEIVE_DURATION, MoMessage.class);
  }

  public static Optional<MtStatusMessage> receiveMtStatusMessage() throws JMSException, JAXBException {
    return consumeAndUnmarshalMessage("LOCAL.LOCAL.LOCAL.SOMECMP.MTSTATUS.OUT", RECEIVE_DURATION, MtStatusMessage.class);
  }

  public static void sendMtMessage(MtMessage mtMessage) throws JAXBException, IOException, JMSException {
    sendRequest(mtMessage, "LOCAL.LOCAL.LOCAL.TCEVCE.MT-MESSAGES", MessageTypesJms.TCE_MT_MESSAGE_TYPE, MessageTypesJms.VERSION_2_0);
  }

  public static void waitUtilActivationNotifyEventMessageHasBeenConsumed() {
    Awaitility.await().during(Duration.ofSeconds(1)).until(() -> countMessagesForQueue(DEVICE_NOTIFY_IN_QUEUE_NAME) == 0);
  }

  private static <T> Optional<T> consumeAndUnmarshalMessage(String fullQueueName, Duration timeout, Class<T> clazz) throws JMSException, JAXBException {
    ConnectionFactory connectionFactory = createActiveMQConnectionFactory();
    Optional<Message> optionalMessage = consumeOneMessage(connectionFactory, fullQueueName, timeout);

    if (optionalMessage.isEmpty()) {
      return Optional.empty();
    }

    return Optional.of(IntegrationTestHelper.unmarshal(optionalMessage.get(), clazz));
  }

  private static Optional<Message> consumeOneMessage(ConnectionFactory connectionFactory, String fullQueueName, Duration timeout) throws JMSException {
    try (Connection connection = connectionFactory.createConnection()) {
      connection.start();

      try (Session session = connection.createSession(false, Session.AUTO_ACKNOWLEDGE)) {
        Destination destination = session.createQueue(fullQueueName);

        try (MessageConsumer messageConsumer = session.createConsumer(destination)) {
          Message message = messageConsumer.receive(timeout.toMillis());

          return Optional.ofNullable(message);
        }
      }
    }
  }

  private static int countMessagesForQueue(String fullQueueName) throws JMSException {
    ConnectionFactory connectionFactory = createActiveMQConnectionFactory();
    try (Connection connection = connectionFactory.createConnection()) {
      connection.start();

      try (Session session = connection.createSession(false, Session.AUTO_ACKNOWLEDGE)) {
        Queue queue = session.createQueue(fullQueueName);
        try (QueueBrowser queueBrowser = session.createBrowser(queue)) {
          Enumeration<?> enumeration = queueBrowser.getEnumeration();

          int numberOfMessages = 0;

          while (enumeration.hasMoreElements()) {
            numberOfMessages++;
          }

          return numberOfMessages;
        }
      }
    }
  }

  private static ActiveMQConnectionFactory createActiveMQConnectionFactory() {
    return new ActiveMQConnectionFactory(AMQ_URL);
  }

  private static void publishMessage(ConnectionFactory connectionFactory, String fullQueueName, String xmlString, String messageType, String version)
      throws JMSException {
    try (Connection connection = connectionFactory.createConnection()) {
      connection.start();

      try (Session session = connection.createSession(false, Session.AUTO_ACKNOWLEDGE)) {
        TextMessage textMessage = session.createTextMessage(xmlString);

        textMessage.setStringProperty(TispJmsHeader.MESSAGE_TYPE.value(), messageType);
        textMessage.setStringProperty(TispJmsHeader.MESSAGE_TYPE_VERSION.value(), version);

        Destination destination = session.createQueue(fullQueueName);

        try (MessageProducer messageProducer = session.createProducer(destination)) {
          messageProducer.setDeliveryMode(DeliveryMode.NON_PERSISTENT);
          messageProducer.send(textMessage);
        }
      }
    }
  }

  private static void sendRequest(Object request, String fullQueueName, String messageType, String messageVersion)
      throws JAXBException, IOException, JMSException {
    String xmlString = IntegrationTestHelper.marshalToXml(request);

    ConnectionFactory connectionFactory = createActiveMQConnectionFactory();
    publishMessage(connectionFactory, fullQueueName, xmlString, messageType, messageVersion);
  }
}
