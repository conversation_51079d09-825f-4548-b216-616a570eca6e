package com.wirelesscar.tcevce.integration.tests;

import java.io.IOException;

import org.apache.curator.test.TestingServer;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.context.ConfigurableApplicationContext;

import com.github.tomakehurst.wiremock.client.WireMock;
import com.volvo.tisp.vc.tcp.server.lib.model.HostInfo;
import com.volvo.tisp.vc.tcp.server.lib.server.TcpServer;
import com.volvo.tisp.vc.tcp.server.lib.server.TcpServerBuilder;
import com.wirelesscar.caretrack.dh.caretrack.protocol.ASNException;
import com.wirelesscar.tce.module.api.Message;
import com.wirelesscar.tce.module.api.MetaData;
import com.wirelesscar.tcevce.impl.conf.AppConfig;
import com.wirelesscar.tcevce.integration.tests.util.DataUtil;
import com.wirelesscar.tcevce.integration.tests.util.DeviceDetailedEntryUtil;
import com.wirelesscar.tcevce.integration.tests.util.EmbeddedActiveMqWrapper;
import com.wirelesscar.tcevce.integration.tests.util.IntegrationTestHelper;
import com.wirelesscar.tcevce.integration.tests.util.MessageUtils;
import com.wirelesscar.tcevce.integration.tests.util.WireMockServerWrapper;
import com.wirelesscar.tcevce.wecu.device.info.database.api.DeviceInfoWriterFactory;

class MoSatelliteIntegrationTest {

  private static void receiveAndVerifyMoAckMessage(TcpServer tcpServer) throws IOException, ClassNotFoundException, ASNException {
    Message message = MessageUtils.receiveMessageViaTcpSocket(tcpServer);

    Assertions.assertAll(
        () -> Assertions.assertEquals("true", message.getProperty(MetaData.ACK)),
        () -> Assertions.assertEquals(DataUtil.HANDLE.toString(), message.getProperty(MetaData.HANDLE)),
        () -> Assertions.assertNull(message.getProperty(MetaData.SATELLITE_SOURCE_ADDRESS)),
        () -> Assertions.assertEquals(DataUtil.SATELLITE_ID.toString(), message.getProperty(MetaData.SATELLITE_DEST_ADDRESS)),
        () -> Assertions.assertEquals(MessageUtils.SATELLITE_MESSAGE_ID, message.getProperty(MetaData.SATELLITE_MESSAGE_ID))
    );
    MessageUtils.decodeAsn1PayloadAndVerifyMoAck(message.getPayload());
  }

  @Test
  void integrationTest() throws Exception {
    try (EmbeddedActiveMqWrapper embeddedActiveMqWrapper = IntegrationTestHelper.createAndStartEmbeddedActiveMqWrapper();
        WireMockServerWrapper wireMockServerWrapper = IntegrationTestHelper.createAndStartWireMockServer();
        TcpServer tcpServer = new TcpServerBuilder().buildAndStart();
        TestingServer testingServer = IntegrationTestHelper.createTestingServer()) {
      WireMock.configureFor(wireMockServerWrapper.getWireMockServer().port());

      IntegrationTestHelper.setupConfig(wireMockServerWrapper, tcpServer, testingServer);
      IntegrationTestHelper.stubInfluxdb(wireMockServerWrapper);
      IntegrationTestHelper.stubSubRepo();
      IntegrationTestHelper.stubConrepoNoContentLookupBySatelliteId(wireMockServerWrapper, DataUtil.SATELLITE_ID);

      try (ConfigurableApplicationContext configurableApplicationContext = IntegrationTestHelper.runSpringApplication(AppConfig.class)) {
        final HostInfo hostInfo = new HostInfo("localhost", 20595);

        Message message = MessageUtils.createMoSatelliteMessage(DataUtil.SATELLITE_ID, false);

        MessageUtils.sendMessageOverTcp(message, hostInfo);
        MessageUtils.verifyMoAckNoVehiclesFound(tcpServer);
        MessageUtils.verifyMoMessageIsNotPublished();

        IntegrationTestHelper.stubConrepoOkLookupBySatelliteId(wireMockServerWrapper, DeviceDetailedEntryUtil.createDeviceDetailedEntry(DataUtil.SATELLITE_ID));
        MessageUtils.sendMessageOverTcp(message, hostInfo);
        receiveAndVerifyMoAckMessage(tcpServer);
        MessageUtils.verifyMoMessageIsPublished(DataUtil.HANDLE, DataUtil.VPI.toString());

        DataUtil.deleteDevice(configurableApplicationContext.getBean(DeviceInfoWriterFactory.class), DataUtil.HANDLE);

        IntegrationTestHelper.stubConrepoOkLookupBySatelliteId(wireMockServerWrapper, DataUtil.createDeviceDetailedEntryWithSatelliteAndHandleOnly());
        MessageUtils.sendMessageOverTcp(message, hostInfo);
        receiveAndVerifyMoAckMessage(tcpServer);
        MessageUtils.verifyMoMessageIsPublished(DataUtil.HANDLE, DataUtil.HANDLE.toString());
      }
    }
  }
}
