package com.wirelesscar.tcevce.integration.tests.util;

import com.volvo.tisp.vc.common.dto.lib.vehicle.Imsi;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Ipv4Address;
import com.wirelesscar.conrepo.api.v1.DeviceDetailedEntry;
import com.wirelesscar.conrepo.api.v1.DeviceSim;
import com.wirelesscar.tcevce.wecu.device.info.database.model.SatelliteId;

public final class DeviceDetailedEntryUtil {
  private DeviceDetailedEntryUtil() {
    throw new IllegalStateException();
  }

  public static DeviceDetailedEntry createDeviceDetailedEntry() {
    DeviceDetailedEntry deviceDetailedEntry = new DeviceDetailedEntry();

    deviceDetailedEntry.setHandle(DataUtil.HANDLE.toString());
    deviceDetailedEntry.setSimEntry(createDeviceSim());
    deviceDetailedEntry.setVehiclePlatformId(DataUtil.VPI.toString());

    return deviceDetailedEntry;
  }

  public static DeviceDetailedEntry createDeviceDetailedEntry(Ipv4Address ipv4Address) {
    DeviceDetailedEntry deviceDetailedEntry = createDeviceDetailedEntry();

    deviceDetailedEntry.getSimEntry().setIp(ipv4Address.toString());

    return deviceDetailedEntry;
  }

  public static DeviceDetailedEntry createDeviceDetailedEntry(SatelliteId satelliteId) {
    DeviceDetailedEntry deviceDetailedEntry = createDeviceDetailedEntry();

    deviceDetailedEntry.setSatelliteId(satelliteId.toString());

    return deviceDetailedEntry;
  }

  private static DeviceSim createDeviceSim() {
    DeviceSim deviceSim = new DeviceSim();

    deviceSim.setMsisdn(DataUtil.MSISDN.toString());
    deviceSim.setImsi(Imsi.ofLong(1L).toString());
    deviceSim.setPort(1234);
    deviceSim.setIp("127.0.0.1");
    deviceSim.setOperator("VOLVO");

    return deviceSim;
  }
}
