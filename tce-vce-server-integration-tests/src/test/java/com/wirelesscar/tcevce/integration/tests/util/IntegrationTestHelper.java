package com.wirelesscar.tcevce.integration.tests.util;

import static com.github.tomakehurst.wiremock.client.WireMock.equalTo;
import static com.github.tomakehurst.wiremock.client.WireMock.urlPathEqualTo;

import java.io.IOException;
import java.io.StringReader;
import java.io.StringWriter;

import jakarta.jms.JMSException;
import jakarta.jms.Message;
import jakarta.jms.TextMessage;
import jakarta.xml.bind.JAXBContext;
import jakarta.xml.bind.JAXBException;
import jakarta.xml.bind.Marshaller;
import jakarta.xml.bind.Unmarshaller;

import javax.xml.transform.stream.StreamSource;

import org.apache.activemq.artemis.core.config.Configuration;
import org.apache.activemq.artemis.core.config.impl.ConfigurationImpl;
import org.apache.activemq.artemis.core.server.embedded.EmbeddedActiveMQ;
import org.apache.activemq.artemis.jms.client.ActiveMQTextMessage;
import org.apache.curator.test.TestingServer;
import org.springframework.boot.SpringApplication;
import org.springframework.context.ConfigurableApplicationContext;

import com.github.tomakehurst.wiremock.WireMockServer;
import com.github.tomakehurst.wiremock.client.WireMock;
import com.github.tomakehurst.wiremock.common.Slf4jNotifier;
import com.github.tomakehurst.wiremock.core.WireMockConfiguration;
import com.volvo.tisp.subscriptionrepository.client.Destination;
import com.volvo.tisp.subscriptionrepository.client.SubscriptionStubber;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Ipv4Address;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Msisdn;
import com.volvo.tisp.vc.tcp.server.lib.server.TcpServer;
import com.wirelesscar.config.mock.MockConfiguration;
import com.wirelesscar.conrepo.ContentTypes;
import com.wirelesscar.conrepo.api.v1.DeviceDetailedEntry;
import com.wirelesscar.tce.client.opus.MessageTypesJms;
import com.wirelesscar.tcevce.wecu.device.info.database.model.SatelliteId;

public final class IntegrationTestHelper {
  private static final String INFLUX_DB_NAME = "vgcs_default";

  private IntegrationTestHelper() {
    throw new IllegalStateException();
  }

  public static EmbeddedActiveMqWrapper createAndStartEmbeddedActiveMqWrapper() throws Exception {
    EmbeddedActiveMQ embeddedActiveMQ = new EmbeddedActiveMQ();
    embeddedActiveMQ.setConfiguration(createActiveMQConfiguration());
    embeddedActiveMQ.start();
    return new EmbeddedActiveMqWrapper(embeddedActiveMQ);
  }

  public static WireMockServerWrapper createAndStartWireMockServer() {
    WireMockServer wireMockServer = createWireMockServer();
    wireMockServer.start();

    return new WireMockServerWrapper(wireMockServer);
  }

  public static TestingServer createTestingServer() throws Exception {
    TestingServer testingServer = new TestingServer();
    testingServer.start();
    return testingServer;
  }

  public static <T> String marshalToXml(T object) throws JAXBException, IOException {
    JAXBContext jaxbContext = JAXBContext.newInstance(object.getClass());
    Marshaller marshaller = jaxbContext.createMarshaller();

    try (StringWriter stringWriter = new StringWriter()) {
      marshaller.marshal(object, stringWriter);
      return stringWriter.toString();
    }
  }

  public static ConfigurableApplicationContext runSpringApplication(Class<?> configurationClass) {
    return SpringApplication.run(new Class<?>[] {configurationClass}, new String[0]);
  }

  public static void setupConfig(WireMockServerWrapper wireMockServerWrapper, TcpServer tcpServer, TestingServer testingServer) {
    System.setProperty("service.confdir", "src/test/resources");
    System.setProperty("VGTZOOKEEPER", testingServer.getConnectString());

    MockConfiguration mockConfiguration = MockConfiguration.getConfig();
    mockConfiguration.deleteAllProperties();
    mockConfiguration.setSite("local");

    final String componentShortName = mockConfiguration.getComponentShortName();
    final int tcpServerPortNumber = tcpServer.getHostInfo().port();
    final int wireMockPortNumber = wireMockServerWrapper.getWireMockServer().port();
    mockConfiguration.setPropertySpecific(componentShortName, "tce.testdb.inmemory", "true");

    mockConfiguration.setPropertySpecific(componentShortName, "VGTZOOKEEPER", testingServer.getConnectString());
    mockConfiguration.setPlatformProperty("spring.artemis.broker-url", JmsUtil.AMQ_URL);

    mockConfiguration.setPlatformProperty("management.influx.metrics.export.uri", "http://localhost:" + wireMockPortNumber);
    mockConfiguration.setPropertySpecific(componentShortName, "management.influx.metrics.export.db", INFLUX_DB_NAME);
    mockConfiguration.setPropertySpecific(componentShortName, "management.influx.metrics.export.retention-policy", "30days");

    mockConfiguration.setPlatformProperty("servicediscovery.active-by-default", "true");
    mockConfiguration.setPropertySpecific(componentShortName, "servicediscovery.conrepo", "http://localhost:" + wireMockPortNumber + "/");
    mockConfiguration.setPlatformProperty("servicediscovery.subr", "http://localhost:" + wireMockPortNumber + "/");

    mockConfiguration.setPropertySpecific(componentShortName, "UDP.scooter-agent.enabled", "false");
    mockConfiguration.setPropertySpecific(componentShortName, "UDP.udp.bindadress", "127.0.0.1");
    mockConfiguration.setPropertySpecific(componentShortName, "SAT.tcp.stack-specific-server-port-list", "127.0.0.1:" + tcpServerPortNumber);
    mockConfiguration.setPropertySpecific(componentShortName, "SMS.tcp.stack-specific-server-port-list", "127.0.0.1:" + tcpServerPortNumber);

    mockConfiguration.setPropertySpecific(componentShortName, "device-service.maximum-cache-size-in-bytes", "1000000");

    mockConfiguration.setPropertySpecific(componentShortName, "lifecyclemanager.startup.runlevelstate", "RUNNING");
    mockConfiguration.setPropertySpecific(componentShortName, "tce.repo.cache.maintenance.enable", "false");

    mockConfiguration.setPropertySpecific(componentShortName, "message.server.jms.api.lookup.name", "MT-WRITE");
  }

  public static void stubConrepoNoContentLookupByIp(WireMockServerWrapper wireMockServerWrapper, Ipv4Address ipv4Address) {
    wireMockServerWrapper.getWireMockServer()
        .stubFor(WireMock.get(urlPathEqualTo("/conrepo/ip"))
            .withQueryParam("ip", equalTo(ipv4Address.toString()))
            .willReturn(WireMock.noContent()));
  }

  public static void stubConrepoNoContentLookupByMsisdn(WireMockServerWrapper wireMockServerWrapper, Msisdn msisdn) {
    wireMockServerWrapper.getWireMockServer()
        .stubFor(WireMock.get("/conrepo/msisdn/" + msisdn)
            .willReturn(WireMock.noContent()));
  }

  public static void stubConrepoNoContentLookupBySatelliteId(WireMockServerWrapper wireMockServerWrapper, SatelliteId satelliteId) {
    wireMockServerWrapper.getWireMockServer()
        .stubFor(WireMock.get("/conrepo/satid/" + satelliteId)
            .willReturn(WireMock.noContent()));
  }

  public static void stubConrepoOkLookupByHandle(WireMockServerWrapper wireMockServerWrapper, DeviceDetailedEntry deviceDetailedEntry)
      throws JAXBException, IOException {
    wireMockServerWrapper.getWireMockServer()
        .stubFor(WireMock.get(urlPathEqualTo("/conrepo/handle"))
            .withQueryParam("handle", equalTo(deviceDetailedEntry.getHandle()))
            .willReturn(WireMock.okForContentType(ContentTypes.CONREPO_1_0_XML, marshalToXml(deviceDetailedEntry))));
  }

  public static void stubConrepoOkLookupByIp(WireMockServerWrapper wireMockServerWrapper, DeviceDetailedEntry deviceDetailedEntry)
      throws JAXBException, IOException {
    wireMockServerWrapper.getWireMockServer()
        .stubFor(WireMock.get(urlPathEqualTo("/conrepo/ip"))
            .withQueryParam("ip", equalTo(deviceDetailedEntry.getSimEntry().getIp()))
            .willReturn(WireMock.okForContentType(ContentTypes.CONREPO_1_0_XML, marshalToXml(deviceDetailedEntry))));
  }

  public static void stubConrepoOkLookupByMsisdn(WireMockServerWrapper wireMockServerWrapper, DeviceDetailedEntry deviceDetailedEntry)
      throws JAXBException, IOException {
    wireMockServerWrapper.getWireMockServer()
        .stubFor(WireMock.get("/conrepo/msisdn/" + deviceDetailedEntry.getSimEntry().getMsisdn())
            .willReturn(WireMock.okForContentType(ContentTypes.CONREPO_1_0_XML, marshalToXml(deviceDetailedEntry))));
  }

  public static void stubConrepoOkLookupBySatelliteId(WireMockServerWrapper wireMockServerWrapper, DeviceDetailedEntry deviceDetailedEntry)
      throws JAXBException, IOException {
    wireMockServerWrapper.getWireMockServer()
        .stubFor(WireMock.get("/conrepo/satid/" + deviceDetailedEntry.getSatelliteId())
            .willReturn(WireMock.okForContentType(ContentTypes.CONREPO_1_0_XML, marshalToXml(deviceDetailedEntry))));
  }

  public static void stubConrepoOkLookupByVehiclePlatformId(WireMockServerWrapper wireMockServerWrapper, DeviceDetailedEntry deviceDetailedEntry)
      throws JAXBException, IOException {
    wireMockServerWrapper.getWireMockServer()
        .stubFor(WireMock.get("/conrepo/vehicleplatformid/" + deviceDetailedEntry.getVehiclePlatformId())
            .willReturn(WireMock.okForContentType(ContentTypes.CONREPO_1_0_XML, marshalToXml(deviceDetailedEntry))));
  }

  public static void stubInfluxdb(WireMockServerWrapper wireMockServerWrapper) {
    WireMockServer wireMockServer = wireMockServerWrapper.getWireMockServer();

    wireMockServer.stubFor(WireMock.post("/write?db=" + INFLUX_DB_NAME + "&precision=ns&rp=30days").willReturn(WireMock.ok()));
    wireMockServer.stubFor(WireMock.post("/write?consistency=one&precision=ms&db=" + INFLUX_DB_NAME + "&rp=30days").willReturn(WireMock.ok()));
    wireMockServer.stubFor(WireMock.post("/query?q=CREATE+DATABASE+%22" + INFLUX_DB_NAME + "%22+WITH+NAME+30days").willReturn(WireMock.ok()));
    wireMockServer.stubFor(WireMock.post("/events/").willReturn(WireMock.ok()));
  }

  public static void stubSubRepo() {
    Destination destination = new Destination(MessageTypesJms.TCE_MO_MESSAGE_TYPE, MessageTypesJms.VERSION_2_0,
        "activemq:queue:MO.MESSAGE.IN", "destinationId");

    SubscriptionStubber.builder()
        .whenPublisherWithName("tcevce")
        .triesToPublishMessageOfType(MessageTypesJms.TCE_MO_MESSAGE_TYPE)
        .thenMessageShouldBeDeliveredTo(destination);
  }

  public static <T> T unmarshal(Message message, Class<T> type) throws JAXBException, JMSException {
    TextMessage textMessage = (ActiveMQTextMessage) message;
    JAXBContext jaxbContext = JAXBContext.newInstance(type);
    Unmarshaller unmarshaller = jaxbContext.createUnmarshaller();

    try (StringReader stringReader = new StringReader(textMessage.getText())) {
      return unmarshaller.unmarshal(new StreamSource(stringReader), type).getValue();
    }
  }

  private static Configuration createActiveMQConfiguration() throws Exception {
    return new ConfigurationImpl()
        .addAcceptorConfiguration("tcp", JmsUtil.AMQ_URL)
        .setPersistenceEnabled(false)
        .setSecurityEnabled(false);
  }

  private static WireMockServer createWireMockServer() {
    WireMockConfiguration wireMockConfiguration = new WireMockConfiguration().dynamicPort().notifier(new Slf4jNotifier(false));
    return new WireMockServer(wireMockConfiguration);
  }
}
