package com.wirelesscar.tcevce.integration.tests;

import java.io.IOException;
import java.util.Optional;

import javax.sql.DataSource;

import org.apache.curator.test.TestingServer;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.context.ConfigurableApplicationContext;

import com.github.tomakehurst.wiremock.client.WireMock;
import com.volvo.tisp.vc.tcp.server.lib.server.TcpServer;
import com.volvo.tisp.vc.tcp.server.lib.server.TcpServerBuilder;
import com.wirelesscar.conrepo.api.v1.DeviceDetailedEntry;
import com.wirelesscar.tce.module.api.Message;
import com.wirelesscar.tce.module.api.MessageStatus;
import com.wirelesscar.tce.module.api.MetaData;
import com.wirelesscar.tcevce.impl.conf.AppConfig;
import com.wirelesscar.tcevce.integration.tests.util.DataUtil;
import com.wirelesscar.tcevce.integration.tests.util.DeviceDetailedEntryUtil;
import com.wirelesscar.tcevce.integration.tests.util.EmbeddedActiveMqWrapper;
import com.wirelesscar.tcevce.integration.tests.util.IntegrationTestHelper;
import com.wirelesscar.tcevce.integration.tests.util.JmsUtil;
import com.wirelesscar.tcevce.integration.tests.util.MessageUtils;
import com.wirelesscar.tcevce.integration.tests.util.WireMockServerWrapper;
import com.wirelesscar.tcevce.wecu.device.info.database.api.DeviceInfoWriterFactory;
import com.wirelesscar.tcevce.wecu.device.info.database.model.DeviceInfo;

class MtAckSatelliteStatusIntegrationTest {
  private static void createAndInsertDeviceInfo(ConfigurableApplicationContext configurableApplicationContext) {
    DeviceInfo deviceInfo = DataUtil.createDeviceInfoBuilder()
        .setSimInfo(Optional.empty())
        .setSatelliteId(Optional.of(DataUtil.SATELLITE_ID))
        .build();
    DataUtil.createAndInsertDeviceInfo(deviceInfo, configurableApplicationContext.getBean(DeviceInfoWriterFactory.class));
  }

  private static void receiveAndVerifySatelliteStatusMessage(TcpServer tcpServer) throws ClassNotFoundException, IOException {
    Message message = MessageUtils.receiveMessageViaTcpSocket(tcpServer);

    Assertions.assertAll(
        () -> Assertions.assertEquals("true", message.getProperty(MetaData.ACK)),
        () -> Assertions.assertEquals(DataUtil.SATELLITE_ID.toString(), message.getProperty(MetaData.SATELLITE_DEST_ADDRESS)),
        () -> Assertions.assertEquals(MessageStatus.DELIVERED, message.getStatus()),
        () -> Assertions.assertNull(message.getMessageId()),
        () -> Assertions.assertNull(message.getProperty(MetaData.SATELLITE_SOURCE_ADDRESS)),
        () -> Assertions.assertEquals(DataUtil.SATELLITE_ID.toString(), message.getProperty(MetaData.SATELLITE_DEST_ADDRESS))
    );
  }

  @Test
  void integrationTest() throws Exception {
    try (EmbeddedActiveMqWrapper embeddedActiveMqWrapper = IntegrationTestHelper.createAndStartEmbeddedActiveMqWrapper();
        WireMockServerWrapper wireMockServerWrapper = IntegrationTestHelper.createAndStartWireMockServer();
        TcpServer tcpServer = new TcpServerBuilder().buildAndStart();
        TestingServer testingServer = IntegrationTestHelper.createTestingServer()) {
      WireMock.configureFor(wireMockServerWrapper.getWireMockServer().port());

      IntegrationTestHelper.setupConfig(wireMockServerWrapper, tcpServer, testingServer);
      IntegrationTestHelper.stubInfluxdb(wireMockServerWrapper);
      IntegrationTestHelper.stubSubRepo();

      try (ConfigurableApplicationContext configurableApplicationContext = IntegrationTestHelper.runSpringApplication(AppConfig.class)) {
        DataUtil.insertSendSchemasInDatabase(configurableApplicationContext.getBean(DataSource.class));
        createAndInsertDeviceInfo(configurableApplicationContext);

        Message moSatelliteMessage = MessageUtils.createMoSatelliteMessage(DataUtil.SATELLITE_ID, false);

        DeviceDetailedEntry deviceDetailedEntry = DeviceDetailedEntryUtil.createDeviceDetailedEntry(DataUtil.SATELLITE_ID);
        IntegrationTestHelper.stubConrepoOkLookupBySatelliteId(wireMockServerWrapper, deviceDetailedEntry);

        MessageUtils.createMtAckMessageAndReplyViaTcpSocket(moSatelliteMessage);
        receiveAndVerifySatelliteStatusMessage(tcpServer);

        Assertions.assertTrue(JmsUtil.receiveMtStatusMessage().isEmpty());
      }
    }
  }
}
