package com.wirelesscar.tcevce.integration.tests;

import java.io.IOException;
import java.util.Optional;

import jakarta.jms.JMSException;
import jakarta.xml.bind.JAXBException;

import org.apache.curator.test.TestingServer;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.context.ConfigurableApplicationContext;

import com.volvo.tisp.vc.tcp.server.lib.server.TcpServer;
import com.volvo.tisp.vc.tcp.server.lib.server.TcpServerBuilder;
import com.wirelesscar.conrepo.api.v1.ActivationNotifyEvent;
import com.wirelesscar.conrepo.api.v1.ActivationNotifyEventMessage;
import com.wirelesscar.conrepo.api.v1.ChangeStatus;
import com.wirelesscar.conrepo.api.v1.DeviceDetailedEntry;
import com.wirelesscar.conrepo.api.v1.DeviceSim;
import com.wirelesscar.conrepo.api.v1.State;
import com.wirelesscar.tcevce.impl.conf.AppConfig;
import com.wirelesscar.tcevce.integration.tests.util.DataUtil;
import com.wirelesscar.tcevce.integration.tests.util.EmbeddedActiveMqWrapper;
import com.wirelesscar.tcevce.integration.tests.util.IntegrationTestHelper;
import com.wirelesscar.tcevce.integration.tests.util.JmsUtil;
import com.wirelesscar.tcevce.integration.tests.util.WireMockServerWrapper;
import com.wirelesscar.tcevce.wecu.device.info.database.api.DeviceInfoReader;
import com.wirelesscar.tcevce.wecu.device.info.database.api.DeviceInfoWriter;
import com.wirelesscar.tcevce.wecu.device.info.database.api.DeviceInfoWriterFactory;
import com.wirelesscar.tcevce.wecu.device.info.database.model.DeviceInfo;
import com.wirelesscar.tcevce.wecu.device.info.database.model.DeviceInfoBuilder;
import com.wirelesscar.tcevce.wecu.device.info.database.model.DeviceInfoId;
import com.wirelesscar.tcevce.wecu.device.info.database.model.DeviceSequence;
import com.wirelesscar.tcevce.wecu.device.info.database.model.Handle;
import com.wirelesscar.tcevce.wecu.device.info.database.model.PersistedDeviceInfo;
import com.wirelesscar.tcevce.wecu.device.info.database.model.PersistedDeviceSequence;
import com.wirelesscar.tcevce.wecu.device.info.database.model.SimInfo;

class ActivationNotifyEventMessageV1IntegrationTest {
  private static int countDevices(DeviceInfoWriterFactory deviceInfoWriterFactory) {
    try (DeviceInfoReader deviceInfoReader = createDeviceInfoWriter(deviceInfoWriterFactory)) {
      return deviceInfoReader.countAllDevices();
    }
  }

  private static ActivationNotifyEvent createActivationNotifyEvent(ChangeStatus changeStatus, State state) {
    ActivationNotifyEvent activationNotifyEvent = new ActivationNotifyEvent();

    activationNotifyEvent.setChangeStatus(changeStatus);
    activationNotifyEvent.setDeviceDetail(createDeviceDetailedEntry(state));

    return activationNotifyEvent;
  }

  private static ActivationNotifyEventMessage createActivationNotifyEventMessage(ChangeStatus changeStatus, State state) {
    ActivationNotifyEventMessage activationNotifyEventMessage = new ActivationNotifyEventMessage();
    activationNotifyEventMessage.getActivationNotifyEvents().add(createActivationNotifyEvent(changeStatus, state));

    return activationNotifyEventMessage;
  }

  private static DeviceDetailedEntry createDeviceDetailedEntry(State state) {
    DeviceDetailedEntry deviceDetailedEntry = new DeviceDetailedEntry();

    deviceDetailedEntry.setHandle(DataUtil.HANDLE.toString());
    deviceDetailedEntry.setSimEntry(createDeviceSim());
    deviceDetailedEntry.setSatelliteId(DataUtil.SATELLITE_ID.toString());
    deviceDetailedEntry.setState(state);
    deviceDetailedEntry.setVehiclePlatformId(DataUtil.VPI.toString());

    return deviceDetailedEntry;
  }

  private static DeviceInfoWriter createDeviceInfoWriter(DeviceInfoWriterFactory deviceInfoWriterFactory) {
    return deviceInfoWriterFactory.createReadCommitted();
  }

  private static DeviceSim createDeviceSim() {
    DeviceSim deviceSim = new DeviceSim();

    deviceSim.setImsi(DataUtil.IMSI.toString());
    deviceSim.setIp(DataUtil.IPV4_ADDRESS.toString());
    deviceSim.setMsisdn(DataUtil.MSISDN.toString());
    deviceSim.setOperator(DataUtil.MOBILE_NETWORK_OPERATOR.toString());
    deviceSim.setPort(DataUtil.IPV4_PORT.toInt());

    return deviceSim;
  }

  private static DeviceSequence findDeviceSequence(DeviceInfoWriter deviceInfoWriter, DeviceInfoId deviceInfoId) {
    return deviceInfoWriter.findDeviceSequenceByDeviceInfoId(deviceInfoId)
        .map(PersistedDeviceSequence::getDeviceSequence)
        .get();
  }

  private static void sendActivationNotifyEventMessageAndVerifyDevice(DeviceInfoWriterFactory deviceInfoWriterFactory)
      throws JAXBException, IOException, JMSException {
    ActivationNotifyEventMessage activationNotifyEventMessage = createActivationNotifyEventMessage(ChangeStatus.UPDATED, State.ACTIVATED);

    JmsUtil.publishActivationNotifyEventMessage(activationNotifyEventMessage);
    JmsUtil.waitUtilActivationNotifyEventMessageHasBeenConsumed();
    Assertions.assertEquals(1, countDevices(deviceInfoWriterFactory));

    try (DeviceInfoWriter deviceInfoWriter = createDeviceInfoWriter(deviceInfoWriterFactory)) {
      Optional<PersistedDeviceInfo> actualPersistedDeviceInfoOptional = deviceInfoWriter.findDeviceInfoByVpi(DataUtil.VPI);

      verifyDeviceInfoEquals(activationNotifyEventMessage.getActivationNotifyEvents().get(0).getDeviceDetail(),
          actualPersistedDeviceInfoOptional.map(PersistedDeviceInfo::getDeviceInfo).get()
      );

      Optional<DeviceSequence> actualDeviceSequenceOptional = actualPersistedDeviceInfoOptional.map(PersistedDeviceInfo::getDeviceInfoId)
          .flatMap(deviceInfoWriter::findDeviceSequenceByDeviceInfoId)
          .map(PersistedDeviceSequence::getDeviceSequence);
      Assertions.assertEquals(Optional.of(DataUtil.createDeviceSequence()), actualDeviceSequenceOptional);
    }
  }

  private static void sendDeactivationNotifyEventMessageAndVerifyDevice(DeviceInfoWriterFactory deviceInfoWriterFactory)
      throws JAXBException, IOException, JMSException {
    ActivationNotifyEventMessage activationNotifyEventMessage = createActivationNotifyEventMessage(ChangeStatus.DELETED, State.DEACTIVATED);

    JmsUtil.publishActivationNotifyEventMessage(activationNotifyEventMessage);
    JmsUtil.waitUtilActivationNotifyEventMessageHasBeenConsumed();

    Assertions.assertEquals(0, countDevices(deviceInfoWriterFactory));
  }

  private static void updateDeviceAndVerify(DeviceInfoWriterFactory deviceInfoWriterFactory) {
    try (DeviceInfoWriter deviceInfoWriter = createDeviceInfoWriter(deviceInfoWriterFactory)) {
      Handle handle = DataUtil.HANDLE;

      PersistedDeviceInfo oldPersistedDeviceInfo = deviceInfoWriter.findDeviceInfoByHandle(handle).get();

      DeviceInfoId deviceInfoId = oldPersistedDeviceInfo.getDeviceInfoId();
      DeviceInfo oldDeviceInfo = oldPersistedDeviceInfo.getDeviceInfo();

      DeviceSequence expectedDeviceSequence = findDeviceSequence(deviceInfoWriter, deviceInfoId);
      updateDeviceInfo(oldDeviceInfo, deviceInfoWriter);

      DeviceInfo actualDeviceInfo = deviceInfoWriter.findDeviceInfoByHandle(handle)
          .map(PersistedDeviceInfo::getDeviceInfo)
          .get();

      verifyDeviceInfoAfterUpdate(oldDeviceInfo, actualDeviceInfo);

      DeviceSequence actualDeviceSequence = findDeviceSequence(deviceInfoWriter, deviceInfoId);
      Assertions.assertEquals(expectedDeviceSequence, actualDeviceSequence);
    }
  }

  private static void updateDeviceInfo(DeviceInfo deviceInfo, DeviceInfoWriter deviceInfoWriter) {
    DeviceInfo deviceInfoToUpdate = DeviceInfoBuilder.from(deviceInfo)
        .setSimInfo(Optional.empty())
        .setSatelliteId(Optional.empty())
        .build();

    int updatedRows = deviceInfoWriter.updateDeviceInfoByHandle(deviceInfoToUpdate);
    Assertions.assertEquals(1, updatedRows);
  }

  private static void verifyDeviceInfoAfterUpdate(DeviceInfo oldDeviceInfo, DeviceInfo actualDeviceInfo) {
    Assertions.assertEquals(Optional.empty(), actualDeviceInfo.getSimInfo());
    Assertions.assertEquals(Optional.empty(), actualDeviceInfo.getSatelliteId());
    Assertions.assertEquals(oldDeviceInfo.getVpi(), actualDeviceInfo.getVpi());
    Assertions.assertEquals(oldDeviceInfo.getHandle(), actualDeviceInfo.getHandle());
  }

  private static void verifyDeviceInfoEquals(DeviceDetailedEntry deviceDetailedEntry, DeviceInfo deviceInfo) {
    Assertions.assertEquals(deviceDetailedEntry.getHandle(), deviceInfo.getHandle().toString());
    Assertions.assertEquals(deviceDetailedEntry.getSatelliteId(), deviceInfo.getSatelliteId().get().toString());
    Assertions.assertEquals(deviceDetailedEntry.getVehiclePlatformId(), deviceInfo.getVpi().get().toString());

    verifySimInfo(deviceDetailedEntry.getSimEntry(), deviceInfo.getSimInfo().get());
  }

  private static void verifySimInfo(DeviceSim deviceSim, SimInfo simInfo) {
    Assertions.assertEquals(deviceSim.getPort(), simInfo.getIpv4Port().toInt());
    Assertions.assertEquals(deviceSim.getImsi(), simInfo.getImsi().get().toString());
    Assertions.assertEquals(deviceSim.getIp(), simInfo.getIpv4Address().toString());
    Assertions.assertEquals(deviceSim.getMsisdn(), simInfo.getMsisdn().toString());
    Assertions.assertEquals(deviceSim.getOperator(), simInfo.getMobileNetworkOperator().toString());
  }

  @Test
  void integrationTest() throws Exception {
    try (EmbeddedActiveMqWrapper embeddedActiveMqWrapper = IntegrationTestHelper.createAndStartEmbeddedActiveMqWrapper();
        WireMockServerWrapper wireMockServerWrapper = IntegrationTestHelper.createAndStartWireMockServer();
        TcpServer tcpServer = new TcpServerBuilder().buildAndStart();
        TestingServer testingServer = IntegrationTestHelper.createTestingServer()) {
      IntegrationTestHelper.setupConfig(wireMockServerWrapper, tcpServer, testingServer);
      IntegrationTestHelper.stubInfluxdb(wireMockServerWrapper);

      try (ConfigurableApplicationContext configurableApplicationContext = IntegrationTestHelper.runSpringApplication(AppConfig.class)) {
        DeviceInfoWriterFactory deviceInfoWriterFactory = configurableApplicationContext.getBeanFactory().getBean(DeviceInfoWriterFactory.class);

        Assertions.assertEquals(0, countDevices(deviceInfoWriterFactory));

        sendDeactivationNotifyEventMessageAndVerifyDevice(deviceInfoWriterFactory);

        sendActivationNotifyEventMessageAndVerifyDevice(deviceInfoWriterFactory);
        updateDeviceAndVerify(deviceInfoWriterFactory);
        sendActivationNotifyEventMessageAndVerifyDevice(deviceInfoWriterFactory);

        sendDeactivationNotifyEventMessageAndVerifyDevice(deviceInfoWriterFactory);
      }
    }
  }
}
