package com.wirelesscar.tcevce.integration.tests.util;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Optional;
import java.util.function.Consumer;

import javax.sql.DataSource;

import org.jdbi.v3.core.Jdbi;
import org.junit.jupiter.api.Assertions;

import com.volvo.tisp.vc.common.dto.lib.jms.CorrelationId;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Imsi;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Ipv4Address;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Msisdn;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.tisp.vc.main.utils.lib.type.Either;
import com.wirelesscar.caretrack.dh.caretrack.protocol.ASNException;
import com.wirelesscar.caretrack.dh.caretrack.protocol.MessageContent;
import com.wirelesscar.caretrack.dh.caretrack.protocol.PERStream;
import com.wirelesscar.conrepo.api.v1.DeviceDetailedEntry;
import com.wirelesscar.tce.api.v2.MtMessage;
import com.wirelesscar.tce.api.v2.MtStatusMessage;
import com.wirelesscar.tce.api.v2.MtStatusReplyOption;
import com.wirelesscar.tce.api.v2.SchedulerOption;
import com.wirelesscar.tce.module.api.EnqueueingType;
import com.wirelesscar.tcevce.wecu.device.info.database.api.DeviceInfoWriter;
import com.wirelesscar.tcevce.wecu.device.info.database.api.DeviceInfoWriterFactory;
import com.wirelesscar.tcevce.wecu.device.info.database.model.DeviceInfo;
import com.wirelesscar.tcevce.wecu.device.info.database.model.DeviceInfoBuilder;
import com.wirelesscar.tcevce.wecu.device.info.database.model.DeviceInfoId;
import com.wirelesscar.tcevce.wecu.device.info.database.model.DeviceSequence;
import com.wirelesscar.tcevce.wecu.device.info.database.model.DeviceSequenceBuilder;
import com.wirelesscar.tcevce.wecu.device.info.database.model.InsertionFailure;
import com.wirelesscar.tcevce.wecu.device.info.database.model.Ipv4Port;
import com.wirelesscar.tcevce.wecu.device.info.database.model.MobileNetworkOperator;
import com.wirelesscar.tcevce.wecu.device.info.database.model.SatelliteId;
import com.wirelesscar.tcevce.wecu.device.info.database.model.SequenceNumber;
import com.wirelesscar.tcevce.wecu.device.info.database.model.SimInfo;
import com.wirelesscar.tcevce.wecu.device.info.database.model.SimInfoBuilder;

public final class DataUtil {
  public static final CorrelationId CORRELATION_ID = CorrelationId.ofString("someCorrelationId");
  public static final DeviceInfoId DEVICE_INFO_ID = DeviceInfoId.ofLong(1);
  public static final com.wirelesscar.tcevce.wecu.device.info.database.model.Handle HANDLE =
      com.wirelesscar.tcevce.wecu.device.info.database.model.Handle.ofString("someHandle");
  public static final Imsi IMSI = Imsi.ofLong(1L);
  public static final Ipv4Address IPV4_ADDRESS = Ipv4Address.ofString("*******");
  public static final Ipv4Port IPV4_PORT = Ipv4Port.ofInt(51_000);
  public static final MobileNetworkOperator MOBILE_NETWORK_OPERATOR = MobileNetworkOperator.ofString("telenor");
  public static final Msisdn MSISDN = Msisdn.ofString("+461234567891");
  public static final SatelliteId SATELLITE_ID = SatelliteId.ofString("HQ1234567890x1");
  public static final SequenceNumber SEQUENCE_NUMBER = SequenceNumber.ofByte((byte) 0);
  public static final Vpi VPI = Vpi.ofString("1234567890ABCDEF1234567890ABCDEF");

  private DataUtil() {
    throw new IllegalStateException();
  }

  public static void checkMtStatusMessageWithVpi(MtStatusMessage mtStatusMessage) {
    checkMtStatusMessage(mtStatusMessage);
    Assertions.assertEquals(VPI.toString(), mtStatusMessage.getVehiclePlatformId());
  }

  public static void checkMtStatusMessageWithoutVpi(MtStatusMessage mtStatusMessage) {
    checkMtStatusMessage(mtStatusMessage);
  }

  public static void createAndInsertDeviceInfo(DeviceInfoWriterFactory deviceInfoWriterFactory) {
    createAndInsertDeviceInfo(createDeviceInfo(), deviceInfoWriterFactory);
  }

  public static void createAndInsertDeviceInfo(DeviceInfo deviceInfo, DeviceInfoWriterFactory deviceInfoWriterFactory) {
    performInDatabase(deviceInfoWriterFactory, deviceInfoWriter -> {
      Either<InsertionFailure, DeviceInfoId> insertionEither = deviceInfoWriter.insertDeviceInfo(deviceInfo);
      DeviceSequence deviceSequence = createDeviceSequenceBuilder()
          .setDeviceInfoId(insertionEither.getRight())
          .build();

      Assertions.assertTrue(deviceInfoWriter.insertDeviceSequence(deviceSequence).isRight());
    });
  }

  public static void createAndInsertDeviceInfoWithoutVpi(DeviceInfoWriterFactory deviceInfoWriterFactory) {
    createAndInsertDeviceInfo(createDeviceInfoWithoutVpi(), deviceInfoWriterFactory);
  }

  public static DeviceDetailedEntry createDeviceDetailedEntryWithSatelliteAndHandleOnly() {
    DeviceDetailedEntry deviceDetailedEntry = new DeviceDetailedEntry();
    deviceDetailedEntry.setHandle(DataUtil.HANDLE.toString());
    deviceDetailedEntry.setSatelliteId(SATELLITE_ID.toString());

    return deviceDetailedEntry;
  }

  public static DeviceInfo createDeviceInfo() {
    return createDeviceInfoBuilder().build();
  }

  public static DeviceInfoBuilder createDeviceInfoBuilder() {
    return createDeviceInfoBuilder(Optional.of(VPI));
  }

  public static DeviceInfoBuilder createDeviceInfoBuilderWithoutVpi() {
    return createDeviceInfoBuilder(Optional.empty());
  }

  public static DeviceInfo createDeviceInfoWithoutVpi() {
    return createDeviceInfoBuilderWithoutVpi().build();
  }

  public static DeviceSequence createDeviceSequence() {
    return createDeviceSequenceBuilder().build();
  }

  public static DeviceSequenceBuilder createDeviceSequenceBuilder() {
    return new DeviceSequenceBuilder()
        .setDeviceInfoId(DEVICE_INFO_ID)
        .setSequenceNumber(SEQUENCE_NUMBER);
  }

  public static MtMessage createMtMessageWithVpi() throws ASNException {
    MtMessage mtMessage = createMtMessage();
    mtMessage.setVehiclePlatformId(VPI.toString());

    return mtMessage;
  }

  public static MtMessage createMtMessageWithoutVpi() throws ASNException {
    return createMtMessage();
  }

  public static SimInfo createSimInfo() {
    return createSimInfoBuilder().build();
  }

  public static SimInfoBuilder createSimInfoBuilder() {
    return new SimInfoBuilder()
        .setImsi(IMSI)
        .setIpv4Address(IPV4_ADDRESS)
        .setIpv4Port(IPV4_PORT)
        .setMobileNetworkOperator(MOBILE_NETWORK_OPERATOR)
        .setMsisdn(MSISDN);
  }

  public static void deleteDevice(DeviceInfoWriterFactory deviceInfoWriterFactory, com.wirelesscar.tcevce.wecu.device.info.database.model.Handle handle) {
    performInDatabase(deviceInfoWriterFactory, deviceInfoWriter -> Assertions.assertEquals(1, deviceInfoWriter.deleteDeviceInfoByHandle(handle)));
  }

  public static void insertSendSchemasInDatabase(DataSource dataSource) throws IOException {
    Path path = Paths.get("../deployable-assembly/src/main/content/jar/sql/scheduler_ct_db.sql");

    try (org.jdbi.v3.core.Handle handle = Jdbi.create(dataSource).open()) {
      handle.createScript(Files.readString(path)).execute();
    }
  }

  private static void checkMtStatusMessage(MtStatusMessage mtStatusMessage) {
    Assertions.assertEquals(CORRELATION_ID.toString(), mtStatusMessage.getCorrelationId());
    Assertions.assertEquals(HANDLE.toString(), mtStatusMessage.getHandle());
    Assertions.assertEquals("DELIVERED", mtStatusMessage.getStatus());
  }

  private static DeviceInfoBuilder createDeviceInfoBuilder(Optional<Vpi> vpi) {
    return new DeviceInfoBuilder()
        .setHandle(HANDLE)
        .setSimInfo(Optional.of(createSimInfo()))
        .setVpi(vpi);
  }

  private static MtMessage createMtMessage() throws ASNException {
    MtMessage mtMessage = new MtMessage();

    mtMessage.setClientId("someClientId");
    mtMessage.setLegacyIdentifyVehicleByDeviceHandleOption(HANDLE.toString());
    mtMessage.setMtStatusReplyOption(createMtStatusReplyOption());
    mtMessage.setPayload(createStatusRequestPayload());
    mtMessage.setSchedulerOption(createSchedulerOption());
    return mtMessage;
  }

  private static MtStatusReplyOption createMtStatusReplyOption() {
    MtStatusReplyOption mtStatusReplyOption = new MtStatusReplyOption();

    mtStatusReplyOption.setCorrelationId(CORRELATION_ID.toString());
    mtStatusReplyOption.setReplyDestination("LOCAL.LOCAL.LOCAL.SOMECMP.MTSTATUS.OUT");

    return mtStatusReplyOption;
  }

  private static SchedulerOption createSchedulerOption() {
    SchedulerOption schedulerOption = new SchedulerOption();

    schedulerOption.setEnqueueingType(EnqueueingType.NORMAL.toString());
    schedulerOption.setHint("sms");

    return schedulerOption;
  }

  private static byte[] createStatusRequestPayload() throws ASNException {
    MessageContent messageContent = new MessageContent();
    messageContent.setData();
    messageContent.getData().setSize(1);
    messageContent.getData().getArrayItem(0).setInstantMachineDataRequest();

    PERStream perStream = new PERStream();
    messageContent.encode(perStream);
    perStream.alignOnByte();
    return perStream.getBuffer();
  }

  private static void performInDatabase(DeviceInfoWriterFactory deviceInfoWriterFactory, Consumer<DeviceInfoWriter> deviceInfoWriterConsumer) {
    try (DeviceInfoWriter deviceInfoWriter = deviceInfoWriterFactory.createReadCommitted()) {
      deviceInfoWriterConsumer.accept(deviceInfoWriter);
    }
  }
}
