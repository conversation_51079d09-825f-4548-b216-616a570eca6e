#--------------------[ CORE ]--------------------


#--------------------[ MODULES ]--------------------
stacks:
  - stack: MT-WRITE
    down: MT-READ
    modules:
      - module: vce_identify
        properties:
          metadata: MESSAGE_ID
          activate-up: false
          activate-down: true
      - module: mt_doorkeeper
      - module: vce_schedule_selector
      - module: scheduler
        properties:
          readprocess.start: false
          persistfordown: true
          instackdown: true
          inprocessdown: false
# TODO: default values differs: MESSAGE_ID;SERVICE_ID;SCHEDULE_NAME
          metadata: MESSAGE_ID
          hash.key: IP_DST_ADDRESS

  - stack: MT-READ
    down: VCE
    modules:
      - module: scheduler
        properties:
          metadata: MESSAGE_ID
          persistfordown: false
          readprocess.start: true
          inprocessdown: true
          instackdown: false
          ack: true
          stackname-status-msg: MO-READ
# TODO: should we use conn-est for VCE?
          connection.established: true
          integration-logging.use.transport-type: true
          processor.thread.active_msg_cap: 1
          processor.number_of_threads: 5

  - stack: MO-READ
    modules:
      - module: subscription_router
        properties:
          options: default:VCE_SERVICE
      - module: mo_throttling
        properties:
          transactions.per.second: 500
          buffer.size.in.seconds: 5
          throttling.thread.count: 20

  - stack: MO-WRITE
    up: MO-READ
# TODO: UDP should not be present here according to stack-conf overview in ppt, remove after test
#    down: UDP
    modules:
      - module: persist
        properties:
          hash.key: IP_SRC_ADDRESS
          persistforup: true
          readprocess.start: false
          inprocessup: false
          instackup: true
          mo.ack: false
          mt.ack: true
          metadata: MESSAGE_ID
          processor.number_of_threads: 50
      - module: vce_logging
        properties:

  - stack: UDP
    up: VCE
    modules:
      - module: scooter_agent
        enabled: ${UDP.scooter-agent.enabled}
        properties:
          udp-router-mode: false
          reset-ip: ${UDP.scooter-agent.reset-ip}
          force.scooter-ip: true
          ip-address: ${UDP.scooter-agent.dest-adress}
          ip-port: ${UDP.scooter-agent.dest-port}
      - module: udp
        properties:
          bindaddress: ${UDP.udp.bindadress}
          sourceport: 51000
          receiveport: 51000
          destport-force: 0
          receive-enabled: false
      - module: threadpool
        properties:
          activate.up: true
      - module: tcp
        properties:
          upflow: SERVER
          downflow: NONE
          server-max-threads: 100
          server-port: 20585
          server-terminate-timeout.seconds: 60

  - stack: VCE
    up: MO-WRITE
    down: UDP
    modules:
      - module: vce_split_ack
        properties:
          satellite-stack-name: SAT
          sms-stack-name: SMS
      - module: vce_segmentation
      - module: vce_identify
        properties:
          metadata: MESSAGE_ID
          activate-up: true
          activate-down: false

  - stack: SAT
    up: VCE
    modules:
      - module: tcp
        properties:
          upflow: SERVER
          downflow: CLIENT
          server-port: 20595
          server-terminate-timeout.seconds: 60
          stack-specific-server-port-list: ${SAT.tcp.stack-specific-server-port-list}
          stack-specific-server-port-list-force: true
          server-max-threads: 100

  - stack: SMS
    up: VCE
    modules:
      - module: tcp
        properties:
          upflow: SERVER
          downflow: CLIENT
          server-port: 20587
          server-terminate-timeout.seconds: 60
          stack-specific-server-port-list: ${SMS.tcp.stack-specific-server-port-list}
          stack-specific-server-port-list-force: true
          server-max-threads: 100
