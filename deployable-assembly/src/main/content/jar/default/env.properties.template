tcevce.server.port=20597

## InfluxDB
tcevce.management.influx.metrics.export.enabled=true

tcevce.de.influx.event.enabled=false
tcevce.management.influx.metrics.export.db=vehicle_communication

tcevce.management.influx.metrics.export.retention-policy=30days
tcevce.caretrack.val.management.influx.metrics.export.retention-policy=30days
tcevce.caretrack.qa.management.influx.metrics.export.retention-policy=60days
tcevce.caretrack.prod.management.influx.metrics.export.retention-policy=365days

tcevce.local.management.influx.metrics.export.uri=http://localhost:8086

## Device Service
tcevce.device-service.maximum-cache-size-in-bytes=10000000

# JMS Artemis
tcevce.spring.jms.listener.max-concurrency=5

platform.de.spring.artemis.broker-url=tcp://mockhost:61616
platform.local.spring.artemis.broker-url=tcp://localhost:61616

platform.de.spring.artemis.user=admin
platform.local.spring.artemis.user=admin

platform.de.spring.artemis.password=admin
platform.local.spring.artemis.password=admin

# Subscription repository
# servicediscovery.subr
platform.de.servicediscovery.subr=http://mockhost:8080
platform.local.servicediscovery.subr=http://127.0.0.1:8080

# Connectivity repository
# servicediscovery.conrepo
tcevce.caretrack.prod.servicediscovery.conrepo=http://conrepo.prod.shared.eu-west-1.prod.aws.vgthosting.net:33580
tcevce.caretrack.qa.servicediscovery.conrepo=http://conrepo.qa.shared.eu-west-1.qa.aws.vgthosting.net:33580
tcevce.caretrack.val.servicediscovery.conrepo=http://conrepo.test.shared.eu-west-1.test.aws.vgthosting.net:33580
tcevce.local.servicediscovery.conrepo=http://localhost:33580
tcevce.de.servicediscovery.conrepo=http://mockhost:33580

# ZooKeeper
tcevce.caretrack.prod.servicediscovery.tce_vce_prod=zk://tce_vce_prod
tcevce.caretrack.qa.servicediscovery.tce_vce_qa=zk://tce_vce_qa
tcevce.caretrack.val.servicediscovery.tce_vce_val=zk://tce_vce_val

tcevce.tisp.service-discovery.type=servicediscovery-lib

tcevce.spring.flyway.enabled=false

# SubscriptionRepo criteria: SYSTEM=<value below>
tcevce.system.id=${solution}.${site}.${environment}.${short-name}

# Opus integration logging
tcevce.logging.integration.server-in.enabled=false
tcevce.logging.integration.server-out.enabled=false
tcevce.logging.integration.client-in.enabled=true
tcevce.logging.integration.client-out.enabled=true
tcevce.logging.integration.server-in.payload.enabled=false
tcevce.logging.integration.server-out.payload.enabled=false
tcevce.logging.integration.client-in.payload.enabled=false
tcevce.logging.integration.client-out.payload.enabled=false

# Logging for QA
tcevce.qa.logging.level.com.wirelesscar.tcevce.module.split.ack.VceSplitAckModule=DEBUG
tcevce.qa.logging.level.com.wirelesscar.tcevce.module.persist.PersistModule=DEBUG
tcevce.qa.logging.level.com.wirelesscar.tcevce.module.identify.VceIdentifyModule=DEBUG
tcevce.qa.logging.level.com.wirelesscar.tcevce.module.segmentation.VceSegmentationModule=DEBUG
tcevce.qa.logging.level.com.wirelesscar.tcevce.module.selector.VceScheduleSelectorModule=DEBUG
tcevce.qa.logging.level.com.wirelesscar.tcevce.module.subscriptionrouter.SubscriptionRouterModule=DEBUG
tcevce.qa.logging.level.com.wirelesscar.tcevce.module.mtdoorkeeper.MtDoorKeeperModule=DEBUG
tcevce.qa.logging.level.com.wirelesscar.tcevce.module.mothrottling.throttling.MoThrottlingModule=DEBUG
tcevce.qa.logging.integration.payload.enabled=true

# JMS Spring listener tuning
tcevce.jms.listener.amq.concurrency=1-40
tcevce.jms.listener.amq.maxMessagesPerTask=1000
tcevce.jms.listener.amq.maxReceiveTimeout=1000

## Database Liquibase
tcevce.spring.liquibase.enabled=false

## Database - Flyway
tcevce.spring.flyway.enabled=false

# router rest
tcevce.router.rest.module.metadata.export=IP_SRC_ADDRESS;IP_SRC_PORT;PRIORITY;HANDLE;TRANSPORT_TYPE

# Override queue Id in VCE
tcevce.tce.message.server.queue.id.override=vce-all

# Feature flag to control how externally sent MO message field vehiclePlatformId is mapped - using strictly VPI (if true) or with fallback to handle (default).
tcevce.FEATURE_USE_VPI_WITHOUT_FALLBACK_TO_HANDLE_FOR_EXTERNAL_MO_MESSAGE_VPI_FIELD=true

# Feature flag to propagate MSISDN in external MO message.
# Used for VCE / Caretrack in activation messages to propagate MSISDN to Caretrack for later use by a tool &quot;ct-admin tool&quot; that seems to rely on MSISDN.
tcevce.FEATURE_PROPAGATE_MSISDN_IN_EXTERNAL_MO_MESSAGE=true

# Feature flag to update the next check time for mt messages to a particular vehicle.
# Just to ensure that the messages are sent out regardless of what server receives an ack eg
tcevce.FEATURE_MT_UPDATE_NEXTCHECKTIME_WHEN_VEHICLE_COMMUNICATES=true

# Message JMS API
tcevce.message.server.jms.api.lookup.name=MT-WRITE
tcevce.message.server.jms.api.queue.name=MT-MESSAGES

# TODO is set in YAML??? is that working?
tcevce.MT-READ.scheduler.workerthread.thread.active_msg_cap=1

# Delay messages for 100 days before they are removed if they are not sent
tcevce.message.initial_removetime_delay_ms=8640000000

# DB properties
# db.url
tcevce.caretrack.prod.tce.db.url=*********************************
tcevce.caretrack.qa.tce.db.url=***********************************************************************************************************************************************************************************************)))
tcevce.caretrack.val.tce.db.url=********************************
tcevce.de.tce.db.url=**********************************************************************************************************************************************************************************************)))
tcevce.local.tce.db.url=***********************************

# db.user
tcevce.caretrack.prod.tce.db.user=vce_got_prod_tce
tcevce.caretrack.qa.tce.db.user=ct_euwest1_qa_tce
tcevce.caretrack.val.tce.db.user=ct_got_val_tce
tcevce.de.tce.db.user=de_admin
tcevce.local.tce.db.user=test_user

# db.pw
tcevce.caretrack.prod.tce.db.pw=TceL1kesOracle
tcevce.caretrack.qa.tce.db.pw=Kalas1ct_euwest1_qa
tcevce.caretrack.val.tce.db.pw=Kalas1ct_got_val
tcevce.de.tce.db.pw=de_admin
tcevce.local.tce.db.pw=test_user

# DB default
tcevce.tce.db.maxpoolsize=100
tcevce.de.tce.db.maxpoolsize=10
tcevce.local.tce.db.maxpoolsize=5

tcevce.tce.db.minpoolsize=20
tcevce.de.tce.db.minpoolsize=2
tcevce.local.tce.db.minpoolsize=2

# collect database metrics on all servers in case the twin TCE server is down
tcevce.tce.db.metrics.enable=true

tcevce.tce.db.close-non-returned-connections=0
tcevce.tce.db.idle-time-out=2200
tcevce.tce.db.idle-check-time=800

# DB maintenance schedule (DB cleanup)
tcevce.tce.db.table.maintenance.schedule.cron=0 5 * * * ?

# Active instance that will delete MO,MT only master instance, default is false
tcevce.caretrack.prod.got.segotl2767.tce.db.table.maintenance.enable=true
tcevce.caretrack.qa.got.segotl2756.tce.db.table.maintenance.enable=true
tcevce.caretrack.val.got.segotl2373.tce.db.table.maintenance.enable=true
tcevce.de.tce.db.table.maintenance.enable=true

# bindaddress:
tcevce.caretrack.prod.got.segotl2767.UDP.udp.bindadress=************
tcevce.caretrack.prod.got.segotl2768.UDP.udp.bindadress=************
tcevce.caretrack.qa.got.segotl2756.UDP.udp.bindadress=************
tcevce.caretrack.qa.got.segotl2752.UDP.udp.bindadress=************
tcevce.caretrack.val.got.segotl2373.UDP.udp.bindadress=************
tcevce.caretrack.val.got.segotl2374.UDP.udp.bindadress=************

## Cluster
# change here for clustring
tcevce.caretrack.prod.got.segotl2767.cluster.instance-id-number=0
tcevce.caretrack.prod.got.segotl2768.cluster.instance-id-number=1
tcevce.caretrack.qa.got.segotl2756.cluster.instance-id-number=0
tcevce.caretrack.qa.got.segotl2752.cluster.instance-id-number=1
tcevce.caretrack.val.got.segotl2373.cluster.instance-id-number=0
tcevce.caretrack.val.got.segotl2374.cluster.instance-id-number=1
tcevce.cluster.numberof.instances=2

## Hazelcast
# The free community Hazelcast version doesn't support rolling cluster updates.
# It is not possible to join a cluster which was created with an older version.
# Therefore we include the version number in the cluster name as a workaround.
tcevce.hazelcast.cluster-id=tcevce-notify-hazelcast-5.3
tcevce.hazelcast.root-path=/tceactivation
tcevce.hazelcast.component-name=tcevce
tcevce.de.hazelcast.network.interface=
tcevce.local.hazelcast.network.interface=
tcevce.hazelcast.network.interface=${env:VGTPRIVATEIP}
tcevce.shared.hazelcast.network.interface=${sys:VGTPRIVATEIP}
tcevce.shared.hazelcast.report.interval=PT1M

# HA Config
# Active instance
# Set the startup RunLevelState, use one of: SHUTDOWN, BOOTED, HALTED, RUNNING
tcevce.lifecyclemanager.startup.runlevelstate=HALTED
tcevce.ha.controller.failback.enabled=true
tcevce.ha.controller.activate=true

# Supervisor shutdown URL
tcevce.ha.controller.shutdown.url=http://localhost:9003/index.html?processname=jar-tce-vce-server-deployable&action=stop

# Service discovery name
tcevce.local.ha.controller.service.name.common=tce_vce_local
tcevce.de.ha.controller.service.name.common=tce_vce_de
tcevce.caretrack.prod.ha.controller.service.name.common=tce_vce_prod
tcevce.caretrack.qa.ha.controller.service.name.common=tce_vce_qa
tcevce.caretrack.val.ha.controller.service.name.common=tce_vce_val

# Master instance used in HA
tcevce.local.ha.controller.instance.main=true
tcevce.de.ha.controller.instance.main=true
tcevce.caretrack.prod.got.segotl2767.ha.controller.instance.main=true
tcevce.caretrack.qa.got.segotl2756.ha.controller.instance.main=true
tcevce.caretrack.val.got.segotl2373.ha.controller.instance.main=true

# Reduce cache lifetime from default 24h to 1m due to invalidation problems with the in-memory caches
# This is just an ugly work-around until we implement a proper new device service/cache invalidation
tcevce.connectivityrepo.cache.minutes=1


# Outgoing TCP SAT communication
tcevce.caretrack.prod.SAT.tcp.stack-specific-server-port-list=************:20596;************:20596
tcevce.caretrack.qa.SAT.tcp.stack-specific-server-port-list=************:20596;************:20596
tcevce.caretrack.val.SAT.tcp.stack-specific-server-port-list=************:20596;************:20596

# Outgoing TCP SMS communication
tcevce.caretrack.prod.SMS.tcp.stack-specific-server-port-list=************:20589;************:20589
tcevce.caretrack.qa.SMS.tcp.stack-specific-server-port-list=************:20589;************:20589
tcevce.caretrack.val.SMS.tcp.stack-specific-server-port-list=************:20589;************:20589
tcevce.de.SMS.tcp.stack-specific-server-port-list=testhost:20589
tcevce.local.SMS.tcp.stack-specific-server-port-list=127.0.0.1:20589

# Buildpipe configuration
tcevce.de.SAT.tcp.stack-specific-server-port-list=*******:1
tcevce.local.SAT.tcp.stack-specific-server-port-list=*******:1

tcevce.de.UDP.udp.bindadress=localhost
tcevce.local.UDP.udp.bindadress=localhost

# Load test configuration
tcevce.UDP.scooter-agent.enabled=false
tcevce.caretrack.val.UDP.scooter-agent.enabled=true
tcevce.caretrack.val.UDP.scooter-agent.dest-adress=************
tcevce.caretrack.val.UDP.scooter-agent.dest-port=51000
tcevce.caretrack.val.UDP.scooter-agent.reset-ip=true

tcevce.caretrack.val.message.server.jms.conrepo2.consume-allowed=true
tcevce.caretrack.val.tce.jms.conrepo.notify.device.changes.enable=false
