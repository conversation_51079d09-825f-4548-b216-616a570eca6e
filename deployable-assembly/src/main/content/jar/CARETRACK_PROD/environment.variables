JDK_VERSION=21
JAVA_OPTS= \
 -DVGTZOOKEEPER=zoo.prod.shared.got.prod.hcl.vgthosting.net:2181 \
 -DVGTCONFIGFILE=env.properties \
 -DVGTLOGDIR=/var/opt/vgt/logs/tce-vce-server \
 -DCONFIGFILEOVERRIDE=/var/opt/vgt/tce-vce-server-deployable/jar/override.properties \
 -Xms4g \
 -Xmx4g \
 -XX:MaxMetaspaceSize=256m \
 -XX:+ExitOnOutOfMemoryError \
 -XX:+HeapDumpOnOutOfMemoryError \
 -XX:HeapDumpPath=/var/opt/vgt/logs/tce-vce-server/ \
 -Djava.net.preferIPv4Stack=true \
 -Doracle.net.tns_admin=/opt/vgt/tnsadmin
