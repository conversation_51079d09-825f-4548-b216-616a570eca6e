delete
from <PERSON>HD_ACTION;
delete
from <PERSON>HD_TRANSITION;
delete
from SCHD_SCHEDULE;

Insert into SCHD_SCHEDULE (ID, NAME, STATUS, TIMETOLIVE)
values ('13', 'Sms_text_0', 'a', '10000');


Insert into <PERSON>HD_TRANSITION (ID, SCHED<PERSON><PERSON><PERSON>, NAM<PERSON>, <PERSON><PERSON><PERSON><PERSON>MB<PERSON>, TIMETOLIVE, MAXTRIESPERMSG, TIMEOUTREPEAT, LASTUPDATE, CONNECTIONESTABLISHED)
values ('230', '13', 'SMS', '10', '5000', '5', '1', sysdate, '0');


Insert into SCHD_ACTION (ID, TRANSITIONID, NAME, ORDERNUMBER, ISINTERNAL, PROPERTIES)
values ('2301', '230', 'transport-type', '20', 'true', 'ActionType<==>AddProperty<;;>NewPropertyKey<==>TRANSPORT_TYPE<;;>NewPropertyValue<==>SMS');
Insert into <PERSON>HD_ACTION (ID, TRA<PERSON><PERSON>IONID, NAM<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ISINTE<PERSON><PERSON>, PROPERTIES)
values ('2302', '230', 'sms-prop', '30', 'true', 'ActionType<==>AddProperty<;;>NewPropertyKey<==>SMS_DATA_CODING<;;>NewPropertyValue<==>0');
Insert into SCHD_ACTION (ID, TRANSITIONID, NAME, ORDERNUMBER, ISINTERNAL, PROPERTIES)
values ('2303', '230', 'sms-prop', '40', 'true', 'ActionType<==>AddProperty<;;>NewPropertyKey<==>SMS_VALID_PERIOD_SECONDS<;;>NewPropertyValue<==>86400');
Insert into SCHD_ACTION (ID, TRANSITIONID, NAME, ORDERNUMBER, ISINTERNAL, PROPERTIES)
values ('2304', '230', 'sms-prop', '50', 'true', 'ActionType<==>AddProperty<;;>NewPropertyKey<==>SMPP_SOURCE_ADDRESS<;;>NewPropertyValue<==>10863');
Insert into SCHD_ACTION (ID, TRANSITIONID, NAME, ORDERNUMBER, ISINTERNAL, PROPERTIES)
values ('2305', '230', 'validate-encryption', '60', 'true', 'ActionType<==>AddProperty<;;>NewPropertyKey<==>CHECK_ENCRYPTION<;;>NewPropertyValue<==>true');
Insert into SCHD_ACTION (ID, TRANSITIONID, NAME, ORDERNUMBER, ISINTERNAL, PROPERTIES)
values ('2306', '230', 'send', '70', 'false', 'key<==>value');
