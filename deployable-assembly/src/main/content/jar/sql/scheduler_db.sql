delete from <PERSON>HD_ACTION;
delete from SCHD_TRANSITION;
delete from SCHD_SCHEDULE;

REM Use this in environments with SMS and SAT server, e.g. PROD and QA environments.
REM INSERTING into SCHD_SCHEDULE
SET DEFINE OFF;
Insert into SCHD_SCHEDULE (ID,NAME,STATUS,TIMETOLIVE) values ('1','Gprs_ack_0','a','10000');
Insert into SCHD_SCHEDULE (ID,NAME,STATUS,TIMETOLIVE) values ('2','ip_only','a','53460000');
Insert into SCHD_SCHEDULE (ID,NAME,STATUS,TIMETOLIVE) values ('3','SmsGprs_TimeoutTest_5s','a','5000');
Insert into SCHD_SCHEDULE (ID,NAME,STATUS,TIMETOLIVE) values ('4','udp_only','a','10000');
Insert into SCHD_SCHEDULE (ID,NAME,STATUS,<PERSON>IM<PERSON><PERSON><PERSON>) values ('5','Sat_ack_0','a','25000');
Insert into SCHD_SCHEDULE (ID,NAME,STATUS,TIMETOLIVE) values ('6','Sat_activation_30d_Dp36','a','2592000000');
Insert into SCHD_SCHEDULE (ID,NAME,STATUS,TIMETOLIVE) values ('7','Sat_activation_30d','a','2592000000');
Insert into SCHD_SCHEDULE (ID,NAME,STATUS,TIMETOLIVE) values ('8','Sat_default_7d_Dp36','a','604800000');
Insert into SCHD_SCHEDULE (ID,NAME,STATUS,TIMETOLIVE) values ('9','Sat_default_7d','a','604800000');
Insert into SCHD_SCHEDULE (ID,NAME,STATUS,TIMETOLIVE) values ('10','Sat_immobilizer_1d','a','86400000');
--Insert into SCHD_SCHEDULE (ID,NAME,STATUS,TIMETOLIVE) values ('11','Sms_ack_0','a','25000');
Insert into SCHD_SCHEDULE (ID,NAME,STATUS,TIMETOLIVE) values ('12','Sms_activation_30d','a','2592000000');
Insert into SCHD_SCHEDULE (ID,NAME,STATUS,TIMETOLIVE) values ('13','Sms_text_0','a','10000');
Insert into SCHD_SCHEDULE (ID,NAME,STATUS,TIMETOLIVE) values ('14','SmsGprs_default_7d','a','604800000');
Insert into SCHD_SCHEDULE (ID,NAME,STATUS,TIMETOLIVE) values ('15','SmsGprs_immobilizer_1d','a','86400000');
Insert into SCHD_SCHEDULE (ID,NAME,STATUS,TIMETOLIVE) values ('16','SmsGprsSat_default_7d_Dp36','a','604800000');
Insert into SCHD_SCHEDULE (ID,NAME,STATUS,TIMETOLIVE) values ('17','SmsGprsSat_default_7d','a','604800000');
Insert into SCHD_SCHEDULE (ID,NAME,STATUS,TIMETOLIVE) values ('18','SmsGprsSat_immobilizer_1d','a','86400000');
Insert into SCHD_SCHEDULE (ID,NAME,STATUS,TIMETOLIVE) values ('19','smsonly','a','15552000000');
Insert into SCHD_SCHEDULE (ID,NAME,STATUS,TIMETOLIVE) values ('20','SmsSat_activation_30d_Dp36','a','2592000000');
Insert into SCHD_SCHEDULE (ID,NAME,STATUS,TIMETOLIVE) values ('21','SmsSat_activation_30d','a','2592000000');
Insert into SCHD_SCHEDULE (ID,NAME,STATUS,TIMETOLIVE) values ('22','common_normal','a','604800000');

REM INSERTING into SCHD_TRANSITION
SET DEFINE OFF;
Insert into SCHD_TRANSITION (ID,SCHEDULEID,NAME,ORDERNUMBER,TIMETOLIVE,MAXTRIESPERMSG,TIMEOUTREPEAT,LASTUPDATE,CONNECTIONESTABLISHED) values ('100','1','UDP','10','5000','10','1',sysdate,'0');

Insert into SCHD_TRANSITION (ID,SCHEDULEID,NAME,ORDERNUMBER,TIMETOLIVE,MAXTRIESPERMSG,TIMEOUTREPEAT,LASTUPDATE,CONNECTIONESTABLISHED) values ('110','2','UDP','10','120000','10','1',sysdate,'0');
Insert into SCHD_TRANSITION (ID,SCHEDULEID,NAME,ORDERNUMBER,TIMETOLIVE,MAXTRIESPERMSG,TIMEOUTREPEAT,LASTUPDATE,CONNECTIONESTABLISHED) values ('111','2','UDP2','20','240000','10','1',sysdate,'0');
Insert into SCHD_TRANSITION (ID,SCHEDULEID,NAME,ORDERNUMBER,TIMETOLIVE,MAXTRIESPERMSG,TIMEOUTREPEAT,LASTUPDATE,CONNECTIONESTABLISHED) values ('112','2','UDP3','30','480000','10','1',sysdate,'0');
Insert into SCHD_TRANSITION (ID,SCHEDULEID,NAME,ORDERNUMBER,TIMETOLIVE,MAXTRIESPERMSG,TIMEOUTREPEAT,LASTUPDATE,CONNECTIONESTABLISHED) values ('113','2','UDP4','40','900000','10','1',sysdate,'0');
Insert into SCHD_TRANSITION (ID,SCHEDULEID,NAME,ORDERNUMBER,TIMETOLIVE,MAXTRIESPERMSG,TIMEOUTREPEAT,LASTUPDATE,CONNECTIONESTABLISHED) values ('114','2','UDP5','50','1800000','10','1',sysdate,'0');
Insert into SCHD_TRANSITION (ID,SCHEDULEID,NAME,ORDERNUMBER,TIMETOLIVE,MAXTRIESPERMSG,TIMEOUTREPEAT,LASTUPDATE,CONNECTIONESTABLISHED) values ('115','2','UDP6','60','3600000','10','1',sysdate,'0');
Insert into SCHD_TRANSITION (ID,SCHEDULEID,NAME,ORDERNUMBER,TIMETOLIVE,MAXTRIESPERMSG,TIMEOUTREPEAT,LASTUPDATE,CONNECTIONESTABLISHED) values ('116','2','UDP7','70','7200000','10','1',sysdate,'0');
Insert into SCHD_TRANSITION (ID,SCHEDULEID,NAME,ORDERNUMBER,TIMETOLIVE,MAXTRIESPERMSG,TIMEOUTREPEAT,LASTUPDATE,CONNECTIONESTABLISHED) values ('117','2','UDP8','80','14400000','10','1',sysdate,'0');
Insert into SCHD_TRANSITION (ID,SCHEDULEID,NAME,ORDERNUMBER,TIMETOLIVE,MAXTRIESPERMSG,TIMEOUTREPEAT,LASTUPDATE,CONNECTIONESTABLISHED) values ('118','2','UDP9','90','28800000','10','1',sysdate,'0');

Insert into SCHD_TRANSITION (ID,SCHEDULEID,NAME,ORDERNUMBER,TIMETOLIVE,MAXTRIESPERMSG,TIMEOUTREPEAT,LASTUPDATE,CONNECTIONESTABLISHED) values ('130','3','UDP','10','5000','10','1',sysdate,'0');

Insert into SCHD_TRANSITION (ID,SCHEDULEID,NAME,ORDERNUMBER,TIMETOLIVE,MAXTRIESPERMSG,TIMEOUTREPEAT,LASTUPDATE,CONNECTIONESTABLISHED) values ('140','4','UDP','10','5000','10','1',sysdate,'0');

Insert into SCHD_TRANSITION (ID,SCHEDULEID,NAME,ORDERNUMBER,TIMETOLIVE,MAXTRIESPERMSG,TIMEOUTREPEAT,LASTUPDATE,CONNECTIONESTABLISHED) values ('150','5','SAT','10','20000','10','1',sysdate,'0');

Insert into SCHD_TRANSITION (ID,SCHEDULEID,NAME,ORDERNUMBER,TIMETOLIVE,MAXTRIESPERMSG,TIMEOUTREPEAT,LASTUPDATE,CONNECTIONESTABLISHED) values ('160','6','SAT','10','21660000','35','35',sysdate,'0');

Insert into SCHD_TRANSITION (ID,SCHEDULEID,NAME,ORDERNUMBER,TIMETOLIVE,MAXTRIESPERMSG,TIMEOUTREPEAT,LASTUPDATE,CONNECTIONESTABLISHED) values ('170','7','SAT','10','2460000','1054','1054',sysdate,'0');

Insert into SCHD_TRANSITION (ID,SCHEDULEID,NAME,ORDERNUMBER,TIMETOLIVE,MAXTRIESPERMSG,TIMEOUTREPEAT,LASTUPDATE,CONNECTIONESTABLISHED) values ('180','8','SAT','10','21660000','35','35',sysdate,'0');

Insert into SCHD_TRANSITION (ID,SCHEDULEID,NAME,ORDERNUMBER,TIMETOLIVE,MAXTRIESPERMSG,TIMEOUTREPEAT,LASTUPDATE,CONNECTIONESTABLISHED) values ('190','9','SAT','10','2460000','246','246',sysdate,'0');

Insert into SCHD_TRANSITION (ID,SCHEDULEID,NAME,ORDERNUMBER,TIMETOLIVE,MAXTRIESPERMSG,TIMEOUTREPEAT,LASTUPDATE,CONNECTIONESTABLISHED) values ('200','10','SAT','10','2460000','35','35',sysdate,'0');

--Insert into SCHD_TRANSITION (ID,SCHEDULEID,NAME,ORDERNUMBER,TIMETOLIVE,MAXTRIESPERMSG,TIMEOUTREPEAT,LASTUPDATE,CONNECTIONESTABLISHED) values ('210','11','SMS','10','20000','5','1',sysdate,'0');

Insert into SCHD_TRANSITION (ID,SCHEDULEID,NAME,ORDERNUMBER,TIMETOLIVE,MAXTRIESPERMSG,TIMEOUTREPEAT,LASTUPDATE,CONNECTIONESTABLISHED) values ('220','12','SMS','10','86460000','1','1',sysdate,'0');
--Insert into SCHD_TRANSITION (ID,SCHEDULEID,NAME,ORDERNUMBER,TIMETOLIVE,MAXTRIESPERMSG,TIMEOUTREPEAT,LASTUPDATE,CONNECTIONESTABLISHED) values ('221','12','SMS2','20','86460000','35','35',sysdate,'0');

Insert into SCHD_TRANSITION (ID,SCHEDULEID,NAME,ORDERNUMBER,TIMETOLIVE,MAXTRIESPERMSG,TIMEOUTREPEAT,LASTUPDATE,CONNECTIONESTABLISHED) values ('230','13','SMS','10','5000','5','1',sysdate,'0');

Insert into SCHD_TRANSITION (ID,SCHEDULEID,NAME,ORDERNUMBER,TIMETOLIVE,MAXTRIESPERMSG,TIMEOUTREPEAT,LASTUPDATE,CONNECTIONESTABLISHED) values ('240','14','UDP','10','120000','10','2',sysdate,'0');
Insert into SCHD_TRANSITION (ID,SCHEDULEID,NAME,ORDERNUMBER,TIMETOLIVE,MAXTRIESPERMSG,TIMEOUTREPEAT,LASTUPDATE,CONNECTIONESTABLISHED) values ('241','14','SMS','20','120000','10','1',sysdate,'0');
Insert into SCHD_TRANSITION (ID,SCHEDULEID,NAME,ORDERNUMBER,TIMETOLIVE,MAXTRIESPERMSG,TIMEOUTREPEAT,LASTUPDATE,CONNECTIONESTABLISHED) values ('242','14','SMS2','30','420000','5','1',sysdate,'0');
Insert into SCHD_TRANSITION (ID,SCHEDULEID,NAME,ORDERNUMBER,TIMETOLIVE,MAXTRIESPERMSG,TIMEOUTREPEAT,LASTUPDATE,CONNECTIONESTABLISHED) values ('243','14','UDP2','40','120000','85','1',sysdate,'0');
Insert into SCHD_TRANSITION (ID,SCHEDULEID,NAME,ORDERNUMBER,TIMETOLIVE,MAXTRIESPERMSG,TIMEOUTREPEAT,LASTUPDATE,CONNECTIONESTABLISHED) values ('244','14','SMS3','50','7260000','85','1',sysdate,'0');
Insert into SCHD_TRANSITION (ID,SCHEDULEID,NAME,ORDERNUMBER,TIMETOLIVE,MAXTRIESPERMSG,TIMEOUTREPEAT,LASTUPDATE,CONNECTIONESTABLISHED, REPEATFROM) values ('245','14','RETURN','60','180000','85','1',sysdate,'0', '243');

Insert into SCHD_TRANSITION (ID,SCHEDULEID,NAME,ORDERNUMBER,TIMETOLIVE,MAXTRIESPERMSG,TIMEOUTREPEAT,LASTUPDATE,CONNECTIONESTABLISHED) values ('250','15','UDP','10','120000','10','2',sysdate,'0');
Insert into SCHD_TRANSITION (ID,SCHEDULEID,NAME,ORDERNUMBER,TIMETOLIVE,MAXTRIESPERMSG,TIMEOUTREPEAT,LASTUPDATE,CONNECTIONESTABLISHED) values ('252','15','SMS','30','420000','5','1',sysdate,'0');
Insert into SCHD_TRANSITION (ID,SCHEDULEID,NAME,ORDERNUMBER,TIMETOLIVE,MAXTRIESPERMSG,TIMEOUTREPEAT,LASTUPDATE,CONNECTIONESTABLISHED) values ('253','15','UDP2','40','120000','30','1',sysdate,'0');
Insert into SCHD_TRANSITION (ID,SCHEDULEID,NAME,ORDERNUMBER,TIMETOLIVE,MAXTRIESPERMSG,TIMEOUTREPEAT,LASTUPDATE,CONNECTIONESTABLISHED) values ('254','15','SMS2','50','7260000','30','1',sysdate,'0');
Insert into SCHD_TRANSITION (ID,SCHEDULEID,NAME,ORDERNUMBER,TIMETOLIVE,MAXTRIESPERMSG,TIMEOUTREPEAT,LASTUPDATE,CONNECTIONESTABLISHED, REPEATFROM) values ('255','15','RETURN','60','180000','30','1',sysdate,'0', '253');

Insert into SCHD_TRANSITION (ID,SCHEDULEID,NAME,ORDERNUMBER,TIMETOLIVE,MAXTRIESPERMSG,TIMEOUTREPEAT,LASTUPDATE,CONNECTIONESTABLISHED) values ('260','16','UDP','10','120000','10','2',sysdate,'0');
Insert into SCHD_TRANSITION (ID,SCHEDULEID,NAME,ORDERNUMBER,TIMETOLIVE,MAXTRIESPERMSG,TIMEOUTREPEAT,LASTUPDATE,CONNECTIONESTABLISHED) values ('262','16','SMS','20','460000','5','1',sysdate,'0');
Insert into SCHD_TRANSITION (ID,SCHEDULEID,NAME,ORDERNUMBER,TIMETOLIVE,MAXTRIESPERMSG,TIMEOUTREPEAT,LASTUPDATE,CONNECTIONESTABLISHED) values ('263','16','SAT','30','21660000','5','1',sysdate,'0');
Insert into SCHD_TRANSITION (ID,SCHEDULEID,NAME,ORDERNUMBER,TIMETOLIVE,MAXTRIESPERMSG,TIMEOUTREPEAT,LASTUPDATE,CONNECTIONESTABLISHED) values ('264','16','UDP2','40','120000','30','1',sysdate,'0');
Insert into SCHD_TRANSITION (ID,SCHEDULEID,NAME,ORDERNUMBER,TIMETOLIVE,MAXTRIESPERMSG,TIMEOUTREPEAT,LASTUPDATE,CONNECTIONESTABLISHED) values ('265','16','SMS2','50','43260000','30','1',sysdate,'0');
Insert into SCHD_TRANSITION (ID,SCHEDULEID,NAME,ORDERNUMBER,TIMETOLIVE,MAXTRIESPERMSG,TIMEOUTREPEAT,LASTUPDATE,CONNECTIONESTABLISHED) values ('266','16','SAT2','60','21660000','30','1',sysdate,'0');
Insert into SCHD_TRANSITION (ID,SCHEDULEID,NAME,ORDERNUMBER,TIMETOLIVE,MAXTRIESPERMSG,TIMEOUTREPEAT,LASTUPDATE,CONNECTIONESTABLISHED, REPEATFROM) values ('267','16','RETURN','70','180000','30','1',sysdate,'0', '264');

Insert into SCHD_TRANSITION (ID,SCHEDULEID,NAME,ORDERNUMBER,TIMETOLIVE,MAXTRIESPERMSG,TIMEOUTREPEAT,LASTUPDATE,CONNECTIONESTABLISHED) values ('270','17','UDP','10','60000','10','1',sysdate,'0');
Insert into SCHD_TRANSITION (ID,SCHEDULEID,NAME,ORDERNUMBER,TIMETOLIVE,MAXTRIESPERMSG,TIMEOUTREPEAT,LASTUPDATE,CONNECTIONESTABLISHED) values ('271','17','UDP2','20','120000','10','1',sysdate,'0');
Insert into SCHD_TRANSITION (ID,SCHEDULEID,NAME,ORDERNUMBER,TIMETOLIVE,MAXTRIESPERMSG,TIMEOUTREPEAT,LASTUPDATE,CONNECTIONESTABLISHED) values ('272','17','SMS','30','120000','5','1',sysdate,'0');
Insert into SCHD_TRANSITION (ID,SCHEDULEID,NAME,ORDERNUMBER,TIMETOLIVE,MAXTRIESPERMSG,TIMEOUTREPEAT,LASTUPDATE,CONNECTIONESTABLISHED) values ('273','17','SMS2','40','360000','5','1',sysdate,'0');
Insert into SCHD_TRANSITION (ID,SCHEDULEID,NAME,ORDERNUMBER,TIMETOLIVE,MAXTRIESPERMSG,TIMEOUTREPEAT,LASTUPDATE,CONNECTIONESTABLISHED) values ('274','17','SAT','50','2460000','10','9',sysdate,'0');
Insert into SCHD_TRANSITION (ID,SCHEDULEID,NAME,ORDERNUMBER,TIMETOLIVE,MAXTRIESPERMSG,TIMEOUTREPEAT,LASTUPDATE,CONNECTIONESTABLISHED) values ('275','17','UDP3','60','120000','10','1',sysdate,'0');
Insert into SCHD_TRANSITION (ID,SCHEDULEID,NAME,ORDERNUMBER,TIMETOLIVE,MAXTRIESPERMSG,TIMEOUTREPEAT,LASTUPDATE,CONNECTIONESTABLISHED) values ('276','17','SMS3','70','43260000','10','1',sysdate,'0');
Insert into SCHD_TRANSITION (ID,SCHEDULEID,NAME,ORDERNUMBER,TIMETOLIVE,MAXTRIESPERMSG,TIMEOUTREPEAT,LASTUPDATE,CONNECTIONESTABLISHED) values ('277','17','SAT2','80','2460000','82','9',sysdate,'0');
Insert into SCHD_TRANSITION (ID,SCHEDULEID,NAME,ORDERNUMBER,TIMETOLIVE,MAXTRIESPERMSG,TIMEOUTREPEAT,LASTUPDATE,CONNECTIONESTABLISHED, REPEATFROM) values ('278','17','RETURN','90','180000','30','1',sysdate,'0', '275');

Insert into SCHD_TRANSITION (ID,SCHEDULEID,NAME,ORDERNUMBER,TIMETOLIVE,MAXTRIESPERMSG,TIMEOUTREPEAT,LASTUPDATE,CONNECTIONESTABLISHED) values ('280','18','UDP','10','120000','10','2',sysdate,'0');
Insert into SCHD_TRANSITION (ID,SCHEDULEID,NAME,ORDERNUMBER,TIMETOLIVE,MAXTRIESPERMSG,TIMEOUTREPEAT,LASTUPDATE,CONNECTIONESTABLISHED) values ('282','18','SMS','20','460000','10','1',sysdate,'0');
Insert into SCHD_TRANSITION (ID,SCHEDULEID,NAME,ORDERNUMBER,TIMETOLIVE,MAXTRIESPERMSG,TIMEOUTREPEAT,LASTUPDATE,CONNECTIONESTABLISHED) values ('283','18','SAT','30','2460000','50','9',sysdate,'0');
Insert into SCHD_TRANSITION (ID,SCHEDULEID,NAME,ORDERNUMBER,TIMETOLIVE,MAXTRIESPERMSG,TIMEOUTREPEAT,LASTUPDATE,CONNECTIONESTABLISHED, REPEATFROM) values ('287','18','RETURN','60','180000','30','1',sysdate,'0', '282');

Insert into SCHD_TRANSITION (ID,SCHEDULEID,NAME,ORDERNUMBER,TIMETOLIVE,MAXTRIESPERMSG,TIMEOUTREPEAT,LASTUPDATE,CONNECTIONESTABLISHED) values ('290','19','SMS','10','86400000','20','17',sysdate,'0');
Insert into SCHD_TRANSITION (ID,SCHEDULEID,NAME,ORDERNUMBER,TIMETOLIVE,MAXTRIESPERMSG,TIMEOUTREPEAT,LASTUPDATE,CONNECTIONESTABLISHED) values ('291','19','SMS2','20','604800000','25','22',sysdate,'0');

Insert into SCHD_TRANSITION (ID,SCHEDULEID,NAME,ORDERNUMBER,TIMETOLIVE,MAXTRIESPERMSG,TIMEOUTREPEAT,LASTUPDATE,CONNECTIONESTABLISHED) values ('300','20','SMS','10','420000','5','1',sysdate,'0');
Insert into SCHD_TRANSITION (ID,SCHEDULEID,NAME,ORDERNUMBER,TIMETOLIVE,MAXTRIESPERMSG,TIMEOUTREPEAT,LASTUPDATE,CONNECTIONESTABLISHED) values ('301','20','SAT','20','21660000','5','1',sysdate,'0');
Insert into SCHD_TRANSITION (ID,SCHEDULEID,NAME,ORDERNUMBER,TIMETOLIVE,MAXTRIESPERMSG,TIMEOUTREPEAT,LASTUPDATE,CONNECTIONESTABLISHED) values ('302','20','SMS2','30','43260000','30','1',sysdate,'0');
Insert into SCHD_TRANSITION (ID,SCHEDULEID,NAME,ORDERNUMBER,TIMETOLIVE,MAXTRIESPERMSG,TIMEOUTREPEAT,LASTUPDATE,CONNECTIONESTABLISHED) values ('303','20','SAT2','40','21660000','30','1',sysdate,'0');
Insert into SCHD_TRANSITION (ID,SCHEDULEID,NAME,ORDERNUMBER,TIMETOLIVE,MAXTRIESPERMSG,TIMEOUTREPEAT,LASTUPDATE,CONNECTIONESTABLISHED, REPEATFROM) values ('304','20','RETURN','60','180000','30','1',sysdate,'0', '302');

Insert into SCHD_TRANSITION (ID,SCHEDULEID,NAME,ORDERNUMBER,TIMETOLIVE,MAXTRIESPERMSG,TIMEOUTREPEAT,LASTUPDATE,CONNECTIONESTABLISHED) values ('310','21','SMS','10','120000','5','1',sysdate,'0'); -- start TTL = 0
Insert into SCHD_TRANSITION (ID,SCHEDULEID,NAME,ORDERNUMBER,TIMETOLIVE,MAXTRIESPERMSG,TIMEOUTREPEAT,LASTUPDATE,CONNECTIONESTABLISHED) values ('311','21','SMS2','20','420000','5','1',sysdate,'0'); -- gsm_sms_1 TTL = 360000
Insert into SCHD_TRANSITION (ID,SCHEDULEID,NAME,ORDERNUMBER,TIMETOLIVE,MAXTRIESPERMSG,TIMEOUTREPEAT,LASTUPDATE,CONNECTIONESTABLISHED) values ('312','21','SAT','30','2460000','10','9',sysdate,'0'); -- sat_orbcomm_1 -- 5
Insert into SCHD_TRANSITION (ID,SCHEDULEID,NAME,ORDERNUMBER,TIMETOLIVE,MAXTRIESPERMSG,TIMEOUTREPEAT,LASTUPDATE,CONNECTIONESTABLISHED) values ('313','21','SMS3','40','43260000','40','1',sysdate,'0'); -- gsm_sms_2  TTL = 43200000
Insert into SCHD_TRANSITION (ID,SCHEDULEID,NAME,ORDERNUMBER,TIMETOLIVE,MAXTRIESPERMSG,TIMEOUTREPEAT,LASTUPDATE,CONNECTIONESTABLISHED) values ('314','21','SAT2','50','2460000','352','9',sysdate,'0'); -- sat_orbcomm_10 -- 18
Insert into SCHD_TRANSITION (ID,SCHEDULEID,NAME,ORDERNUMBER,TIMETOLIVE,MAXTRIESPERMSG,TIMEOUTREPEAT,LASTUPDATE,CONNECTIONESTABLISHED, REPEATFROM) values ('315','21','RETURN','60','180000','40','1',sysdate,'0', '313');

Insert into SCHD_TRANSITION (ID,SCHEDULEID,NAME,ORDERNUMBER,TIMETOLIVE,MAXTRIESPERMSG,TIMEOUTREPEAT,LASTUPDATE,CONNECTIONESTABLISHED) values ('320','22','UDP','10','300000','10','1',sysdate,'0');
Insert into SCHD_TRANSITION (ID,SCHEDULEID,NAME,ORDERNUMBER,TIMETOLIVE,MAXTRIESPERMSG,TIMEOUTREPEAT,LASTUPDATE,CONNECTIONESTABLISHED) values ('321','22','SMS','20','86400000','5','1',sysdate,'0');
Insert into SCHD_TRANSITION (ID,SCHEDULEID,NAME,ORDERNUMBER,TIMETOLIVE,MAXTRIESPERMSG,TIMEOUTREPEAT,LASTUPDATE,CONNECTIONESTABLISHED, REPEATFROM) values ('322','22','RETURN','60','180000','30','1',sysdate,'0', '320');



REM INSERTING into SCHD_ACTION
SET DEFINE OFF;
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('1000','100','transport-type','10','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>TRANSPORT_TYPE<;;>NewPropertyValue<==>UDP');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('1001','100','stack-routing','20','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>STACK_ROUTING<;;>NewPropertyValue<==>VCE');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('1002','100','send','30','false','key<==>value');


Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('1100','110','transport-type','10','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>TRANSPORT_TYPE<;;>NewPropertyValue<==>UDP');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('1101','110','stack-routing','20','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>STACK_ROUTING<;;>NewPropertyValue<==>VCE');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('1102','110','send','30','false','key<==>value');


Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('1110','111','transport-type','10','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>TRANSPORT_TYPE<;;>NewPropertyValue<==>UDP');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('1111','111','stack-routing','20','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>STACK_ROUTING<;;>NewPropertyValue<==>VCE');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('1112','111','send','30','false','key<==>value');


Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('1120','112','transport-type','10','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>TRANSPORT_TYPE<;;>NewPropertyValue<==>UDP');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('1121','112','stack-routing','20','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>STACK_ROUTING<;;>NewPropertyValue<==>VCE');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('1122','112','send','30','false','key<==>value');


Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('1130','113','transport-type','10','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>TRANSPORT_TYPE<;;>NewPropertyValue<==>UDP');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('1131','113','stack-routing','20','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>STACK_ROUTING<;;>NewPropertyValue<==>VCE');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('1132','113','send','30','false','key<==>value');


Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('1140','114','transport-type','10','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>TRANSPORT_TYPE<;;>NewPropertyValue<==>UDP');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('1141','114','stack-routing','20','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>STACK_ROUTING<;;>NewPropertyValue<==>VCE');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('1142','114','send','30','false','key<==>value');


Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('1150','115','transport-type','10','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>TRANSPORT_TYPE<;;>NewPropertyValue<==>UDP');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('1151','115','stack-routing','20','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>STACK_ROUTING<;;>NewPropertyValue<==>VCE');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('1152','115','send','30','false','key<==>value');


Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('1160','116','transport-type','10','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>TRANSPORT_TYPE<;;>NewPropertyValue<==>UDP');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('1161','116','stack-routing','20','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>STACK_ROUTING<;;>NewPropertyValue<==>VCE');

Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('1162','116','send','30','false','key<==>value');


Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('1170','117','transport-type','10','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>TRANSPORT_TYPE<;;>NewPropertyValue<==>UDP');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('1171','117','stack-routing','20','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>STACK_ROUTING<;;>NewPropertyValue<==>VCE');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('1172','117','send','30','false','key<==>value');


Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('1180','118','transport-type','10','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>TRANSPORT_TYPE<;;>NewPropertyValue<==>UDP');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('1181','118','stack-routing','20','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>STACK_ROUTING<;;>NewPropertyValue<==>VCE');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('1182','118','send','30','false','key<==>value');


Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('1300','130','transport-type','10','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>TRANSPORT_TYPE<;;>NewPropertyValue<==>UDP');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('1301','130','stack-routing','20','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>STACK_ROUTING<;;>NewPropertyValue<==>VCE');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('1302','130','send','30','false','key<==>value');


Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('1400','140','transport-type','10','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>TRANSPORT_TYPE<;;>NewPropertyValue<==>UDP');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('1401','140','stack-routing','20','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>STACK_ROUTING<;;>NewPropertyValue<==>VCE');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('1402','140','send','30','false','key<==>value');


Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('1500','150','transport-type','10','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>TRANSPORT_TYPE<;;>NewPropertyValue<==>SATELLITE');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('1501','150','stack-routing','20','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>STACK_ROUTING<;;>NewPropertyValue<==>VCE');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('1502','150','send','30','false','key<==>value');


Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('1600','160','transport-type','10','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>TRANSPORT_TYPE<;;>NewPropertyValue<==>SATELLITE');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('1601','160','stack-routing','20','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>STACK_ROUTING<;;>NewPropertyValue<==>VCE');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('1602','160','send','30','false','key<==>value');


Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('1700','170','transport-type','10','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>TRANSPORT_TYPE<;;>NewPropertyValue<==>SATELLITE');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('1701','170','stack-routing','20','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>STACK_ROUTING<;;>NewPropertyValue<==>VCE');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('1702','170','send','30','false','key<==>value');


Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('1800','180','transport-type','10','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>TRANSPORT_TYPE<;;>NewPropertyValue<==>SATELLITE');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('1801','180','stack-routing','20','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>STACK_ROUTING<;;>NewPropertyValue<==>VCE');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('1802','180','send','30','false','key<==>value');


Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('1900','190','transport-type','10','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>TRANSPORT_TYPE<;;>NewPropertyValue<==>SATELLITE');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('1901','190','stack-routing','20','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>STACK_ROUTING<;;>NewPropertyValue<==>VCE');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('1902','190','send','30','false','key<==>value');


Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('2000','200','transport-type','10','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>TRANSPORT_TYPE<;;>NewPropertyValue<==>SATELLITE');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('2001','200','stack-routing','20','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>STACK_ROUTING<;;>NewPropertyValue<==>VCE');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('2002','200','send','30','false','key<==>value');


--Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('1110','210','stack-routing','10','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>STACK_ROUTING<;;>NewPropertyValue<==>SMS');
--Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('1111','210','send','20','false','key<==>value');


Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('2200','220','transport-type','10','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>TRANSPORT_TYPE<;;>NewPropertyValue<==>SMS');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('2201','220','sms-prop','20','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>SMS_VALID_PERIOD_SECONDS<;;>NewPropertyValue<==>86400');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('2202','220','stack-routing','30','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>STACK_ROUTING<;;>NewPropertyValue<==>VCE');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('2203','220','send','40','false','key<==>value');


--Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('2210','221','transport-type','10','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>TRANSPORT_TYPE<;;>NewPropertyValue<==>SMS');
--Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('2211','221','sms-prop','20','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>SMS_VALID_PERIOD_SECONDS<;;>NewPropertyValue<==>86400');
--Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('2212','221','stack-routing','30','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>STACK_ROUTING<;;>NewPropertyValue<==>VCE');
--Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('2213','221','send','40','false','key<==>value');


Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('2300','230','stack-routing','10','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>STACK_ROUTING<;;>NewPropertyValue<==>SMS');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('2301','230','transport-type','20','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>TRANSPORT_TYPE<;;>NewPropertyValue<==>SMS');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('2302','230','sms-prop','30','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>SMS_DATA_CODING<;;>NewPropertyValue<==>0');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('2303','230','sms-prop','40','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>SMS_VALID_PERIOD_SECONDS<;;>NewPropertyValue<==>86400');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('2304','230','sms-prop','50','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>SMPP_SOURCE_ADDRESS<;;>NewPropertyValue<==>10863');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('2305','230','validate-encryption','60','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>CHECK_ENCRYPTION<;;>NewPropertyValue<==>true');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('2306','230','send','70','false','key<==>value');


Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('2400','240','transport-type','10','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>TRANSPORT_TYPE<;;>NewPropertyValue<==>UDP');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('2401','240','stack-routing','20','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>STACK_ROUTING<;;>NewPropertyValue<==>VCE');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('2402','240','send','30','false','key<==>value');


Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('2410','241','transport-type','10','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>TRANSPORT_TYPE<;;>NewPropertyValue<==>SMS');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('2411','241','sms-prop','20','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>SMS_VALID_PERIOD_SECONDS<;;>NewPropertyValue<==>100');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('2412','241','stack-routing','30','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>STACK_ROUTING<;;>NewPropertyValue<==>VCE');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('2413','241','send','40','false','key<==>value');


Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('2420','242','transport-type','10','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>TRANSPORT_TYPE<;;>NewPropertyValue<==>SMS');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('2421','242','sms-prop','20','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>SMS_VALID_PERIOD_SECONDS<;;>NewPropertyValue<==>360');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('2422','242','stack-routing','30','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>STACK_ROUTING<;;>NewPropertyValue<==>VCE');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('2423','242','send','40','false','key<==>value');


Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('2430','243','transport-type','10','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>TRANSPORT_TYPE<;;>NewPropertyValue<==>UDP');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('2431','243','stack-routing','20','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>STACK_ROUTING<;;>NewPropertyValue<==>VCE');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('2432','243','send','30','false','key<==>value');


Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('2440','244','transport-type','10','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>TRANSPORT_TYPE<;;>NewPropertyValue<==>SMS');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('2441','244','sms-prop','20','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>SMS_VALID_PERIOD_SECONDS<;;>NewPropertyValue<==>7200');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('2442','244','stack-routing','30','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>STACK_ROUTING<;;>NewPropertyValue<==>VCE');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('2443','244','send','40','false','key<==>value');


Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('2500','250','transport-type','10','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>TRANSPORT_TYPE<;;>NewPropertyValue<==>UDP');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('2501','250','stack-routing','20','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>STACK_ROUTING<;;>NewPropertyValue<==>VCE');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('2502','250','send','30','false','key<==>value');


Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('2520','252','transport-type','10','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>TRANSPORT_TYPE<;;>NewPropertyValue<==>SMS');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('2521','252','sms-prop','20','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>SMS_VALID_PERIOD_SECONDS<;;>NewPropertyValue<==>360');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('2522','252','stack-routing','30','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>STACK_ROUTING<;;>NewPropertyValue<==>VCE');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('2523','252','send','40','false','key<==>value');


Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('2530','253','transport-type','10','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>TRANSPORT_TYPE<;;>NewPropertyValue<==>UDP');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('2531','253','stack-routing','20','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>STACK_ROUTING<;;>NewPropertyValue<==>VCE');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('2532','253','send','30','false','key<==>value');


Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('2540','254','transport-type','10','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>TRANSPORT_TYPE<;;>NewPropertyValue<==>SMS');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('2541','254','sms-prop','20','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>SMS_VALID_PERIOD_SECONDS<;;>NewPropertyValue<==>7200');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('2542','254','stack-routing','30','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>STACK_ROUTING<;;>NewPropertyValue<==>VCE');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('2543','254','send','40','false','key<==>value');


Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('2600','260','transport-type','10','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>TRANSPORT_TYPE<;;>NewPropertyValue<==>UDP');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('2601','260','stack-routing','20','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>STACK_ROUTING<;;>NewPropertyValue<==>VCE');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('2602','260','send','30','false','key<==>value');


Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('2620','262','transport-type','10','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>TRANSPORT_TYPE<;;>NewPropertyValue<==>SMS');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('2621','262','sms-prop','20','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>SMS_VALID_PERIOD_SECONDS<;;>NewPropertyValue<==>400');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('2622','262','stack-routing','30','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>STACK_ROUTING<;;>NewPropertyValue<==>VCE');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('2623','262','send','40','false','key<==>value');


Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('2630','263','transport-type','10','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>TRANSPORT_TYPE<;;>NewPropertyValue<==>SATELLITE');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('2631','263','stack-routing','20','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>STACK_ROUTING<;;>NewPropertyValue<==>VCE');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('2632','263','send','30','false','key<==>value');


Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('2640','264','transport-type','10','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>TRANSPORT_TYPE<;;>NewPropertyValue<==>UDP');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('2641','264','stack-routing','20','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>STACK_ROUTING<;;>NewPropertyValue<==>VCE');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('2642','264','send','30','false','key<==>value');


Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('2650','265','transport-type','10','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>TRANSPORT_TYPE<;;>NewPropertyValue<==>SMS');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('2651','265','sms-prop','20','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>SMS_VALID_PERIOD_SECONDS<;;>NewPropertyValue<==>43200');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('2652','265','stack-routing','30','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>STACK_ROUTING<;;>NewPropertyValue<==>VCE');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('2653','265','send','40','false','key<==>value');


Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('2660','266','transport-type','10','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>TRANSPORT_TYPE<;;>NewPropertyValue<==>SATELLITE');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('2661','266','stack-routing','30','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>STACK_ROUTING<;;>NewPropertyValue<==>VCE');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('2662','266','send','30','false','key<==>value');


Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('2700','270','transport-type','10','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>TRANSPORT_TYPE<;;>NewPropertyValue<==>UDP');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('2701','270','stack-routing','20','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>STACK_ROUTING<;;>NewPropertyValue<==>VCE');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('2702','270','send','30','false','key<==>value');

Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('2710','271','transport-type','10','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>TRANSPORT_TYPE<;;>NewPropertyValue<==>UDP');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('2711','271','stack-routing','20','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>STACK_ROUTING<;;>NewPropertyValue<==>VCE');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('2712','271','send','30','false','key<==>value');


Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('2720','272','transport-type','10','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>TRANSPORT_TYPE<;;>NewPropertyValue<==>SMS');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('2721','272','sms-prop','20','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>SMS_VALID_PERIOD_SECONDS<;;>NewPropertyValue<==>100');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('2722','272','stack-routing','30','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>STACK_ROUTING<;;>NewPropertyValue<==>VCE');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('2723','272','send','40','false','key<==>value');


Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('2730','273','transport-type','10','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>TRANSPORT_TYPE<;;>NewPropertyValue<==>SMS');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('2731','273','sms-prop','20','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>SMS_VALID_PERIOD_SECONDS<;;>NewPropertyValue<==>300');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('2732','273','stack-routing','30','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>STACK_ROUTING<;;>NewPropertyValue<==>VCE');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('2733','273','send','40','false','key<==>value');


Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('2740','274','transport-type','10','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>TRANSPORT_TYPE<;;>NewPropertyValue<==>SATELLITE');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('2741','274','stack-routing','20','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>STACK_ROUTING<;;>NewPropertyValue<==>VCE');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('2742','274','send','30','false','key<==>value');


Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('2750','275','transport-type','10','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>TRANSPORT_TYPE<;;>NewPropertyValue<==>UDP');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('2751','275','stack-routing','20','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>STACK_ROUTING<;;>NewPropertyValue<==>VCE');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('2752','275','send','30','false','key<==>value');


Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('2760','276','transport-type','10','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>TRANSPORT_TYPE<;;>NewPropertyValue<==>SMS');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('2761','276','sms-prop','20','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>SMS_VALID_PERIOD_SECONDS<;;>NewPropertyValue<==>43200');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('2762','276','stack-routing','30','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>STACK_ROUTING<;;>NewPropertyValue<==>VCE');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('2763','276','send','40','false','key<==>value');


Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('2770','277','transport-type','10','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>TRANSPORT_TYPE<;;>NewPropertyValue<==>SATELLITE');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('2771','277','stack-routing','20','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>STACK_ROUTING<;;>NewPropertyValue<==>VCE');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('2772','277','send','30','false','key<==>value');


Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('2800','280','transport-type','10','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>TRANSPORT_TYPE<;;>NewPropertyValue<==>UDP');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('2801','280','stack-routing','20','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>STACK_ROUTING<;;>NewPropertyValue<==>VCE');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('2802','280','send','30','false','key<==>value');


Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('2820','282','transport-type','10','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>TRANSPORT_TYPE<;;>NewPropertyValue<==>SMS');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('2821','282','sms-prop','20','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>SMS_VALID_PERIOD_SECONDS<;;>NewPropertyValue<==>400');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('2822','282','stack-routing','30','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>STACK_ROUTING<;;>NewPropertyValue<==>VCE');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('2823','282','send','40','false','key<==>value');


Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('2830','283','transport-type','10','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>TRANSPORT_TYPE<;;>NewPropertyValue<==>SATELLITE');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('2831','283','stack-routing','20','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>STACK_ROUTING<;;>NewPropertyValue<==>VCE');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('2832','283','send','30','false','key<==>value');


Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('2900','290','transport-type','10','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>TRANSPORT_TYPE<;;>NewPropertyValue<==>SMS');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('2901','290','sms-prop','20','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>SMS_VALID_PERIOD_SECONDS<;;>NewPropertyValue<==>86340');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('2902','290','stack-routing','30','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>STACK_ROUTING<;;>NewPropertyValue<==>VCE');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('2903','290','send','40','false','key<==>value');


Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('2910','291','transport-type','10','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>TRANSPORT_TYPE<;;>NewPropertyValue<==>SMS');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('2911','291','sms-prop','20','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>SMS_VALID_PERIOD_SECONDS<;;>NewPropertyValue<==>604740');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('2912','291','stack-routing','30','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>STACK_ROUTING<;;>NewPropertyValue<==>VCE');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('2913','291','send','40','false','key<==>value');


Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('3000','300','transport-type','10','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>TRANSPORT_TYPE<;;>NewPropertyValue<==>SMS');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('3001','300','sms-prop','20','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>SMS_VALID_PERIOD_SECONDS<;;>NewPropertyValue<==>360');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('3002','300','stack-routing','30','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>STACK_ROUTING<;;>NewPropertyValue<==>VCE');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('3003','300','send','40','false','key<==>value');


Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('3010','301','transport-type','10','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>TRANSPORT_TYPE<;;>NewPropertyValue<==>SATELLITE');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('3011','301','stack-routing','20','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>STACK_ROUTING<;;>NewPropertyValue<==>VCE');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('3012','301','send','30','false','key<==>value');


Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('3020','302','transport-type','10','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>TRANSPORT_TYPE<;;>NewPropertyValue<==>SMS');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('3021','302','sms-prop','20','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>SMS_VALID_PERIOD_SECONDS<;;>NewPropertyValue<==>43200');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('3022','302','stack-routing','30','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>STACK_ROUTING<;;>NewPropertyValue<==>VCE');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('3023','302','send','40','false','key<==>value');


Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('3030','303','transport-type','10','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>TRANSPORT_TYPE<;;>NewPropertyValue<==>SATELLITE');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('3031','303','stack-routing','20','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>STACK_ROUTING<;;>NewPropertyValue<==>VCE');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('3032','303','send','30','false','key<==>value');


Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('3100','310','transport-type','10','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>TRANSPORT_TYPE<;;>NewPropertyValue<==>SMS');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('3101','310','sms-prop','20','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>SMS_VALID_PERIOD_SECONDS<;;>NewPropertyValue<==>100');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('3102','310','stack-routing','30','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>STACK_ROUTING<;;>NewPropertyValue<==>VCE');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('3103','310','send','40','false','key<==>value');


Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('3110','311','transport-type','10','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>TRANSPORT_TYPE<;;>NewPropertyValue<==>SMS');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('3111','311','sms-prop','20','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>SMS_VALID_PERIOD_SECONDS<;;>NewPropertyValue<==>360');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('3112','311','stack-routing','30','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>STACK_ROUTING<;;>NewPropertyValue<==>VCE');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('3113','311','send','40','false','key<==>value');


Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('3120','312','transport-type','10','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>TRANSPORT_TYPE<;;>NewPropertyValue<==>SATELLITE');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('3121','312','stack-routing','20','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>STACK_ROUTING<;;>NewPropertyValue<==>VCE');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('3122','312','send','30','false','key<==>value');


Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('3130','313','transport-type','10','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>TRANSPORT_TYPE<;;>NewPropertyValue<==>SMS');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('3131','313','sms-prop','20','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>SMS_VALID_PERIOD_SECONDS<;;>NewPropertyValue<==>43200');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('3132','313','stack-routing','30','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>STACK_ROUTING<;;>NewPropertyValue<==>VCE');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('3133','313','send','40','false','key<==>value');


Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('3140','314','transport-type','10','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>TRANSPORT_TYPE<;;>NewPropertyValue<==>SATELLITE');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('3141','314','stack-routing','20','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>STACK_ROUTING<;;>NewPropertyValue<==>VCE');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('3142','314','send','30','false','key<==>value');


Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('3200','320','transport-type','10','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>TRANSPORT_TYPE<;;>NewPropertyValue<==>UDP');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('3201','320','stack-routing','20','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>STACK_ROUTING<;;>NewPropertyValue<==>VCE');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('3202','320','send','30','false','key<==>value');


Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('3210','321','transport-type','10','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>TRANSPORT_TYPE<;;>NewPropertyValue<==>SMS');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('3211','321','sms-prop','20','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>SMS_VALID_PERIOD_SECONDS<;;>NewPropertyValue<==>86340');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('3212','321','stack-routing','30','true','ActionType<==>AddProperty<;;>NewPropertyKey<==>STACK_ROUTING<;;>NewPropertyValue<==>VCE');
Insert into SCHD_ACTION (ID,TRANSITIONID,NAME,ORDERNUMBER,ISINTERNAL,PROPERTIES) values ('3213','321','send','40','false','key<==>value');
