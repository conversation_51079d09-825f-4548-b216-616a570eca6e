#!/bin/bash

readonly PORT=20597
readonly EXPECTED_COUNT=1
readonly TCP_PORT=20585

checkOpenPorts() {
    for i in {0..60}; do
        count=$(netstat --numeric --all --tcp | grep ":${TCP_PORT} " | grep --count LISTEN)
        if [ "$count" == "${EXPECTED_COUNT}" ]; then
            echo "TCP port $TCP_PORT is in state LISTEN"
            exit 0
        fi

        sleep 2
    done

    echo "TCP port check failed, expected <${EXPECTED_COUNT}>, got <${count}>" && exit 1
}

for i in {0..60}; do
  RESULT=$(curl -s -w "%{http_code}\n" localhost:${PORT}/actuator/health/ping)
  STATUS=$(printf "%s" "$RESULT" | tail -c 3)

  if [ "$STATUS" -eq "200" ];
  then
      exit 0
  fi

  sleep 2
done

BODY=${RESULT::-3}

echo "Expected status code <200>, got <${STATUS}> with body <$BODY>" && exit 1
