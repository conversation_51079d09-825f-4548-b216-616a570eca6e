package com.wirelesscar.tcevce.message.jms;

import java.util.HexFormat;
import java.util.Optional;
import java.util.function.Function;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.volvo.tisp.framework.jms.JmsMessage;
import com.volvo.tisp.framework.jms.annotation.JmsController;
import com.volvo.tisp.framework.jms.annotation.JmsMessageMapping;
import com.volvo.tisp.vc.common.dto.lib.jms.CorrelationId;
import com.volvo.tisp.vc.common.dto.lib.jms.ReplyTo;
import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.wirelesscar.tce.api.v2.MtMessage;
import com.wirelesscar.tce.api.v2.MtStatusReplyOption;
import com.wirelesscar.tce.api.v2.SchedulerOption;
import com.wirelesscar.tce.client.opus.MessageTypesJms;
import com.wirelesscar.tce.module.api.Message;
import com.wirelesscar.tcevce.message.jms.metrics.MessageServerMetricReporter;

@JmsController(destination = "MT-MESSAGES")
public class MessageServerJmsController {
  private static final Logger logger = LoggerFactory.getLogger(MessageServerJmsController.class);
  private final Function<MtMessage, Message> inputConverterFunctionV2;
  private final MessageDomainManager messageDomainManager;
  private final MessageServerMetricReporter messageServerMetricReporter;

  public MessageServerJmsController(Function<MtMessage, Message> inputConverterFunctionV2, MessageDomainManager messageDomainManager,
      MessageServerMetricReporter messageServerMetricReporter) {
    this.inputConverterFunctionV2 = inputConverterFunctionV2;
    this.messageDomainManager = messageDomainManager;
    this.messageServerMetricReporter = messageServerMetricReporter;
  }

  @JmsMessageMapping(consumesType = MessageTypesJms.TCE_MT_MESSAGE_TYPE, consumesVersion = MessageTypesJms.VERSION_2_0)
  public void receiveMessageTceV2(final JmsMessage<MtMessage> jmsMessage) {
    Validate.notNull(jmsMessage, "jmsMessage");

    messageServerMetricReporter.onReceivedMessageV2();

    MtMessage mtMessage = jmsMessage.payload();

    String hint = Optional.ofNullable(mtMessage.getSchedulerOption()).map(SchedulerOption::getHint).orElse("");
    byte[] payload = Optional.ofNullable(mtMessage.getPayload()).orElse(new byte[0]);

    logger.debug("MtMessage vpi={}, handle={}, scheduler_hint={}, string_payload={}, hex_payload={}",
        mtMessage.getVehiclePlatformId(), mtMessage.getLegacyIdentifyVehicleByDeviceHandleOption(),
        hint, new String(payload),
        HexFormat.of().withDelimiter(" ").withUpperCase().formatHex(payload));

    MtStatusReplyOption mtStatusReplyOption = mtMessage.getMtStatusReplyOption();

    final Optional<ReplyTo> replyTo = Optional.ofNullable(mtStatusReplyOption).map(MtStatusReplyOption::getReplyDestination).map(ReplyTo::ofString);
    final Optional<CorrelationId> correlationId = Optional.ofNullable(mtStatusReplyOption)
        .map(MtStatusReplyOption::getCorrelationId)
        .map(CorrelationId::ofString);

    final Message message = inputConverterFunctionV2.apply(mtMessage);

    messageDomainManager.receiveMessage(message, replyTo, correlationId);
  }
}
