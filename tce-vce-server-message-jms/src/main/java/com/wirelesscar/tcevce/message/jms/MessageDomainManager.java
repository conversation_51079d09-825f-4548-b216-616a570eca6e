package com.wirelesscar.tcevce.message.jms;

import java.util.Optional;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.volvo.tisp.vc.common.dto.lib.jms.CorrelationId;
import com.volvo.tisp.vc.common.dto.lib.jms.ReplyTo;
import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.wirelesscar.config.Config;
import com.wirelesscar.config.ConfigFactory;
import com.wirelesscar.tce.core.stack.ModuleStack;
import com.wirelesscar.tce.core.stack.ModuleStackManager;
import com.wirelesscar.tce.module.api.Message;
import com.wirelesscar.tce.module.api.MetaData;
import com.wirelesscar.tce.module.api.Module;

@Component
public class MessageDomainManager {
  private static final Config config = ConfigFactory.getConfig();
  private static final Logger log = LoggerFactory.getLogger(MessageDomainManager.class);
  private static final String STACK_NAME_PROPERTY = "message.server.jms.api.lookup.name";

  private final ModuleStackManager moduleStackManager;

  public MessageDomainManager(ModuleStackManager moduleStackManager) {
    this.moduleStackManager = moduleStackManager;
  }

  public void receiveMessage(Message message, Optional<ReplyTo> replyTo, Optional<CorrelationId> correlationId) {
    Validate.notNull(message, "message");
    Validate.notNull(replyTo, "replyTo");
    Validate.notNull(correlationId, "correlationId");

    log.trace("Received message on JMS: {}", message);

    String stackName = config
        .getString(STACK_NAME_PROPERTY)
        .orElseThrow(() -> new IllegalStateException("missing string config: " + STACK_NAME_PROPERTY));
    ModuleStack moduleStack = moduleStackManager.getByName(stackName);

    if (replyTo.isPresent()) {
      message.setProperty(MetaData.JMS_REPLY_TO, replyTo.get().toString());

      if (correlationId.isPresent()) {
        message.setProperty(MetaData.JMS_CORRELATION_ID, correlationId.get().toString());
      }
    }

    Module topModule = moduleStack.getTopModule();

    if (topModule != null) {
      topModule.down(message);
      return;
    }

    throw new IllegalStateException("Message Service has wrong config top module not found: " + stackName);
  }
}
