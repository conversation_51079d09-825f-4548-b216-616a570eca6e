package com.wirelesscar.tcevce.message.jms.metrics;

import org.springframework.stereotype.Component;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tags;

@Component
public class MessageServerMetricReporter {
  private static final String VERSION = "version";
  private static final String VERSION2 = "v2";

  private final Counter messageReceivedV2Counter;

  public MessageServerMetricReporter(MeterRegistry meterRegistry) {

    messageReceivedV2Counter = meterRegistry.counter("api.jms.message.received", Tags.of(VERSION, VERSION2));
  }

  public void onReceivedMessageV2() {
    messageReceivedV2Counter.increment();
  }
}
