package com.wirelesscar.tcevce.message.jms;

import java.util.Optional;
import java.util.function.Function;

import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import com.volvo.tisp.framework.jms.JmsMessage;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;
import com.wirelesscar.tce.api.v2.MtMessage;
import com.wirelesscar.tce.module.api.Message;
import com.wirelesscar.tcevce.message.jms.metrics.MessageServerMetricReporter;

class MessageServerJmsControllerTest {
  private static <T, R> Function<T, R> mockFunction() {
    return Mockito.mock(Function.class);
  }

  private static <T> JmsMessage<T> mockJmsMessage(T body) {
    JmsMessage<T> jmsMessage = Mockito.mock(JmsMessage.class);
    Mockito.when(jmsMessage.payload()).thenReturn(body);
    return jmsMessage;
  }

  @Test
  void receiveMessageTceV2NullTest() {
    final Function<MtMessage, Message> inputConverterFunctionV2 = mockFunction();
    final MessageDomainManager messageDomainManager = Mockito.mock(MessageDomainManager.class);
    final MessageServerMetricReporter messageServerMetricReporter = Mockito.mock(MessageServerMetricReporter.class);

    MessageServerJmsController messageServerJmsController = new MessageServerJmsController(inputConverterFunctionV2, messageDomainManager,
        messageServerMetricReporter);

    AssertThrows.illegalArgumentException(() -> messageServerJmsController.receiveMessageTceV2(null), "jmsMessage must not be null");

    Mockito.verifyNoInteractions(inputConverterFunctionV2, messageDomainManager, messageServerMetricReporter);
  }

  @Test
  void receiveMessageTceV2Test() {
    final MtMessage mtMessage = Mockito.mock(MtMessage.class);
    final Message message = Message.createMessage();

    final Function<MtMessage, Message> inputConverterFunctionV2 = mockFunction();
    Mockito.when(inputConverterFunctionV2.apply(mtMessage)).thenReturn(message);

    final MessageDomainManager messageDomainManager = Mockito.mock(MessageDomainManager.class);
    final MessageServerMetricReporter messageServerMetricReporter = Mockito.mock(MessageServerMetricReporter.class);

    final JmsMessage<MtMessage> jmsMessage = mockJmsMessage(mtMessage);

    MessageServerJmsController messageServerJmsController = new MessageServerJmsController(inputConverterFunctionV2, messageDomainManager,
        messageServerMetricReporter);

    messageServerJmsController.receiveMessageTceV2(jmsMessage);

    Mockito.verify(messageServerMetricReporter).onReceivedMessageV2();
    Mockito.verify(inputConverterFunctionV2).apply(mtMessage);
    Mockito.verify(messageDomainManager).receiveMessage(message, Optional.empty(), Optional.empty());
    Mockito.verifyNoMoreInteractions(inputConverterFunctionV2, messageDomainManager, messageServerMetricReporter);
  }
}
