package com.wirelesscar.tcevce.message.jms.metrics;

import org.junit.jupiter.api.Test;

import io.micrometer.core.instrument.Tags;

class MessageServerMetricReporterTest {
  @Test
  void onReceivedMessageV2Test() {
    MetricsReporterTestUtils.initReporterAndTest(MessageServerMetricReporter::new, (meterRegistry, messageServerMetricReporter) -> {
      messageServerMetricReporter.onReceivedMessageV2();
      MetricsReporterTestUtils.checkCounter(meterRegistry, 1, "api.jms.message.received", Tags.of("version", "v2"));
      MetricsReporterTestUtils.checkCounter(meterRegistry, 1, "api.jms.message.received", Tags.of("version", "v2"));
    });
  }
}
