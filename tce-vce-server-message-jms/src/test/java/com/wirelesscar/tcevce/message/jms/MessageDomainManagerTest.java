package com.wirelesscar.tcevce.message.jms;

import java.util.Optional;

import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import com.volvo.tisp.vc.test.utils.lib.AssertThrows;
import com.wirelesscar.config.mock.MockConfiguration;
import com.wirelesscar.tce.core.stack.ModuleStack;
import com.wirelesscar.tce.core.stack.ModuleStackManager;
import com.wirelesscar.tce.module.api.Message;
import com.wirelesscar.tce.module.api.Module;

class MessageDomainManagerTest {
  private static MockConfiguration getEmptyMockConfiguration() {
    MockConfiguration mockConfiguration = MockConfiguration.getConfig();
    mockConfiguration.deleteAllProperties();
    return mockConfiguration;
  }

  @Test
  void receiveMessageInvalidParameterTest() {
    final ModuleStackManager moduleStackManager = Mockito.mock(ModuleStackManager.class);
    MessageDomainManager messageDomainManager = new MessageDomainManager(moduleStackManager);

    final Message message = Message.createMessage();

    AssertThrows.illegalArgumentException(() -> messageDomainManager.receiveMessage(null, Optional.empty(), Optional.empty()), "message must not be null");
    AssertThrows.illegalArgumentException(() -> messageDomainManager.receiveMessage(message, null, Optional.empty()), "replyTo must not be null");
    AssertThrows.illegalArgumentException(() -> messageDomainManager.receiveMessage(message, Optional.empty(), null),
        "correlationId must not be null");

    Mockito.verifyNoInteractions(moduleStackManager);
  }

  @Test
  void receiveMessageTest() {
    final String stackName = "foo";

    MockConfiguration mockConfiguration = getEmptyMockConfiguration();
    mockConfiguration.setPropertySpecific(mockConfiguration.getComponentShortName(), "message.server.jms.api.lookup.name", stackName);

    final Module topModule = Mockito.mock(Module.class);

    final ModuleStack moduleStack = Mockito.mock(ModuleStack.class);
    Mockito.when(moduleStack.getTopModule()).thenReturn(topModule);

    final ModuleStackManager moduleStackManager = Mockito.mock(ModuleStackManager.class);
    Mockito.when(moduleStackManager.getByName(stackName)).thenReturn(moduleStack);

    final Message message = Message.createMessage();
    MessageDomainManager messageDomainManager = new MessageDomainManager(moduleStackManager);

    messageDomainManager.receiveMessage(message, Optional.empty(), Optional.empty());

    Mockito.verify(moduleStackManager).getByName(stackName);
    Mockito.verify(moduleStack).getTopModule();
    Mockito.verify(topModule).down(message);
    Mockito.verifyNoMoreInteractions(moduleStackManager, moduleStack, topModule);
  }
}
