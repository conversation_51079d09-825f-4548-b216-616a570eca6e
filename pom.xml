<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
     xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>com.volvo.tisp</groupId>
    <artifactId>tisp-parent</artifactId>
    <version>144</version>
  </parent>

  <artifactId>tce-vce-server</artifactId>
  <version>0-SNAPSHOT</version>
  <packaging>pom</packaging>

  <properties>
    <component.long-name>tce-vce-server</component.long-name>
    <component.short-name>tcevce</component.short-name>
    <component.debug.port>8787</component.debug.port>
    <component.debug.suspend>n</component.debug.suspend>

    <caretrack-protocol.version>238</caretrack-protocol.version>
    <common-dto-lib.version>162</common-dto-lib.version>
    <connectivity-repository-client.version>242</connectivity-repository-client.version>
    <jdbi.version>3.45.4</jdbi.version>
    <!-- override surfire plugin version until https://issues.apache.org/jira/browse/SUREFIRE-1815 is fixed -->
    <surefire.plugin>3.5.0</surefire.plugin>
    <subscriptionrepository-client.version>649</subscriptionrepository-client.version>
    <tce-lib.version>1786</tce-lib.version>
    <tcp-server-lib.version>64</tcp-server-lib.version>
    <test-utils-lib.version>63</test-utils-lib.version>
    <tisp-dependencies.version>144</tisp-dependencies.version>
    <udp-server-lib.version>82</udp-server-lib.version>
    <vc-influxdb-event-reporter-lib.version>46</vc-influxdb-event-reporter-lib.version>
    <vc-uncaught-exception-handler-lib.version>13</vc-uncaught-exception-handler-lib.version>
    <xml-utils-lib.version>82</xml-utils-lib.version>
    <vc-crypto-lib.version>115</vc-crypto-lib.version>
  </properties>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>com.volvo.tisp</groupId>
        <artifactId>tisp-dependencies</artifactId>
        <version>${tisp-dependencies.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.volvo.tisp</groupId>
        <artifactId>common-dto-lib</artifactId>
        <version>${common-dto-lib.version}</version>
      </dependency>
      <dependency>
        <groupId>com.volvo.tisp</groupId>
        <artifactId>tce-module-api</artifactId>
        <version>${tce-lib.version}</version>
      </dependency>
      <dependency>
        <groupId>com.volvo.tisp</groupId>
        <artifactId>tce-module-udp</artifactId>
        <version>${tce-lib.version}</version>
      </dependency>
      <dependency>
        <groupId>com.volvo.tisp</groupId>
        <artifactId>tce-module-threadpool</artifactId>
        <version>${tce-lib.version}</version>
      </dependency>
      <dependency>
        <groupId>com.volvo.tisp</groupId>
        <artifactId>tce-module-shard-router</artifactId>
        <version>${tce-lib.version}</version>
      </dependency>
      <dependency>
        <groupId>com.volvo.tisp</groupId>
        <artifactId>tce-module-scooter-agent</artifactId>
        <version>${tce-lib.version}</version>
      </dependency>
      <dependency>
        <groupId>com.volvo.tisp</groupId>
        <artifactId>tce-module-tcpip</artifactId>
        <version>${tce-lib.version}</version>
      </dependency>
      <dependency>
        <groupId>com.volvo.tisp</groupId>
        <artifactId>tce-common-database-standard</artifactId>
        <version>${tce-lib.version}</version>
      </dependency>
      <dependency>
        <groupId>com.volvo.tisp</groupId>
        <artifactId>tce-core-legacy-ha</artifactId>
        <version>${tce-lib.version}</version>
      </dependency>
      <dependency>
        <groupId>com.volvo.tisp</groupId>
        <artifactId>caretrack-protocol</artifactId>
        <version>${caretrack-protocol.version}</version>
      </dependency>
      <dependency>
        <groupId>com.wirelesscar.conrepo</groupId>
        <artifactId>connectivity-repository-client-api</artifactId>
        <version>${connectivity-repository-client.version}</version>
      </dependency>
      <dependency>
        <groupId>com.volvo.tisp</groupId>
        <artifactId>vc-influxdb-event-reporter-lib</artifactId>
        <version>${vc-influxdb-event-reporter-lib.version}</version>
      </dependency>
      <dependency>
        <groupId>com.volvo.tisp</groupId>
        <artifactId>vc-uncaught-exception-handler-lib</artifactId>
        <version>${vc-uncaught-exception-handler-lib.version}</version>
      </dependency>
      <dependency>
        <groupId>com.volvo.tisp</groupId>
        <artifactId>tce-common-hazelcast</artifactId>
        <version>${tce-lib.version}</version>
      </dependency>
      <dependency>
        <groupId>org.jdbi</groupId>
        <artifactId>jdbi3-core</artifactId>
        <version>${jdbi.version}</version>
      </dependency>
      <dependency>
        <groupId>com.wirelesscar.subscriptionrepository</groupId>
        <artifactId>subscriptionrepository-client-impl</artifactId>
        <version>${subscriptionrepository-client.version}</version>
        <exclusions>
          <exclusion>
            <groupId>org.wiremock</groupId>
            <artifactId>wiremock</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>com.volvo.tisp</groupId>
        <artifactId>tce-api-converter</artifactId>
        <version>${tce-lib.version}</version>
      </dependency>
      <dependency>
        <groupId>com.volvo.tisp</groupId>
        <artifactId>tce-core-api</artifactId>
        <version>${tce-lib.version}</version>
      </dependency>
      <dependency>
        <groupId>com.volvo.tisp</groupId>
        <artifactId>tce-legacy-integration-logging</artifactId>
        <version>${tce-lib.version}</version>
      </dependency>
      <dependency>
        <groupId>com.volvo.tisp</groupId>
        <artifactId>tce-common-database-api</artifactId>
        <version>${tce-lib.version}</version>
      </dependency>
      <dependency>
        <groupId>com.volvo.tisp</groupId>
        <artifactId>vc-crypto-symmetric</artifactId>
        <version>${vc-crypto-lib.version}</version>
      </dependency>

      <dependency>
        <groupId>com.wirelesscar.subscriptionrepository</groupId>
        <artifactId>subscriptionrepository-client-test-util</artifactId>
        <version>${subscriptionrepository-client.version}</version>
        <scope>test</scope>
        <exclusions>
          <exclusion>
            <groupId>org.wiremock</groupId>
            <artifactId>wiremock</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>com.volvo.tisp</groupId>
        <artifactId>tcp-server-lib</artifactId>
        <version>${tcp-server-lib.version}</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>com.volvo.tisp</groupId>
        <artifactId>udp-server-lib</artifactId>
        <version>${udp-server-lib.version}</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>com.volvo.tisp</groupId>
        <artifactId>test-utils-lib</artifactId>
        <version>${test-utils-lib.version}</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>com.volvo.tisp</groupId>
        <artifactId>xml-utils-lib</artifactId>
        <version>${xml-utils-lib.version}</version>
        <scope>test</scope>
      </dependency>
    </dependencies>
  </dependencyManagement>

  <build>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-site-plugin</artifactId>
        <configuration>
          <skip>true</skip>
          <skipDeploy>true</skipDeploy>
        </configuration>
      </plugin>
    </plugins>
  </build>

  <profiles>
    <profile>
      <id>default</id>
      <activation>
        <activeByDefault>true</activeByDefault>
      </activation>
      <modules>
        <module>tce-vce-server-app</module>
        <module>tce-vce-server-common-database-tools</module>
        <module>tce-vce-server-connectivity-repo-parent</module>
        <module>tce-vce-server-impl</module>
        <module>tce-vce-server-integration-tests</module>
        <module>tce-vce-server-message-jms</module>
        <module>tce-vce-server-modules</module>
        <module>wecu-device-info</module>
      </modules>
    </profile>
    <profile>
      <id>deployable-assembly</id>
      <activation>
        <property>
          <name>deployable-assembly</name>
        </property>
      </activation>
      <modules>
        <module>deployable-assembly</module>
      </modules>
    </profile>
    <profile>
      <id>component-tests</id>
    </profile>
  </profiles>
</project>
