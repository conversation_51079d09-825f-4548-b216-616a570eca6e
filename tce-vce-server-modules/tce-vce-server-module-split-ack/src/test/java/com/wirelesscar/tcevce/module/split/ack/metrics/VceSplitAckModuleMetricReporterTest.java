package com.wirelesscar.tcevce.module.split.ack.metrics;

import static com.wirelesscar.tcevce.module.split.ack.metrics.VceSplitAckModuleMetricReporter.HANDLE_MO_ACK;
import static com.wirelesscar.tcevce.module.split.ack.metrics.VceSplitAckModuleMetricReporter.HANDLE_MO_DATA;
import static com.wirelesscar.tcevce.module.split.ack.metrics.VceSplitAckModuleMetricReporter.HANDLE_MO_MESSAGE;
import static com.wirelesscar.tcevce.module.split.ack.metrics.VceSplitAckModuleMetricReporter.HANDLE_MO_OR_ACK;
import static com.wirelesscar.tcevce.module.split.ack.metrics.VceSplitAckModuleMetricReporter.HANDLE_MO_PING;
import static com.wirelesscar.tcevce.module.split.ack.metrics.VceSplitAckModuleMetricReporter.HANDLE_MT_DATA;
import static com.wirelesscar.tcevce.module.split.ack.metrics.VceSplitAckModuleMetricReporter.HANDLE_MT_MESSAGE;
import static com.wirelesscar.tcevce.module.split.ack.metrics.VceSplitAckModuleMetricReporter.MO_HANDLE_ACK;
import static com.wirelesscar.tcevce.module.split.ack.metrics.VceSplitAckModuleMetricReporter.MO_MESSAGE_UP;
import static com.wirelesscar.tcevce.module.split.ack.metrics.VceSplitAckModuleMetricReporter.MO_PING_HANDLED;
import static com.wirelesscar.tcevce.module.split.ack.metrics.VceSplitAckModuleMetricReporter.MO_PONG_RECEIVED;
import static com.wirelesscar.tcevce.module.split.ack.metrics.VceSplitAckModuleMetricReporter.MO_UNKNOWN_CHOICE;
import static com.wirelesscar.tcevce.module.split.ack.metrics.VceSplitAckModuleMetricReporter.MT_HANDLE_MESSAGE;
import static com.wirelesscar.tcevce.module.split.ack.metrics.VceSplitAckModuleMetricReporter.MT_RECEIVE_ACK_EXECUTE;
import static com.wirelesscar.tcevce.module.split.ack.metrics.VceSplitAckModuleMetricReporter.MT_RECEIVE_PING_EXECUTE;
import static com.wirelesscar.tcevce.module.split.ack.metrics.VceSplitAckModuleMetricReporter.MT_RECEIVE_PONG_EXECUTE;
import static com.wirelesscar.tcevce.module.split.ack.metrics.VceSplitAckModuleMetricReporter.MT_UNKNOWN_CHOICE;
import static com.wirelesscar.tcevce.module.split.ack.metrics.VceSplitAckModuleMetricReporter.PRODUCED_ACK;
import static com.wirelesscar.tcevce.module.split.ack.metrics.VceSplitAckModuleMetricReporter.PROPS_FOUND_IN_CACHE;
import static com.wirelesscar.tcevce.module.split.ack.metrics.VceSplitAckModuleMetricReporter.PROPS_NOT_FOUND_IN_CACHE;
import static com.wirelesscar.tcevce.module.split.ack.metrics.VceSplitAckModuleMetricReporter.STACK_VAR_NAME;
import static org.junit.jupiter.api.Assertions.assertEquals;

import java.util.function.BiConsumer;
import java.util.function.Function;

import org.junit.jupiter.api.Test;

import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tags;
import io.micrometer.core.instrument.simple.SimpleMeterRegistry;

class VceSplitAckModuleMetricReporterTest {
  private static final String STACK_TEST = "stack";

  private static void checkCounter(MeterRegistry meterRegistry, int expectedCount, String name, Tags tags) {
    final Counter counter = meterRegistry.find(name).tags(tags).counter();

    assertEquals(expectedCount, counter.count());
  }

  private static <T> void initReporterAndTest(Function<MeterRegistry, T> initMetricsReporter, BiConsumer<MeterRegistry, T> test) {
    MeterRegistry meterRegistry = new SimpleMeterRegistry();
    T metricsReporter = initMetricsReporter.apply(meterRegistry);

    test.accept(meterRegistry, metricsReporter);
  }

  @Test
  void onHandleMoAckTest() {
    initReporterAndTest(VceSplitAckModuleMetricReporter::new, (meterRegistry, vceSplitAckModuleMetricReporter) -> {
      vceSplitAckModuleMetricReporter.onHandleMoAck(STACK_TEST);
      checkCounter(meterRegistry, 1, HANDLE_MO_ACK, Tags.of(STACK_VAR_NAME, STACK_TEST));
    });
  }

  @Test
  void onHandleMoDataTest() {
    initReporterAndTest(VceSplitAckModuleMetricReporter::new, (meterRegistry, vceSplitAckModuleMetricReporter) -> {
      vceSplitAckModuleMetricReporter.onHandleMoData(STACK_TEST);
      checkCounter(meterRegistry, 1, HANDLE_MO_DATA, Tags.of(STACK_VAR_NAME, STACK_TEST));
    });
  }

  @Test
  void onHandleMoMessageTest() {
    initReporterAndTest(VceSplitAckModuleMetricReporter::new, (meterRegistry, vceSplitAckModuleMetricReporter) -> {
      vceSplitAckModuleMetricReporter.onHandleMoMessage(STACK_TEST);
      checkCounter(meterRegistry, 1, HANDLE_MO_MESSAGE, Tags.of(STACK_VAR_NAME, STACK_TEST));
    });
  }

  @Test
  void onHandleMoOrAckTest() {
    initReporterAndTest(VceSplitAckModuleMetricReporter::new, (meterRegistry, vceSplitAckModuleMetricReporter) -> {
      vceSplitAckModuleMetricReporter.onHandleMoOrAck(STACK_TEST);
      checkCounter(meterRegistry, 1, HANDLE_MO_OR_ACK, Tags.of(STACK_VAR_NAME, STACK_TEST));
    });
  }

  @Test
  void onHandleMoPingTest() {
    initReporterAndTest(VceSplitAckModuleMetricReporter::new, (meterRegistry, vceSplitAckModuleMetricReporter) -> {
      vceSplitAckModuleMetricReporter.onHandleMoPing(STACK_TEST);
      checkCounter(meterRegistry, 1, HANDLE_MO_PING, Tags.of(STACK_VAR_NAME, STACK_TEST));
    });
  }

  @Test
  void onHandleMtDataTest() {
    initReporterAndTest(VceSplitAckModuleMetricReporter::new, (meterRegistry, vceSplitAckModuleMetricReporter) -> {
      vceSplitAckModuleMetricReporter.onHandleMtData(STACK_TEST);
      checkCounter(meterRegistry, 1, HANDLE_MT_DATA, Tags.of(STACK_VAR_NAME, STACK_TEST));
    });
  }

  @Test
  void onHandleMtMessageInvalidTest() {
    initReporterAndTest(VceSplitAckModuleMetricReporter::new, (meterRegistry, vceSplitAckModuleMetricReporter) -> {
      AssertThrows.illegalArgumentException(() -> vceSplitAckModuleMetricReporter.onHandleMtMessage(null), "stackName must not be null");
      AssertThrows.illegalArgumentException(() -> vceSplitAckModuleMetricReporter.onHandleMtMessage(""), "stackName must not be empty");
    });
  }

  @Test
  void onHandleMtMessageTest() {
    initReporterAndTest(VceSplitAckModuleMetricReporter::new, (meterRegistry, vceSplitAckModuleMetricReporter) -> {
      vceSplitAckModuleMetricReporter.onHandleMtMessage(STACK_TEST);
      checkCounter(meterRegistry, 1, HANDLE_MT_MESSAGE, Tags.of(STACK_VAR_NAME, STACK_TEST));
    });
  }

  @Test
  void onMoHandleAckTest() {
    initReporterAndTest(VceSplitAckModuleMetricReporter::new, (meterRegistry, vceSplitAckModuleMetricReporter) -> {
      vceSplitAckModuleMetricReporter.onMoHandleAck(STACK_TEST);
      checkCounter(meterRegistry, 1, MO_HANDLE_ACK, Tags.of(STACK_VAR_NAME, STACK_TEST));
    });
  }

  @Test
  void onMoMessageUpTest() {
    initReporterAndTest(VceSplitAckModuleMetricReporter::new, (meterRegistry, vceSplitAckModuleMetricReporter) -> {
      vceSplitAckModuleMetricReporter.onMoMessageUp(STACK_TEST);
      checkCounter(meterRegistry, 1, MO_MESSAGE_UP, Tags.of(STACK_VAR_NAME, STACK_TEST));
    });
  }

  @Test
  void onMoPingHandledTest() {
    initReporterAndTest(VceSplitAckModuleMetricReporter::new, (meterRegistry, vceSplitAckModuleMetricReporter) -> {
      vceSplitAckModuleMetricReporter.onMoPingHandled(STACK_TEST);
      checkCounter(meterRegistry, 1, MO_PING_HANDLED, Tags.of(STACK_VAR_NAME, STACK_TEST));
    });
  }

  @Test
  void onMoPongReceivedTest() {
    initReporterAndTest(VceSplitAckModuleMetricReporter::new, (meterRegistry, vceSplitAckModuleMetricReporter) -> {
      vceSplitAckModuleMetricReporter.onMoPongReceived(STACK_TEST);
      checkCounter(meterRegistry, 1, MO_PONG_RECEIVED, Tags.of(STACK_VAR_NAME, STACK_TEST));
    });
  }

  @Test
  void onMoUnknownChoiceTest() {
    initReporterAndTest(VceSplitAckModuleMetricReporter::new, (meterRegistry, vceSplitAckModuleMetricReporter) -> {
      vceSplitAckModuleMetricReporter.onMoUnknownChoice(STACK_TEST);
      checkCounter(meterRegistry, 1, MO_UNKNOWN_CHOICE, Tags.of(STACK_VAR_NAME, STACK_TEST));
    });
  }

  @Test
  void onMtHandleMessageTest() {
    initReporterAndTest(VceSplitAckModuleMetricReporter::new, (meterRegistry, vceSplitAckModuleMetricReporter) -> {
      vceSplitAckModuleMetricReporter.onMtHandleMessage(STACK_TEST);
      checkCounter(meterRegistry, 1, MT_HANDLE_MESSAGE, Tags.of(STACK_VAR_NAME, STACK_TEST));
    });
  }

  @Test
  void onMtReceivedAckExecuteTest() {
    initReporterAndTest(VceSplitAckModuleMetricReporter::new, (meterRegistry, vceSplitAckModuleMetricReporter) -> {
      vceSplitAckModuleMetricReporter.onMtReceivedAckExecute(STACK_TEST);
      checkCounter(meterRegistry, 1, MT_RECEIVE_ACK_EXECUTE, Tags.of(STACK_VAR_NAME, STACK_TEST));
    });
  }

  @Test
  void onMtReceivedPingExecuteTest() {
    initReporterAndTest(VceSplitAckModuleMetricReporter::new, (meterRegistry, vceSplitAckModuleMetricReporter) -> {
      vceSplitAckModuleMetricReporter.onMtReceivedPingExecute(STACK_TEST);
      checkCounter(meterRegistry, 1, MT_RECEIVE_PING_EXECUTE, Tags.of(STACK_VAR_NAME, STACK_TEST));
    });
  }

  @Test
  void onMtReceivedPongExecuteTest() {
    initReporterAndTest(VceSplitAckModuleMetricReporter::new, (meterRegistry, vceSplitAckModuleMetricReporter) -> {
      vceSplitAckModuleMetricReporter.onMtReceivedPongExecute(STACK_TEST);
      checkCounter(meterRegistry, 1, MT_RECEIVE_PONG_EXECUTE, Tags.of(STACK_VAR_NAME, STACK_TEST));
    });
  }

  @Test
  void onMtUnknownChoiceTest() {
    initReporterAndTest(VceSplitAckModuleMetricReporter::new, (meterRegistry, vceSplitAckModuleMetricReporter) -> {
      vceSplitAckModuleMetricReporter.onMtUnknownChoice(STACK_TEST);
      checkCounter(meterRegistry, 1, MT_UNKNOWN_CHOICE, Tags.of(STACK_VAR_NAME, STACK_TEST));
    });
  }

  @Test
  void onProducedAckTest() {
    initReporterAndTest(VceSplitAckModuleMetricReporter::new, (meterRegistry, vceSplitAckModuleMetricReporter) -> {
      vceSplitAckModuleMetricReporter.onProducedAck(STACK_TEST);
      checkCounter(meterRegistry, 1, PRODUCED_ACK, Tags.of(STACK_VAR_NAME, STACK_TEST));
    });
  }

  @Test
  void onPropsFoundInCacheTest() {
    initReporterAndTest(VceSplitAckModuleMetricReporter::new, (meterRegistry, vceSplitAckModuleMetricReporter) -> {
      vceSplitAckModuleMetricReporter.onPropsFoundInCache(STACK_TEST);
      checkCounter(meterRegistry, 1, PROPS_FOUND_IN_CACHE, Tags.of(STACK_VAR_NAME, STACK_TEST));
    });
  }

  @Test
  void onPropsNotFoundInCacheTest() {
    initReporterAndTest(VceSplitAckModuleMetricReporter::new, (meterRegistry, vceSplitAckModuleMetricReporter) -> {
      vceSplitAckModuleMetricReporter.onPropsNotFoundInCache(STACK_TEST);
      checkCounter(meterRegistry, 1, PROPS_NOT_FOUND_IN_CACHE, Tags.of(STACK_VAR_NAME, STACK_TEST));
    });
  }
}
