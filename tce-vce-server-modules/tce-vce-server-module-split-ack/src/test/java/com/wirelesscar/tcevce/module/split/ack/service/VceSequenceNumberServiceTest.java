package com.wirelesscar.tcevce.module.split.ack.service;

import java.util.Optional;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.InOrder;
import org.mockito.Mockito;

import com.wirelesscar.tce.module.api.Message;
import com.wirelesscar.tce.module.api.MetaData;
import com.wirelesscar.tcevce.module.split.ack.TestUtils;
import com.wirelesscar.tcevce.wecu.device.info.database.api.DeviceInfoWriter;
import com.wirelesscar.tcevce.wecu.device.info.database.api.DeviceInfoWriterFactory;
import com.wirelesscar.tcevce.wecu.device.info.database.model.PersistedDeviceInfo;
import com.wirelesscar.tcevce.wecu.device.info.database.model.SequenceNumber;

class VceSequenceNumberServiceTest {
  private static final PersistedDeviceInfo PERSISTED_DEVICE_INFO = TestUtils.createPersistedDeviceInfo();

  private static void verifyMessage(Message message, Optional<SequenceNumber> sequenceNumberOptional) {
    if (sequenceNumberOptional.isEmpty()) {
      Assertions.assertNull(message.getProperty(MetaData.SEQUENCE_NUMBER));
    } else {
      Assertions.assertEquals(message.getProperty(MetaData.SEQUENCE_NUMBER), sequenceNumberOptional.get().toString());
    }
  }

  @Test
  void setSequenceNumberNoDeviceTest() {
    Message message = TestUtils.createMessage();

    DeviceInfoWriter deviceInfoWriter = Mockito.mock(DeviceInfoWriter.class);
    DeviceInfoWriterFactory deviceInfoWriterFactory = Mockito.mock(DeviceInfoWriterFactory.class);

    Mockito.when(deviceInfoWriterFactory.createReadCommitted()).thenReturn(deviceInfoWriter);
    Mockito.when(deviceInfoWriter.findDeviceInfoByVpi(TestUtils.VPI)).thenReturn(Optional.empty());

    verifyMessage(message, Optional.empty());

    VceSequenceNumberService vceSequenceNumberService = new VceSequenceNumberService(deviceInfoWriterFactory);
    vceSequenceNumberService.setSequenceNumber(message);

    verifyMessage(message, Optional.empty());
    InOrder inOrder = Mockito.inOrder(deviceInfoWriter, deviceInfoWriterFactory);

    inOrder.verify(deviceInfoWriterFactory).createReadCommitted();
    inOrder.verify(deviceInfoWriter).findDeviceInfoByVpi(TestUtils.VPI);
    inOrder.verify(deviceInfoWriter).close();
    inOrder.verifyNoMoreInteractions();
  }

  @Test
  void setSequenceNumberWithExistingSequenceNumberTest() {
    SequenceNumber sequenceNumber = SequenceNumber.ofByte((byte) 4);

    Message message = TestUtils.createMessage();
    message.setProperty(MetaData.SEQUENCE_NUMBER, sequenceNumber.toString());

    DeviceInfoWriterFactory deviceInfoWriterFactory = Mockito.mock(DeviceInfoWriterFactory.class);

    verifyMessage(message, Optional.of(sequenceNumber));

    VceSequenceNumberService vceSequenceNumberService = new VceSequenceNumberService(deviceInfoWriterFactory);
    vceSequenceNumberService.setSequenceNumber(message);

    verifyMessage(message, Optional.of(sequenceNumber));

    Mockito.verifyNoMoreInteractions(deviceInfoWriterFactory);
  }

  @Test
  void setSequenceNumberWithNoSequenceNumberTest() {
    Message message = TestUtils.createMessage();

    DeviceInfoWriter deviceInfoWriter = Mockito.mock(DeviceInfoWriter.class);
    DeviceInfoWriterFactory deviceInfoWriterFactory = Mockito.mock(DeviceInfoWriterFactory.class);

    Mockito.when(deviceInfoWriterFactory.createReadCommitted()).thenReturn(deviceInfoWriter);
    Mockito.when(deviceInfoWriter.findDeviceInfoByVpi(TestUtils.VPI)).thenReturn(Optional.of(PERSISTED_DEVICE_INFO));
    Mockito.when(deviceInfoWriter.findDeviceSequenceByDeviceInfoId(TestUtils.DEVICE_INFO_ID)).thenReturn(Optional.empty());

    verifyMessage(message, Optional.empty());

    VceSequenceNumberService vceSequenceNumberService = new VceSequenceNumberService(deviceInfoWriterFactory);
    vceSequenceNumberService.setSequenceNumber(message);

    verifyMessage(message, Optional.of(SequenceNumber.ofByte((byte) 0)));

    InOrder inOrder = Mockito.inOrder(deviceInfoWriter, deviceInfoWriterFactory);
    inOrder.verify(deviceInfoWriterFactory).createReadCommitted();
    inOrder.verify(deviceInfoWriter).findDeviceInfoByVpi(TestUtils.VPI);
    inOrder.verify(deviceInfoWriter).findDeviceSequenceByDeviceInfoId(TestUtils.DEVICE_INFO_ID);
    inOrder.verify(deviceInfoWriter).insertDeviceSequence(TestUtils.createDeviceSequence(SequenceNumber.ofByte((byte) 1)));
    inOrder.verify(deviceInfoWriter).close();
    inOrder.verifyNoMoreInteractions();
  }

  @Test
  void setSequenceNumberWithSequenceNumberTest() {
    Message message = TestUtils.createMessage();

    SequenceNumber sequenceNumber = SequenceNumber.ofByte((byte) 15);

    DeviceInfoWriter deviceInfoWriter = Mockito.mock(DeviceInfoWriter.class);
    DeviceInfoWriterFactory deviceInfoWriterFactory = Mockito.mock(DeviceInfoWriterFactory.class);

    Mockito.when(deviceInfoWriterFactory.createReadCommitted()).thenReturn(deviceInfoWriter);
    Mockito.when(deviceInfoWriter.findDeviceInfoByVpi(TestUtils.VPI)).thenReturn(Optional.of(PERSISTED_DEVICE_INFO));
    Mockito.when(deviceInfoWriter.findDeviceSequenceByDeviceInfoId(TestUtils.DEVICE_INFO_ID))
        .thenReturn(Optional.of(TestUtils.createPersistedDeviceSequence(sequenceNumber)));

    verifyMessage(message, Optional.empty());

    VceSequenceNumberService vceSequenceNumberService = new VceSequenceNumberService(deviceInfoWriterFactory);
    vceSequenceNumberService.setSequenceNumber(message);

    verifyMessage(message, Optional.of(sequenceNumber));

    InOrder inOrder = Mockito.inOrder(deviceInfoWriter, deviceInfoWriterFactory);
    inOrder.verify(deviceInfoWriterFactory).createReadCommitted();
    inOrder.verify(deviceInfoWriter).findDeviceInfoByVpi(TestUtils.VPI);
    inOrder.verify(deviceInfoWriter).findDeviceSequenceByDeviceInfoId(TestUtils.DEVICE_INFO_ID);
    inOrder.verify(deviceInfoWriter).updateDeviceSequence(TestUtils.createDeviceSequence(sequenceNumber.next()));
    inOrder.verify(deviceInfoWriter).close();
    inOrder.verifyNoMoreInteractions();
  }

  @Test
  void findDeviceInfoWithVpiTest() {
    Message message = TestUtils.createMessage();

    SequenceNumber sequenceNumber = SequenceNumber.ofByte((byte) 15);

    DeviceInfoWriterFactory deviceInfoWriterFactory = Mockito.mock(DeviceInfoWriterFactory.class);
    DeviceInfoWriter deviceInfoWriter = Mockito.mock(DeviceInfoWriter.class);
    Mockito.when(deviceInfoWriterFactory.createReadCommitted()).thenReturn(deviceInfoWriter);
    Mockito.when(deviceInfoWriter.findDeviceInfoByVpi(TestUtils.VPI)).thenReturn(Optional.of(PERSISTED_DEVICE_INFO));
    Mockito.when(deviceInfoWriter.findDeviceSequenceByDeviceInfoId(TestUtils.DEVICE_INFO_ID))
        .thenReturn(Optional.of(TestUtils.createPersistedDeviceSequence(sequenceNumber)));
    VceSequenceNumberService vceSequenceNumberService = new VceSequenceNumberService(deviceInfoWriterFactory);
    vceSequenceNumberService.setSequenceNumber(message);

    InOrder inOrder = Mockito.inOrder(deviceInfoWriter, deviceInfoWriterFactory);

    inOrder.verify(deviceInfoWriterFactory).createReadCommitted();
    inOrder.verify(deviceInfoWriter).findDeviceInfoByVpi(TestUtils.VPI);
    inOrder.verify(deviceInfoWriter).findDeviceSequenceByDeviceInfoId(TestUtils.DEVICE_INFO_ID);
    inOrder.verify(deviceInfoWriter).updateDeviceSequence(TestUtils.createDeviceSequence(sequenceNumber.next()));
    inOrder.verify(deviceInfoWriter).close();
    inOrder.verifyNoMoreInteractions();
  }

  @Test
  void findDeviceInfoWithHandleTest() {
    Message message = TestUtils.createMessageWithHandle();

    SequenceNumber sequenceNumber = SequenceNumber.ofByte((byte) 15);

    DeviceInfoWriterFactory deviceInfoWriterFactory = Mockito.mock(DeviceInfoWriterFactory.class);
    DeviceInfoWriter deviceInfoWriter = Mockito.mock(DeviceInfoWriter.class);
    Mockito.when(deviceInfoWriterFactory.createReadCommitted()).thenReturn(deviceInfoWriter);
    Mockito.when(deviceInfoWriter.findDeviceInfoByHandle(TestUtils.HANDLE)).thenReturn(Optional.of(PERSISTED_DEVICE_INFO));
    Mockito.when(deviceInfoWriter.findDeviceSequenceByDeviceInfoId(TestUtils.DEVICE_INFO_ID))
        .thenReturn(Optional.of(TestUtils.createPersistedDeviceSequence(sequenceNumber)));
    VceSequenceNumberService vceSequenceNumberService = new VceSequenceNumberService(deviceInfoWriterFactory);
    vceSequenceNumberService.setSequenceNumber(message);

    InOrder inOrder = Mockito.inOrder(deviceInfoWriter, deviceInfoWriterFactory);

    inOrder.verify(deviceInfoWriterFactory).createReadCommitted();
    inOrder.verify(deviceInfoWriter).findDeviceInfoByHandle(TestUtils.HANDLE);
    inOrder.verify(deviceInfoWriter).findDeviceSequenceByDeviceInfoId(TestUtils.DEVICE_INFO_ID);
    inOrder.verify(deviceInfoWriter).updateDeviceSequence(TestUtils.createDeviceSequence(sequenceNumber.next()));
    inOrder.verify(deviceInfoWriter).close();
    inOrder.verifyNoMoreInteractions();
  }
}
