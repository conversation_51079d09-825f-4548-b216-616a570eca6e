package com.wirelesscar.tcevce.module.split.ack;

import java.time.Clock;
import java.time.Instant;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import com.volvo.tisp.vc.test.utils.lib.AssertThrows;
import com.volvo.tisp.vc.test.utils.lib.UtilClassVerifier;
import com.wirelesscar.caretrack.dh.caretrack.protocol.ASNException;
import com.wirelesscar.caretrack.dh.caretrack.protocol.MessageContent;
import com.wirelesscar.caretrack.dh.caretrack.protocol.MessageType;
import com.wirelesscar.caretrack.dh.caretrack.protocol.PERStream;
import com.wirelesscar.caretrack.dh.caretrack.protocol.TransportHeader;
import com.wirelesscar.tce.module.api.Message;
import com.wirelesscar.tce.module.api.MessageStatus;
import com.wirelesscar.tce.module.api.MetaData;
import com.wirelesscar.tce.module.api.TransportType;
import com.wirelesscar.tcevce.wecu.device.info.database.model.SequenceNumber;

class MessageUtilsTest {
  private static Clock mockClock() {
    Clock clock = Mockito.mock(Clock.class);
    Mockito.when(clock.instant()).thenReturn(MessageUtils.TIME_OFFSET_2000_01_01);
    return clock;
  }

  private static void setSwappedAddressWhenUdpOrUnknownTransportType(String transportType) {
    String ipSrcAddressValue = "IP_SRC_ADDRESS";
    String ipDstAddressValue = "IP_DST_ADDRESS";
    String ipSrcPortValue = "IP_SRC_PORT";
    String ipDstPortValue = "IP_DST_PORT";

    Message message = new Message();
    message.setProperty(MetaData.TRANSPORT_TYPE, transportType);
    message.setProperty(MetaData.IP_SRC_ADDRESS, ipSrcAddressValue);
    message.setProperty(MetaData.IP_DST_ADDRESS, ipDstAddressValue);
    message.setProperty(MetaData.IP_SRC_PORT, ipSrcPortValue);
    message.setProperty(MetaData.IP_DST_PORT, ipDstPortValue);

    Message swappedAddressMessage = new Message();
    MessageUtils.setSwappedAddress(swappedAddressMessage, message);

    Assertions.assertEquals(ipDstAddressValue, swappedAddressMessage.getProperty(MetaData.IP_SRC_ADDRESS));
    Assertions.assertEquals(ipSrcAddressValue, swappedAddressMessage.getProperty(MetaData.IP_DST_ADDRESS));
    Assertions.assertEquals(ipDstPortValue, swappedAddressMessage.getProperty(MetaData.IP_SRC_PORT));
    Assertions.assertEquals(ipSrcPortValue, swappedAddressMessage.getProperty(MetaData.IP_DST_PORT));
  }

  private static void verifyBoundary(Instant instant) throws ASNException {
    Clock clock = Clock.fixed(instant, ZoneId.systemDefault());
    MessageContent messageContentWithWrongDate = MessageUtils.createUtcTimeResponseMessageContent(clock);
    PERStream perStream = new PERStream(100);

    Assertions.assertThrows(ASNException.class, () -> messageContentWithWrongDate.encode(perStream));
  }

  private static void verifyEmptyMoAckMessageContent(MessageContent emptyMoAckMessageContent) throws ASNException {
    Assertions.assertEquals(1, emptyMoAckMessageContent.getChoice());
    Assertions.assertEquals("<ack>Null</ack>\r\n", emptyMoAckMessageContent.getAck().toString());
  }

  private static void verifyMobileOriginatedTransportHeader(SequenceNumber expectedSequenceNumber, TransportHeader transportHeader) throws ASNException {
    Assertions.assertTrue(transportHeader.getMobileOriginated());
    verifyTransportHeader(expectedSequenceNumber, transportHeader);
  }

  private static void verifyNonMobileOriginatedTransportHeader(SequenceNumber expectedSequenceNumber, TransportHeader transportHeader) throws ASNException {
    Assertions.assertFalse(transportHeader.getMobileOriginated());
    verifyTransportHeader(expectedSequenceNumber, transportHeader);
  }

  private static void verifyTimeResponseMessageContent(MessageContent timeResponseMessageContent) throws ASNException {
    Assertions.assertEquals(1L, timeResponseMessageContent.getData().getSize());
    Assertions.assertEquals(0L, timeResponseMessageContent.getData().getArrayItem(0).getTimeResponse().getCurrentTime());
    Assertions.assertEquals(0L, timeResponseMessageContent.getData().getArrayItem(0).getTimeResponse().getRequestTime());
  }

  private static void verifyTransportHeader(SequenceNumber expectedSequenceNumber, TransportHeader transportHeader) throws ASNException {
    Assertions.assertEquals(expectedSequenceNumber.toByte(), transportHeader.getSequenceNumber());
    Assertions.assertFalse(transportHeader.getSkipAck());
    Assertions.assertFalse(transportHeader.getEncrypted());
    Assertions.assertEquals(0L, transportHeader.getMessageCount().getStandard().getIndexOfCurrentPacket());
    Assertions.assertEquals(1L, transportHeader.getMessageCount().getStandard().getTotalNumberOfPackets());
  }

  @Test
  void BoundaryMessageContentTest() throws ASNException {
    MessageContent messageContentWithRightDate = MessageUtils.createUtcTimeResponseMessageContent(Clock.systemUTC());
    SequenceNumber sequenceNumber = SequenceNumber.ofByte((byte) 15);
    Assertions.assertDoesNotThrow(() -> MessageUtils.createAckPayload(messageContentWithRightDate, true, sequenceNumber));
  }

  @Test
  void convertToCommTimeTest() {
    Assertions.assertEquals(0, MessageUtils.convertToCommTime(MessageUtils.TIME_OFFSET_2000_01_01).toSeconds());
  }

  @Test
  void createAckPayloadInvalidTest() {
    AssertThrows.illegalArgumentException(() -> MessageUtils.createAckPayload(null, true, null), "messageContent must not be null");
    AssertThrows.illegalArgumentException(() -> MessageUtils.createAckPayload(null, false, null), "messageContent must not be null");

    AssertThrows.illegalArgumentException(() -> MessageUtils.createAckPayload(new MessageContent(), false, null), "sequenceNumber must not be null");
  }

  @Test
  void createAckPayloadTest() throws ASNException {
    MessageContent ackMessageContent = TestUtils.createAckMessageContent();

    SequenceNumber expectedSequenceNumber = SequenceNumber.ofByte((byte) 15);

    byte[] nonMobileOriginatedAckPayload = MessageUtils.createAckPayload(ackMessageContent, false, expectedSequenceNumber);
    verifyNonMobileOriginatedTransportHeader(expectedSequenceNumber, TestUtils.createTransportHeader(nonMobileOriginatedAckPayload));

    byte[] mobileOriginatedAckPayload = MessageUtils.createAckPayload(ackMessageContent, true, expectedSequenceNumber);
    verifyMobileOriginatedTransportHeader(expectedSequenceNumber, TestUtils.createTransportHeader(mobileOriginatedAckPayload));
  }

  @Test
  void createEmptyMoAckMessageContentTest() throws ASNException {
    MessageContent emptyMoAckMessageContent = MessageUtils.createEmptyMoAckMessageContent();
    verifyEmptyMoAckMessageContent(emptyMoAckMessageContent);
  }

  @Test
  void createMoAckMessageContentInvalidTest() {
    AssertThrows.illegalArgumentException(() -> MessageUtils.createMoAckMessageContent(null, new MessageType()), "clock must not be null");
    AssertThrows.illegalArgumentException(() -> MessageUtils.createMoAckMessageContent(mockClock(), null), "messageType must not be null");
  }

  @Test
  void createMoAckMessageContentWhenETimeRequestTest() throws ASNException {
    MessageType messageType = new MessageType();
    messageType.setChoice(MessageType.E_TIMEREQUEST);
    messageType.setTimeRequest();

    MessageContent moAckMessageContent = MessageUtils.createMoAckMessageContent(mockClock(), messageType);
    verifyTimeResponseMessageContent(moAckMessageContent);
  }

  @Test
  void createMoAckMessageContentWhenEUtcTimeRequestTest() throws ASNException {
    MessageType messageType = new MessageType();
    messageType.setChoice(MessageType.E_UTCTIMEREQUEST);

    MessageContent moAckMessageContent = MessageUtils.createMoAckMessageContent(mockClock(), messageType);
    Assertions.assertEquals(0L, moAckMessageContent.getData().getArrayItem(0).getUtcTimeResponse().getCurrentTime());
  }

  @Test
  void createMoAckMessageContentWhenInvalidRequestTest() throws ASNException {
    MessageType messageType = new MessageType();
    messageType.setChoice(MessageType.E_FENCELIST);

    MessageContent moAckMessageContent = MessageUtils.createMoAckMessageContent(mockClock(), messageType);
    verifyEmptyMoAckMessageContent(moAckMessageContent);
  }

  @Test
  void createMtPayloadInvalidTest() {
    AssertThrows.illegalArgumentException(() -> MessageUtils.createMtPayload(null, TestUtils.SEQUENCE_NUMBER), "message must not be null");
    AssertThrows.illegalArgumentException(() -> MessageUtils.createMtPayload(new Message(), null), "sequenceNumber must not be null");
  }

  @Test
  void createMtPayloadTest() throws ASNException {
    Message message = new Message("1", new byte[1]);

    SequenceNumber expectedSequenceNumber = SequenceNumber.ofByte((byte) 15);

    byte[] mtPayload = MessageUtils.createMtPayload(message, expectedSequenceNumber);

    TransportHeader transportHeader = TestUtils.createTransportHeader(mtPayload);
    verifyNonMobileOriginatedTransportHeader(expectedSequenceNumber, transportHeader);
  }

  @Test
  void createSatelliteStatusMessageInvalidTest() {
    AssertThrows.illegalArgumentException(() -> VceMessageHandler.createSatelliteStatusMessage(null, new MessageContent()), "message must not be null");
    AssertThrows.illegalArgumentException(() -> VceMessageHandler.createSatelliteStatusMessage(Message.createMessage(), null),
        "messageContent must not be null");
  }

  @Test
  void createSatelliteStatusMessageTest() throws ASNException {
    Message message = Message.createMessage();
    message.setMessageId(TestUtils.MESSAGE_ID);
    message.setVehicleID(TestUtils.VPI.toString());
    message.setProperty(MetaData.HANDLE, TestUtils.HANDLE.toString());
    message.setProperty(MetaData.TRANSPORT_TYPE, TransportType.SATELLITE.toString());
    message.setProperty(MetaData.SATELLITE_SOURCE_ADDRESS, TestUtils.SATELLITE_ID_1.toString());

    Message satelliteStatusMessage = VceMessageHandler.createSatelliteStatusMessage(message, TestUtils.createDataMessageContent());

    Assertions.assertAll(
        () -> Assertions.assertEquals(MessageStatus.DELIVERED, satelliteStatusMessage.getStatus()),
        () -> Assertions.assertNull(satelliteStatusMessage.getMessageId()),
        () -> Assertions.assertEquals(TestUtils.VPI.toString(), satelliteStatusMessage.getVehicleID()),
        () -> Assertions.assertEquals(TestUtils.HANDLE.toString(), satelliteStatusMessage.getProperty(MetaData.HANDLE)),
        () -> Assertions.assertEquals(TestUtils.SATELLITE_ID_1.toString(), satelliteStatusMessage.getProperty(MetaData.SATELLITE_DEST_ADDRESS)),
        () -> Assertions.assertEquals(Boolean.TRUE.toString(), satelliteStatusMessage.getProperty(MetaData.ACK)),
        () -> Assertions.assertTrue(satelliteStatusMessage.getPayload().length > 0));
  }

  @Test
  void createTimeResponseMessageContentInvalidTest() {
    AssertThrows.illegalArgumentException(() -> MessageUtils.createTimeResponseMessageContent(null, new MessageType()), "clock must not be null");
    AssertThrows.illegalArgumentException(() -> MessageUtils.createTimeResponseMessageContent(mockClock(), null), "messageType must not be null");
  }

  @Test
  void createTimeResponseMessageContentTest() throws ASNException {
    MessageType messageType = new MessageType();
    messageType.setTimeRequest();

    MessageContent timeResponseMessageContent = MessageUtils.createTimeResponseMessageContent(mockClock(), messageType);
    verifyTimeResponseMessageContent(timeResponseMessageContent);
  }

  @Test
  void createTransportHeaderInvalidTest() {
    AssertThrows.illegalArgumentException(() -> MessageUtils.createTransportHeader(null, true), "sequenceNumber must not be null");
    AssertThrows.illegalArgumentException(() -> MessageUtils.createTransportHeader(null, false), "sequenceNumber must not be null");
  }

  @Test
  void createTransportHeaderTest() throws ASNException {
    TransportHeader transportHeader = MessageUtils.createTransportHeader(SequenceNumber.ofByte((byte) 15), true);

    Assertions.assertEquals(15, transportHeader.getSequenceNumber());
    Assertions.assertFalse(transportHeader.getSkipAck());
    Assertions.assertFalse(transportHeader.getEncrypted());
    Assertions.assertTrue(transportHeader.getMobileOriginated());
    Assertions.assertEquals(0L, transportHeader.getMessageCount().getStandard().getIndexOfCurrentPacket());
    Assertions.assertEquals(1L, transportHeader.getMessageCount().getStandard().getTotalNumberOfPackets());
  }

  @Test
  void createUtcTimeResponseMessageContentInvalidTest() {
    AssertThrows.illegalArgumentException(() -> MessageUtils.createUtcTimeResponseMessageContent(null), "clock must not be null");
  }

  @Test
  void createUtcTimeResponseMessageContentTest() throws ASNException {
    MessageContent utcTimeResponseMessageContent = MessageUtils.createUtcTimeResponseMessageContent(mockClock());
    Assertions.assertEquals(0L, utcTimeResponseMessageContent.getData().getArrayItem(0).getUtcTimeResponse().getCurrentTime());
  }

  @Test
  void getMessageContentInvalidTest() {
    AssertThrows.illegalArgumentException(() -> MessageUtils.getMessageContent(null), "payload must not be null");
  }

  @Test
  void getMessageContentTest() throws ASNException {
    MessageContent messageContent = MessageUtils.getMessageContent(new byte[1]);

    Assertions.assertEquals(0, messageContent.getData().getSize());
  }

  @Test
  void hasMetaDataInvalidTest() {
    AssertThrows.illegalArgumentException(() -> MessageUtils.hasMetaData(null, MetaData.VPI), "message must not be null");
    AssertThrows.illegalArgumentException(() -> MessageUtils.hasMetaData(new Message(), null), "metaData must not be null");
  }

  @Test
  void hasMetaDataTest() {
    Message message = new Message();

    Assertions.assertFalse(MessageUtils.hasMetaData(message, MetaData.VPI));

    message.setProperty(MetaData.VPI, TestUtils.VPI.toString());
    Assertions.assertTrue(MessageUtils.hasMetaData(message, MetaData.VPI));
  }

  @Test
  void highBoundaryMessageContentTest() throws ASNException {
    Instant instant = Instant.now().plus(45000, ChronoUnit.DAYS);
    verifyBoundary(instant);
  }

  @Test
  void isAckForMtInvalidTest() {
    AssertThrows.illegalArgumentException(() -> MessageUtils.isAckForMt(null, false), "messageContent must not be null");
  }

  @Test
  void isAckForMtTest() throws ASNException {
    MessageContent messageContent = new MessageContent();

    messageContent.setChoice(MessageContent.E_DATA);
    Assertions.assertFalse(MessageUtils.isAckForMt(messageContent, true));
    Assertions.assertTrue(MessageUtils.isAckForMt(messageContent, false));

    messageContent.setChoice(MessageContent.E_ACK);
    Assertions.assertTrue(MessageUtils.isAckForMt(messageContent, true));
    Assertions.assertTrue(MessageUtils.isAckForMt(messageContent, false));

    messageContent.setChoice(MessageContent.E_PING);
    Assertions.assertFalse(MessageUtils.isAckForMt(messageContent, true));
    Assertions.assertFalse(MessageUtils.isAckForMt(messageContent, false));

    messageContent.setChoice(MessageContent.E_PONG);
    Assertions.assertFalse(MessageUtils.isAckForMt(messageContent, true));
    Assertions.assertFalse(MessageUtils.isAckForMt(messageContent, false));
  }

  @Test
  void isMobileOriginatedInvalidTest() {
    AssertThrows.illegalArgumentException(() -> MessageUtils.setSwappedAddress(null, null), "swappedAddressMessage must not be null");
  }

  @Test
  void isMobileOriginatedTest() {
    Message message = new Message();

    message.setProperty(MetaData.MOBILE_ORIGINATED, Boolean.TRUE.toString());
    Assertions.assertTrue(MessageUtils.isMobileOriginated(message));

    message.setProperty(MetaData.MOBILE_ORIGINATED, Boolean.FALSE.toString());
    Assertions.assertFalse(MessageUtils.isMobileOriginated(message));

    message.setProperty(MetaData.MOBILE_ORIGINATED, "definitelyNotBoolean");
    Assertions.assertFalse(MessageUtils.isMobileOriginated(message));
  }

  @Test
  void isSatelliteMessageInvalidTest() {
    AssertThrows.illegalArgumentException(() -> MessageUtils.isSatelliteMessage(null), "message must not be null");
  }

  @Test
  void isSatelliteMessageTest() {
    Message message = Message.createMessage();
    message.setProperty(MetaData.TRANSPORT_TYPE, TransportType.SATELLITE.name());

    Assertions.assertTrue(MessageUtils.isSatelliteMessage(message));
  }

  @Test
  void lowBoundaryMessageContentTest() throws ASNException {
    Instant instant = Instant.parse("1999-12-31T00:00:00Z");
    verifyBoundary(instant);
  }

  @Test
  void setSwappedAddressInvalidTest() {
    AssertThrows.illegalArgumentException(() -> MessageUtils.setSwappedAddress(null, new Message()), "swappedAddressMessage must not be null");
    AssertThrows.illegalArgumentException(() -> MessageUtils.setSwappedAddress(new Message(), null), "message must not be null");
  }

  @Test
  void setSwappedAddressWhenSatelliteTransportTypeTest() {
    String satelliteDestAddressValue = "SATELLITE_DEST_ADDRESS";
    String satelliteSourceAddressValue = "SATELLITE_SOURCE_ADDRESS";

    Message message = new Message();
    message.setProperty(MetaData.TRANSPORT_TYPE, TransportType.SATELLITE.name());
    message.setProperty(MetaData.SATELLITE_DEST_ADDRESS, satelliteDestAddressValue);
    message.setProperty(MetaData.SATELLITE_SOURCE_ADDRESS, satelliteSourceAddressValue);

    Message swappedAddressMessage = new Message();
    MessageUtils.setSwappedAddress(swappedAddressMessage, message);

    Assertions.assertEquals(satelliteSourceAddressValue, swappedAddressMessage.getProperty(MetaData.SATELLITE_DEST_ADDRESS));
    Assertions.assertEquals(satelliteDestAddressValue, swappedAddressMessage.getProperty(MetaData.SATELLITE_SOURCE_ADDRESS));
  }

  @Test
  void setSwappedAddressWhenSmsTransportTypeTest() {
    String smppDestAddressValue = "SMPP_DEST_ADDRESS";
    String smppSourceAddressValue = "SMPP_SOURCE_ADDRESS";

    Message message = new Message();
    message.setProperty(MetaData.TRANSPORT_TYPE, TransportType.SMS.name());
    message.setProperty(MetaData.SMPP_DEST_ADDRESS, smppDestAddressValue);
    message.setProperty(MetaData.SMPP_SOURCE_ADDRESS, smppSourceAddressValue);

    Message swappedAddressMessage = new Message();
    MessageUtils.setSwappedAddress(swappedAddressMessage, message);

    Assertions.assertEquals(smppSourceAddressValue, swappedAddressMessage.getProperty(MetaData.SMPP_DEST_ADDRESS));
    Assertions.assertEquals(smppDestAddressValue, swappedAddressMessage.getProperty(MetaData.SMPP_SOURCE_ADDRESS));
  }

  @Test
  void setSwappedAddressWhenUdpTransportTypeTest() {
    setSwappedAddressWhenUdpOrUnknownTransportType(TransportType.UDP.name());
  }

  @Test
  void setSwappedAddressWhenUnknownTransportTypeTest() {
    setSwappedAddressWhenUdpOrUnknownTransportType("someTransportType");
  }

  @Test
  void shallAckInvalidTest() {
    AssertThrows.illegalArgumentException(() -> MessageUtils.shallAck(null), "message must not be null");
  }

  @Test
  void shallAckTest() {
    Message messageWithSkipAck = new Message();
    messageWithSkipAck.setProperty(MetaData.SKIP_ACK, Boolean.TRUE.toString());
    Assertions.assertFalse(MessageUtils.shallAck(messageWithSkipAck));

    Message messageWithoutSkipAck = new Message();
    messageWithoutSkipAck.setProperty(MetaData.SKIP_ACK, Boolean.FALSE.toString());
    Assertions.assertTrue(MessageUtils.shallAck(messageWithoutSkipAck));
  }

  @Test
  void shallAnswerOnPingInvalidTest() {
    AssertThrows.illegalArgumentException(() -> MessageUtils.shallAnswerOnPing(null), "messageContent must not be null");
  }

  @Test
  void shallAnswerOnPingTest() throws ASNException {
    MessageContent messageContent = new MessageContent();
    messageContent.setPing("Log: ");
    Assertions.assertFalse(MessageUtils.shallAnswerOnPing(messageContent));

    messageContent.setPing("Log: ".repeat(25));
    Assertions.assertTrue(MessageUtils.shallAnswerOnPing(messageContent));
  }

  @Test
  void verifyIsUtilClassTest() throws ReflectiveOperationException {
    UtilClassVerifier.verifyUtilClass(MessageUtils.class);
  }
}
