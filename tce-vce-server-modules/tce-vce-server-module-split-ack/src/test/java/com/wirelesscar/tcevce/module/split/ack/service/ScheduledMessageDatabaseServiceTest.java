package com.wirelesscar.tcevce.module.split.ack.service;

import java.sql.SQLException;
import java.util.Collections;
import java.util.List;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.InOrder;
import org.mockito.Mockito;

import com.volvo.tisp.vc.test.utils.lib.AssertThrows;
import com.wirelesscar.tce.module.api.Message;
import com.wirelesscar.tce.module.api.exception.EngineRuntimeException;
import com.wirelesscar.tcevce.module.scheduler.db.impl.ScheduledMessagePersisterImpl;

class ScheduledMessageDatabaseServiceTest {

  private static final String MESSAGE_ID = "1";

  private static String getFindSqlQuery() {
    return "SELECT * FROM SCHD_SCHEDULED_MESSAGE WHERE vehicleid = '1' AND status = 'a' AND properties LIKE '%SEQUENCE_NUMBER%'";
  }

  @Test
  void createInvalidTest() {
    AssertThrows.illegalArgumentException(() -> new ScheduledMessageDatabaseService(null), "scheduledMessagePersisterImpl must not be null");
  }

  @Test
  void findActiveMessagesWithSequenceNumberByVehicleIdWhenMessageFoundTest() throws SQLException {
    Message messageExpected = Message.createMessage();

    ScheduledMessagePersisterImpl scheduledMessagePersisterImpl = Mockito.mock(ScheduledMessagePersisterImpl.class);

    String findSqlQuery = getFindSqlQuery();
    Mockito.when(scheduledMessagePersisterImpl.find(findSqlQuery, Collections.emptyList(), Collections.emptySet(), Collections.emptySet()))
        .thenReturn(List.of(messageExpected));

    ScheduledMessageDatabaseService scheduledMessageDatabaseService = new ScheduledMessageDatabaseService(scheduledMessagePersisterImpl);
    Assertions.assertEquals(List.of(messageExpected), scheduledMessageDatabaseService.findActiveMessagesWithSequenceNumberByVehicleId(MESSAGE_ID));

    InOrder inOrder = Mockito.inOrder(scheduledMessagePersisterImpl);
    inOrder.verify(scheduledMessagePersisterImpl).find(findSqlQuery, Collections.emptyList(), Collections.emptySet(), Collections.emptySet());
    inOrder.verifyNoMoreInteractions();
  }

  @Test
  void findActiveMessagesWithSequenceNumberByVehicleIdWhenMessageNotFoundTest() throws SQLException {
    ScheduledMessagePersisterImpl scheduledMessagePersisterImpl = Mockito.mock(ScheduledMessagePersisterImpl.class);

    String findSqlQuery = getFindSqlQuery();
    Mockito.when(scheduledMessagePersisterImpl.find(findSqlQuery, Collections.emptyList(), Collections.emptySet(), Collections.emptySet()))
        .thenReturn(List.of());

    ScheduledMessageDatabaseService scheduledMessageDatabaseService = new ScheduledMessageDatabaseService(scheduledMessagePersisterImpl);
    Assertions.assertTrue(scheduledMessageDatabaseService.findActiveMessagesWithSequenceNumberByVehicleId(MESSAGE_ID).isEmpty());

    InOrder inOrder = Mockito.inOrder(scheduledMessagePersisterImpl);
    inOrder.verify(scheduledMessagePersisterImpl).find(findSqlQuery, Collections.emptyList(), Collections.emptySet(), Collections.emptySet());
    inOrder.verifyNoMoreInteractions();
  }

  @Test
  void findActiveMessagesWithSequenceNumberByVehicleIdWhenSqlExceptionWasThrownTest() throws SQLException {
    ScheduledMessagePersisterImpl scheduledMessagePersisterImpl = Mockito.mock(ScheduledMessagePersisterImpl.class);

    String findSqlQuery = getFindSqlQuery();
    Mockito.when(scheduledMessagePersisterImpl.find(findSqlQuery, Collections.emptyList(), Collections.emptySet(), Collections.emptySet()))
        .thenThrow(SQLException.class);

    ScheduledMessageDatabaseService scheduledMessageDatabaseService = new ScheduledMessageDatabaseService(scheduledMessagePersisterImpl);
    AssertThrows.exception(
        () -> scheduledMessageDatabaseService.findActiveMessagesWithSequenceNumberByVehicleId(MESSAGE_ID),
        "Unable to find activeMessages with sequenceNumber by vehicleId: " + MESSAGE_ID, EngineRuntimeException.class);

    InOrder inOrder = Mockito.inOrder(scheduledMessagePersisterImpl);
    inOrder.verify(scheduledMessagePersisterImpl).find(findSqlQuery, Collections.emptyList(), Collections.emptySet(), Collections.emptySet());
    inOrder.verifyNoMoreInteractions();
  }

  @Test
  void findMessageByMessageIdNotInvalidTest() {
    ScheduledMessagePersisterImpl scheduledMessagePersisterImpl = Mockito.mock(ScheduledMessagePersisterImpl.class);
    ScheduledMessageDatabaseService scheduledMessageDatabaseService = new ScheduledMessageDatabaseService(scheduledMessagePersisterImpl);

    AssertThrows.illegalArgumentException(() -> scheduledMessageDatabaseService.findActiveMessagesWithSequenceNumberByVehicleId(null),
        "vehicleId must not be null");
    AssertThrows.illegalArgumentException(() -> scheduledMessageDatabaseService.findActiveMessagesWithSequenceNumberByVehicleId(""),
        "vehicleId must not be empty");

    InOrder inOrder = Mockito.inOrder(scheduledMessagePersisterImpl);
    inOrder.verifyNoMoreInteractions();
  }
}
