package com.wirelesscar.tcevce.module.split.ack;

import java.time.Instant;
import java.util.Optional;

import com.volvo.tisp.vc.common.dto.lib.vehicle.Imsi;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Ipv4Address;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Msisdn;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.wirelesscar.caretrack.dh.caretrack.protocol.ASNException;
import com.wirelesscar.caretrack.dh.caretrack.protocol.MessageContent;
import com.wirelesscar.caretrack.dh.caretrack.protocol.MessageType;
import com.wirelesscar.caretrack.dh.caretrack.protocol.PERStream;
import com.wirelesscar.caretrack.dh.caretrack.protocol.TransportHeader;
import com.wirelesscar.tce.module.api.Message;
import com.wirelesscar.tce.module.api.MessageStatus;
import com.wirelesscar.tce.module.api.MetaData;
import com.wirelesscar.tcevce.wecu.device.info.database.model.DeviceInfo;
import com.wirelesscar.tcevce.wecu.device.info.database.model.DeviceInfoBuilder;
import com.wirelesscar.tcevce.wecu.device.info.database.model.DeviceInfoId;
import com.wirelesscar.tcevce.wecu.device.info.database.model.DeviceSequence;
import com.wirelesscar.tcevce.wecu.device.info.database.model.DeviceSequenceBuilder;
import com.wirelesscar.tcevce.wecu.device.info.database.model.DeviceSequenceId;
import com.wirelesscar.tcevce.wecu.device.info.database.model.Handle;
import com.wirelesscar.tcevce.wecu.device.info.database.model.Ipv4Port;
import com.wirelesscar.tcevce.wecu.device.info.database.model.MobileNetworkOperator;
import com.wirelesscar.tcevce.wecu.device.info.database.model.PersistedDeviceInfo;
import com.wirelesscar.tcevce.wecu.device.info.database.model.PersistedDeviceInfoBuilder;
import com.wirelesscar.tcevce.wecu.device.info.database.model.PersistedDeviceSequence;
import com.wirelesscar.tcevce.wecu.device.info.database.model.PersistedDeviceSequenceBuilder;
import com.wirelesscar.tcevce.wecu.device.info.database.model.SatelliteId;
import com.wirelesscar.tcevce.wecu.device.info.database.model.SequenceNumber;
import com.wirelesscar.tcevce.wecu.device.info.database.model.SimInfo;
import com.wirelesscar.tcevce.wecu.device.info.database.model.SimInfoBuilder;

public final class TestUtils {
  public static final DeviceInfoId DEVICE_INFO_ID = DeviceInfoId.ofLong(1);
  public static final Handle HANDLE = Handle.ofString("123456");
  public static final String MESSAGE_ID = "1".repeat(32);
  public static final Msisdn MSISDN = Msisdn.ofString("+469123456789");
  public static final String PING_TEXT = "PING";
  public static final SatelliteId SATELLITE_ID_1 = SatelliteId.ofString("HQ1234567890x1");
  public static final String SATELLITE_MESSAGE_ID = "1244626794";
  public static final SequenceNumber SEQUENCE_NUMBER = SequenceNumber.ofByte((byte) 0);
  public static final Vpi VPI = Vpi.ofString("1234567890ABCDEF1234567890ABCDEF");
  private static final Instant CREATED = Instant.ofEpochSecond(1);
  private static final DeviceSequenceId DEVICE_SEQUENCE_ID = DeviceSequenceId.ofLong(1L);
  private static final String DST_ADDR = "*******";
  private static final String DST_PORT = "1235";
  private static final Ipv4Address IPV4_ADDRESS = Ipv4Address.ofString("*******");
  private static final Ipv4Port IPV4_PORT = Ipv4Port.ofInt(9_062);
  private static final Instant LAST_UPDATED = Instant.ofEpochSecond(2);
  private static final MobileNetworkOperator MOBILE_NETWORK_OPERATOR = MobileNetworkOperator.ofString("testOperator");
  private static final String PONG_TEXT = "PONG";
  private static final SatelliteId SATELLITE_ID = SatelliteId.ofString("HQ1234567890x1");
  private static final String SRC_ADDR = "*******";
  private static final String SRC_PORT = "1234";

  public TestUtils() {
    throw new IllegalStateException();
  }

  public static MessageContent createAckMessageContent() throws ASNException {
    MessageContent messageContent = new MessageContent();
    messageContent.setAck();

    return messageContent;
  }

  public static MessageContent createDataMessageContent() throws ASNException {
    MessageContent messageContent = new MessageContent();

    messageContent.setData();
    messageContent.getData().setSize(1);
    messageContent.getData().getArrayItem(0).setInstantMachineDataRequest();

    return messageContent;
  }

  public static MessageContent createPublicKeyDataMessageContent() throws ASNException {
    MessageContent messageContent = new MessageContent();

    messageContent.setData();
    messageContent.getData().setSize(1);
    MessageType messageType = messageContent.getData().getArrayItem(0);
    messageType.setPublicKey1();
    messageType.getPublicKey1().setPublicKey("1234567890123456789012345678901234567890123456789012345678901234");

    return messageContent;
  }

  public static DeviceInfo createDeviceInfo() {
    return createDeviceInfoBuilder().build();
  }

  public static DeviceInfoBuilder createDeviceInfoBuilder() {
    return new DeviceInfoBuilder()
        .setHandle(HANDLE)
        .setSatelliteId(Optional.of(SATELLITE_ID))
        .setSimInfo(Optional.of(createSimInfo()))
        .setVpi(Optional.of(VPI));
  }

  public static DeviceSequence createDeviceSequence(SequenceNumber sequenceNumber) {
    return createDeviceSequenceBuilder()
        .setSequenceNumber(sequenceNumber)
        .build();
  }

  public static DeviceSequenceBuilder createDeviceSequenceBuilder() {
    return new DeviceSequenceBuilder()
        .setDeviceInfoId(DEVICE_INFO_ID)
        .setSequenceNumber(SEQUENCE_NUMBER);
  }

  public static Message createMessage() {
    Message message = new Message("1234567", new byte[42]);
    message.setVehicleID(VPI.toString());
    return message;
  }

  public static Message createMessageWithHandle() {
    Message message = new Message("1234567", new byte[42]);
    message.setVehicleID(HANDLE.toString());
    return message;
  }

  public static PersistedDeviceInfo createPersistedDeviceInfo() {
    return new PersistedDeviceInfoBuilder()
        .setCreated(CREATED)
        .setDeviceInfo(createDeviceInfo())
        .setDeviceInfoId(DEVICE_INFO_ID)
        .setLastUpdated(LAST_UPDATED)
        .build();
  }

  public static PersistedDeviceSequence createPersistedDeviceSequence(SequenceNumber sequenceNumber) {
    return new PersistedDeviceSequenceBuilder()
        .setCreated(CREATED)
        .setLastUpdated(LAST_UPDATED)
        .setDeviceSequenceId(DEVICE_SEQUENCE_ID)
        .setDeviceSequence(createDeviceSequence(sequenceNumber))
        .build();
  }

  public static MessageContent createPingMessageContent() throws ASNException {
    return createPingMessageContent(PING_TEXT);
  }

  public static MessageContent createPingMessageContent(String pingText) throws ASNException {
    MessageContent messageContent = new MessageContent();

    messageContent.setPing(pingText);

    return messageContent;
  }

  public static MessageContent createPongMessageContent() throws ASNException {
    MessageContent messageContent = new MessageContent();

    messageContent.setPong(PONG_TEXT);

    return messageContent;
  }

  public static SimInfo createSimInfo() {
    return createSimInfoBuilder().build();
  }

  public static SimInfoBuilder createSimInfoBuilder() {
    return new SimInfoBuilder()
        .setIpv4Address(IPV4_ADDRESS)
        .setIpv4Port(IPV4_PORT)
        .setImsi(Imsi.ofLong(1L))
        .setMobileNetworkOperator(MOBILE_NETWORK_OPERATOR)
        .setMsisdn(MSISDN);
  }

  public static Message createStatusMessage(Message message) {
    Message statusMessage = Message.createStatusMessage(MessageStatus.DELIVERED);
    statusMessage.setMessageId(message.getMessageId());
    statusMessage.addProperties(message.getProperties());
    statusMessage.setVehicleID(message.getVehicleID());

    return statusMessage;
  }

  public static TransportHeader createTransportHeader(byte[] payload) throws ASNException {
    TransportHeader transportHeader = new TransportHeader();

    PERStream perStream = new PERStream(payload);
    transportHeader.decode(perStream);
    perStream.alignOnByte();

    return transportHeader;
  }

  public static Message createVceMessage(MessageContent messageContent) throws ASNException {
    PERStream perStream = new PERStream((int) (messageContent.encodedSize()));
    messageContent.encode(perStream);
    perStream.alignOnByte();

    Message message = new Message();
    message.setMessageId(MESSAGE_ID);
    message.setPayload(perStream.getBuffer());

    return message;
  }

  public static Message createVceMessageWithIpData(MessageContent messageContent) throws ASNException {
    Message message = createVceMessage(messageContent);

    message.setProperty(MetaData.IP_SRC_ADDRESS, SRC_ADDR);
    message.setProperty(MetaData.IP_SRC_PORT, SRC_PORT);
    message.setProperty(MetaData.IP_DST_ADDRESS, DST_ADDR);
    message.setProperty(MetaData.IP_DST_PORT, DST_PORT);

    return message;
  }
}
