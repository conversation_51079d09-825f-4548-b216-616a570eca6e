package com.wirelesscar.tcevce.module.split.ack;

import java.time.Clock;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.InOrder;
import org.mockito.Mockito;

import com.volvo.tisp.vc.common.dto.lib.vehicle.Msisdn;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;
import com.wirelesscar.caretrack.dh.caretrack.protocol.ASNException;
import com.wirelesscar.caretrack.dh.caretrack.protocol.MessageContent;
import com.wirelesscar.caretrack.dh.caretrack.protocol.PERStream;
import com.wirelesscar.caretrack.dh.caretrack.protocol.TransportHeader;
import com.wirelesscar.tce.db.common.db.api.MessageFields;
import com.wirelesscar.tce.module.api.Message;
import com.wirelesscar.tce.module.api.MessageStatus;
import com.wirelesscar.tce.module.api.MetaData;
import com.wirelesscar.tce.module.api.SmsDataCoding;
import com.wirelesscar.tce.module.api.TransportType;
import com.wirelesscar.tcevce.module.split.ack.metrics.VceSplitAckModuleMetricReporter;
import com.wirelesscar.tcevce.module.split.ack.service.ScheduledMessageDatabaseService;
import com.wirelesscar.tcevce.module.split.ack.service.VceSequenceNumberService;

class VceMessageHandlerTest {

  private static final String STACK_NAME = "stackName";

  private static void createMoResponseWhenNotHandleEPing(Message message, String pingText) throws ASNException {
    Clock clock = Mockito.mock(Clock.class);
    ScheduledMessageDatabaseService scheduledMessageDatabaseService = Mockito.mock(ScheduledMessageDatabaseService.class);
    VceSequenceNumberService vceSequenceNumberService = Mockito.mock(VceSequenceNumberService.class);
    VceSplitAckModuleMetricReporter vceSplitAckModuleMetricReporter = Mockito.mock(VceSplitAckModuleMetricReporter.class);

    VceMessageHandler vceMessageHandler = new VceMessageHandler(clock, scheduledMessageDatabaseService, vceSequenceNumberService,
        vceSplitAckModuleMetricReporter);

    Message moResponse = vceMessageHandler.createMoResponse(message, TestUtils.createPingMessageContent(pingText), STACK_NAME);

    Assertions.assertNull(moResponse);

    InOrder inOrder = Mockito.inOrder(clock, scheduledMessageDatabaseService, vceSequenceNumberService, vceSplitAckModuleMetricReporter);
    inOrder.verify(vceSplitAckModuleMetricReporter).onHandleMoOrAck(STACK_NAME);
    inOrder.verify(vceSplitAckModuleMetricReporter).onMoPingHandled(STACK_NAME);
    inOrder.verifyNoMoreInteractions();
  }

  private static void verifyMoAckMessage(Message message, Message handledMoAckMessage) {
    Assertions.assertEquals(message.getProperty(MetaData.IP_SRC_ADDRESS), handledMoAckMessage.getProperty(MetaData.IP_SRC_ADDRESS));
    Assertions.assertEquals(message.getProperty(MetaData.IP_SRC_PORT), handledMoAckMessage.getProperty(MetaData.IP_SRC_PORT));
    Assertions.assertEquals(message.getProperty(MetaData.IP_DST_ADDRESS), handledMoAckMessage.getProperty(MetaData.IP_DST_ADDRESS));
    Assertions.assertEquals(message.getProperty(MetaData.IP_DST_PORT), handledMoAckMessage.getProperty(MetaData.IP_DST_PORT));

    Assertions.assertEquals(message.getMessageId(), handledMoAckMessage.getMessageId());
    Assertions.assertEquals(message.getVehicleID(), handledMoAckMessage.getVehicleID());
    Assertions.assertEquals(message.getProperty(MetaData.SEQUENCE_NUMBER), handledMoAckMessage.getProperty(MetaData.SEQUENCE_NUMBER));
    Assertions.assertEquals(message.getProperty(MetaData.MESSAGE_ID), handledMoAckMessage.getProperty(MetaData.MESSAGE_ID));
    Assertions.assertEquals(message.getProperty(MessageFields.messageID.name()), handledMoAckMessage.getProperty(MessageFields.messageID.name()));

    Assertions.assertNull(handledMoAckMessage.getProperty(MetaData.STACK_ROUTING));
  }

  private static void verifyMobileOriginatedTransportHeader(int expectedSequenceNumber, TransportHeader transportHeader) throws ASNException {
    Assertions.assertTrue(transportHeader.getMobileOriginated());
    verifyTransportHeader(expectedSequenceNumber, transportHeader);
  }

  private static void verifyNonMobileOriginatedTransportHeader(int expectedSequenceNumber, TransportHeader transportHeader) throws ASNException {
    Assertions.assertFalse(transportHeader.getMobileOriginated());
    verifyTransportHeader(expectedSequenceNumber, transportHeader);
  }

  private static void verifyTransportHeader(int expectedSequenceNumber, TransportHeader transportHeader) throws ASNException {
    Assertions.assertEquals(expectedSequenceNumber, transportHeader.getSequenceNumber());
    Assertions.assertFalse(transportHeader.getSkipAck());
    Assertions.assertFalse(transportHeader.getEncrypted());
    Assertions.assertEquals(0L, transportHeader.getMessageCount().getStandard().getIndexOfCurrentPacket());
    Assertions.assertEquals(1L, transportHeader.getMessageCount().getStandard().getTotalNumberOfPackets());
  }

  @Test
  void createMoResponseInvalidTest() {
    Clock clock = Mockito.mock(Clock.class);
    ScheduledMessageDatabaseService scheduledMessageDatabaseService = Mockito.mock(ScheduledMessageDatabaseService.class);
    VceSequenceNumberService vceSequenceNumberService = Mockito.mock(VceSequenceNumberService.class);
    VceSplitAckModuleMetricReporter vceSplitAckModuleMetricReporter = Mockito.mock(VceSplitAckModuleMetricReporter.class);

    Message message = new Message();
    MessageContent messageContent = new MessageContent();

    VceMessageHandler vceMessageHandler = new VceMessageHandler(clock, scheduledMessageDatabaseService, vceSequenceNumberService,
        vceSplitAckModuleMetricReporter);

    AssertThrows.illegalArgumentException(() -> vceMessageHandler.createMoResponse(null, messageContent, STACK_NAME), "message must not be null");
    AssertThrows.illegalArgumentException(() -> vceMessageHandler.createMoResponse(message, null, STACK_NAME), "messageContent must not be null");
    AssertThrows.illegalArgumentException(() -> vceMessageHandler.createMoResponse(message, messageContent, null),
        "stackName must not be null");
    AssertThrows.illegalArgumentException(() -> vceMessageHandler.createMoResponse(message, messageContent, ""),
        "stackName must not be empty");

    Mockito.verifyNoInteractions(clock, scheduledMessageDatabaseService, vceSequenceNumberService, vceSplitAckModuleMetricReporter);
  }

  @Test
  void createMoResponseWhenEPongTest() {
    Clock clock = Mockito.mock(Clock.class);
    ScheduledMessageDatabaseService scheduledMessageDatabaseService = Mockito.mock(ScheduledMessageDatabaseService.class);
    VceSequenceNumberService vceSequenceNumberService = Mockito.mock(VceSequenceNumberService.class);
    VceSplitAckModuleMetricReporter vceSplitAckModuleMetricReporter = Mockito.mock(VceSplitAckModuleMetricReporter.class);

    VceMessageHandler vceMessageHandler = new VceMessageHandler(clock, scheduledMessageDatabaseService, vceSequenceNumberService,
        vceSplitAckModuleMetricReporter);

    Message message = TestUtils.createMessage();

    AssertThrows.illegalStateException(
        () -> vceMessageHandler.createMoResponse(message, TestUtils.createPongMessageContent(), STACK_NAME),
        "Received a E_PONG message as a MO-message: " + message
    );

    InOrder inOrder = Mockito.inOrder(clock, scheduledMessageDatabaseService, vceSequenceNumberService, vceSplitAckModuleMetricReporter);
    inOrder.verify(vceSplitAckModuleMetricReporter).onHandleMoOrAck(STACK_NAME);
    inOrder.verify(vceSplitAckModuleMetricReporter).onMoPongReceived(STACK_NAME);
    inOrder.verifyNoMoreInteractions();
  }

  @Test
  void createMoResponseWhenHandleEAckTest() throws ASNException {
    Clock clock = Mockito.mock(Clock.class);
    ScheduledMessageDatabaseService scheduledMessageDatabaseService = Mockito.mock(ScheduledMessageDatabaseService.class);
    VceSequenceNumberService vceSequenceNumberService = Mockito.mock(VceSequenceNumberService.class);
    VceSplitAckModuleMetricReporter vceSplitAckModuleMetricReporter = Mockito.mock(VceSplitAckModuleMetricReporter.class);

    VceMessageHandler vceMessageHandler = new VceMessageHandler(clock, scheduledMessageDatabaseService, vceSequenceNumberService,
        vceSplitAckModuleMetricReporter);

    Message message = TestUtils.createVceMessageWithIpData(TestUtils.createAckMessageContent());
    message.setVehicleID(TestUtils.VPI.toString());
    message.setProperty(MetaData.SEQUENCE_NUMBER, "0");

    Mockito.when(scheduledMessageDatabaseService.findActiveMessagesWithSequenceNumberByVehicleId(message.getVehicleID())).thenReturn(List.of(message));

    Message handledMoAckMessage = vceMessageHandler.createMoResponse(message, TestUtils.createAckMessageContent(), STACK_NAME);
    verifyMoAckMessage(message, handledMoAckMessage);

    InOrder inOrder = Mockito.inOrder(clock, scheduledMessageDatabaseService, vceSequenceNumberService, vceSplitAckModuleMetricReporter);
    inOrder.verify(vceSplitAckModuleMetricReporter).onHandleMoOrAck(STACK_NAME);
    inOrder.verify(vceSplitAckModuleMetricReporter).onMoHandleAck(STACK_NAME);
    inOrder.verify(vceSplitAckModuleMetricReporter).onHandleMoAck(STACK_NAME);
    inOrder.verify(scheduledMessageDatabaseService).findActiveMessagesWithSequenceNumberByVehicleId(message.getVehicleID());
    inOrder.verifyNoMoreInteractions();
  }

  @Test
  void createMoResponseWhenHandleEDataAndMobileOriginatedAndShallAckTest() throws ASNException {
    Clock clock = Mockito.mock(Clock.class);
    ScheduledMessageDatabaseService scheduledMessageDatabaseService = Mockito.mock(ScheduledMessageDatabaseService.class);
    VceSequenceNumberService vceSequenceNumberService = Mockito.mock(VceSequenceNumberService.class);
    VceSplitAckModuleMetricReporter vceSplitAckModuleMetricReporter = Mockito.mock(VceSplitAckModuleMetricReporter.class);

    VceMessageHandler vceMessageHandler = new VceMessageHandler(clock, scheduledMessageDatabaseService, vceSequenceNumberService,
        vceSplitAckModuleMetricReporter);

    Message message = TestUtils.createMessage();
    message.setProperty(MetaData.MOBILE_ORIGINATED, Boolean.TRUE.toString());
    message.setProperty(MetaData.TRANSPORT_TYPE, TransportType.SMS.name());
    message.setProperty(MetaData.SMPP_DEST_ADDRESS, TestUtils.MSISDN.toString());
    message.setProperty(MetaData.SMPP_SOURCE_ADDRESS, Msisdn.ofString("+469123456788").toString());

    Message moResponse = vceMessageHandler.createMoResponse(message, TestUtils.createDataMessageContent(), STACK_NAME);

    InOrder inOrder = Mockito.inOrder(clock, scheduledMessageDatabaseService, vceSequenceNumberService, vceSplitAckModuleMetricReporter);
    inOrder.verify(vceSplitAckModuleMetricReporter).onHandleMoOrAck(STACK_NAME);
    inOrder.verify(vceSplitAckModuleMetricReporter).onHandleMoData(STACK_NAME);
    inOrder.verifyNoMoreInteractions();

    Assertions.assertEquals(message.getMessageId(), moResponse.getMessageId());
    Assertions.assertEquals(message.getVehicleID(), moResponse.getVehicleID());
    Assertions.assertTrue(Boolean.parseBoolean(moResponse.getProperty(MetaData.ACK)));
    Assertions.assertEquals(SmsDataCoding.BINARY.toString(), moResponse.getProperty(MetaData.SMS_DATA_CODING));
    Assertions.assertEquals(message.getProperty(MetaData.TRANSPORT_TYPE), moResponse.getProperty(MetaData.TRANSPORT_TYPE));

    TransportHeader transportHeader = TestUtils.createTransportHeader(moResponse.getPayload());
    verifyMobileOriginatedTransportHeader(0, transportHeader);
  }

  @Test
  void createMoResponseWhenHandleEDataAndMobileOriginatedAndShallNotAckAndNotSatelliteTransportTypeTest() throws ASNException {
    Clock clock = Mockito.mock(Clock.class);
    ScheduledMessageDatabaseService scheduledMessageDatabaseService = Mockito.mock(ScheduledMessageDatabaseService.class);
    VceSequenceNumberService vceSequenceNumberService = Mockito.mock(VceSequenceNumberService.class);
    VceSplitAckModuleMetricReporter vceSplitAckModuleMetricReporter = Mockito.mock(VceSplitAckModuleMetricReporter.class);

    VceMessageHandler vceMessageHandler = new VceMessageHandler(clock, scheduledMessageDatabaseService, vceSequenceNumberService,
        vceSplitAckModuleMetricReporter);

    Message message = TestUtils.createMessage();
    message.setProperty(MetaData.MOBILE_ORIGINATED, Boolean.TRUE.toString());
    message.setProperty(MetaData.TRANSPORT_TYPE, TransportType.SMS.name());
    message.setProperty(MetaData.SKIP_ACK, Boolean.TRUE.toString());
    message.setProperty(MetaData.SMPP_DEST_ADDRESS, TestUtils.MSISDN.toString());
    message.setProperty(MetaData.SMPP_SOURCE_ADDRESS, Msisdn.ofString("+469123456788").toString());

    Message moResponse = vceMessageHandler.createMoResponse(message, TestUtils.createDataMessageContent(), STACK_NAME);

    Assertions.assertNull(moResponse);

    InOrder inOrder = Mockito.inOrder(clock, scheduledMessageDatabaseService, vceSequenceNumberService, vceSplitAckModuleMetricReporter);
    inOrder.verify(vceSplitAckModuleMetricReporter).onHandleMoOrAck(STACK_NAME);
    inOrder.verify(vceSplitAckModuleMetricReporter).onHandleMoData(STACK_NAME);
    inOrder.verifyNoMoreInteractions();
  }

  @Test
  void createMoResponseWhenHandleEDataAndMobileOriginatedAndShallNotAckAndSatelliteTransportTypeTest() throws ASNException {
    Clock clock = Mockito.mock(Clock.class);
    ScheduledMessageDatabaseService scheduledMessageDatabaseService = Mockito.mock(ScheduledMessageDatabaseService.class);
    VceSequenceNumberService vceSequenceNumberService = Mockito.mock(VceSequenceNumberService.class);
    VceSplitAckModuleMetricReporter vceSplitAckModuleMetricReporter = Mockito.mock(VceSplitAckModuleMetricReporter.class);

    VceMessageHandler vceMessageHandler = new VceMessageHandler(clock, scheduledMessageDatabaseService, vceSequenceNumberService,
        vceSplitAckModuleMetricReporter);

    Message message = TestUtils.createMessage();
    message.setProperty(MetaData.MOBILE_ORIGINATED, Boolean.TRUE.toString());
    message.setProperty(MetaData.TRANSPORT_TYPE, TransportType.SATELLITE.name());
    message.setProperty(MetaData.SKIP_ACK, Boolean.TRUE.toString());
    message.setProperty(MetaData.SMPP_DEST_ADDRESS, TestUtils.MSISDN.toString());
    message.setProperty(MetaData.SMPP_SOURCE_ADDRESS, Msisdn.ofString("+469123456788").toString());

    Message moResponse = vceMessageHandler.createMoResponse(message, TestUtils.createDataMessageContent(), STACK_NAME);

    InOrder inOrder = Mockito.inOrder(clock, scheduledMessageDatabaseService, vceSequenceNumberService, vceSplitAckModuleMetricReporter);
    inOrder.verify(vceSplitAckModuleMetricReporter).onHandleMoOrAck(STACK_NAME);
    inOrder.verify(vceSplitAckModuleMetricReporter).onHandleMoData(STACK_NAME);
    inOrder.verifyNoMoreInteractions();

    Assertions.assertEquals(message.getMessageId(), moResponse.getMessageId());
    Assertions.assertEquals(message.getVehicleID(), moResponse.getVehicleID());
    Assertions.assertTrue(Boolean.parseBoolean(moResponse.getProperty(MetaData.ACK)));
    Assertions.assertEquals(message.getProperty(MetaData.TRANSPORT_TYPE), moResponse.getProperty(MetaData.TRANSPORT_TYPE));
    Assertions.assertEquals(message.getProperty(MetaData.MOBILE_ORIGINATED), moResponse.getProperty(MetaData.MOBILE_ORIGINATED));
    Assertions.assertEquals(MessageStatus.ACCEPTED, moResponse.getStatus());

    TransportHeader transportHeader = TestUtils.createTransportHeader(moResponse.getPayload());
    verifyMobileOriginatedTransportHeader(0, transportHeader);
  }

  @Test
  void createMoResponseWhenHandleEDataAndNotMobileOriginatedTest() throws ASNException {
    Clock clock = Mockito.mock(Clock.class);
    ScheduledMessageDatabaseService scheduledMessageDatabaseService = Mockito.mock(ScheduledMessageDatabaseService.class);
    VceSequenceNumberService vceSequenceNumberService = Mockito.mock(VceSequenceNumberService.class);
    VceSplitAckModuleMetricReporter vceSplitAckModuleMetricReporter = Mockito.mock(VceSplitAckModuleMetricReporter.class);

    VceMessageHandler vceMessageHandler = new VceMessageHandler(clock, scheduledMessageDatabaseService, vceSequenceNumberService,
        vceSplitAckModuleMetricReporter);

    Message message = TestUtils.createMessage();
    message.setProperty(MetaData.MOBILE_ORIGINATED, Boolean.FALSE.toString());

    Message moResponse = vceMessageHandler.createMoResponse(message, TestUtils.createDataMessageContent(), STACK_NAME);

    Assertions.assertNull(moResponse);

    InOrder inOrder = Mockito.inOrder(clock, scheduledMessageDatabaseService, vceSequenceNumberService, vceSplitAckModuleMetricReporter);
    inOrder.verify(vceSplitAckModuleMetricReporter).onHandleMoOrAck(STACK_NAME);
    inOrder.verify(vceSplitAckModuleMetricReporter).onHandleMoData(STACK_NAME);
    inOrder.verifyNoMoreInteractions();
  }

  @Test
  void createMoResponseWhenHandleEPingTest() throws ASNException {
    Clock clock = Mockito.mock(Clock.class);
    ScheduledMessageDatabaseService scheduledMessageDatabaseService = Mockito.mock(ScheduledMessageDatabaseService.class);
    VceSequenceNumberService vceSequenceNumberService = Mockito.mock(VceSequenceNumberService.class);
    VceSplitAckModuleMetricReporter vceSplitAckModuleMetricReporter = Mockito.mock(VceSplitAckModuleMetricReporter.class);

    VceMessageHandler vceMessageHandler = new VceMessageHandler(clock, scheduledMessageDatabaseService, vceSequenceNumberService,
        vceSplitAckModuleMetricReporter);

    Message message = TestUtils.createMessage();
    message.setProperty(MetaData.MOBILE_ORIGINATED, Boolean.TRUE.toString());
    message.setProperty(MetaData.TRANSPORT_TYPE, TransportType.SMS.name());
    message.setProperty(MetaData.SMPP_DEST_ADDRESS, TestUtils.MSISDN.toString());
    message.setProperty(MetaData.SMPP_SOURCE_ADDRESS, Msisdn.ofString("+469123456788").toString());

    Message moResponse = vceMessageHandler.createMoResponse(message, TestUtils.createPingMessageContent(), STACK_NAME);

    InOrder inOrder = Mockito.inOrder(clock, scheduledMessageDatabaseService, vceSequenceNumberService, vceSplitAckModuleMetricReporter);
    inOrder.verify(vceSplitAckModuleMetricReporter).onHandleMoOrAck(STACK_NAME);
    inOrder.verify(vceSplitAckModuleMetricReporter).onMoPingHandled(STACK_NAME);
    inOrder.verify(vceSplitAckModuleMetricReporter).onHandleMoPing(STACK_NAME);
    inOrder.verifyNoMoreInteractions();

    Assertions.assertEquals(message.getMessageId(), moResponse.getMessageId());
    Assertions.assertEquals(message.getVehicleID(), moResponse.getVehicleID());
    Assertions.assertTrue(Boolean.parseBoolean(moResponse.getProperty(MetaData.ACK)));
    Assertions.assertEquals(SmsDataCoding.BINARY.toString(), moResponse.getProperty(MetaData.SMS_DATA_CODING));
    Assertions.assertEquals(message.getProperty(MetaData.TRANSPORT_TYPE), moResponse.getProperty(MetaData.TRANSPORT_TYPE));

    Assertions.assertEquals(message.getProperty(MetaData.SMPP_DEST_ADDRESS), moResponse.getProperty(MetaData.SMPP_SOURCE_ADDRESS));
    Assertions.assertEquals(message.getProperty(MetaData.SMPP_SOURCE_ADDRESS), moResponse.getProperty(MetaData.SMPP_DEST_ADDRESS));

    TransportHeader transportHeader = TestUtils.createTransportHeader(moResponse.getPayload());
    verifyMobileOriginatedTransportHeader(0, transportHeader);
  }

  @Test
  void createMoResponseWhenNotHandleEPingTest() throws ASNException {
    Message message = TestUtils.createMessage();

    message.setProperty(MetaData.MOBILE_ORIGINATED, Boolean.FALSE.toString());
    createMoResponseWhenNotHandleEPing(message, "abcd".repeat(25));
    createMoResponseWhenNotHandleEPing(message, "Log: ");
    createMoResponseWhenNotHandleEPing(message, TestUtils.PING_TEXT);

    message.setProperty(MetaData.SKIP_ACK, Boolean.TRUE.toString());
    createMoResponseWhenNotHandleEPing(message, TestUtils.PING_TEXT);
  }

  @Test
  void createMoResponseWhenUnknownMessageContentTest() throws ASNException {
    Clock clock = Mockito.mock(Clock.class);
    ScheduledMessageDatabaseService scheduledMessageDatabaseService = Mockito.mock(ScheduledMessageDatabaseService.class);
    VceSequenceNumberService vceSequenceNumberService = Mockito.mock(VceSequenceNumberService.class);
    VceSplitAckModuleMetricReporter vceSplitAckModuleMetricReporter = Mockito.mock(VceSplitAckModuleMetricReporter.class);

    VceMessageHandler vceMessageHandler = new VceMessageHandler(clock, scheduledMessageDatabaseService, vceSequenceNumberService,
        vceSplitAckModuleMetricReporter);

    Message message = TestUtils.createMessage();

    Message moResponse = vceMessageHandler.createMoResponse(message, new MessageContent(), STACK_NAME);
    Assertions.assertNull(moResponse);

    InOrder inOrder = Mockito.inOrder(clock, scheduledMessageDatabaseService, vceSequenceNumberService, vceSplitAckModuleMetricReporter);
    inOrder.verify(vceSplitAckModuleMetricReporter).onHandleMoOrAck(STACK_NAME);
    inOrder.verify(vceSplitAckModuleMetricReporter).onMoUnknownChoice(STACK_NAME);
    inOrder.verifyNoMoreInteractions();
  }

  @Test
  void handleMoAckInvalidTest() {
    Clock clock = Mockito.mock(Clock.class);
    ScheduledMessageDatabaseService scheduledMessageDatabaseService = Mockito.mock(ScheduledMessageDatabaseService.class);
    VceSequenceNumberService vceSequenceNumberService = Mockito.mock(VceSequenceNumberService.class);
    VceSplitAckModuleMetricReporter vceSplitAckModuleMetricReporter = Mockito.mock(VceSplitAckModuleMetricReporter.class);

    VceMessageHandler vceMessageHandler = new VceMessageHandler(clock, scheduledMessageDatabaseService, vceSequenceNumberService,
        vceSplitAckModuleMetricReporter);

    AssertThrows.illegalArgumentException(() -> vceMessageHandler.handleMoAck(null, STACK_NAME), "message must not be null");
    AssertThrows.illegalArgumentException(() -> vceMessageHandler.handleMoAck(new Message(), null), "stackName must not be null");

    Mockito.verifyNoInteractions(clock, scheduledMessageDatabaseService, vceSequenceNumberService, vceSplitAckModuleMetricReporter);
  }

  @Test
  void handleMoAckWhenFoundMtMessageWaitingForAckAndSequenceNumbersNotEqualTest() throws ASNException {
    Clock clock = Mockito.mock(Clock.class);
    ScheduledMessageDatabaseService scheduledMessageDatabaseService = Mockito.mock(ScheduledMessageDatabaseService.class);
    VceSequenceNumberService vceSequenceNumberService = Mockito.mock(VceSequenceNumberService.class);
    VceSplitAckModuleMetricReporter vceSplitAckModuleMetricReporter = Mockito.mock(VceSplitAckModuleMetricReporter.class);

    VceMessageHandler vceMessageHandler = new VceMessageHandler(clock, scheduledMessageDatabaseService, vceSequenceNumberService,
        vceSplitAckModuleMetricReporter);

    Message vceMessage = TestUtils.createVceMessageWithIpData(TestUtils.createAckMessageContent());
    vceMessage.setVehicleID(TestUtils.VPI.toString());

    Message activeMtMessage = TestUtils.createMessage();
    activeMtMessage.setProperty(MetaData.SEQUENCE_NUMBER, "1");

    Mockito.when(scheduledMessageDatabaseService.findActiveMessagesWithSequenceNumberByVehicleId(vceMessage.getVehicleID()))
        .thenReturn(List.of(activeMtMessage));

    Assertions.assertNull(vceMessageHandler.handleMoAck(vceMessage, STACK_NAME));

    InOrder inOrder = Mockito.inOrder(clock, scheduledMessageDatabaseService, vceSequenceNumberService, vceSplitAckModuleMetricReporter);
    inOrder.verify(vceSplitAckModuleMetricReporter).onHandleMoAck(STACK_NAME);
    inOrder.verify(scheduledMessageDatabaseService).findActiveMessagesWithSequenceNumberByVehicleId(vceMessage.getVehicleID());
    inOrder.verifyNoMoreInteractions();
  }

  @Test
  void handleMoAckWhenFoundMultipleMtMessagesWaitingForAckTest() throws ASNException {
    Clock clock = Mockito.mock(Clock.class);
    ScheduledMessageDatabaseService scheduledMessageDatabaseService = Mockito.mock(ScheduledMessageDatabaseService.class);
    VceSequenceNumberService vceSequenceNumberService = Mockito.mock(VceSequenceNumberService.class);
    VceSplitAckModuleMetricReporter vceSplitAckModuleMetricReporter = Mockito.mock(VceSplitAckModuleMetricReporter.class);

    VceMessageHandler vceMessageHandler = new VceMessageHandler(clock, scheduledMessageDatabaseService, vceSequenceNumberService,
        vceSplitAckModuleMetricReporter);

    Message vceMessage = TestUtils.createVceMessageWithIpData(TestUtils.createAckMessageContent());
    vceMessage.setVehicleID(TestUtils.VPI.toString());

    Message activeMtMessage = TestUtils.createMessage();
    activeMtMessage.setProperty(MetaData.SEQUENCE_NUMBER, "0");

    Mockito.when(scheduledMessageDatabaseService.findActiveMessagesWithSequenceNumberByVehicleId(vceMessage.getVehicleID()))
        .thenReturn(List.of(activeMtMessage, activeMtMessage));

    Assertions.assertNull(vceMessageHandler.handleMoAck(vceMessage, STACK_NAME));

    InOrder inOrder = Mockito.inOrder(clock, scheduledMessageDatabaseService, vceSequenceNumberService, vceSplitAckModuleMetricReporter);
    inOrder.verify(vceSplitAckModuleMetricReporter).onHandleMoAck(STACK_NAME);
    inOrder.verify(scheduledMessageDatabaseService).findActiveMessagesWithSequenceNumberByVehicleId(vceMessage.getVehicleID());
    inOrder.verifyNoMoreInteractions();
  }

  @Test
  void handleMoAckWhenFoundValidMtMessageWaitingForAckTest() throws ASNException {
    Clock clock = Mockito.mock(Clock.class);
    ScheduledMessageDatabaseService scheduledMessageDatabaseService = Mockito.mock(ScheduledMessageDatabaseService.class);
    VceSequenceNumberService vceSequenceNumberService = Mockito.mock(VceSequenceNumberService.class);
    VceSplitAckModuleMetricReporter vceSplitAckModuleMetricReporter = Mockito.mock(VceSplitAckModuleMetricReporter.class);

    VceMessageHandler vceMessageHandler = new VceMessageHandler(clock, scheduledMessageDatabaseService, vceSequenceNumberService,
        vceSplitAckModuleMetricReporter);

    Message message = TestUtils.createVceMessageWithIpData(TestUtils.createAckMessageContent());
    message.setVehicleID(TestUtils.VPI.toString());
    message.setProperty(MetaData.SEQUENCE_NUMBER, "0");

    Mockito.when(scheduledMessageDatabaseService.findActiveMessagesWithSequenceNumberByVehicleId(message.getVehicleID())).thenReturn(List.of(message));

    Message handledMoAckMessage = vceMessageHandler.handleMoAck(message, STACK_NAME);
    verifyMoAckMessage(message, handledMoAckMessage);

    InOrder inOrder = Mockito.inOrder(clock, scheduledMessageDatabaseService, vceSequenceNumberService, vceSplitAckModuleMetricReporter);
    inOrder.verify(vceSplitAckModuleMetricReporter).onHandleMoAck(STACK_NAME);
    inOrder.verify(scheduledMessageDatabaseService).findActiveMessagesWithSequenceNumberByVehicleId(message.getVehicleID());
    inOrder.verifyNoMoreInteractions();
  }

  @Test
  void handleMoAckWhenNotFoundMtMessageWaitingForAckTest() throws ASNException {
    Clock clock = Mockito.mock(Clock.class);
    ScheduledMessageDatabaseService scheduledMessageDatabaseService = Mockito.mock(ScheduledMessageDatabaseService.class);
    VceSequenceNumberService vceSequenceNumberService = Mockito.mock(VceSequenceNumberService.class);
    VceSplitAckModuleMetricReporter vceSplitAckModuleMetricReporter = Mockito.mock(VceSplitAckModuleMetricReporter.class);

    VceMessageHandler vceMessageHandler = new VceMessageHandler(clock, scheduledMessageDatabaseService, vceSequenceNumberService,
        vceSplitAckModuleMetricReporter);

    Message vceMessage = TestUtils.createVceMessageWithIpData(TestUtils.createAckMessageContent());
    vceMessage.setVehicleID(TestUtils.VPI.toString());

    Mockito.when(scheduledMessageDatabaseService.findActiveMessagesWithSequenceNumberByVehicleId(vceMessage.getVehicleID()))
        .thenReturn(Collections.emptyList());

    Assertions.assertNull(vceMessageHandler.handleMoAck(vceMessage, STACK_NAME));

    InOrder inOrder = Mockito.inOrder(clock, scheduledMessageDatabaseService, vceSequenceNumberService, vceSplitAckModuleMetricReporter);
    inOrder.verify(vceSplitAckModuleMetricReporter).onHandleMoAck(STACK_NAME);
    inOrder.verify(scheduledMessageDatabaseService).findActiveMessagesWithSequenceNumberByVehicleId(vceMessage.getVehicleID());
    inOrder.verifyNoMoreInteractions();
  }

  @Test
  void handleMtMessageInvalidTest() {
    Clock clock = Mockito.mock(Clock.class);
    ScheduledMessageDatabaseService scheduledMessageDatabaseService = Mockito.mock(ScheduledMessageDatabaseService.class);
    VceSequenceNumberService vceSequenceNumberService = Mockito.mock(VceSequenceNumberService.class);
    VceSplitAckModuleMetricReporter vceSplitAckModuleMetricReporter = Mockito.mock(VceSplitAckModuleMetricReporter.class);

    VceMessageHandler vceMessageHandler = new VceMessageHandler(clock, scheduledMessageDatabaseService, vceSequenceNumberService,
        vceSplitAckModuleMetricReporter);

    AssertThrows.illegalArgumentException(() -> vceMessageHandler.handleMtMessage(null, STACK_NAME), "message must not be null");
    AssertThrows.illegalArgumentException(() -> vceMessageHandler.handleMtMessage(new Message(), null), "stackName must not be null");
    AssertThrows.illegalArgumentException(() -> vceMessageHandler.handleMtMessage(new Message(), ""), "stackName must not be empty");

    Mockito.verifyNoInteractions(clock, scheduledMessageDatabaseService, vceSequenceNumberService, vceSplitAckModuleMetricReporter);
  }

  @Test
  void handleMtMessageWhenEAckTest() throws ASNException {
    Clock clock = Mockito.mock(Clock.class);
    ScheduledMessageDatabaseService scheduledMessageDatabaseService = Mockito.mock(ScheduledMessageDatabaseService.class);
    VceSequenceNumberService vceSequenceNumberService = Mockito.mock(VceSequenceNumberService.class);
    VceSplitAckModuleMetricReporter vceSplitAckModuleMetricReporter = Mockito.mock(VceSplitAckModuleMetricReporter.class);

    VceMessageHandler vceMessageHandler = new VceMessageHandler(clock, scheduledMessageDatabaseService, vceSequenceNumberService,
        vceSplitAckModuleMetricReporter);

    Message message = TestUtils.createVceMessageWithIpData(TestUtils.createAckMessageContent());

    AssertThrows.illegalStateException(() -> vceMessageHandler.handleMtMessage(message, STACK_NAME), "Sending a E_ACK message as a MT-message: " + message);

    InOrder inOrder = Mockito.inOrder(clock, scheduledMessageDatabaseService, vceSequenceNumberService, vceSplitAckModuleMetricReporter);
    inOrder.verify(vceSplitAckModuleMetricReporter).onHandleMtMessage(STACK_NAME);
    inOrder.verify(vceSplitAckModuleMetricReporter).onMtReceivedAckExecute(STACK_NAME);
    inOrder.verifyNoMoreInteractions();
  }

  @Test
  void handleMtMessageWhenEDataAndHasNoSequenceNumberTest() throws ASNException {
    Clock clock = Mockito.mock(Clock.class);
    ScheduledMessageDatabaseService scheduledMessageDatabaseService = Mockito.mock(ScheduledMessageDatabaseService.class);
    VceSequenceNumberService vceSequenceNumberService = Mockito.mock(VceSequenceNumberService.class);
    VceSplitAckModuleMetricReporter vceSplitAckModuleMetricReporter = Mockito.mock(VceSplitAckModuleMetricReporter.class);

    VceMessageHandler vceMessageHandler = new VceMessageHandler(clock, scheduledMessageDatabaseService, vceSequenceNumberService,
        vceSplitAckModuleMetricReporter);

    Message message = TestUtils.createVceMessageWithIpData(TestUtils.createDataMessageContent());
    message.setVehicleID(TestUtils.VPI.toString());

    Mockito.doNothing().when(vceSequenceNumberService).setSequenceNumber(message);

    Optional<Message> messageOptional = vceMessageHandler.handleMtMessage(message, STACK_NAME);
    Assertions.assertTrue(messageOptional.isEmpty());

    InOrder inOrder = Mockito.inOrder(clock, scheduledMessageDatabaseService, vceSequenceNumberService, vceSplitAckModuleMetricReporter);
    inOrder.verify(vceSplitAckModuleMetricReporter).onHandleMtMessage(STACK_NAME);
    inOrder.verify(vceSplitAckModuleMetricReporter).onMtHandleMessage(STACK_NAME);
    inOrder.verify(vceSplitAckModuleMetricReporter).onHandleMtData(STACK_NAME);
    inOrder.verify(vceSequenceNumberService).setSequenceNumber(message);
    inOrder.verifyNoMoreInteractions();
  }

  @Test
  void handleMtMessageWhenEDataAndHasSequenceNumberTest() throws ASNException {
    Clock clock = Mockito.mock(Clock.class);
    ScheduledMessageDatabaseService scheduledMessageDatabaseService = Mockito.mock(ScheduledMessageDatabaseService.class);
    VceSequenceNumberService vceSequenceNumberService = Mockito.mock(VceSequenceNumberService.class);
    VceSplitAckModuleMetricReporter vceSplitAckModuleMetricReporter = Mockito.mock(VceSplitAckModuleMetricReporter.class);

    VceMessageHandler vceMessageHandler = new VceMessageHandler(clock, scheduledMessageDatabaseService, vceSequenceNumberService,
        vceSplitAckModuleMetricReporter);

    Message message = TestUtils.createVceMessageWithIpData(TestUtils.createDataMessageContent());
    message.setVehicleID(TestUtils.VPI.toString());

    Mockito.doAnswer(invocation -> {
          Message messageArg = invocation.getArgument(0, Message.class);
          message.setProperty(MetaData.SEQUENCE_NUMBER, "1");
          return messageArg;
        })
        .when(vceSequenceNumberService).setSequenceNumber(message);

    Message handledMtMessage = vceMessageHandler.handleMtMessage(message, STACK_NAME).get();

    TransportHeader transportHeader = TestUtils.createTransportHeader(handledMtMessage.getPayload());
    verifyNonMobileOriginatedTransportHeader(1, transportHeader);

    InOrder inOrder = Mockito.inOrder(clock, scheduledMessageDatabaseService, vceSequenceNumberService, vceSplitAckModuleMetricReporter);
    inOrder.verify(vceSplitAckModuleMetricReporter).onHandleMtMessage(STACK_NAME);
    inOrder.verify(vceSplitAckModuleMetricReporter).onMtHandleMessage(STACK_NAME);
    inOrder.verify(vceSplitAckModuleMetricReporter).onHandleMtData(STACK_NAME);
    inOrder.verify(vceSequenceNumberService).setSequenceNumber(message);
    inOrder.verifyNoMoreInteractions();
  }

  @Test
  void handleMtMessageWhenEPingTest() throws ASNException {
    Clock clock = Mockito.mock(Clock.class);
    ScheduledMessageDatabaseService scheduledMessageDatabaseService = Mockito.mock(ScheduledMessageDatabaseService.class);
    VceSequenceNumberService vceSequenceNumberService = Mockito.mock(VceSequenceNumberService.class);
    VceSplitAckModuleMetricReporter vceSplitAckModuleMetricReporter = Mockito.mock(VceSplitAckModuleMetricReporter.class);

    VceMessageHandler vceMessageHandler = new VceMessageHandler(clock, scheduledMessageDatabaseService, vceSequenceNumberService,
        vceSplitAckModuleMetricReporter);

    Message message = TestUtils.createVceMessageWithIpData(TestUtils.createPingMessageContent());

    AssertThrows.illegalStateException(() -> vceMessageHandler.handleMtMessage(message, STACK_NAME), "Sending a E_PING message as a MT-message: " + message);

    InOrder inOrder = Mockito.inOrder(clock, scheduledMessageDatabaseService, vceSequenceNumberService, vceSplitAckModuleMetricReporter);
    inOrder.verify(vceSplitAckModuleMetricReporter).onHandleMtMessage(STACK_NAME);
    inOrder.verify(vceSplitAckModuleMetricReporter).onMtReceivedPingExecute(STACK_NAME);
    inOrder.verifyNoMoreInteractions();
  }

  @Test
  void handleMtMessageWhenEPongTest() throws ASNException {
    Clock clock = Mockito.mock(Clock.class);
    ScheduledMessageDatabaseService scheduledMessageDatabaseService = Mockito.mock(ScheduledMessageDatabaseService.class);
    VceSequenceNumberService vceSequenceNumberService = Mockito.mock(VceSequenceNumberService.class);
    VceSplitAckModuleMetricReporter vceSplitAckModuleMetricReporter = Mockito.mock(VceSplitAckModuleMetricReporter.class);

    VceMessageHandler vceMessageHandler = new VceMessageHandler(clock, scheduledMessageDatabaseService, vceSequenceNumberService,
        vceSplitAckModuleMetricReporter);

    Message message = TestUtils.createVceMessageWithIpData(TestUtils.createPongMessageContent());

    AssertThrows.illegalStateException(() -> vceMessageHandler.handleMtMessage(message, STACK_NAME), "Sending a E_PONG message as a MT-message: " + message);

    InOrder inOrder = Mockito.inOrder(clock, scheduledMessageDatabaseService, vceSequenceNumberService, vceSplitAckModuleMetricReporter);
    inOrder.verify(vceSplitAckModuleMetricReporter).onHandleMtMessage(STACK_NAME);
    inOrder.verify(vceSplitAckModuleMetricReporter).onMtReceivedPongExecute(STACK_NAME);
    inOrder.verifyNoMoreInteractions();
  }
}
