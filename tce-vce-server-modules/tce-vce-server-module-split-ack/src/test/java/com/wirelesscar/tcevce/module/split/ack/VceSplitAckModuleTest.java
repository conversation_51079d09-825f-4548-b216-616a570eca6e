package com.wirelesscar.tcevce.module.split.ack;

import java.util.Optional;
import java.util.function.Function;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentMatchers;
import org.mockito.InOrder;
import org.mockito.Mockito;

import com.volvo.tisp.framework.context.TispContext;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;
import com.wirelesscar.caretrack.dh.caretrack.protocol.ASNException;
import com.wirelesscar.caretrack.dh.caretrack.protocol.MessageContent;
import com.wirelesscar.config.mock.MockConfiguration;
import com.wirelesscar.tce.core.stack.ModuleStack;
import com.wirelesscar.tce.core.stack.ModuleStackManager;
import com.wirelesscar.tce.module.api.Message;
import com.wirelesscar.tce.module.api.MetaData;
import com.wirelesscar.tce.module.api.Module;
import com.wirelesscar.tce.module.api.TransportType;
import com.wirelesscar.tce.module.api.exception.EngineRuntimeException;
import com.wirelesscar.tce.module.metrics.ModuleMetricReporter;
import com.wirelesscar.tcevce.module.split.ack.metrics.VceSplitAckModuleMetricReporter;

class VceSplitAckModuleTest {
  private static final String STACK_NAME = "stackName";

  private static void configureModule(VceSplitAckModule vceSplitAckModule, Module lowerModule, Module upperModule) {
    vceSplitAckModule.setStackName(STACK_NAME);
    vceSplitAckModule.setDown(lowerModule);
    vceSplitAckModule.setUp(upperModule);

    vceSplitAckModule.initConfig();
  }

  private static void downMtDataTest(TransportType transportType, String expectedStackRouting) throws ASNException {
    MessageContent messageContent = TestUtils.createDataMessageContent();
    Message message = TestUtils.createVceMessageWithIpData(messageContent);
    message.setVehicleID(TestUtils.VPI.toString());
    if (transportType != null) {
      message.setProperty(MetaData.TRANSPORT_TYPE, transportType.name());
    }

    VceSplitAckModuleMetricReporter vceSplitAckModuleMetricReporterMock = Mockito.mock(VceSplitAckModuleMetricReporter.class);

    VceMessageHandler vceMessageHandler = Mockito.mock(VceMessageHandler.class);
    Mockito.when(vceMessageHandler.handleMtMessage(message, STACK_NAME)).thenReturn(Optional.of(message));

    VceSplitAckModule vceSplitAckModule = new VceSplitAckModule(vceSplitAckModuleMetricReporterMock, Mockito.mock(ModuleMetricReporter.class),
        Mockito.mock(ModuleStackManager.class), vceMessageHandler);
    Module lowerModule = Mockito.mock(Module.class);
    Module upperModule = Mockito.mock(Module.class);
    configureModule(vceSplitAckModule, lowerModule, upperModule);

    vceSplitAckModule.down(message);

    Assertions.assertEquals(expectedStackRouting, message.getProperty(MetaData.STACK_ROUTING));

    Mockito.verify(vceMessageHandler).handleMtMessage(message, STACK_NAME);
    Mockito.verify(lowerModule, Mockito.times(2)).getName();
    Mockito.verify(lowerModule).down(message);
    Mockito.verify(upperModule).getName();
    Mockito.verifyNoMoreInteractions(vceMessageHandler, lowerModule, upperModule);
  }

  private static MockConfiguration getEmptyMockConfiguration() {
    MockConfiguration mockConfiguration = MockConfiguration.getConfig();
    mockConfiguration.deleteAllProperties();
    return mockConfiguration;
  }

  private static void processWhenModuleNotInitialized(Function<VceSplitAckModule, IllegalStateException> exceptionFunction) {
    VceSplitAckModuleMetricReporter vceSplitAckModuleMetricReporter = Mockito.mock(VceSplitAckModuleMetricReporter.class);
    VceMessageHandler vceMessageHandler = Mockito.mock(VceMessageHandler.class);
    ModuleMetricReporter moduleMetricReporter = Mockito.mock(ModuleMetricReporter.class);

    VceSplitAckModule vceSplitAckModule = new VceSplitAckModule(vceSplitAckModuleMetricReporter, moduleMetricReporter, Mockito.mock(ModuleStackManager.class), vceMessageHandler);

    vceSplitAckModule.setStackName(STACK_NAME);

    Module lowerModule = Mockito.mock(Module.class);
    vceSplitAckModule.setDown(lowerModule);
    Module upperModule = Mockito.mock(Module.class);
    vceSplitAckModule.setUp(upperModule);

    exceptionFunction.apply(vceSplitAckModule);

    InOrder inOrder = Mockito.inOrder(vceSplitAckModuleMetricReporter, vceMessageHandler, moduleMetricReporter, lowerModule, upperModule);
    inOrder.verify(lowerModule).getName();
    inOrder.verify(upperModule).getName();
    inOrder.verifyNoMoreInteractions();
  }

  private static void upMoDataAckRequestedTest(TransportType transportType, String expectedStackRouting) throws ASNException {
    Message message = TestUtils.createVceMessageWithIpData(TestUtils.createDataMessageContent());
    message.setVehicleID(TestUtils.VPI.toString());
    message.setProperty(MetaData.MOBILE_ORIGINATED, "true");

    VceSplitAckModuleMetricReporter vceSplitAckModuleMetricReporterMock = Mockito.mock(VceSplitAckModuleMetricReporter.class);

    VceMessageHandler vceMessageHandler = Mockito.mock(VceMessageHandler.class);
    Message ackMessage = new Message();
    if (transportType != null) {
      ackMessage.setProperty(MetaData.TRANSPORT_TYPE, transportType.name());
    }
    Mockito.when(vceMessageHandler.createMoResponse(ArgumentMatchers.eq(message), ArgumentMatchers.any(MessageContent.class), ArgumentMatchers.eq(STACK_NAME)))
        .thenReturn(ackMessage);

    VceSplitAckModule vceSplitAckModule = new VceSplitAckModule(vceSplitAckModuleMetricReporterMock, Mockito.mock(ModuleMetricReporter.class),
        Mockito.mock(ModuleStackManager.class), vceMessageHandler);
    Module lowerModule = Mockito.mock(Module.class);
    Module upperModule = Mockito.mock(Module.class);
    configureModule(vceSplitAckModule, lowerModule, upperModule);

    vceSplitAckModule.up(message);

    Assertions.assertEquals(expectedStackRouting, ackMessage.getProperty(MetaData.STACK_ROUTING));

    InOrder inOrder = Mockito.inOrder(vceSplitAckModuleMetricReporterMock, vceMessageHandler, lowerModule, upperModule);
    inOrder.verify(vceSplitAckModuleMetricReporterMock).onMoMessageUp(STACK_NAME);
    inOrder.verify(upperModule).getName();
    inOrder.verify(upperModule).up(message);
    inOrder.verify(vceMessageHandler)
        .createMoResponse(ArgumentMatchers.eq(message), ArgumentMatchers.any(MessageContent.class), ArgumentMatchers.eq(STACK_NAME));
    inOrder.verify(vceSplitAckModuleMetricReporterMock).onProducedAck(STACK_NAME);
    inOrder.verify(lowerModule).getName();
    inOrder.verify(lowerModule).down(ackMessage);
    inOrder.verifyNoMoreInteractions();
  }

  @Test
  void downMtDataExceptionTest() throws ASNException {
    MessageContent messageContent = TestUtils.createDataMessageContent();
    Message message = TestUtils.createVceMessageWithIpData(messageContent);
    message.setVehicleID(TestUtils.VPI.toString());

    VceSplitAckModuleMetricReporter vceSplitAckModuleMetricReporterMock = Mockito.mock(VceSplitAckModuleMetricReporter.class);

    VceMessageHandler vceMessageHandler = Mockito.mock(VceMessageHandler.class);
    Mockito.when(vceMessageHandler.handleMtMessage(message, STACK_NAME)).thenThrow(new ASNException("test"));

    VceSplitAckModule vceSplitAckModule = new VceSplitAckModule(vceSplitAckModuleMetricReporterMock, Mockito.mock(ModuleMetricReporter.class),
        Mockito.mock(ModuleStackManager.class), vceMessageHandler);
    Module lowerModule = Mockito.mock(Module.class);
    Module upperModule = Mockito.mock(Module.class);
    configureModule(vceSplitAckModule, lowerModule, upperModule);

    AssertThrows.exception(() -> vceSplitAckModule.down(message), "Failed to handle MT message with id=11111111111111111111111111111111",
        EngineRuntimeException.class);

    Mockito.verify(vceMessageHandler).handleMtMessage(message, STACK_NAME);
    Mockito.verify(lowerModule).getName();
    Mockito.verify(upperModule).getName();
    Mockito.verifyNoMoreInteractions(vceMessageHandler, lowerModule, upperModule);
  }

  @Test
  void downMtDataNotSendingDownTest() throws ASNException {
    Message message = TestUtils.createVceMessageWithIpData(TestUtils.createDataMessageContent());
    message.setVehicleID(TestUtils.VPI.toString());

    VceSplitAckModuleMetricReporter vceSplitAckModuleMetricReporterMock = Mockito.mock(VceSplitAckModuleMetricReporter.class);

    VceMessageHandler vceMessageHandler = Mockito.mock(VceMessageHandler.class);
    Mockito.when(vceMessageHandler.handleMtMessage(message, STACK_NAME)).thenReturn(Optional.empty());

    VceSplitAckModule vceSplitAckModule = new VceSplitAckModule(vceSplitAckModuleMetricReporterMock, Mockito.mock(ModuleMetricReporter.class),
        Mockito.mock(ModuleStackManager.class), vceMessageHandler);
    Module lowerModule = Mockito.mock(Module.class);
    Module upperModule = Mockito.mock(Module.class);
    configureModule(vceSplitAckModule, lowerModule, upperModule);

    vceSplitAckModule.down(message);

    InOrder inOrder = Mockito.inOrder(vceSplitAckModuleMetricReporterMock, vceMessageHandler, lowerModule, upperModule);
    inOrder.verify(vceMessageHandler).handleMtMessage(message, STACK_NAME);
    inOrder.verifyNoMoreInteractions();
  }

  @Test
  void downMtDataSatTest() throws ASNException {
    downMtDataTest(TransportType.SATELLITE, "SATELLITE");
  }

  @Test
  void downMtDataSmsTest() throws ASNException {
    downMtDataTest(TransportType.SMS, "SMS");
  }

  @Test
  void downMtDataUdpTest() throws ASNException {
    downMtDataTest(TransportType.UDP, "UDP");
  }

  @Test
  void downMtDataWithNoTransportTypeTest() throws ASNException {
    downMtDataTest(null, "UDP");
  }

  @Test
  void downWhenModuleNotInitializedTest() {
    processWhenModuleNotInitialized(vceSplitAckModule ->
        AssertThrows.illegalStateException(() -> vceSplitAckModule.down(new Message()), "initDone == false")
    );
  }

  @Test
  void stopTest() {
    VceSplitAckModuleMetricReporter vceSplitAckModuleMetricReporter = Mockito.mock(VceSplitAckModuleMetricReporter.class);
    VceMessageHandler vceMessageHandler = Mockito.mock(VceMessageHandler.class);
    ModuleMetricReporter moduleMetricReporter = Mockito.mock(ModuleMetricReporter.class);

    VceSplitAckModule vceSplitAckModule = new VceSplitAckModule(vceSplitAckModuleMetricReporter, moduleMetricReporter, Mockito.mock(ModuleStackManager.class), vceMessageHandler);
    Module lowerModule = Mockito.mock(Module.class);
    Module upperModule = Mockito.mock(Module.class);
    configureModule(vceSplitAckModule, lowerModule, upperModule);

    vceSplitAckModule.stop();

    InOrder inOrder = Mockito.inOrder(vceSplitAckModuleMetricReporter, vceMessageHandler, moduleMetricReporter, lowerModule, upperModule);
    inOrder.verify(lowerModule).getName();
    inOrder.verify(upperModule).getName();
    inOrder.verify(vceMessageHandler).stop();
    inOrder.verifyNoMoreInteractions();
  }

  @Test
  void upExceptionTest() throws ASNException {
    Message message = TestUtils.createVceMessageWithIpData(TestUtils.createDataMessageContent());
    message.setVehicleID(TestUtils.VPI.toString());
    message.setProperty(MetaData.MOBILE_ORIGINATED, "true");

    VceSplitAckModuleMetricReporter vceSplitAckModuleMetricReporterMock = Mockito.mock(VceSplitAckModuleMetricReporter.class);

    VceMessageHandler vceMessageHandler = Mockito.mock(VceMessageHandler.class);
    Mockito.when(vceMessageHandler.createMoResponse(ArgumentMatchers.eq(message), ArgumentMatchers.any(MessageContent.class), ArgumentMatchers.eq(STACK_NAME)))
        .thenThrow(new ASNException("test"));

    VceSplitAckModule vceSplitAckModule = new VceSplitAckModule(vceSplitAckModuleMetricReporterMock, Mockito.mock(ModuleMetricReporter.class),
        Mockito.mock(ModuleStackManager.class), vceMessageHandler);
    Module lowerModule = Mockito.mock(Module.class);
    Module upperModule = Mockito.mock(Module.class);
    configureModule(vceSplitAckModule, lowerModule, upperModule);

    AssertThrows.exception(() -> vceSplitAckModule.up(message), errorMessage -> errorMessage.startsWith("Failed to handle MO or Ack message"),
        EngineRuntimeException.class);

    InOrder inOrder = Mockito.inOrder(vceSplitAckModuleMetricReporterMock, vceMessageHandler, lowerModule, upperModule);
    inOrder.verify(upperModule).getName();
    inOrder.verify(upperModule).up(message);
    inOrder.verify(vceMessageHandler)
        .createMoResponse(ArgumentMatchers.eq(message), ArgumentMatchers.any(MessageContent.class), ArgumentMatchers.eq(STACK_NAME));
    inOrder.verifyNoMoreInteractions();
  }

  @Test
  void upMoAckNoMatchTest() throws ASNException {
    Message message = TestUtils.createVceMessageWithIpData(TestUtils.createAckMessageContent());
    message.setVehicleID(TestUtils.VPI.toString());
    message.setProperty(MetaData.MOBILE_ORIGINATED, "true");
    message.setProperty(MetaData.TRANSPORT_TYPE, TransportType.SATELLITE.name());

    VceSplitAckModuleMetricReporter vceSplitAckModuleMetricReporterMock = Mockito.mock(VceSplitAckModuleMetricReporter.class);

    VceMessageHandler vceMessageHandler = Mockito.mock(VceMessageHandler.class);

    VceSplitAckModule vceSplitAckModule = new VceSplitAckModule(vceSplitAckModuleMetricReporterMock, Mockito.mock(ModuleMetricReporter.class),
        Mockito.mock(ModuleStackManager.class), vceMessageHandler);
    Module lowerModule = Mockito.mock(Module.class);
    Module upperModule = Mockito.mock(Module.class);
    configureModule(vceSplitAckModule, lowerModule, upperModule);

    Message satelliteStatusMessage = TestUtils.createStatusMessage(message);
    satelliteStatusMessage.setMessageId(null);

    vceSplitAckModule.up(message);

    InOrder inOrder = Mockito.inOrder(vceMessageHandler, lowerModule, upperModule, vceSplitAckModuleMetricReporterMock);
    inOrder.verify(vceMessageHandler).handleMoAck(message, STACK_NAME);
    inOrder.verify(lowerModule).down(satelliteStatusMessage);
    inOrder.verifyNoMoreInteractions();
  }

  @Test
  void upMoAckTest() throws ASNException {
    Message message = TestUtils.createVceMessageWithIpData(TestUtils.createAckMessageContent());
    message.setVehicleID(TestUtils.VPI.toString());
    message.setProperty(MetaData.MOBILE_ORIGINATED, "true");

    VceSplitAckModuleMetricReporter vceSplitAckModuleMetricReporterMock = Mockito.mock(VceSplitAckModuleMetricReporter.class);

    Message statusMessage = TestUtils.createStatusMessage(message);
    statusMessage.getProperties().remove(MetaData.STACK_ROUTING.name());
    statusMessage.setMessageId("2".repeat(32));

    VceMessageHandler vceMessageHandler = Mockito.mock(VceMessageHandler.class);
    Mockito.when(vceMessageHandler.handleMoAck(message, STACK_NAME)).thenReturn(statusMessage);

    VceSplitAckModule vceSplitAckModule = new VceSplitAckModule(vceSplitAckModuleMetricReporterMock, Mockito.mock(ModuleMetricReporter.class),
        Mockito.mock(ModuleStackManager.class), vceMessageHandler);
    Module lowerModule = Mockito.mock(Module.class);
    Module upperModule = Mockito.mock(Module.class);
    configureModule(vceSplitAckModule, lowerModule, upperModule);

    TispContext.runInContext(() -> vceSplitAckModule.up(message));

    InOrder inOrder = Mockito.inOrder(vceSplitAckModuleMetricReporterMock, vceMessageHandler, lowerModule, upperModule);
    inOrder.verify(vceMessageHandler).handleMoAck(message, STACK_NAME);
    inOrder.verify(upperModule).up(statusMessage);
    inOrder.verifyNoMoreInteractions();
  }

  @Test
  void upMoDataAckNotRequestedTest() throws ASNException {
    Message message = TestUtils.createVceMessageWithIpData(TestUtils.createDataMessageContent());
    message.setVehicleID(TestUtils.VPI.toString());
    message.setProperty(MetaData.MOBILE_ORIGINATED, "true");

    VceSplitAckModuleMetricReporter vceSplitAckModuleMetricReporterMock = Mockito.mock(VceSplitAckModuleMetricReporter.class);
    VceMessageHandler vceMessageHandler = Mockito.mock(VceMessageHandler.class);

    VceSplitAckModule vceSplitAckModule = new VceSplitAckModule(vceSplitAckModuleMetricReporterMock, Mockito.mock(ModuleMetricReporter.class),
        Mockito.mock(ModuleStackManager.class), vceMessageHandler);
    Module lowerModule = Mockito.mock(Module.class);
    Module upperModule = Mockito.mock(Module.class);
    configureModule(vceSplitAckModule, lowerModule, upperModule);

    vceSplitAckModule.up(message);

    InOrder inOrder = Mockito.inOrder(vceSplitAckModuleMetricReporterMock, vceMessageHandler, lowerModule, upperModule);
    inOrder.verify(vceSplitAckModuleMetricReporterMock).onMoMessageUp(STACK_NAME);
    Assertions.assertTrue(MessageUtils.hasMetaData(message, MetaData.VCE_SERVICE));
    inOrder.verify(upperModule).getName();
    inOrder.verify(upperModule).up(message);
    inOrder.verify(vceMessageHandler)
        .createMoResponse(ArgumentMatchers.eq(message), ArgumentMatchers.any(MessageContent.class), ArgumentMatchers.eq(STACK_NAME));
    inOrder.verifyNoMoreInteractions();
  }

  @Test
  void upMoDataAckRequestedSatTest() throws ASNException {
    upMoDataAckRequestedTest(TransportType.SATELLITE, "SATELLITE");
  }

  @Test
  void upMoDataAckRequestedSmsTest() throws ASNException {
    upMoDataAckRequestedTest(TransportType.SMS, "SMS");
  }

  @Test
  void upMoDataAckRequestedUdpTest() throws ASNException {
    upMoDataAckRequestedTest(TransportType.UDP, "UDP");
  }

  @Test
  void upMoDataAckRequestedWithNoTransportTypeTest() throws ASNException {
    upMoDataAckRequestedTest(null, "UDP");
  }

  @Test
  void upMoDataPublicKeyTest() throws ASNException {
    Message message = TestUtils.createVceMessageWithIpData(TestUtils.createPublicKeyDataMessageContent());
    message.setVehicleID(TestUtils.VPI.toString());
    message.setProperty(MetaData.MOBILE_ORIGINATED, "true");

    final String stackName = "foo";

    MockConfiguration mockConfiguration = getEmptyMockConfiguration();
    mockConfiguration.setPropertySpecific(mockConfiguration.getComponentShortName(), "message.server.jms.api.lookup.name", stackName);

    final Module topModule = Mockito.mock(Module.class);

    final ModuleStack moduleStack = Mockito.mock(ModuleStack.class);
    Mockito.when(moduleStack.getTopModule()).thenReturn(topModule);

    final ModuleStackManager moduleStackManager = Mockito.mock(ModuleStackManager.class);
    Mockito.when(moduleStackManager.getByName(stackName)).thenReturn(moduleStack);

    VceSplitAckModuleMetricReporter vceSplitAckModuleMetricReporterMock = Mockito.mock(VceSplitAckModuleMetricReporter.class);

    VceMessageHandler vceMessageHandler = Mockito.mock(VceMessageHandler.class);
    Message ackMessage = new Message();
    ackMessage.setProperty(MetaData.TRANSPORT_TYPE, TransportType.UDP.name());
    Mockito.when(vceMessageHandler.createMoResponse(ArgumentMatchers.eq(message), ArgumentMatchers.any(MessageContent.class), ArgumentMatchers.eq(STACK_NAME)))
        .thenReturn(ackMessage);

    VceSplitAckModule vceSplitAckModule = new VceSplitAckModule(vceSplitAckModuleMetricReporterMock, Mockito.mock(ModuleMetricReporter.class),
        moduleStackManager, vceMessageHandler);
    Module lowerModule = Mockito.mock(Module.class);
    Module upperModule = Mockito.mock(Module.class);
    configureModule(vceSplitAckModule, lowerModule, upperModule);

    vceSplitAckModule.up(message);

    Assertions.assertEquals("UDP", ackMessage.getProperty(MetaData.STACK_ROUTING));

    InOrder inOrder = Mockito.inOrder(vceSplitAckModuleMetricReporterMock, vceMessageHandler, lowerModule, upperModule);
    Mockito.verify(moduleStackManager).getByName(stackName);
    Mockito.verify(moduleStack).getTopModule();
    Mockito.verify(topModule).down(message);
    inOrder.verify(vceMessageHandler)
        .createMoResponse(ArgumentMatchers.eq(message), ArgumentMatchers.any(MessageContent.class), ArgumentMatchers.eq(STACK_NAME));
    inOrder.verify(vceSplitAckModuleMetricReporterMock).onProducedAck(STACK_NAME);
    inOrder.verify(lowerModule).getName();
    inOrder.verify(lowerModule).down(ackMessage);
    inOrder.verifyNoMoreInteractions();
  }

  @Test
  void upMoDataVehicleNotFoundTest() throws ASNException {
    MessageContent messageContent = TestUtils.createDataMessageContent();
    Message message = TestUtils.createVceMessageWithIpData(messageContent);
    message.setVehicleID(TestUtils.VPI.toString());
    message.setProperty(MetaData.MOBILE_ORIGINATED, "true");
    message.setProperty(MetaData.VEHICLE_NOT_FOUND, "true");

    VceSplitAckModuleMetricReporter vceSplitAckModuleMetricReporterMock = Mockito.mock(VceSplitAckModuleMetricReporter.class);

    Message ackMessage = new Message();
    VceMessageHandler vceMessageHandler = Mockito.mock(VceMessageHandler.class);
    Mockito.when(vceMessageHandler.createMoResponse(ArgumentMatchers.eq(message), ArgumentMatchers.any(MessageContent.class), ArgumentMatchers.eq(STACK_NAME)))
        .thenReturn(ackMessage);

    VceSplitAckModule vceSplitAckModule = new VceSplitAckModule(vceSplitAckModuleMetricReporterMock, Mockito.mock(ModuleMetricReporter.class),
        Mockito.mock(ModuleStackManager.class), vceMessageHandler);
    Module lowerModule = Mockito.mock(Module.class);
    Module upperModule = Mockito.mock(Module.class);
    configureModule(vceSplitAckModule, lowerModule, upperModule);

    vceSplitAckModule.up(message);

    Mockito.verify(vceMessageHandler)
        .createMoResponse(ArgumentMatchers.eq(message), ArgumentMatchers.any(MessageContent.class), ArgumentMatchers.eq(STACK_NAME));
    Mockito.verify(lowerModule, Mockito.times(2)).getName();
    Mockito.verify(upperModule).getName();
    Mockito.verify(lowerModule).down(ackMessage);
    Mockito.verifyNoMoreInteractions(vceMessageHandler, lowerModule, upperModule);
  }

  @Test
  void upMoPingTest() throws ASNException {
    Message message = TestUtils.createVceMessageWithIpData(TestUtils.createPingMessageContent());
    message.setVehicleID(TestUtils.VPI.toString());
    message.setProperty(MetaData.MOBILE_ORIGINATED, "true");

    VceSplitAckModuleMetricReporter vceSplitAckModuleMetricReporterMock = Mockito.mock(VceSplitAckModuleMetricReporter.class);

    Message ackMessage = new Message();
    ackMessage.setMessageId("12345678901234567890123456789032");
    VceMessageHandler vceMessageHandler = Mockito.mock(VceMessageHandler.class);

    Mockito.when(vceMessageHandler.createMoResponse(ArgumentMatchers.eq(message), ArgumentMatchers.any(MessageContent.class), ArgumentMatchers.eq(STACK_NAME)))
        .thenReturn(ackMessage);

    VceSplitAckModule vceSplitAckModule = new VceSplitAckModule(vceSplitAckModuleMetricReporterMock, Mockito.mock(ModuleMetricReporter.class),
        Mockito.mock(ModuleStackManager.class), vceMessageHandler);
    Module lowerModule = Mockito.mock(Module.class);
    Module upperModule = Mockito.mock(Module.class);
    configureModule(vceSplitAckModule, lowerModule, upperModule);

    vceSplitAckModule.up(message);

    InOrder inOrder = Mockito.inOrder(vceSplitAckModuleMetricReporterMock, vceMessageHandler, lowerModule, upperModule);
    inOrder.verify(upperModule).getName();
    inOrder.verify(vceMessageHandler)
        .createMoResponse(ArgumentMatchers.eq(message), ArgumentMatchers.any(MessageContent.class), ArgumentMatchers.eq(STACK_NAME));
    inOrder.verify(lowerModule).getName();
    inOrder.verify(lowerModule).down(ackMessage);
    inOrder.verifyNoMoreInteractions();
  }

  @Test
  void upMoWithoutPayloadTest() {
    Message message = new Message();
    message.setVehicleID(TestUtils.VPI.toString());
    message.setProperty(MetaData.MOBILE_ORIGINATED, "true");

    VceSplitAckModuleMetricReporter vceSplitAckModuleMetricReporterMock = Mockito.mock(VceSplitAckModuleMetricReporter.class);
    VceMessageHandler vceMessageHandler = Mockito.mock(VceMessageHandler.class);

    VceSplitAckModule vceSplitAckModule = new VceSplitAckModule(vceSplitAckModuleMetricReporterMock, Mockito.mock(ModuleMetricReporter.class),
        Mockito.mock(ModuleStackManager.class), vceMessageHandler);
    Module lowerModule = Mockito.mock(Module.class);
    Module upperModule = Mockito.mock(Module.class);
    configureModule(vceSplitAckModule, lowerModule, upperModule);

    vceSplitAckModule.up(message);

    InOrder inOrder = Mockito.inOrder(vceSplitAckModuleMetricReporterMock, vceMessageHandler, lowerModule, upperModule);
    inOrder.verify(lowerModule).getName();
    inOrder.verify(upperModule).getName();
    inOrder.verifyNoMoreInteractions();
  }

  @Test
  void upMtEDataAckRequestedTest() throws ASNException {
    Message message = TestUtils.createVceMessageWithIpData(TestUtils.createDataMessageContent());
    message.setVehicleID(TestUtils.VPI.toString());
    message.setProperty(MetaData.MOBILE_ORIGINATED, "false");

    VceSplitAckModuleMetricReporter vceSplitAckModuleMetricReporterMock = Mockito.mock(VceSplitAckModuleMetricReporter.class);

    VceMessageHandler vceMessageHandler = Mockito.mock(VceMessageHandler.class);
    Message statusMessage = TestUtils.createStatusMessage(message);
    statusMessage.getProperties().remove(MetaData.STACK_ROUTING.name());
    statusMessage.setMessageId("2".repeat(32));

    Mockito.when(vceMessageHandler.handleMoAck(message, STACK_NAME)).thenReturn(statusMessage);

    VceSplitAckModule vceSplitAckModule = new VceSplitAckModule(vceSplitAckModuleMetricReporterMock, Mockito.mock(ModuleMetricReporter.class),
        Mockito.mock(ModuleStackManager.class), vceMessageHandler);
    Module lowerModule = Mockito.mock(Module.class);
    Module upperModule = Mockito.mock(Module.class);
    configureModule(vceSplitAckModule, lowerModule, upperModule);

    TispContext.runInContext(() -> vceSplitAckModule.up(message));

    InOrder inOrder = Mockito.inOrder(vceSplitAckModuleMetricReporterMock, vceMessageHandler, lowerModule, upperModule);
    inOrder.verify(vceMessageHandler).handleMoAck(message, STACK_NAME);
    inOrder.verify(upperModule).up(statusMessage);
    inOrder.verify(vceSplitAckModuleMetricReporterMock).onMoMessageUp(STACK_NAME);
    inOrder.verify(upperModule).getName();
    inOrder.verify(upperModule).up(message);
    inOrder.verifyNoMoreInteractions();
  }

  @Test
  void upWhenModuleNotInitializedTest() {
    processWhenModuleNotInitialized(vceSplitAckModule ->
        AssertThrows.illegalStateException(() -> vceSplitAckModule.up(new Message()), "initDone == false")
    );
  }

  @Test
  void upWhenSatelliteMtAckNoMatchTest() throws ASNException {
    Message message = TestUtils.createVceMessage(TestUtils.createAckMessageContent());
    message.setVehicleID(TestUtils.VPI.toString());
    message.setProperty(MetaData.HANDLE, TestUtils.HANDLE.toString());
    message.setProperty(MetaData.SATELLITE_SOURCE_ADDRESS, TestUtils.SATELLITE_MESSAGE_ID);
    message.setProperty(MetaData.SATELLITE_MESSAGE_ID, TestUtils.SATELLITE_MESSAGE_ID);
    message.setProperty(MetaData.MOBILE_ORIGINATED, Boolean.FALSE.toString());
    message.setProperty(MetaData.TRANSPORT_TYPE, TransportType.SATELLITE.name());

    VceSplitAckModuleMetricReporter vceSplitAckModuleMetricReporterMock = Mockito.mock(VceSplitAckModuleMetricReporter.class);

    VceMessageHandler vceMessageHandler = Mockito.mock(VceMessageHandler.class);

    VceSplitAckModule vceSplitAckModule = new VceSplitAckModule(vceSplitAckModuleMetricReporterMock, Mockito.mock(ModuleMetricReporter.class),
        Mockito.mock(ModuleStackManager.class), vceMessageHandler);
    Module lowerModule = Mockito.mock(Module.class);
    Module upperModule = Mockito.mock(Module.class);
    configureModule(vceSplitAckModule, lowerModule, upperModule);

    Message satelliteStatusMessage = TestUtils.createStatusMessage(message);
    satelliteStatusMessage.setMessageId(null);

    vceSplitAckModule.up(message);

    InOrder inOrder = Mockito.inOrder(vceMessageHandler, lowerModule, upperModule, vceSplitAckModuleMetricReporterMock);
    inOrder.verify(vceMessageHandler).handleMoAck(message, STACK_NAME);
    inOrder.verify(lowerModule).down(satelliteStatusMessage);
    inOrder.verifyNoMoreInteractions();
  }
}
