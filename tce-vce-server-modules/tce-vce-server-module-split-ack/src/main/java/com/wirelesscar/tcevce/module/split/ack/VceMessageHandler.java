package com.wirelesscar.tcevce.module.split.ack;

import java.time.Clock;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.wirelesscar.caretrack.dh.caretrack.protocol.ASNException;
import com.wirelesscar.caretrack.dh.caretrack.protocol.MessageContent;
import com.wirelesscar.caretrack.dh.caretrack.protocol.MessageType;
import com.wirelesscar.tce.db.common.db.api.MessageFields;
import com.wirelesscar.tce.module.api.Message;
import com.wirelesscar.tce.module.api.MessageStatus;
import com.wirelesscar.tce.module.api.MetaData;
import com.wirelesscar.tce.module.api.SmsDataCoding;
import com.wirelesscar.tce.module.api.TransportType;
import com.wirelesscar.tcevce.module.split.ack.metrics.VceSplitAckModuleMetricReporter;
import com.wirelesscar.tcevce.module.split.ack.service.ScheduledMessageDatabaseService;
import com.wirelesscar.tcevce.module.split.ack.service.VceSequenceNumberService;
import com.wirelesscar.tcevce.wecu.device.info.database.model.SequenceNumber;

@Component
public class VceMessageHandler {
  public static final String MESSAGE_CONTENT_VAR_NAME = "messageContent";
  public static final String MESSAGE_VAR_NAME = "message";
  public static final String STACK_NAME_VAR_NAME = "stackName";
  private static final int MAX_CACHE_SIZE = 500_000;
  private static final Logger logger = LoggerFactory.getLogger(VceMessageHandler.class);

  private final Clock clock;
  private final Cache<String, Map<String, String>> mtMessageCache;
  private final ScheduledMessageDatabaseService scheduledMessageDatabaseService;
  private final VceSequenceNumberService vceSequenceNumberService;
  private final VceSplitAckModuleMetricReporter vceSplitAckModuleMetricReporter;

  public VceMessageHandler(Clock clock, ScheduledMessageDatabaseService scheduledMessageDatabaseService, VceSequenceNumberService vceSequenceNumberService,
      VceSplitAckModuleMetricReporter vceSplitAckModuleMetricReporter) {
    this.clock = clock;
    this.vceSplitAckModuleMetricReporter = vceSplitAckModuleMetricReporter;
    this.scheduledMessageDatabaseService = scheduledMessageDatabaseService;
    this.vceSequenceNumberService = vceSequenceNumberService;

    mtMessageCache = CacheBuilder.newBuilder()
        .maximumSize(MAX_CACHE_SIZE)
        .expireAfterWrite(3_600, TimeUnit.SECONDS)
        .build();
  }

  /**
   * Creates status {@link Message} with {@link MessageStatus#DELIVERED}. Used for notifying <code>Orbcomm XML Gateway</code> to delete message
   */
  static Message createSatelliteStatusMessage(Message message, MessageContent messageContent) throws ASNException {
    Validate.notNull(message, MESSAGE_VAR_NAME);
    Validate.notNull(messageContent, MESSAGE_CONTENT_VAR_NAME);

    Message statusMessage = Message.createStatusMessage(MessageStatus.DELIVERED);

    statusMessage.addProperties(message.getProperties());
    //needed for sending message back to sender
    MessageUtils.setSwappedAddress(statusMessage, message);
    statusMessage.setVehicleID(message.getVehicleID());
    statusMessage.setProperty(MetaData.ACK, Boolean.TRUE.toString());

    // Payload is not necessary but as each Module has a validateBeforeDown which expects a payload.
    // TODO: Change in the Future after a Design discussion
    statusMessage.setPayload(
        MessageUtils.createAckPayload(messageContent, MessageUtils.isMobileOriginated(message), getSequenceNumberOrDefault(message)));

    return statusMessage;
  }

  /**
   * Creates an ack message
   */
  private static Message createMoAckMessageToVehicle(Message message, MessageContent messageContent)
      throws ASNException {
    // Create a statusMessage (Message is accepted by TCE)
    Message ackMessage = Message.createMessage();
    ackMessage.setMessageId(message.getMessageId());
    ackMessage.setVehicleID(message.getVehicleID());
    ackMessage.addProperties(message.getProperties());
    ackMessage.setProperty(MetaData.ACK, Boolean.TRUE.toString());

    // Set message meta data
    ackMessage.setProperty(MetaData.SMS_DATA_CODING, SmsDataCoding.BINARY.toString());

    MessageUtils.setSwappedAddress(ackMessage, message);
    ackMessage.setProperty(MetaData.TRANSPORT_TYPE, message.getProperty(MetaData.TRANSPORT_TYPE));
    // setRouting(ack); done in module instead

    // Create the ack payload
    ackMessage.setPayload(MessageUtils.createAckPayload(messageContent, MessageUtils.isMobileOriginated(message), getSequenceNumberOrDefault(message)));

    logger.trace("Created MO ACK message: {}", ackMessage);

    return ackMessage;
  }

  private static Message createMtStatusMessage(Message message, SequenceNumber sequenceNumber, Map<String, String> mtMessageProperties) {
    Message statusMessage = Message.createStatusMessage(MessageStatus.DELIVERED);
    statusMessage.addProperties(message.getProperties());

    String messageId = mtMessageProperties.get(MessageFields.messageID.name());
    logger.debug("Change messageId from: {} to match acknowledged message: {} for sequenceNumber: {}", message.getMessageId(), messageId, sequenceNumber);
    statusMessage.setMessageId(messageId);

    statusMessage.setVehicleID(message.getVehicleID());
    statusMessage.addProperties(mtMessageProperties);
    statusMessage.getProperties().remove(MetaData.STACK_ROUTING.name());

    logger.trace("Preparing to send MT Status message up: {}", statusMessage);
    return statusMessage;
  }

  private static Optional<SequenceNumber> getSequenceNumber(Message message) {
    return Optional.ofNullable(message.getProperty(MetaData.SEQUENCE_NUMBER))
        .map(Byte::parseByte)
        .map(SequenceNumber::ofByte);
  }

  private static SequenceNumber getSequenceNumberOrDefault(Message message) {
    return getSequenceNumber(message)
        .orElseGet(() -> {
          logger.error("Sequence number is not set. This is BAD and should be checked. Setting seqNr to 0 and hope for the best");
          return SequenceNumber.ofByte((byte) 0);
        });
  }

  private static boolean isSequenceNumbersEquals(Message message, SequenceNumber sequenceNumber) {
    return getSequenceNumber(message)
        .filter(foundMessageSequenceNumber -> foundMessageSequenceNumber.equals(sequenceNumber))
        .isPresent();
  }

  /**
   * Top level handler for MO messages
   *
   * @return acknowledge message or null if shouldn't be acknowledged,
   */
  public Message createMoResponse(Message message, MessageContent messageContent, String stackName) throws ASNException {
    Validate.notNull(message, MESSAGE_VAR_NAME);
    Validate.notNull(messageContent, MESSAGE_CONTENT_VAR_NAME);
    Validate.notEmpty(stackName, STACK_NAME_VAR_NAME);

    logger.debug("Trying to create MO response for {}", message);
    vceSplitAckModuleMetricReporter.onHandleMoOrAck(stackName);

    switch (messageContent.getChoice()) {
      case MessageContent.E_PING:
        logger.trace("Received MO E_PING {}", message);
        vceSplitAckModuleMetricReporter.onMoPingHandled(stackName);
        return handleMoPing(messageContent, message, stackName);

      case MessageContent.E_PONG:
        // No ping message available on MT this case should not happen
        logger.error("Strange MessageContent.E_PONG - Should not be possible on MO-messages, messageId: {}", message.getMessageId());
        vceSplitAckModuleMetricReporter.onMoPongReceived(stackName);
        throw new IllegalStateException("Received a E_PONG message as a MO-message: " + message);

      case MessageContent.E_DATA:
        logger.trace("Received MO E_DATA {}", message);
        return handleMoData(messageContent, message, stackName);

      case MessageContent.E_ACK:
        logger.trace("Received MO E_ACK {}", message);
        vceSplitAckModuleMetricReporter.onMoHandleAck(stackName);
        return handleMoAck(message, stackName);

      default:
        logger.error("Unknown MO MessageContent choice: {}. Silent dropping message.", messageContent.getChoice());
        vceSplitAckModuleMetricReporter.onMoUnknownChoice(stackName);
        return null;
    }
  }

  /**
   * Handle an MT message. Find a new sequence number
   */
  public Optional<Message> handleMtMessage(Message message, String stackName) throws ASNException {
    Validate.notNull(message, MESSAGE_VAR_NAME);
    Validate.notEmpty(stackName, STACK_NAME_VAR_NAME);

    vceSplitAckModuleMetricReporter.onHandleMtMessage(stackName);

    MessageContent messageContent = MessageUtils.getMessageContent(message.getPayload());

    switch (messageContent.getChoice()) {
      case MessageContent.E_PING:
        vceSplitAckModuleMetricReporter.onMtReceivedPingExecute(stackName);
        throw new IllegalStateException("Sending a E_PING message as a MT-message: " + message);

      case MessageContent.E_PONG:
        vceSplitAckModuleMetricReporter.onMtReceivedPongExecute(stackName);
        throw new IllegalStateException("Sending a E_PONG message as a MT-message: " + message);

      case MessageContent.E_DATA:
        logger.trace("Received MT E_DATA {}", message);
        vceSplitAckModuleMetricReporter.onMtHandleMessage(stackName);
        return handleMtData(message, stackName);

      case MessageContent.E_ACK:
        vceSplitAckModuleMetricReporter.onMtReceivedAckExecute(stackName);
        throw new IllegalStateException("Sending a E_ACK message as a MT-message: " + message);

      default:
        vceSplitAckModuleMetricReporter.onMtUnknownChoice(stackName);
        throw new IllegalStateException("Unknown MessageContent choice: " + messageContent.getChoice() + " for message: " + message);
    }
  }

  public void stop() {
    if (mtMessageCache != null) {
      mtMessageCache.invalidateAll();
    }
  }

  /**
   * Handle a MO ack - will try to create a status message or will drop this ACK
   */
  Message handleMoAck(Message message, String stackName) {
    Validate.notNull(message, MESSAGE_VAR_NAME);
    Validate.notEmpty(stackName, STACK_NAME_VAR_NAME);

    logger.debug("Handling MO ACK: {}, stackName: {}", message, stackName);
    vceSplitAckModuleMetricReporter.onHandleMoAck(stackName);

    SequenceNumber sequenceNumber = getSequenceNumberOrDefault(message);
    Map<String, String> mtMessageProperties = getMtMessageIdWaitingForAck(message.getVehicleID(), sequenceNumber);
    logger.debug("Found MT message properties waiting for ack: {}", mtMessageProperties);

    if (!mtMessageProperties.isEmpty()) {
      return createMtStatusMessage(message, sequenceNumber, mtMessageProperties);
    }
    // This ACK could not be matched to a MT message for this device
    logger.error("Ack with sequence number [{}] could not be matched to MT message [vehicleId={}], will drop this ACK for message with id: {}!", sequenceNumber,
        message.getVehicleID(), message.getMessageId());
    return null;
  }

  private MessageContent createMoAckMessageContent(MessageContent messageContent) {
    try {
      MessageType messageType = messageContent.getData().get(0);
      MessageContent response = MessageUtils.createMoAckMessageContent(clock, messageType);
      return response;
    } catch (Exception e) {
      logger.error("", e);
      return null;
    }
  }

  /**
   * Find the MT-Message waiting for this ack
   */
  private Map<String, String> getMtMessageIdWaitingForAck(String vehicleId, SequenceNumber sequenceNumber) {
    Map<String, String> cacheMessage = mtMessageCache.getIfPresent(vehicleId);
    logger.trace("Retrieved cached MT message: {}", cacheMessage);

    if (cacheMessage == null || Byte.parseByte(cacheMessage.get(MetaData.SEQUENCE_NUMBER.name())) != sequenceNumber.toByte()) {
      logger.trace("Getting MT message id waiting for ACK from DB");
      return getMtMessageIdWaitingForAckFromDb(vehicleId, sequenceNumber);
    }

    mtMessageCache.invalidate(vehicleId);
    return cacheMessage;
  }

  private Map<String, String> getMtMessageIdWaitingForAckFromDb(String vehicleId, SequenceNumber sequenceNumber) {
    List<Message> messages = scheduledMessageDatabaseService.findActiveMessagesWithSequenceNumberByVehicleId(vehicleId);

    if (messages == null || messages.isEmpty()) {
      logger.warn("Can't find any active message by vehicleId:{}", vehicleId);
      return Collections.emptyMap();
    }

    logger.debug("Amount of active messages with sequence number: {}, vehicleId: {}", messages.size(), vehicleId);
    messages = messages.stream()
        .filter(message -> isSequenceNumbersEquals(message, sequenceNumber))
        .collect(Collectors.toList());

    if (messages.isEmpty()) {
      logger.trace("Couldn't find any MT message with sequenceNumber={}", sequenceNumber);
      return Collections.emptyMap();
    }

    if (messages.size() > 1) {
      logger.warn("Found multiple messages - Could not decide which mt message should be acked");
      return Collections.emptyMap();
    }

    Map<String, String> properties = messages.get(0).getProperties();
    properties.put(MessageFields.messageID.name(), messages.get(0).getMessageId());
    return properties;
  }

  /**
   * Handle MessageContent of type "data" spawning new messages if a messages are batched together
   */
  private Message handleMoData(MessageContent messageContent, Message message, String stackName) throws ASNException {
    logger.debug("Handling MO data: {}, stack: {}", message, stackName);
    vceSplitAckModuleMetricReporter.onHandleMoData(stackName);

    if (MessageUtils.isMobileOriginated(message)) {
      MessageContent response = createMoAckMessageContent(messageContent);
      if (MessageUtils.shallAck(message)) {
        logger.trace("Should ack on MO message: {}", message);
        return createMoAckMessageToVehicle(message, response);
      }
      /**
       * Need to send an Ack even if Skip_ack is present as SatModule needs to delete MO Message from OrbComm Gateway. So, we send a StatusMessage with an Ack
       */
      if (message.getProperty(MetaData.TRANSPORT_TYPE).equals(TransportType.SATELLITE.name())) {
        Message ackStatusMessage = Message.createStatusMessage(MessageStatus.ACCEPTED);
        ackStatusMessage.setMessageId(message.getMessageId());
        ackStatusMessage.setVehicleID(message.getVehicleID());
        ackStatusMessage.addProperties(message.getProperties());
        ackStatusMessage.setProperty(MetaData.ACK, "true");
        // Payload is not necessary but as each Module has a validateBeforeDown which expects a payload.
        // TODO: Change in the Future after a Design discussion
        ackStatusMessage.setPayload(
            MessageUtils.createAckPayload(response, MessageUtils.isMobileOriginated(message), getSequenceNumberOrDefault(message)));

        logger.trace("Created status message: {} to make sure MO message is deleted from OrbComm", ackStatusMessage);
        return ackStatusMessage;
      }
    }
    return null;
  }

  /** Handle MessageContent of type "ping" answer with pong if conditions are right */
  private Message handleMoPing(MessageContent messageContent, Message message, String stack) throws ASNException {
    if (MessageUtils.isMobileOriginated(message) && MessageUtils.shallAck(message) && MessageUtils.shallAnswerOnPing(messageContent)) {
      logger.debug("Handling MO ping: {}, stack: {}", message, stack);
      vceSplitAckModuleMetricReporter.onHandleMoPing(stack);

      messageContent.setPong(messageContent.getPing());
      return createMoAckMessageToVehicle(message, messageContent);
    }
    return null;
  }

  /**
   * Create and populate a transport header. Calculate and increment new seqNr in cache/database for this device. Header is added to head of old message payload
   * and a message containing the header is returned.
   */
  private Optional<Message> handleMtData(Message message, String stackName) throws ASNException {
    logger.debug("Handling MT data {}, stack: {}", message, stackName);
    vceSplitAckModuleMetricReporter.onHandleMtData(stackName);

    vceSequenceNumberService.setSequenceNumber(message);

    Optional<SequenceNumber> sequenceNumber = getSequenceNumber(message);
    if (sequenceNumber.isEmpty()) {
      logger.error("Could not process a message without SEQUENCE_NUMBER: {}", message);
      return Optional.empty();
    }

    Map<String, String> properties = message.getProperties();
    properties.put(MessageFields.messageID.name(), message.getMessageId());
    mtMessageCache.put(message.getVehicleID(), properties);

    message.setPayload(MessageUtils.createMtPayload(message, sequenceNumber.get()));
    return Optional.of(message);
  }
}
