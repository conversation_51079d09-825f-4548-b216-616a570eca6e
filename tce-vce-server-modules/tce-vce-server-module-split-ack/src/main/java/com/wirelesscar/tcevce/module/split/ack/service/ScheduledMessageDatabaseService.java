package com.wirelesscar.tcevce.module.split.ack.service;

import java.sql.SQLException;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;

import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.wirelesscar.tce.module.api.Message;
import com.wirelesscar.tce.module.api.exception.EngineRuntimeException;
import com.wirelesscar.tcevce.module.scheduler.db.impl.ScheduledMessagePersisterImpl;

public class ScheduledMessageDatabaseService {
  private final ScheduledMessagePersisterImpl scheduledMessagePersisterImpl;

  public ScheduledMessageDatabaseService(ScheduledMessagePersisterImpl scheduledMessagePersisterImpl) {
    Validate.notNull(scheduledMessagePersisterImpl, "scheduledMessagePersisterImpl");

    this.scheduledMessagePersisterImpl = scheduledMessagePersisterImpl;
  }

  public List<Message> findActiveMessagesWithSequenceNumberByVehicleId(String vehicleId) {
    Validate.notEmpty(vehicleId, "vehicleId");

    try {
      return scheduledMessagePersisterImpl.find(
          "SELECT * FROM SCHD_SCHEDULED_MESSAGE WHERE vehicleid = '" + vehicleId + "' AND status = 'a' AND properties LIKE '%SEQUENCE_NUMBER%'",
          Collections.emptyList(),
          Collections.emptySet(),
          new HashSet<>()
      );
    } catch (SQLException e) {
      throw new EngineRuntimeException("Unable to find activeMessages with sequenceNumber by vehicleId: " + vehicleId, e);
    }
  }
}
