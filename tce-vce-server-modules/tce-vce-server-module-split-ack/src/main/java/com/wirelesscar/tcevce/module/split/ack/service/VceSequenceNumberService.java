package com.wirelesscar.tcevce.module.split.ack.service;

import java.util.Optional;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.wirelesscar.tce.module.api.Message;
import com.wirelesscar.tce.module.api.MetaData;
import com.wirelesscar.tcevce.module.split.ack.MessageUtils;
import com.wirelesscar.tcevce.wecu.device.info.database.api.DeviceInfoWriter;
import com.wirelesscar.tcevce.wecu.device.info.database.api.DeviceInfoWriterFactory;
import com.wirelesscar.tcevce.wecu.device.info.database.model.DeviceInfoId;
import com.wirelesscar.tcevce.wecu.device.info.database.model.DeviceSequence;
import com.wirelesscar.tcevce.wecu.device.info.database.model.DeviceSequenceBuilder;
import com.wirelesscar.tcevce.wecu.device.info.database.model.Handle;
import com.wirelesscar.tcevce.wecu.device.info.database.model.PersistedDeviceInfo;
import com.wirelesscar.tcevce.wecu.device.info.database.model.PersistedDeviceSequence;
import com.wirelesscar.tcevce.wecu.device.info.database.model.SequenceNumber;

@Component
public class VceSequenceNumberService {
  private static final Logger logger = LoggerFactory.getLogger(VceSequenceNumberService.class);

  private final DeviceInfoWriterFactory deviceInfoWriterFactory;

  public VceSequenceNumberService(DeviceInfoWriterFactory deviceInfoWriterFactory) {
    this.deviceInfoWriterFactory = deviceInfoWriterFactory;
  }

  private static DeviceSequence createDeviceSequence(DeviceInfoId deviceInfoId, SequenceNumber sequenceNumber) {
    return new DeviceSequenceBuilder()
        .setDeviceInfoId(deviceInfoId)
        .setSequenceNumber(sequenceNumber)
        .build();
  }

  private static void setSequenceNumberAndUpdateNextSequenceNumber(final Message message, DeviceInfoId deviceInfoId, DeviceInfoWriter deviceInfoWriter) {
    Optional<PersistedDeviceSequence> persistedDeviceSequenceOptional = deviceInfoWriter.findDeviceSequenceByDeviceInfoId(deviceInfoId);

    if (persistedDeviceSequenceOptional.isEmpty()) {
      message.setProperty(MetaData.SEQUENCE_NUMBER, "0");

      DeviceSequence newDeviceSequence = createDeviceSequence(deviceInfoId, SequenceNumber.ofByte((byte) 1));
      logger.trace("Created new DeviceSequence: {}", newDeviceSequence);

      deviceInfoWriter.insertDeviceSequence(newDeviceSequence);
    } else {
      logger.debug("Found PersistedDeviceSequence: {}", persistedDeviceSequenceOptional);
      DeviceSequence deviceSequence = persistedDeviceSequenceOptional.get().getDeviceSequence();

      SequenceNumber sequenceNumber = deviceSequence.getSequenceNumber();
      message.setProperty(MetaData.SEQUENCE_NUMBER, sequenceNumber.toString());

      DeviceSequence updatedDeviceSequence = createDeviceSequence(deviceSequence.getDeviceInfoId(), sequenceNumber.next());
      logger.trace("Updated DeviceSequence: {}", updatedDeviceSequence);

      deviceInfoWriter.updateDeviceSequence(updatedDeviceSequence);
    }
  }

  public void setSequenceNumber(final Message message) {
    Validate.notNull(message, "message");

    if (StringUtils.hasText(message.getProperty(MetaData.SEQUENCE_NUMBER))) {
      logger.debug("{} already has SEQUENCE_NUMBER - will not set any new SEQUENCE_NUMBER", message);
      return;
    }

    try (DeviceInfoWriter deviceInfoWriter = deviceInfoWriterFactory.createReadCommitted()) {
      findDeviceInfoByVehicleIdOrHandle(message.getVehicleID(), deviceInfoWriter)
          .ifPresent(persistedDeviceInfo -> setSequenceNumberAndUpdateNextSequenceNumber(message, persistedDeviceInfo.getDeviceInfoId(), deviceInfoWriter));
    }
  }

  private static Optional<PersistedDeviceInfo> findDeviceInfoByVehicleIdOrHandle(String vehicleId, DeviceInfoWriter deviceInfoWriter) {
    if (MessageUtils.isVpi(vehicleId)) {
      logger.debug("Find device info by VPI: {}", vehicleId);
      return deviceInfoWriter.findDeviceInfoByVpi(Vpi.ofString(vehicleId));
    }

    if (MessageUtils.isHandle(vehicleId)) {
      logger.debug("Find device info by handle: {}", vehicleId);
      return deviceInfoWriter.findDeviceInfoByHandle(Handle.ofString(vehicleId));
    }

    return Optional.empty();
  }
}
