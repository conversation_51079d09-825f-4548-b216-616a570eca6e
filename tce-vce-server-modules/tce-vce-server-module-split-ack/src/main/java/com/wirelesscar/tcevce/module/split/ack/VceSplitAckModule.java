package com.wirelesscar.tcevce.module.split.ack;

import java.util.HexFormat;
import java.util.Map;
import java.util.Optional;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import com.volvo.tisp.framework.context.TispContext;
import com.volvo.tisp.identifier.TrackingIdentifier;
import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.wirelesscar.caretrack.dh.caretrack.protocol.ASNException;
import com.wirelesscar.caretrack.dh.caretrack.protocol.MessageContent;
import com.wirelesscar.caretrack.dh.caretrack.protocol.MessageType;
import com.wirelesscar.config.Config;
import com.wirelesscar.config.ConfigFactory;
import com.wirelesscar.tce.core.conf.TceConfig;
import com.wirelesscar.tce.core.stack.ModuleStack;
import com.wirelesscar.tce.core.stack.ModuleStackManager;
import com.wirelesscar.tce.module.api.Message;
import com.wirelesscar.tce.module.api.MetaData;
import com.wirelesscar.tce.module.api.Module;
import com.wirelesscar.tce.module.api.ModuleBase;
import com.wirelesscar.tce.module.api.ModuleTypeConstants;
import com.wirelesscar.tce.module.api.SmsDataCoding;
import com.wirelesscar.tce.module.api.exception.EngineRuntimeException;
import com.wirelesscar.tce.module.metrics.ModuleMetricReporter;
import com.wirelesscar.tce.utils.MetadataUtils;
import com.wirelesscar.tcevce.module.split.ack.metrics.VceSplitAckModuleMetricReporter;

@Component(ModuleTypeConstants.VCE_SPLIT_ACK)
@Scope("prototype")
public class VceSplitAckModule extends ModuleBase {
  private static final String SATELLITE_NAME = "satellite-stack-name";
  private static final String SMS_NAME = "sms-stack-name";
  private static final String STACK_NAME_PROPERTY = "message.server.jms.api.lookup.name";
  private static final String UDP_NAME = "udp-stack-name";
  private static final Config config = ConfigFactory.getConfig();
  private static final Logger logger = LoggerFactory.getLogger(VceSplitAckModule.class);

  private boolean initDone = false;

  @TceConfig(configKey = SATELLITE_NAME, defaultValue = "SATELLITE")
  private String satStackName = "SATELLITE";

  @TceConfig(configKey = SMS_NAME, defaultValue = "SMS")
  private String smsStackName = "SMS";

  @TceConfig(configKey = UDP_NAME, defaultValue = "UDP")
  private String udpStackName = "UDP";

  private final ModuleStackManager moduleStackManager;
  private final VceMessageHandler vceMessageHandler;
  private final VceSplitAckModuleMetricReporter vceSplitAckModuleMetricReporter;

  public VceSplitAckModule(VceSplitAckModuleMetricReporter vceSplitAckModuleMetricReporter, ModuleMetricReporter moduleMetricReporter,
      ModuleStackManager moduleStackManager, VceMessageHandler vceMessageHandler) {
    super(moduleMetricReporter);
    this.vceSplitAckModuleMetricReporter = vceSplitAckModuleMetricReporter;
    this.moduleStackManager = moduleStackManager;
    this.vceMessageHandler = vceMessageHandler;
  }

  /**
   * Creates an MT public key not supported message
   */
  private static Message createMtNotSupportedMessageToVehicle(Message message)
      throws ASNException {
    Message notSupportedMessage = Message.createMessage();
    notSupportedMessage.setMessageId(message.getMessageId());
    notSupportedMessage.setVehicleID(message.getVehicleID());
    notSupportedMessage.addProperties(message.getProperties());
    notSupportedMessage.setProperty(MetaData.ACK, Boolean.TRUE.toString());

    // Set message meta data
    notSupportedMessage.setProperty(MetaData.SMS_DATA_CODING, SmsDataCoding.BINARY.toString());

    MessageUtils.setSwappedAddress(notSupportedMessage, message);
    notSupportedMessage.setProperty(MetaData.TRANSPORT_TYPE, message.getProperty(MetaData.TRANSPORT_TYPE));

    // Create the message payload
    notSupportedMessage.setPayload(MessageUtils.createPublicKeyNotSupportedMessageContent());

    logger.trace("Created MT public key not supported message: {}", notSupportedMessage);

    return notSupportedMessage;
  }

  private static TispContext.Scope getTispContextScope(Message statusMessage) {
    return TispContext.current()
        .newScope()
        .tid(TrackingIdentifier.fromString(statusMessage.getMessageId()))
        .activate();
  }

  @Override
  public void deploy() {
    initConfig();
  }

  @Override
  public void down(Message message) {
    Validate.notNull(message, "message");

    validateState();
    try {
      logger.debug("Received message on DOWN, message: {}", message);
      byte[] payload = Optional.ofNullable(message.getPayload()).orElse(new byte[0]);
      logger.debug("stackname={}, message={}, string_payload={}, hex_payload={}", getStackName(), message, new String(payload),
          HexFormat.of().withDelimiter(" ").withUpperCase().formatHex(payload));

      Optional<Message> optionalMessage = vceMessageHandler.handleMtMessage(message, getStackName());
      if (optionalMessage.isPresent()) {
        sendMessageDown(optionalMessage.get());
      } else {
        logger.warn("Couldn't handle MT message - Not sending the message DOWN: {}", message);
      }
    } catch (ASNException e) {
      throw new EngineRuntimeException("Failed to handle MT message with id=" + message.getMessageId(), e);
    }
  }

  public String getSatStackName() {
    return satStackName;
  }

  public String getSmsStackName() {
    return smsStackName;
  }

  public String getUdpStackName() {
    return udpStackName;
  }

  @Override
  public void stop() {
    if (vceMessageHandler != null) {
      vceMessageHandler.stop();
    }
  }

  @Override
  public void up(Message message) {
    Validate.notNull(message, "message");

    validateState();
    if (message.getPayload() == null || message.getPayload().length == 0) {
      logger.warn("Payload is absent - Will not handle message: {}", message);
      return;
    }

    try {
      MessageContent messageContent = MessageUtils.getMessageContent(message.getPayload());
      boolean isMobileOriginated = MessageUtils.isMobileOriginated(message);

      logger.debug("Received message on UP, messageContent choice: {}, message: {}", messageContent.getChoice(), message);

      if (MessageUtils.isAckForMt(messageContent, isMobileOriginated)) {
        processAckForMtMessage(message, messageContent);

        if (messageContent.getChoice() == MessageContent.E_DATA) {
          sendUpMoData(message, messageContent);
        }
      } else {
        sendUpMoData(message, messageContent);
        createMoAckAndSentToVehicle(message, messageContent);
      }
    } catch (ASNException e) {
      throw new EngineRuntimeException("Failed to handle MO or Ack message: " + message, e);
    }
  }

  protected void initConfig() {
    logger.info("Initializing VCE-split-ack for stack {}", getStackName());
    initDone = true;
  }

  @Override
  protected void setOldModuleConfig(Map<String, String> oldConfigProperties) {
    oldConfigProperties.put(SMS_NAME, ConfigFactory.getConfig().getString(buildConfigKey(ModuleTypeConstants.VCE_SPLIT_ACK + "." + SMS_NAME)).orElse("SMS"));
    oldConfigProperties.put(SATELLITE_NAME,
        ConfigFactory.getConfig().getString(buildConfigKey(ModuleTypeConstants.VCE_SPLIT_ACK + "." + SATELLITE_NAME)).orElse("SATELLITE"));
  }

  /**
   * build configuration key
   */
  private String buildConfigKey(String key) {
    return new StringBuilder().append(getStackName()).append(".").append(key).toString();
  }

  private void createMoAckAndSentToVehicle(Message message, MessageContent messageContent) throws ASNException {
    Message ack = vceMessageHandler.createMoResponse(message, messageContent, getStackName());
    if (null != ack) {
      logger.debug("Shall handle ack for message with id={}, new status message: {}", message.getMessageId(), ack);
      vceSplitAckModuleMetricReporter.onProducedAck(getStackName());
      sendMessageDown(ack);
    }
  }

  /**
   * Handles ACK message for MT message. Tries to create MT status message and send it up. If no MT status message was created,
   * for {@link Message} with {@link com.wirelesscar.tce.module.api.TransportType#SATELLITE}, creates a new status {@link Message}
   * for notifying <code>Orbcomm XML Gateway</code> to delete a referred message.
   */
  private void processAckForMtMessage(Message message, MessageContent messageContent) throws ASNException {
    logger.debug("Processing ACK for MT message: {}", message);
    Message mtStatusMessage = vceMessageHandler.handleMoAck(message, getStackName());

    if (mtStatusMessage != null) {
      try (final TispContext.Scope ignored = getTispContextScope(mtStatusMessage)) {
        logger.debug("Sending UP status message: {} for MT message with id={}", mtStatusMessage, message.getMessageId());
        sendUp(mtStatusMessage);
      }
    }

    if (mtStatusMessage == null && MessageUtils.isSatelliteMessage(message)) {
      Message satelliteStatusMessage = VceMessageHandler.createSatelliteStatusMessage(message, messageContent);
      logger.debug("Notifying XML Gateway with status message: {}", satelliteStatusMessage);
      sendMessageDown(satelliteStatusMessage);
    }
  }

  /**
   * Sets {@link MetaData#STACK_ROUTING} property (required for routing to the correct module) to the input {@link Message}.
   * Then sends updated {@link Message} down.
   * <br>
   * <br>
   * <b>WARN</b>: impure
   */
  private void sendMessageDown(Message message) {
    setStackRouting(message);

    logger.debug("Sending DOWN: {}", message);
    sendDown(message);
  }

  private void sendUpMoData(Message message, MessageContent messageContent) throws ASNException {
    if (messageContent.getChoice() == MessageContent.E_DATA) {
      if (!MessageUtils.hasMetaData(message, MetaData.VEHICLE_NOT_FOUND)) {
        try {
          if (MessageUtils.isPublicKeyMessage(message)) {
            logger.warn("Unsupported public key message received, sending MT public key not supported message to vehicle: {}", message);
            Message publicKeyNotSupportedMessage = createMtNotSupportedMessageToVehicle(message);

            sendPublicKeyNotSupportedMessage(publicKeyNotSupportedMessage);
            return;
          }
        } catch (Exception e) {
          logger.error("", e);
        }

        MessageType messageType = messageContent.getData().get(0);
        message.setProperty(MetaData.VCE_SERVICE, Integer.toString(messageType.getChoice()));
        logger.debug("Sending up MO data message with id={}", message.getMessageId());
        vceSplitAckModuleMetricReporter.onMoMessageUp(getStackName());
        sendUp(message);
      } else {
        logger.warn("Unable to identify the vehicle, but an ack messages will still be send {}", message);
      }
    }
  }

  private void sendPublicKeyNotSupportedMessage(Message message) {
    String stackName = config
        .getString(STACK_NAME_PROPERTY)
        .orElseThrow(() -> new IllegalStateException("missing string config: " + STACK_NAME_PROPERTY));
    ModuleStack moduleStack = moduleStackManager.getByName(stackName);

    Module topModule = moduleStack.getTopModule();

    if (topModule != null) {
      topModule.down(message);
    }  else {
      logger.error("Message Service has wrong config top module not found: {}", stackName);
    }
  }

  /**
   * Set the dynamic routing MetaData to stack that is configured in VceSplitAckModule TRANSPORT_TYPE select which configuration to choose
   * <br>
   * <br>
   */
  private void setStackRouting(Message message) {
    switch (MetadataUtils.getTransportType(message)) {
      case SMS:
        message.setProperty(MetaData.STACK_ROUTING, getSmsStackName());
        break;
      case SATELLITE:
        message.setProperty(MetaData.STACK_ROUTING, getSatStackName());
        break;
      case UDP:
      default:
        message.setProperty(MetaData.STACK_ROUTING, getUdpStackName());
        break;
    }
    logger.debug("Routing is set to stack: {} for message with id={}", message.getProperty(MetaData.STACK_ROUTING), message.getMessageId());
  }

  private void validateState() {
    if (!initDone) {
      throw new IllegalStateException("initDone == false");
    }
  }
}
