package com.wirelesscar.tcevce.module.split.ack.metrics;

import org.springframework.stereotype.Component;

import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.wirelesscar.tce.module.api.ModuleTypeConstants;

import io.micrometer.core.instrument.MeterRegistry;

@Component
public class VceSplitAckModuleMetricReporter {
  static final String HANDLE_MO_ACK = "vcemessagehandler.handleMoAck";
  static final String HANDLE_MO_DATA = "vcemessagehandler.handleMoData";
  static final String HANDLE_MO_MESSAGE = "vcemessagehandler.handleMoMessage";
  static final String HANDLE_MO_OR_ACK = "vcemessagehandler.handleMoOrAck";
  static final String HANDLE_MO_PING = "vcemessagehandler.handleMoPing";
  static final String HANDLE_MT_DATA = "vcemessagehandler.handleMtData";
  static final String HANDLE_MT_MESSAGE = "vcemessagehandler.handleMtMessage";
  static final String MO_HANDLE_ACK = "module." + ModuleTypeConstants.VCE_SPLIT_ACK + ".mo.handleAck";
  static final String MO_MESSAGE_UP = "module." + ModuleTypeConstants.VCE_SPLIT_ACK + ".mo.messageUp";

  static final String MO_PING_HANDLED = "module." + ModuleTypeConstants.VCE_SPLIT_ACK + ".mo.pingHandled";
  static final String MO_PONG_RECEIVED = "module." + ModuleTypeConstants.VCE_SPLIT_ACK + ".mo.pongReceived";
  static final String MO_UNKNOWN_CHOICE = "module." + ModuleTypeConstants.VCE_SPLIT_ACK + ".mo.unknownChoice";
  static final String MT_HANDLE_MESSAGE = "module." + ModuleTypeConstants.VCE_SPLIT_ACK + ".mt.handleMessage";
  static final String MT_RECEIVE_ACK_EXECUTE = "module." + ModuleTypeConstants.VCE_SPLIT_ACK + ".mt.receivedAckExecute";
  static final String MT_RECEIVE_PING_EXECUTE = "module." + ModuleTypeConstants.VCE_SPLIT_ACK + ".mt.receivedPingExecute";
  static final String MT_RECEIVE_PONG_EXECUTE = "module." + ModuleTypeConstants.VCE_SPLIT_ACK + ".mt.receivedPongExecute";
  static final String MT_UNKNOWN_CHOICE = "module." + ModuleTypeConstants.VCE_SPLIT_ACK + ".mt.unknownChoice";
  static final String PRODUCED_ACK = "module." + ModuleTypeConstants.VCE_SPLIT_ACK + ".producedAck";
  static final String PROPS_FOUND_IN_CACHE = "vcemessagehandler.props-found-in-cache";
  static final String PROPS_NOT_FOUND_IN_CACHE = "vcemessagehandler.props-NOT-found-in-cache";

  static final String STACK_VAR_NAME = "stackName";

  private final MeterRegistry meterRegistry;

  public VceSplitAckModuleMetricReporter(MeterRegistry meterRegistry) {
    this.meterRegistry = meterRegistry;
  }

  public void onHandleMoAck(String stackName) {
    Validate.notEmpty(stackName, STACK_VAR_NAME);

    meterRegistry.counter(HANDLE_MO_ACK, STACK_VAR_NAME, stackName).increment();
  }

  public void onHandleMoData(String stackName) {
    Validate.notEmpty(stackName, STACK_VAR_NAME);

    meterRegistry.counter(HANDLE_MO_DATA, STACK_VAR_NAME, stackName).increment();
  }

  public void onHandleMoMessage(String stackName) {
    Validate.notEmpty(stackName, STACK_VAR_NAME);

    meterRegistry.counter(HANDLE_MO_MESSAGE, STACK_VAR_NAME, stackName).increment();
  }

  public void onHandleMoOrAck(String stackName) {
    Validate.notEmpty(stackName, STACK_VAR_NAME);

    meterRegistry.counter(HANDLE_MO_OR_ACK, STACK_VAR_NAME, stackName).increment();
  }

  public void onHandleMoPing(String stackName) {
    Validate.notEmpty(stackName, STACK_VAR_NAME);

    meterRegistry.counter(HANDLE_MO_PING, STACK_VAR_NAME, stackName).increment();
  }

  public void onHandleMtData(String stackName) {
    Validate.notEmpty(stackName, STACK_VAR_NAME);

    meterRegistry.counter(HANDLE_MT_DATA, STACK_VAR_NAME, stackName).increment();
  }

  public void onHandleMtMessage(String stackName) {
    Validate.notEmpty(stackName, STACK_VAR_NAME);

    meterRegistry.counter(HANDLE_MT_MESSAGE, STACK_VAR_NAME, stackName).increment();
  }

  public void onMoHandleAck(String stackName) {
    Validate.notEmpty(stackName, STACK_VAR_NAME);

    meterRegistry.counter(MO_HANDLE_ACK, STACK_VAR_NAME, stackName).increment();
  }

  public void onMoMessageUp(String stackName) {
    Validate.notEmpty(stackName, STACK_VAR_NAME);

    meterRegistry.counter(MO_MESSAGE_UP, STACK_VAR_NAME, stackName).increment();
  }

  public void onMoPingHandled(String stackName) {
    Validate.notEmpty(stackName, STACK_VAR_NAME);

    meterRegistry.counter(MO_PING_HANDLED, STACK_VAR_NAME, stackName).increment();
  }

  public void onMoPongReceived(String stackName) {
    Validate.notEmpty(stackName, STACK_VAR_NAME);

    meterRegistry.counter(MO_PONG_RECEIVED, STACK_VAR_NAME, stackName).increment();
  }

  public void onMoUnknownChoice(String stackName) {
    Validate.notEmpty(stackName, STACK_VAR_NAME);

    meterRegistry.counter(MO_UNKNOWN_CHOICE, STACK_VAR_NAME, stackName).increment();
  }

  public void onMtHandleMessage(String stackName) {
    Validate.notEmpty(stackName, STACK_VAR_NAME);

    meterRegistry.counter(MT_HANDLE_MESSAGE, STACK_VAR_NAME, stackName).increment();
  }

  public void onMtReceivedAckExecute(String stackName) {
    Validate.notEmpty(stackName, STACK_VAR_NAME);

    meterRegistry.counter(MT_RECEIVE_ACK_EXECUTE, STACK_VAR_NAME, stackName).increment();
  }

  public void onMtReceivedPingExecute(String stackName) {
    Validate.notEmpty(stackName, STACK_VAR_NAME);

    meterRegistry.counter(MT_RECEIVE_PING_EXECUTE, STACK_VAR_NAME, stackName).increment();
  }

  public void onMtReceivedPongExecute(String stackName) {
    Validate.notEmpty(stackName, STACK_VAR_NAME);

    meterRegistry.counter(MT_RECEIVE_PONG_EXECUTE, STACK_VAR_NAME, stackName).increment();
  }

  public void onMtUnknownChoice(String stackName) {
    Validate.notEmpty(stackName, STACK_VAR_NAME);

    meterRegistry.counter(MT_UNKNOWN_CHOICE, STACK_VAR_NAME, stackName).increment();
  }

  public void onProducedAck(String stackName) {
    Validate.notEmpty(stackName, STACK_VAR_NAME);

    meterRegistry.counter(PRODUCED_ACK, STACK_VAR_NAME, stackName).increment();
  }

  public void onPropsFoundInCache(String stackName) {
    Validate.notEmpty(stackName, STACK_VAR_NAME);

    meterRegistry.counter(PROPS_FOUND_IN_CACHE, STACK_VAR_NAME, stackName).increment();
  }

  public void onPropsNotFoundInCache(String stackName) {
    Validate.notEmpty(stackName, STACK_VAR_NAME);

    meterRegistry.counter(PROPS_NOT_FOUND_IN_CACHE, STACK_VAR_NAME, stackName).increment();
  }
}
