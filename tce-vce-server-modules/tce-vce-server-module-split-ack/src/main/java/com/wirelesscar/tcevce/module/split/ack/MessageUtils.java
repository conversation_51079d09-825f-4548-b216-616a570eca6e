package com.wirelesscar.tcevce.module.split.ack;

import java.time.Clock;
import java.time.Duration;
import java.time.Instant;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;

import com.google.common.primitives.Bytes;
import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.wirelesscar.caretrack.dh.caretrack.protocol.ASNException;
import com.wirelesscar.caretrack.dh.caretrack.protocol.MessageContent;
import com.wirelesscar.caretrack.dh.caretrack.protocol.MessageType;
import com.wirelesscar.caretrack.dh.caretrack.protocol.PERStream;
import com.wirelesscar.caretrack.dh.caretrack.protocol.TransportHeader;
import com.wirelesscar.tce.module.api.Message;
import com.wirelesscar.tce.module.api.MetaData;
import com.wirelesscar.tce.module.api.TransportType;
import com.wirelesscar.tce.utils.MetadataUtils;
import com.wirelesscar.tcevce.wecu.device.info.database.model.SequenceNumber;

public final class MessageUtils {
  static final String CLOCK_VAR_NAME = "clock";
  static final String MESSAGE_TYPE_VAR_NAME = "messageType";
  static final String SEQUENCE_NUMBER_VAR_NAME = "sequenceNumber";
  static final Instant TIME_OFFSET_2000_01_01 = Instant.parse("2000-01-01T00:00:00Z");
  private static final String MESSAGE_CONTENT_VAR_NAME = "messageContent";
  private static final String MESSAGE_VAR_NAME = "message";
  private static final Logger logger = LoggerFactory.getLogger(MessageUtils.class);

  private MessageUtils() {
    throw new IllegalStateException();
  }

  public static boolean isHandle(String handle) {
    if (handle == null || handle.length() <= 1 || handle.length() >= 200) {
      return false;
    }

    return true;
  }

  public static boolean isVpi(String vpi) {
    if (vpi.length() != 32 || vpi == null) {
      return false;
    }

    for (int i = 0; i < 32; ++i) {
      if (!isUpperCaseHexChar(vpi.charAt(i))) {
        return false;
      }
    }

    return true;
  }

  static Duration convertToCommTime(Instant instant) {
    return Duration.between(TIME_OFFSET_2000_01_01, instant);
  }

  /**
   * Encodes and loads the content of an ack in a byte array
   */
  static byte[] createAckPayload(MessageContent messageContent, boolean isMobileOriginated, SequenceNumber sequenceNumber) throws ASNException {
    Validate.notNull(messageContent, MESSAGE_CONTENT_VAR_NAME);
    Validate.notNull(sequenceNumber, SEQUENCE_NUMBER_VAR_NAME);

    // Create the Transport Header
    TransportHeader transportHeader = createTransportHeader(sequenceNumber, isMobileOriginated);

    int headerEncodedSize = (int) transportHeader.encodedSize() / 8 + 1;
    int contentEncodedSize = (int) messageContent.encodedSize() / 8 + 1;
    PERStream perStream = new PERStream((headerEncodedSize + contentEncodedSize));
    transportHeader.encode(perStream);
    perStream.alignOnByte();
    messageContent.encode(perStream);
    return perStream.getBuffer();
  }

  static MessageContent createEmptyMoAckMessageContent() throws ASNException {
    MessageContent messageContent = new MessageContent();
    // Always set ack on mo
    messageContent.setAck();
    return messageContent;
  }

  static MessageContent createMoAckMessageContent(Clock clock, MessageType messageType) throws ASNException {
    Validate.notNull(clock, CLOCK_VAR_NAME);
    Validate.notNull(messageType, MESSAGE_TYPE_VAR_NAME);

    switch (messageType.getChoice()) {
      case MessageType.E_UTCTIMEREQUEST:
        logger.trace("Reveived choice E_UTCTIMEREQUEST with ackWithBody true");
        return createUtcTimeResponseMessageContent(clock);
      case MessageType.E_TIMEREQUEST:
        logger.trace("Reveived choice E_TIMEREQUEST with ackWithBody true");
        return createTimeResponseMessageContent(clock, messageType);
      default:
        return createEmptyMoAckMessageContent();
    }
  }

  static byte[] createMtPayload(Message message, SequenceNumber sequenceNumber) throws ASNException {
    Validate.notNull(message, MESSAGE_VAR_NAME);
    Validate.notNull(sequenceNumber, SEQUENCE_NUMBER_VAR_NAME);

    TransportHeader transportHeader = createTransportHeader(sequenceNumber, false);

    // Create a new encoded header
    PERStream headerStream = new PERStream((int) transportHeader.encodedSize());
    headerStream.startEncoding();
    transportHeader.encode(headerStream);
    headerStream.alignOnByte();
    byte[] headerBuffer = headerStream.getBuffer();

    // New payload consists of the new header and the old message
    byte[] oldPayload = message.getPayload();
    return Bytes.concat(headerBuffer, oldPayload);
  }

  static MessageContent createTimeResponseMessageContent(Clock clock, MessageType messageType) throws ASNException {
    Validate.notNull(messageType, MESSAGE_TYPE_VAR_NAME);
    Validate.notNull(clock, CLOCK_VAR_NAME);

    MessageContent messageContent = new MessageContent();
    // Always set ack on mo
    messageContent.setAck();
    messageContent.setData();
    messageContent.getData().setSize(1);
    messageContent.getData().getArrayItem(0).setTimeResponse();
    messageContent
        .getData()
        .getArrayItem(0)
        .getTimeResponse()
        .setCurrentTime(convertToCommTime(clock.instant()).toSeconds());
    messageContent
        .getData()
        .getArrayItem(0)
        .getTimeResponse()
        .setRequestTime(messageType.getTimeRequest().getTimeStamp());

    return messageContent;
  }

  static TransportHeader createTransportHeader(SequenceNumber sequenceNumber, boolean isMobileOriginated) throws ASNException {
    Validate.notNull(sequenceNumber, SEQUENCE_NUMBER_VAR_NAME);

    TransportHeader transportHeader = new TransportHeader();
    transportHeader.setSequenceNumber(sequenceNumber.toByte());
    transportHeader.setSkipAck(false);
    transportHeader.setEncrypted(false);
    transportHeader.setMobileOriginated(isMobileOriginated);
    transportHeader.getMessageCount().setStandard();
    transportHeader.getMessageCount().getStandard().setIndexOfCurrentPacket(0);
    transportHeader.getMessageCount().getStandard().setTotalNumberOfPackets(1);
    return transportHeader;
  }

  // Only do this once for the first found
  static MessageContent createUtcTimeResponseMessageContent(Clock clock) throws ASNException {
    Validate.notNull(clock, CLOCK_VAR_NAME);

    MessageContent messageContent = new MessageContent();
    // Always set ack on mo
    messageContent.setAck();
    messageContent.setData();
    messageContent.getData().setSize(1);
    messageContent.getData().getArrayItem(0).setUtcTimeResponse();
    messageContent
        .getData()
        .getArrayItem(0)
        .getUtcTimeResponse()
        .setCurrentTime(convertToCommTime(clock.instant()).toSeconds());
    return messageContent;
  }

  static MessageContent getMessageContent(byte[] payload) throws ASNException {
    Validate.notNull(payload, "payload");

    PERStream perStream = new PERStream(payload);

    MessageContent messageContent = new MessageContent();
    messageContent.decode(perStream);

    return messageContent;
  }

  static boolean hasMetaData(Message message, MetaData metaData) {
    Validate.notNull(message, MESSAGE_VAR_NAME);
    Validate.notNull(metaData, "metaData");

    return StringUtils.hasText(message.getProperty(metaData));
  }

  static boolean isPublicKeyMessage(Message message) throws ASNException {
    MessageContent messageContent = getMessageContent(message.getPayload());

    return messageContent.getData().get(0).getChoice() == MessageType.E_PUBLICKEY1;
  }

  static byte[] createPayload(MessageContent messageContent) throws ASNException {
    Validate.notNull(messageContent, MESSAGE_CONTENT_VAR_NAME);

    int contentEncodedSize = (int) messageContent.encodedSize() / 8 + 1;
    PERStream perStream = new PERStream((contentEncodedSize));
    perStream.alignOnByte();
    messageContent.encode(perStream);
    return perStream.getBuffer();
  }

  static byte[] createPublicKeyNotSupportedMessageContent() throws ASNException {
    MessageContent messageContent = new MessageContent();
    messageContent.setData();
    messageContent.getData().setSize(1);
    MessageType messageType = messageContent.getData().getArrayItem(0);
    messageType.setPublicKey1();
    messageType.getPublicKey1().setNotSupported();

    return createPayload(messageContent);
  }

  static boolean isAckForMt(MessageContent messageContent, boolean isMobileOriginated) {
    Validate.notNull(messageContent, MESSAGE_CONTENT_VAR_NAME);

    switch (messageContent.getChoice()) {
      case MessageContent.E_DATA:
        return !isMobileOriginated;
      case MessageContent.E_ACK:
        return true;
      case MessageContent.E_PING:
      case MessageContent.E_PONG:
      default:
        return false;
    }
  }

  /**
   * Checks if a message has the MOBILE_ORIGINATED MetaData set
   */
  static boolean isMobileOriginated(Message message) {
    Validate.notNull(message, MESSAGE_VAR_NAME);

    return Boolean.parseBoolean(message.getProperty(MetaData.MOBILE_ORIGINATED));
  }

  static boolean isSatelliteMessage(Message message) {
    Validate.notNull(message, MESSAGE_VAR_NAME);

    return TransportType.SATELLITE == MetadataUtils.getTransportType(message);
  }

  static void setSwappedAddress(Message swappedAddressMessage, Message message) {
    Validate.notNull(swappedAddressMessage, "swappedAddressMessage");
    Validate.notNull(message, MESSAGE_VAR_NAME);

    switch (MetadataUtils.getTransportType(message)) {
      case SMS:
        swappedAddressMessage.setProperty(MetaData.SMPP_SOURCE_ADDRESS, message.getProperty(MetaData.SMPP_DEST_ADDRESS));
        swappedAddressMessage.setProperty(MetaData.SMPP_DEST_ADDRESS, message.getProperty(MetaData.SMPP_SOURCE_ADDRESS));
        break;

      case SATELLITE:
        swappedAddressMessage.setProperty(MetaData.SATELLITE_SOURCE_ADDRESS, message.getProperty(MetaData.SATELLITE_DEST_ADDRESS));
        swappedAddressMessage.setProperty(MetaData.SATELLITE_DEST_ADDRESS, message.getProperty(MetaData.SATELLITE_SOURCE_ADDRESS));
        break;

      case UDP:
      default:
        swappedAddressMessage.setProperty(MetaData.IP_SRC_ADDRESS, message.getProperty(MetaData.IP_DST_ADDRESS));
        swappedAddressMessage.setProperty(MetaData.IP_DST_ADDRESS, message.getProperty(MetaData.IP_SRC_ADDRESS));
        swappedAddressMessage.setProperty(MetaData.IP_SRC_PORT, message.getProperty(MetaData.IP_DST_PORT));
        swappedAddressMessage.setProperty(MetaData.IP_DST_PORT, message.getProperty(MetaData.IP_SRC_PORT));
        break;
    }
  }

  /**
   * Checks if a message has the SKIP_ACK MetaData set
   */
  static boolean shallAck(Message message) {
    Validate.notNull(message, MESSAGE_VAR_NAME);

    return !Boolean.parseBoolean(message.getProperty(MetaData.SKIP_ACK));
  }

  static boolean shallAnswerOnPing(MessageContent messageContent) throws ASNException {
    Validate.notNull(messageContent, MESSAGE_CONTENT_VAR_NAME);

    return !(messageContent.getPing().length < 100 && messageContent.getPingAsString().startsWith("Log:"));
  }

  private static boolean isUpperCaseHexChar(char charValue) {
    return (charValue >= '0' && charValue <= '9') || (charValue >= 'A' && charValue <= 'F');
  }
}
