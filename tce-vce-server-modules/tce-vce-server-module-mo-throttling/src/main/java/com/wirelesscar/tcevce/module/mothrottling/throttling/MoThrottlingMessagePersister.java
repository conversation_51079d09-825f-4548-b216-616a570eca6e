package com.wirelesscar.tcevce.module.mothrottling.throttling;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Locale;
import java.util.concurrent.BlockingQueue;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.wirelesscar.tce.db.common.api.ActivityStatus;
import com.wirelesscar.tce.db.common.db.api.MessageFields;
import com.wirelesscar.tce.db.common.db.api.MessagePersister;
import com.wirelesscar.tce.db.common.db.api.TableName;
import com.wirelesscar.tce.db.standard.sql.impl.AbstractSqlMessagePersister;
import com.wirelesscar.tce.db.standard.sql.impl.SqlPersistanceFactory;
import com.wirelesscar.tce.module.api.Message;

/** Implementation of {@link MessagePersister} for {@link MoThrottlingModule} */
@Component
public class MoThrottlingMessagePersister extends AbstractSqlMessagePersister {
  private static final Logger LOG = LoggerFactory.getLogger(MoThrottlingMessagePersister.class);
  private static final String SQL = String.format(Locale.ENGLISH, "SELECT * FROM %s WHERE %s = '%s' AND %s < ? AND %s BETWEEN ? AND ?",
      TableName.PRST_PERSIST_MESSAGE, MessageFields.status, ActivityStatus.a, MessageFields.nextCheckTime, MessageFields.hashID);

  private boolean isReadyForNextBatch;
  private PreparedStatement preparedStatement;
  private ResultSet resultSet;
  private final ThreadLocal<Connection> threadConnection;

  public MoThrottlingMessagePersister() {
    super(TableName.PRST_PERSIST_MESSAGE.name());
    isReadyForNextBatch = true;
    threadConnection = new ThreadLocal<>();
    preparedStatement = null;
    resultSet = null;
  }

  /**
   * Boilerplate code for closing JDBC resources that implement {@link AutoCloseable}
   *
   * @param closeable {@link AutoCloseable} JDBC resource
   */
  private static void closeClosable(final AutoCloseable closeable) {
    if (closeable != null) {
      try {
        closeable.close();
      } catch (final Exception e) {
        LOG.error("Exception while closing JDBC resource.", e);
      }
    }
  }

  /**
   * A stupid workaround to prevent multiple threads working on same connection while still maintaining single {@link MessagePersister} instance
   */
  @Override
  protected final void closeMyConnection() {
    final Connection connection = threadConnection.get();
    threadConnection.remove();
    closeClosable(connection);
  }

  /**
   * A stupid workaround to prevent multiple threads working on same connection while still maintaining single {@link MessagePersister} instance
   */
  @Override
  protected final Connection getConnection() {
    Connection connection = threadConnection.get();
    try {
      if (connection == null || connection.isClosed()) {
        connection = SqlPersistanceFactory.getConnection();
        threadConnection.set(connection);
      }
    } catch (final SQLException e) {
      LOG.error("Exception while checking if JDBC connection is closed", e);
    }
    return connection;
  }

  @Override
  protected String getHintIndxedName() {
    return "IX_PRSST_MSSG_STTS_LOCAL";
  }

  @Override
  protected Logger getLogger() {
    return LOG;
  }

  /**
   * Populates specified queue with values from the database
   *
   * @param queue a "shadow" queue of {@link MoThrottlingRunnable#getLowPriorityQueue}
   * @param minimumMessageAgeForLowPriorityQueueInMiliseconds minimum age of the {@link Message} to be eligible for inclusion to
   *        {@link MoThrottlingRunnable#getLowPriorityQueue}
   * @throws InterruptedException when thread gets interrupted
   */
  void populateShadowLowPriorityQueue(final BlockingQueue<Message> queue, final long minimumMessageAgeForLowPriorityQueueInMiliseconds)
      throws InterruptedException {
    final int queueCapacity = queue.remainingCapacity();
    try {
      if (isReadyForNextBatch) {
        final Connection connection = getConnection();
        connection.setReadOnly(true);
        connection.setAutoCommit(false);
        connection.setTransactionIsolation(Connection.TRANSACTION_READ_COMMITTED);
        preparedStatement = connection.prepareStatement(SQL, ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY);
        preparedStatement.setFetchSize(queueCapacity);
        populatePreparedStatement(minimumMessageAgeForLowPriorityQueueInMiliseconds);
        resultSet = preparedStatement.executeQuery();
        isReadyForNextBatch = false;
      }

      int count = 0;
      if (resultSet != null) {
        resultSet.setFetchSize(queueCapacity);
        while (count < queueCapacity
            && resultSet.next()
            && queue.offer(populateObject(resultSet))) {
          count++;
        }
        if (count < queueCapacity) {
          releaseDatabaseResources();
        }
      } else {
        Thread.sleep(minimumMessageAgeForLowPriorityQueueInMiliseconds);
        isReadyForNextBatch = true;
      }

    } catch (final SQLException e) {
      releaseDatabaseResources();
      LOG.error("Exception while populating low priority queue", e);
    } catch (final InterruptedException e) {
      releaseDatabaseResources();
      throw e;
    }
  }

  /** Convenience method to free up database resources. */
  void releaseDatabaseResources() {
    closeClosable(resultSet);
    closeClosable(preparedStatement);
    closeMyConnection();
    resultSet = null;
    preparedStatement = null;
  }

  /**
   * Fills specified prepared statement with values for populating {@link MoThrottlingRunnable#getLowPriorityQueue}
   *
   * @param minimumMessageAgeForLowPriorityQueueInMiliseconds minimum age of the {@link Message} to be eligible for inclusion to
   *        {@link MoThrottlingRunnable#getLowPriorityQueue}
   * @throws SQLException if unexpected error occurs
   */
  private void populatePreparedStatement(final long minimumMessageAgeForLowPriorityQueueInMiliseconds) throws SQLException {
    preparedStatement.setLong(1, System.currentTimeMillis() - minimumMessageAgeForLowPriorityQueueInMiliseconds);
    preparedStatement.setInt(2, getLowerHash());
    preparedStatement.setInt(3, getUpperHash());
  }
}
