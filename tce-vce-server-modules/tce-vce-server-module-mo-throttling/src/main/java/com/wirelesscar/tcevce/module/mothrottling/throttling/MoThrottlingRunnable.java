package com.wirelesscar.tcevce.module.mothrottling.throttling;

import java.sql.SQLException;
import java.time.Duration;
import java.util.Queue;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.concurrent.locks.Condition;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

import org.apache.commons.lang3.concurrent.TimedSemaphore;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.volvo.tisp.framework.context.TispContext;
import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.wirelesscar.tce.db.common.api.ActivityStatus;
import com.wirelesscar.tce.db.common.db.api.MessageFields;
import com.wirelesscar.tce.module.api.Message;
import com.wirelesscar.tce.utils.LoggingHelper;
import com.wirelesscar.tcevce.module.mothrottling.metrics.ThrottlingMetricReporter;

/**
 * Runnable that does the throttling of messages that are both sent up the stack and read from the database.
 */
public class MoThrottlingRunnable implements Runnable {
  private static final Logger LOG = LoggerFactory.getLogger(MoThrottlingRunnable.class);
  /**
   * This constant defines resolution of throttling algorithm used below. The value of <b>1</b> would behave just like Guava RateLimiter. This means that
   * message throttling speed average will be equal to specified <b>transactionsPerSecond</b> value, but will allow peaks in throttling that might max out to
   * 100% (<b>2xtransactionsPerSecond</b>). Specifying the value equal to <b>transactionsPerSecond</b> will ensure that peaks will max out to
   * <b>transactionsPerSecond</b>, but the throttling average will be around <b>transactionsPerSecond/2</b>. Value of 8 means that peaks of 12.5% are allowed
   * giving throttling average of 60-85% of specified <b>transactionsPerSecond</b>. Value of 10 means that peaks of 10% are allowed giving throttling average of
   * 60-90% of specified <b>transactionsPerSecond</b>
   */
  private static final int THROTTLING_RESOLUTION = 10;

  /**
   * used to temporary stop acknowledging messages in DB. Must me coordinated with not posting to AMQ.
   */
  private volatile boolean ackInDb = true;

  private final BlockingQueue<Message> highPriorityQueue;
  private final Condition lowPriorityQueueEmptyCondition;

  private final AtomicReference<BlockingQueue<Message>> lowPriorityQueueReference;
  private final Lock lowPriorityQueueStatusLock;

  private final long maximumMessageAgeForHighPriorityQueueInMiliseconds;
  private final MoThrottlingMessagePersister messagePersister;
  private final MoThrottlingModule module;

  private final TimedSemaphore rateLimiter;
  private final ThrottlingMetricReporter throttlingMetricReporter;
  private final long waitTimeForHighPriorityQueueInMicroseconds;

  /**
   * @param module reference to {@link MoThrottlingModule}
   * @param moThrottlingMessagePersister reference to {@link MoThrottlingMessagePersister}
   * @param transactionsPerSecond specifies maximum allowed transaction speed.
   * @param bufferSizeInSeconds specifies how big the {@link #getHighPriorityQueue} should be in
   */
  public MoThrottlingRunnable(final MoThrottlingModule module, final MoThrottlingMessagePersister moThrottlingMessagePersister,
      ThrottlingMetricReporter throttlingMetricReporter, final int transactionsPerSecond, final int bufferSizeInSeconds) {
    Validate.isPositive(transactionsPerSecond, "transactionsPerSecond");
    Validate.isPositive(bufferSizeInSeconds, "bufferSizeInSeconds");

    this.module = module;
    messagePersister = moThrottlingMessagePersister;
    this.throttlingMetricReporter = throttlingMetricReporter;

    final int queueSize = bufferSizeInSeconds * transactionsPerSecond;
    highPriorityQueue = new ArrayBlockingQueue<>(queueSize);
    lowPriorityQueueReference = new AtomicReference<>(new ArrayBlockingQueue<>(queueSize));
    rateLimiter = new TimedSemaphore(1_000_000L / THROTTLING_RESOLUTION, TimeUnit.MICROSECONDS, transactionsPerSecond / THROTTLING_RESOLUTION);

    lowPriorityQueueStatusLock = new ReentrantLock();
    lowPriorityQueueEmptyCondition = lowPriorityQueueStatusLock.newCondition();

    maximumMessageAgeForHighPriorityQueueInMiliseconds = TimeUnit.SECONDS.toMillis(bufferSizeInSeconds);
    waitTimeForHighPriorityQueueInMicroseconds = 1_000_000L / transactionsPerSecond * THROTTLING_RESOLUTION;

    LOG.info("{} is configured for: {} (transactions/second).", MoThrottlingRunnable.class.getSimpleName(), transactionsPerSecond);
  }

  @Override
  public void run() {
    LOG.info("Thread [{}] started.", Thread.currentThread().getName());
    String stackName = module.getStackName();
    while (!Thread.currentThread().isInterrupted()) {
      final long transactionStartNanoseconds = System.nanoTime();
      try {
        final Message message = retrieveMessageFromQueue();
        rateLimiter.acquire();

        TispContext.runInContext(() -> {
          long timestamp = System.nanoTime();
          module.sendMessageUp(message);
          long timeInNanos = System.nanoTime() - timestamp;
          throttlingMetricReporter.logSendUp(stackName, Duration.ofNanos(timeInNanos));
          timestamp = System.nanoTime();
          persistMessageAsDelivered(message);
          long statusUpdateTime = System.nanoTime() - timestamp;
          throttlingMetricReporter.logStatusUpdate(stackName, Duration.ofNanos(statusUpdateTime));
        }, builder -> LoggingHelper.populateContextWithMessage(builder, message));
      } catch (final InterruptedException e) {
        Thread.currentThread().interrupt();
        LOG.debug("", e);
      } catch (final RuntimeException e) {
        LOG.error("Exception in thread [{}].", Thread.currentThread().getName(), e);
      } finally {
        long timeInNanos = System.nanoTime() - transactionStartNanoseconds;
        throttlingMetricReporter.logTransaction(stackName, Duration.ofNanos(timeInNanos));
      }
    }
    LOG.info("Thread [{}] stopped.", Thread.currentThread().getName());
  }

  /**
   * Places a {@link Message} to the tail of {@link #getHighPriorityQueue()}. If queue is full,* removes one message from the head until there is enouth space
   * to put {@link Message} to the queue.
   *
   * @param message {@link Message} to be placed at the tail of {@link #getHighPriorityQueue()}
   */
  void addToHighPriorityQueue(final Message message) {
    while (!highPriorityQueue.offer(message)) {
      highPriorityQueue.poll();
    }
  }

  /**
   * @return {@link BlockingQueue} containing real time messages
   */
  BlockingQueue<Message> getHighPriorityQueue() {
    return highPriorityQueue;
  }

  /**
   * @return {@link BlockingQueue} containing subset of older messages from database
   */
  BlockingQueue<Message> getLowPriorityQueue() {
    return lowPriorityQueueReference.get();
  }

  /**
   * @return {@link Condition} that receives a notification when {@link MoThrottlingRunnable#lowPriorityQueueReference} becomes empty
   */
  Condition getLowPriorityQueueEmptyCondition() {
    return lowPriorityQueueEmptyCondition;
  }

  /**
   * @return {@link Lock} to synchronizing waiting for <b>queueEmptyCondition</b> to happen
   */
  Lock getLowPriorityQueueStatusLock() {
    return lowPriorityQueueStatusLock;
  }

  /**
   * @return {@link MoThrottlingMessagePersister}
   */
  MoThrottlingMessagePersister getMessagePersister() {
    return messagePersister;
  }

  /**
   * @return reference to {@link TimedSemaphore}
   */
  TimedSemaphore getRateLimiter() {
    return rateLimiter;
  }

  /**
   * Method to check if {@link Queue} inside {@link #lowPriorityQueueReference} is empty. Used in a thread loop to handle "spurious wakeups".
   *
   * @return - if {@link Queue} inside {@link #lowPriorityQueueReference} is empty
   */
  boolean islowPriorityQueueEmpty() {
    return lowPriorityQueueReference.get().isEmpty();
  }

  /**
   * Sets new throttling limit
   *
   * @param transactionsPerSecond specifies maximum allowed transaction speed
   */
  void setTransactionsPerSecond(final int transactionsPerSecond) {
    rateLimiter.setLimit(transactionsPerSecond / THROTTLING_RESOLUTION);
  }

  /**
   * Swaps the value of {@link Queue} inside {@link #lowPriorityQueueReference} with a {@link Queue} specified in the parameter.
   *
   * @param queue a {@link Queue} to replace {@link Queue} inside {@link #lowPriorityQueueReference} with.
   * @return - value of {@link Queue} inside {@link #lowPriorityQueueReference} before swapping.
   */
  BlockingQueue<Message> swapLowPriorityQueue(final BlockingQueue<Message> queue) {
    throttlingMetricReporter.onLowQueue(module.getStackName());
    return lowPriorityQueueReference.getAndSet(queue);
  }

  /**
   * Notifies {@link MoLowPriorityQueueLoaderRunnable} that {@link MoThrottlingRunnable#getLowPriorityQueue} is empty
   */
  private void notifyLowPriorityQueueLoaderToLoadMoreMessages() {
    lowPriorityQueueStatusLock.lock();
    try {
      lowPriorityQueueEmptyCondition.signal();
    } finally {
      lowPriorityQueueStatusLock.unlock();
    }
  }

  /**
   * Persists delivery status as 'delivered' for specified message in database.
   *
   * @param message message that needs it's delivery status persisted.
   */
  private void persistMessageAsDelivered(final Message message) {
    if (ackInDb) {
      message.setProperty(MessageFields.status.name(), ActivityStatus.d.name());
      try {
        if (module.isDeleteAfterPublish()) {
          messagePersister.delete(message);
        } else {
          messagePersister.updateStatusAndProps(message);
        }
      } catch (final SQLException e) {
        LOG.error("Exception while persisting message as delivered", e);
      }
    } else {
      LOG.warn("Property 'ackInDb' is 'false'. Status of successfull delivery for message '{}' will not be persisted in database.", message.getMessageId());
    }
  }

  /**
   * Returns {@link Message} from {@link MoThrottlingRunnable#getHighPriorityQueue}. If the {@link MoThrottlingRunnable#getHighPriorityQueue} is empty then
   * returns a {@link Message} from {@link MoThrottlingRunnable#getLowPriorityQueue}. If the {@link MoThrottlingRunnable#getLowPriorityQueue} is empty then
   * notifies {@link MoLowPriorityQueueLoaderRunnable} and tries to load from {@link MoThrottlingRunnable#getHighPriorityQueue} again.
   *
   * @return - {@link Message}
   * @throws InterruptedException when thread is interrupted.
   */
  private Message retrieveMessageFromQueue() throws InterruptedException {
    long nextCheckTime = Long.MIN_VALUE;
    Message message = null;

    while (nextCheckTime < System.currentTimeMillis() - maximumMessageAgeForHighPriorityQueueInMiliseconds) {
      message = highPriorityQueue.poll();
      if (message != null) {
        final String nextCheckTimeString = message.getProperty(MessageFields.nextCheckTime.name());
        if (nextCheckTimeString != null) {
          nextCheckTime = Long.parseLong(nextCheckTimeString);
        } else {
          break;
        }
      } else {
        break;
      }
    }

    while (message == null) {
      message = lowPriorityQueueReference.get().poll();
      if (message == null) {
        notifyLowPriorityQueueLoaderToLoadMoreMessages();
        message = highPriorityQueue.poll(waitTimeForHighPriorityQueueInMicroseconds, TimeUnit.MICROSECONDS);
      }
    }

    return message;
  }
}
