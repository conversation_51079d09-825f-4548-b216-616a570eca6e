package com.wirelesscar.tcevce.module.mothrottling.throttling;

import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.TimeUnit;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.wirelesscar.tce.module.api.Message;

/**
 * Runnable that is responsible for keeping {@link MoThrottlingRunnable#getLowPriorityQueue} loaded with messages. It does so by preloading a "shadow"
 * {@link BlockingQueue} from database and swapping it with {@link MoThrottlingRunnable#getLowPriorityQueue} when it becomes empty.
 */
public class MoLowPriorityQueueLoaderRunnable implements Runnable {
  private static final Logger LOG = LoggerFactory.getLogger(MoLowPriorityQueueLoaderRunnable.class);

  /**
   * {@link #MESSAGE_TOO_OLD_MULTIPLIER} is used to calculate the time window after witch the message is considered too old and can be loaded to low priority
   * queue. It is calculated by multiplying {@code MoThrottlingModule#BUFFER_SIZE_IN_SECONDS} with {@link #MESSAGE_TOO_OLD_MULTIPLIER}
   */
  private static final long MESSAGE_TOO_OLD_MULTIPLIER = 2;

  private final long minimumMessageAgeForQueueInMiliseconds;
  private BlockingQueue<Message> shadowLowPriorityQueue;
  private final MoThrottlingRunnable throttlingRunnable;

  /**
   * @param throttlingRunnable reference to {@link MoThrottlingRunnable}
   * @param transactionsPerSecond specifies maximum allowed transaction speed.
   * @param bufferSizeInSeconds specifies how big the {@link MoThrottlingRunnable#getLowPriorityQueue} should be in milliseconds.
   */
  public MoLowPriorityQueueLoaderRunnable(final MoThrottlingRunnable throttlingRunnable, final int transactionsPerSecond, final int bufferSizeInSeconds) {
    Validate.isPositive(transactionsPerSecond, "transactionsPerSecond");
    Validate.isPositive(bufferSizeInSeconds, "bufferSizeInSeconds");

    this.throttlingRunnable = throttlingRunnable;
    minimumMessageAgeForQueueInMiliseconds = TimeUnit.SECONDS.toMillis(bufferSizeInSeconds) * MESSAGE_TOO_OLD_MULTIPLIER;
    final int queueSize = bufferSizeInSeconds * transactionsPerSecond;
    shadowLowPriorityQueue = new ArrayBlockingQueue<>(queueSize);
  }

  @Override
  public void run() {
    LOG.info("Thread [{}] started.", Thread.currentThread().getName());
    final MoThrottlingMessagePersister messagePersister = throttlingRunnable.getMessagePersister();
    while (!Thread.currentThread().isInterrupted()) {
      try {
        messagePersister.populateShadowLowPriorityQueue(shadowLowPriorityQueue, minimumMessageAgeForQueueInMiliseconds);
        waitToBeNotifiedThatQueueIsEmpty();
        shadowLowPriorityQueue = throttlingRunnable.swapLowPriorityQueue(shadowLowPriorityQueue);
      } catch (final InterruptedException e) {
        Thread.currentThread().interrupt();
        LOG.debug("", e);
      } catch (final RuntimeException e) {
        LOG.error("Exception in thread [{}].", Thread.currentThread().getName(), e);
      }
    }
    messagePersister.releaseDatabaseResources();
    LOG.info("Thread [{}] stopped.", Thread.currentThread().getName());
  }

  /**
   * Waits to be notified that {@link MoThrottlingRunnable#getLowPriorityQueue} is empty.
   *
   * @throws InterruptedException when the thread gets interrupted.
   */
  private void waitToBeNotifiedThatQueueIsEmpty() throws InterruptedException {
    throttlingRunnable.getLowPriorityQueueStatusLock().lock();
    try {
      while (!throttlingRunnable.islowPriorityQueueEmpty()) {
        throttlingRunnable.getLowPriorityQueueEmptyCondition().await();
      }
    } finally {
      throttlingRunnable.getLowPriorityQueueStatusLock().unlock();
    }
  }
}
