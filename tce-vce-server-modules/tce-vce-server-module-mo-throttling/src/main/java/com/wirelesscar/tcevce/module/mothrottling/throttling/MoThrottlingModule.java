package com.wirelesscar.tcevce.module.mothrottling.throttling;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.wirelesscar.tce.core.event.StateChangedEvent;
import com.wirelesscar.tce.module.api.Message;
import com.wirelesscar.tce.module.api.ModuleBase;
import com.wirelesscar.tce.module.api.ModuleTypeConstants;
import com.wirelesscar.tce.module.metrics.ModuleMetricReporter;
import com.wirelesscar.tcevce.module.mothrottling.metrics.ThrottlingMetricReporter;

/** Module that limits throughput to only configured number of messages per second */
@Component(ModuleTypeConstants.MO_THROTTLING)
public class MoThrottlingModule extends ModuleBase {
  private static final Logger LOG = LoggerFactory.getLogger(MoThrottlingModule.class);

  private final int bufferSizeInSeconds;
  private final boolean deleteAfterPublish;
  private MoLowPriorityQueueLoaderRunnable lowPriorityQueueLoaderRunnable;
  private Thread lowPriorityQueueLoaderThread;
  private Thread metricsThread;
  private final MoThrottlingMessagePersister moThrottlingMessagePersister;
  private final ThrottlingMetricReporter throttlingMetricReporter;
  private MoThrottlingRunnable throttlingRunnable;
  private final int throttlingThreadCount;
  private List<Thread> throttlingThreadList;
  private final int transactionsPerSecond;

  public MoThrottlingModule(@Value("${mo_throttling.module.transactions.per.second:400}") final int transactionsPerSecond,
      @Value("${mo_throttling.module.buffer.size.in.seconds:5}") final int bufferSizeInSeconds,
      @Value("${mo_throttling.module.throttling.thread.count:20}") final int throttlingThreadCount,
      @Value("${mo_throttling.module.throttling.delete-after-publish:false}") final boolean deleteAfterPublish,
      final MoThrottlingMessagePersister moThrottlingMessagePersister, ThrottlingMetricReporter throttlingMetricReporter,
      ModuleMetricReporter moduleMetricReporter) {
    super(moduleMetricReporter);
    this.transactionsPerSecond = transactionsPerSecond;
    this.bufferSizeInSeconds = bufferSizeInSeconds;
    this.throttlingThreadCount = throttlingThreadCount;
    this.moThrottlingMessagePersister = moThrottlingMessagePersister;
    this.throttlingMetricReporter = throttlingMetricReporter;
    this.deleteAfterPublish = deleteAfterPublish;
  }

  @Override
  public void deploy() {
    final String moduleName = getName();
    final String stackName = getStackName();
    LOG.info("{}.{} config - throtling speed (transactions/second): {}", stackName, moduleName, transactionsPerSecond);
    LOG.info("{}.{} config - buffer size (seconds): {}", stackName, moduleName, bufferSizeInSeconds);
    LOG.info("{}.{} config - thread count: {}", stackName, moduleName, throttlingThreadCount);

    Validate.isPositive(transactionsPerSecond, "transactionsPerSecond");
    Validate.isPositive(bufferSizeInSeconds, "bufferSizeInSeconds");
    Validate.isPositive(throttlingThreadCount, "throttlingThreadCount");

    throttlingRunnable = new MoThrottlingRunnable(this, moThrottlingMessagePersister, throttlingMetricReporter, transactionsPerSecond, bufferSizeInSeconds);
    lowPriorityQueueLoaderRunnable = new MoLowPriorityQueueLoaderRunnable(throttlingRunnable, transactionsPerSecond, bufferSizeInSeconds);

    throttlingThreadList = new ArrayList<>(throttlingThreadCount);
    lowPriorityQueueLoaderThread = null;

    metricsThread = new Thread(new MoThrottlingMetricsRunnable(this, throttlingRunnable, throttlingMetricReporter), "MO Throttling: Metrics Logger");
    metricsThread.start();
  }

  @Override
  public void down(final Message message) {
    sendDown(message);
  }

  /**
   * Event listener for server state changes
   *
   * @param event instance of {@link StateChangedEvent}
   */
  @EventListener
  public void handleServerStateChange(final StateChangedEvent event) {
    LOG.info("Server state change event received.");
    if (throttlingRunnable != null) {
      int newThrotlingLimit = transactionsPerSecond;
      if (!event.isBothServersAlive()) {
        newThrotlingLimit = transactionsPerSecond * 2;
      }
      LOG.info("Setting instance throtling speed to : {} (transactions/second)", newThrotlingLimit);
      throttlingRunnable.setTransactionsPerSecond(newThrotlingLimit);
    } else {
      LOG.warn("Ignoring server state change event: {} is not yet fully initialized.", MoThrottlingModule.class.getSimpleName());
    }
  }

  public boolean isDeleteAfterPublish() {
    return deleteAfterPublish;
  }

  @Override
  public void start() {
    LOG.info("Starting {}", getClass().getName());

    lowPriorityQueueLoaderThread = new Thread(lowPriorityQueueLoaderRunnable, "MO Throttling: Low Priority Queue Loader");
    lowPriorityQueueLoaderThread.start();

    for (int index = 0; index < throttlingThreadCount; index++) {
      final Thread thread = new Thread(throttlingRunnable, String.format(Locale.ENGLISH, "MO Throttling: Throttler %02d", index));
      throttlingThreadList.add(thread);
      thread.start();
    }
  }

  @Override
  public void stop() {
    LOG.info("Stopping {}", getClass().getName());

    try {
      lowPriorityQueueLoaderThread.interrupt();
      for (final Thread thread : throttlingThreadList) {
        thread.interrupt();
      }

      lowPriorityQueueLoaderThread.join();
      final Iterator<Thread> threadIterator = throttlingThreadList.iterator();
      while (threadIterator.hasNext()) {
        final Thread thread = threadIterator.next();
        thread.join();
        threadIterator.remove();
      }
    } catch (final InterruptedException e) {
      LOG.debug("Error: interupted while stopping MO Throttling module.", e);
      Thread.currentThread().interrupt();
    }
  }

  @Override
  public void undeploy() {
    try {
      metricsThread.interrupt();
      metricsThread.join();
    } catch (final InterruptedException e) {
      LOG.error("Exception while stopping metrics thread", e);
      Thread.currentThread().interrupt();
    }
  }

  @Override
  public void up(final Message message) {
    throttlingRunnable.addToHighPriorityQueue(message);
    throttlingMetricReporter.onReceived(getStackName());
  }

  /**
   * @return number of throttling threads that has been started and has not yet died
   */
  int getAliveThrottlingThreadCount() {
    int threadCount = 0;
    for (final Thread thread : throttlingThreadList) {
      if (thread.isAlive()) {
        threadCount++;
      }
    }
    return threadCount;
  }

  void sendMessageUp(final Message message) {
    sendUp(message);
  }
}
