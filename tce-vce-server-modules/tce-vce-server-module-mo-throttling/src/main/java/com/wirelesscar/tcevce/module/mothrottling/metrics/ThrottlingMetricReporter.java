package com.wirelesscar.tcevce.module.mothrottling.metrics;

import java.time.Duration;

import org.springframework.stereotype.Component;

import com.volvo.tisp.vc.main.utils.lib.Validate;

import io.micrometer.core.instrument.MeterRegistry;

@Component
public class ThrottlingMetricReporter {
  static final String METRIC_HIGH_QUEUE = "module.mo-throttling.high-priority-queue.size";
  static final String METRIC_LOW_QUEUE = "module.mo-throttling.low-priority-queue";
  static final String METRIC_RECEIVED = "module.mo-throttling.received";
  static final String METRIC_SEND_UP = "module.mo-throttling.sendUp";
  static final String METRIC_STATUS_UPDATE = "module.mo-throttling.status-update";
  static final String METRIC_THROTTLING = "module.mo-throttling.active";
  static final String METRIC_THROTTLING_THREADS = "module.mo-throttling.throttling-threads";
  static final String METRIC_TRANSACTION = "module.mo-throttling.transaction";
  static final String STACK_VAR_NAME = "stackName";
  private static final String DURATION_VAR_NAME = "duration";
  private final MeterRegistry meterRegistry;

  public ThrottlingMetricReporter(MeterRegistry meterRegistry) {
    this.meterRegistry = meterRegistry;
  }

  public void logSendUp(String stackName, Duration duration) {
    Validate.notEmpty(stackName, STACK_VAR_NAME);
    Validate.notNegative(duration, DURATION_VAR_NAME);

    meterRegistry.timer(METRIC_SEND_UP, STACK_VAR_NAME, stackName).record(duration);
  }

  public void logStatusUpdate(String stackName, Duration duration) {
    Validate.notEmpty(stackName, STACK_VAR_NAME);
    Validate.notNegative(duration, DURATION_VAR_NAME);

    meterRegistry.timer(METRIC_STATUS_UPDATE, STACK_VAR_NAME, stackName).record(duration);
  }

  public void logTransaction(String stackName, Duration duration) {
    Validate.notEmpty(stackName, STACK_VAR_NAME);
    Validate.notNegative(duration, DURATION_VAR_NAME);

    meterRegistry.timer(METRIC_TRANSACTION, STACK_VAR_NAME, stackName).record(duration);
  }

  public void onHighQueue(String stackName, int size) {
    Validate.notEmpty(stackName, STACK_VAR_NAME);
    Validate.notNegative(size, "size");

    meterRegistry.counter(METRIC_HIGH_QUEUE, STACK_VAR_NAME, stackName).increment(size);
  }

  public void onLowQueue(String stackName) {
    Validate.notEmpty(stackName, STACK_VAR_NAME);

    meterRegistry.counter(METRIC_LOW_QUEUE, STACK_VAR_NAME, stackName).increment();
  }

  public void onReceived(String stackName) {
    Validate.notEmpty(stackName, STACK_VAR_NAME);

    meterRegistry.counter(METRIC_RECEIVED, STACK_VAR_NAME, stackName).increment();
  }

  public void onThrottling(String stackName, int count) {
    Validate.notEmpty(stackName, STACK_VAR_NAME);
    Validate.notNegative(count, "count");

    meterRegistry.counter(METRIC_THROTTLING, STACK_VAR_NAME, stackName).increment(count);
  }

  public void onThrottlingThreads(String stackName, int count) {
    Validate.notEmpty(stackName, STACK_VAR_NAME);
    Validate.notNegative(count, "count");

    meterRegistry.counter(METRIC_THROTTLING_THREADS, STACK_VAR_NAME, stackName).increment(count);
  }
}
