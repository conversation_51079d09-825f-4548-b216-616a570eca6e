package com.wirelesscar.tcevce.module.mothrottling.throttling;

import java.util.concurrent.TimeUnit;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.wirelesscar.tcevce.module.mothrottling.metrics.ThrottlingMetricReporter;

/** Runnable that records queue sizes for metrics reporting. */
final class MoThrottlingMetricsRunnable implements Runnable {
  private static final Logger LOG = LoggerFactory.getLogger(MoThrottlingMetricsRunnable.class);

  private final MoThrottlingModule module;
  private final ThrottlingMetricReporter throttlingMetricReporter;
  private final MoThrottlingRunnable throttlingRunnable;

  /**
   * @param module reference to {@link MoThrottlingModule}
   * @param throttlingRunnable reference to {@link MoThrottlingRunnable}
   * @param throttlingMetricReporter reference to {@link ThrottlingMetricReporter}
   */
  public MoThrottlingMetricsRunnable(final MoThrottlingModule module, final MoThrottlingRunnable throttlingRunnable,
      ThrottlingMetricReporter throttlingMetricReporter) {
    Validate.notNull(module, "module");
    Validate.notNull(throttlingRunnable, "throttlingRunnable");
    Validate.notNull(throttlingMetricReporter, "throttlingMetricReporter");

    this.module = module;
    this.throttlingRunnable = throttlingRunnable;
    this.throttlingMetricReporter = throttlingMetricReporter;
  }

  @Override
  public void run() {
    LOG.info("Thread [{}] started.", Thread.currentThread().getName());
    while (!Thread.currentThread().isInterrupted()) {
      try {
        String stackName = module.getStackName();
        int size = throttlingRunnable.getHighPriorityQueue().size();
        throttlingMetricReporter.onHighQueue(stackName, size);
        throttlingMetricReporter.onThrottlingThreads(stackName, module.getAliveThrottlingThreadCount());
        int count = throttlingRunnable.getRateLimiter().getAvailablePermits() > 0 ? 0 : 1;
        throttlingMetricReporter.onThrottling(stackName, count);

        TimeUnit.SECONDS.sleep(1);
      } catch (final InterruptedException e) {
        Thread.currentThread().interrupt();
        LOG.debug("", e);
      }
    }
    LOG.info("Thread [{}] stopped.", Thread.currentThread().getName());
  }
}
