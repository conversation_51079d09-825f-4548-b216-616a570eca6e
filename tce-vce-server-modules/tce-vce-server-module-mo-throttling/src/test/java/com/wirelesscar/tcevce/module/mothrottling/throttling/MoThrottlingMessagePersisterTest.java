package com.wirelesscar.tcevce.module.mothrottling.throttling;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.sql.SQLException;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.TimeUnit;

import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.framework.context.TispContext;
import com.wirelesscar.config.mock.MockConfiguration;
import com.wirelesscar.tce.db.source.TceDataSource;
import com.wirelesscar.tce.module.api.Message;

class MoThrottlingMessagePersisterTest {
  @BeforeAll
  static void beforeAll() {
    final MockConfiguration mockConfiguration = MockConfiguration.getConfig();
    mockConfiguration.setPropertySpecific(mockConfiguration.getComponentShortName(), TceDataSource.TEST_DB_PROP, "true");
  }

  private static void insertMessages(final MoThrottlingMessagePersister moThrottlingMessagePersister) {
    for (int i = 0; i < 4; ++i) {
      final Message message = new Message();
      message.setVehicleID("foo" + i);

      TispContext.runInContext(() -> {
        try {
          moThrottlingMessagePersister.persist(message);
        } catch (SQLException e) {
          throw new IllegalStateException(e);
        }
      });
    }
  }

  /**
   * Test population of "shadow" low priority queue
   *
   * @throws InterruptedException due to use of {@link TimeUnit#sleep(long)}
   */
  @Test
  void testPopulateShadowLowPriorityQueue() throws InterruptedException {
    final MoThrottlingMessagePersister moThrottlingMessagePersister = new MoThrottlingMessagePersister();
    insertMessages(moThrottlingMessagePersister);

    final BlockingQueue<Message> blockingQueue = new ArrayBlockingQueue<>(3);
    moThrottlingMessagePersister.populateShadowLowPriorityQueue(blockingQueue, 0L);
    assertEquals(3, blockingQueue.size(), "Items in queue populated from resultset");

    blockingQueue.clear();
    moThrottlingMessagePersister.populateShadowLowPriorityQueue(blockingQueue, 0L);
    assertEquals(1, blockingQueue.size(), "Items in queue populated from resultset that reached his end");

    blockingQueue.clear();
    final long minimumMessageAgeForLowPriorityQueueInMiliseconds = TimeUnit.SECONDS.toMillis(1);
    final long timestamp = System.nanoTime();
    moThrottlingMessagePersister.populateShadowLowPriorityQueue(blockingQueue, minimumMessageAgeForLowPriorityQueueInMiliseconds);
    assertTrue(System.nanoTime() - timestamp > TimeUnit.MICROSECONDS.toNanos(minimumMessageAgeForLowPriorityQueueInMiliseconds),
        "Attempt to populate queue after ResultSet reached his end should pause execution");
    assertTrue(blockingQueue.isEmpty(), "Attempt to populate queue after resultset reached his end should return unmodified queue");

    moThrottlingMessagePersister.populateShadowLowPriorityQueue(blockingQueue, 0L);
    assertEquals(3, blockingQueue.size(), "Items in queue should be populated as usual after one skipped cycle");
  }
}
