package com.wirelesscar.tcevce.module.mothrottling.metrics;

import java.time.Duration;

import org.junit.jupiter.api.Test;

import io.micrometer.core.instrument.Tags;

class ThrottlingMetricReporterTest {
  private static final String STACK_TEST = "stack-test";

  @Test
  void logSendUpTest() {
    MetricsReporterTestUtils.initReporterAndTest(ThrottlingMetricReporter::new, (meterRegistry, throttlingMetricReporter) -> {
      throttlingMetricReporter.logSendUp(STACK_TEST, Duration.ofMillis(2));
      MetricsReporterTestUtils.checkTimer(meterRegistry, Duration.ofMillis(2), 1, ThrottlingMetricReporter.METRIC_SEND_UP,
          Tags.of(ThrottlingMetricReporter.STACK_VAR_NAME, STACK_TEST));
    });
  }

  @Test
  void logStatusUpdateTest() {
    MetricsReporterTestUtils.initReporterAndTest(ThrottlingMetricReporter::new, (meterRegistry, throttlingMetricReporter) -> {
      throttlingMetricReporter.logStatusUpdate(STACK_TEST, Duration.ofMillis(1));
      throttlingMetricReporter.logStatusUpdate(STACK_TEST, Duration.ofMillis(2));
      MetricsReporterTestUtils.checkTimer(meterRegistry, Duration.ofMillis(3), 2, ThrottlingMetricReporter.METRIC_STATUS_UPDATE,
          Tags.of(ThrottlingMetricReporter.STACK_VAR_NAME, STACK_TEST));
    });
  }

  @Test
  void logTransactionTest() {
    MetricsReporterTestUtils.initReporterAndTest(ThrottlingMetricReporter::new, (meterRegistry, throttlingMetricReporter) -> {
      throttlingMetricReporter.logTransaction(STACK_TEST, Duration.ofMillis(2));
      MetricsReporterTestUtils.checkTimer(meterRegistry, Duration.ofMillis(2), 1, ThrottlingMetricReporter.METRIC_TRANSACTION,
          Tags.of(ThrottlingMetricReporter.STACK_VAR_NAME, STACK_TEST));
    });
  }

  @Test
  void onHighQueueTest() {
    MetricsReporterTestUtils.initReporterAndTest(ThrottlingMetricReporter::new, (meterRegistry, throttlingMetricReporter) -> {
      throttlingMetricReporter.onHighQueue(STACK_TEST, 2_000);
      MetricsReporterTestUtils.checkCounter(meterRegistry, 2_000, ThrottlingMetricReporter.METRIC_HIGH_QUEUE, Tags.of(ThrottlingMetricReporter.STACK_VAR_NAME,
          STACK_TEST));
    });
  }

  @Test
  void onLowQueueTest() {
    MetricsReporterTestUtils.initReporterAndTest(ThrottlingMetricReporter::new, (meterRegistry, throttlingMetricReporter) -> {
      throttlingMetricReporter.onLowQueue(STACK_TEST);
      MetricsReporterTestUtils.checkCounter(meterRegistry, 1, ThrottlingMetricReporter.METRIC_LOW_QUEUE,
          Tags.of(ThrottlingMetricReporter.STACK_VAR_NAME, STACK_TEST));
    });
  }

  @Test
  void onReceivedTest() {
    MetricsReporterTestUtils.initReporterAndTest(ThrottlingMetricReporter::new, (meterRegistry, throttlingMetricReporter) -> {
      throttlingMetricReporter.onReceived(STACK_TEST);
      MetricsReporterTestUtils.checkCounter(meterRegistry, 1, ThrottlingMetricReporter.METRIC_RECEIVED,
          Tags.of(ThrottlingMetricReporter.STACK_VAR_NAME, STACK_TEST));
    });
  }

  @Test
  void onThrottlingTest() {
    MetricsReporterTestUtils.initReporterAndTest(ThrottlingMetricReporter::new, (meterRegistry, throttlingMetricReporter) -> {
      throttlingMetricReporter.onThrottling(STACK_TEST, 2_000_000);
      MetricsReporterTestUtils.checkCounter(meterRegistry, 2_000_000, ThrottlingMetricReporter.METRIC_THROTTLING,
          Tags.of(ThrottlingMetricReporter.STACK_VAR_NAME, STACK_TEST));
    });
  }

  @Test
  void onThrottlingThreadsTest() {
    MetricsReporterTestUtils.initReporterAndTest(ThrottlingMetricReporter::new, (meterRegistry, throttlingMetricReporter) -> {
      throttlingMetricReporter.onThrottlingThreads(STACK_TEST, 100);
      MetricsReporterTestUtils.checkCounter(meterRegistry, 100, ThrottlingMetricReporter.METRIC_THROTTLING_THREADS,
          Tags.of(ThrottlingMetricReporter.STACK_VAR_NAME, STACK_TEST));
    });
  }
}
