package com.wirelesscar.tcevce.module.mothrottling.metrics;

import static org.junit.jupiter.api.Assertions.assertEquals;

import java.time.Duration;
import java.util.concurrent.TimeUnit;
import java.util.function.BiConsumer;
import java.util.function.Function;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tags;
import io.micrometer.core.instrument.Timer;
import io.micrometer.core.instrument.simple.SimpleMeterRegistry;

final class MetricsReporterTestUtils {
  private MetricsReporterTestUtils() {
    throw new IllegalStateException();
  }

  static void checkCounter(MeterRegistry meterRegistry, int expectedCount, String name, Tags tags) {
    final Counter counter = meterRegistry.find(name).tags(tags).counter();

    assertEquals(expectedCount, counter.count());
  }

  static void checkTimer(MeterRegistry meterRegistry, Duration expectedDuration, int expectedCount, String name, Tags tags) {
    final Timer timer = meterRegistry.find(name).tags(tags).timer();

    assertEquals(expectedDuration.toMillis(), timer.totalTime(TimeUnit.MILLISECONDS), 1);
    assertEquals(expectedCount, timer.count());
  }

  static <T> void initReporterAndTest(Function<MeterRegistry, T> initMetricsReporter, BiConsumer<MeterRegistry, T> test) {
    MeterRegistry meterRegistry = new SimpleMeterRegistry();
    T metricsReporter = initMetricsReporter.apply(meterRegistry);

    test.accept(meterRegistry, metricsReporter);
  }
}
