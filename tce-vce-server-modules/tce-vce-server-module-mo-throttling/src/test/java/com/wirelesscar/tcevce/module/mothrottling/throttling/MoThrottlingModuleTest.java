package com.wirelesscar.tcevce.module.mothrottling.throttling;

import java.sql.SQLException;
import java.util.concurrent.TimeUnit;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import com.wirelesscar.tce.db.common.db.api.MessageFields;
import com.wirelesscar.tce.module.api.Message;
import com.wirelesscar.tce.module.api.Module;
import com.wirelesscar.tce.module.metrics.ModuleMetricReporter;
import com.wirelesscar.tcevce.module.mothrottling.metrics.ThrottlingMetricReporter;

/** Tests life-cycle and functionality of {@link MoThrottlingModule} */
class MoThrottlingModuleTest {
  private static final int QUEUE_BUFFER_SIZE_IN_SECONDS = 2;
  private static final int THROTTLING_THREAD_COUNT = 2;
  private static final int TRANSACTIONS_PER_SECOND = 3;

  private final MoThrottlingMessagePersister messagePersister = Mockito.mock(MoThrottlingMessagePersister.class);

  private final Module mockReceiverModule = Mockito.mock(Module.class);
  private MoThrottlingModule module;
  private final ThrottlingMetricReporter throttlingMetricReporter = Mockito.mock(ThrottlingMetricReporter.class);

  @AfterEach
  void afterEach() {
    module.undeploy();
  }

  /** Configures {@link MoThrottlingModule} for test execution */
  @BeforeEach
  void beforeEach() {
    /*
     * Partially mocking the tested model to be able to inject mocked MoThrottlingMessagePersister
     */
    module = new MoThrottlingModule(TRANSACTIONS_PER_SECOND, QUEUE_BUFFER_SIZE_IN_SECONDS, THROTTLING_THREAD_COUNT, false, messagePersister,
        throttlingMetricReporter, Mockito.mock(ModuleMetricReporter.class));
    module.setBeanName(MoThrottlingModule.class.getSimpleName());
    module.setStackName("MO_THROTTLING_TEST");
    module.setUp(mockReceiverModule);
    module.deploy();
  }

  @Test
  void testDeleteMessageAfterPublish() throws SQLException, InterruptedException {
    MoThrottlingModule moThrottlingModule = new MoThrottlingModule(TRANSACTIONS_PER_SECOND, QUEUE_BUFFER_SIZE_IN_SECONDS, THROTTLING_THREAD_COUNT, true,
        messagePersister, throttlingMetricReporter, Mockito.mock(ModuleMetricReporter.class));
    moThrottlingModule.setBeanName(MoThrottlingModule.class.getSimpleName());
    moThrottlingModule.setStackName("MO_THROTTLING_TEST");
    moThrottlingModule.setUp(mockReceiverModule);
    moThrottlingModule.deploy();

    moThrottlingModule.start();
    final Message message = Message.createMessage();
    message.setProperty(MessageFields.nextCheckTime.name(), Long.toString(Long.MAX_VALUE));
    moThrottlingModule.up(message);
    TimeUnit.SECONDS.sleep(1);
    Mockito.verify(messagePersister).delete(message);
    moThrottlingModule.stop();
    moThrottlingModule.up(message);
    TimeUnit.SECONDS.sleep(1);
    Mockito.verify(messagePersister).delete(message);
    Mockito.verify(messagePersister, Mockito.never()).updateStatusAndProps(message);
  }

  /**
   * Test if an option to stop persisting delivery status to the database works at runtime.
   *
   * @throws SQLException should never be thrown since persister is a mock
   * @throws InterruptedException if test gets interrupted
   */
  @Test
  void testPersistingAsDelivered() throws SQLException, InterruptedException {
    module.start();
    final Message message = Message.createMessage();
    message.setProperty(MessageFields.nextCheckTime.name(), Long.toString(Long.MAX_VALUE));
    module.up(message);
    TimeUnit.SECONDS.sleep(1);
    Mockito.verify(messagePersister).updateStatusAndProps(message);
    module.stop();
    module.up(message);
    TimeUnit.SECONDS.sleep(1);
    Mockito.verify(messagePersister).updateStatusAndProps(message);
    Mockito.verify(messagePersister, Mockito.never()).delete(message);
  }

  /**
   * Test if stopping and starting a module in runtime will work
   *
   * @throws SQLException should never be thrown since persister is a mock
   * @throws InterruptedException if test gets interrupted
   */
  @Test
  void testStartingAndStoppingModule() throws InterruptedException, SQLException {
    module.start();
    module.stop();
    module.start();
    final Message message = Message.createMessage();
    message.setProperty(MessageFields.nextCheckTime.name(), Long.toString(Long.MAX_VALUE));
    module.up(message);
    TimeUnit.SECONDS.sleep(1);
    Mockito.verify(messagePersister).updateStatusAndProps(message);
    module.stop();
  }
}
