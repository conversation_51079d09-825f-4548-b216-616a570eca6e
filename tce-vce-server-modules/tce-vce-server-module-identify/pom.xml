<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
     xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>com.volvo.tisp</groupId>
    <artifactId>tce-vce-server-modules</artifactId>
    <version>0-SNAPSHOT</version>
  </parent>

  <artifactId>tce-vce-server-module-identify</artifactId>

  <dependencies>
    <dependency>
      <groupId>com.volvo.tisp</groupId>
      <artifactId>wecu-device-info-cache-core</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>com.volvo.tisp</groupId>
      <artifactId>tce-vce-server-connectivity-repo-common</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>com.volvo.tisp</groupId>
      <artifactId>tce-vce-server-common-database-tools</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-web</artifactId>
    </dependency>

    <dependency>
      <groupId>com.wirelesscar.config-lib</groupId>
      <artifactId>config-lib-impl-mock</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.h2database</groupId>
      <artifactId>h2</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter-engine</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>ch.qos.logback</groupId>
      <artifactId>logback-classic</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.volvo.tisp</groupId>
      <artifactId>test-utils-lib</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-core</artifactId>
      <scope>test</scope>
    </dependency>
  </dependencies>
</project>
