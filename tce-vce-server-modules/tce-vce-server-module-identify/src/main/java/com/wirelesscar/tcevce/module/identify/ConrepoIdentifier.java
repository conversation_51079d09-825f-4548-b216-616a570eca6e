package com.wirelesscar.tcevce.module.identify;

import java.util.Optional;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.volvo.tisp.vc.common.dto.lib.vehicle.Ipv4Address;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Msisdn;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.volvo.tisp.vc.main.utils.lib.type.Either;
import com.wirelesscar.conrepo.api.v1.DeviceDetailedEntry;
import com.wirelesscar.tce.module.api.exception.EngineRuntimeException;
import com.wirelesscar.tcevce.connectivityrepo.converter.DeviceDetailedEntryInputConverterFunction;
import com.wirelesscar.tcevce.module.identify.metrics.VceIdentifyModuleMetricReporter;
import com.wirelesscar.tcevce.wecu.device.info.database.api.DeviceInfoWriter;
import com.wirelesscar.tcevce.wecu.device.info.database.api.DeviceInfoWriterFactory;
import com.wirelesscar.tcevce.wecu.device.info.database.model.DeviceInfo;
import com.wirelesscar.tcevce.wecu.device.info.database.model.DeviceInfoId;
import com.wirelesscar.tcevce.wecu.device.info.database.model.DeviceSequence;
import com.wirelesscar.tcevce.wecu.device.info.database.model.DeviceSequenceBuilder;
import com.wirelesscar.tcevce.wecu.device.info.database.model.DeviceSequenceId;
import com.wirelesscar.tcevce.wecu.device.info.database.model.Handle;
import com.wirelesscar.tcevce.wecu.device.info.database.model.InsertionFailure;
import com.wirelesscar.tcevce.wecu.device.info.database.model.SatelliteId;
import com.wirelesscar.tcevce.wecu.device.info.database.model.SequenceNumber;

@Component
class ConrepoIdentifier {
  private static final SequenceNumber DEFAULT_SEQUENCE_NUMBER = SequenceNumber.ofByte((byte) 0);
  private static final String STACK_NAME_ARG_VARIABLE_NAME = "stackName";
  private static final Logger logger = LoggerFactory.getLogger(ConrepoIdentifier.class);

  private final ConrepoLookupClient conrepoLookupClient;
  private final DeviceInfoWriterFactory deviceInfoWriterFactory;
  private final VceIdentifyModuleMetricReporter vceIdentifyModuleMetricReporter;

  ConrepoIdentifier(ConrepoLookupClient conrepoLookupClient, DeviceInfoWriterFactory deviceInfoWriterFactory,
      VceIdentifyModuleMetricReporter vceIdentifyModuleMetricReporter) {
    this.conrepoLookupClient = conrepoLookupClient;
    this.deviceInfoWriterFactory = deviceInfoWriterFactory;
    this.vceIdentifyModuleMetricReporter = vceIdentifyModuleMetricReporter;
  }

  private static DeviceSequence createDefaultDeviceSequence(DeviceInfoId deviceInfoId) {
    return new DeviceSequenceBuilder()
        .setDeviceInfoId(deviceInfoId)
        .setSequenceNumber(DEFAULT_SEQUENCE_NUMBER)
        .build();
  }

  Optional<DeviceInfo> tryHandle(Handle handle, String stackName) {
    Validate.notNull(handle, "handle");
    Validate.notEmpty(stackName, STACK_NAME_ARG_VARIABLE_NAME);

    vceIdentifyModuleMetricReporter.onDownConrepo(stackName);
    String handleString = handle.toString();
    logger.debug("Trying to identify device in connectivity repository by handle [{}]", handleString);
    return conrepoLookupClient.lookupByHandle(handleString).map(this::insertDeviceInfo);
  }

  Optional<DeviceInfo> tryIp(Ipv4Address ipv4Address, String stackName) {
    Validate.notNull(ipv4Address, "ipv4Address");
    Validate.notEmpty(stackName, STACK_NAME_ARG_VARIABLE_NAME);

    vceIdentifyModuleMetricReporter.onUpConrepo(stackName);
    String srcIpv4AddressString = ipv4Address.toString();
    logger.debug("Trying to identify device in connectivity repository by ip [{}]", srcIpv4AddressString);
    return conrepoLookupClient.lookupByIp(srcIpv4AddressString).map(this::insertDeviceInfo);
  }

  Optional<DeviceInfo> tryMsisdn(Msisdn msisdn, String stackName) {
    Validate.notNull(msisdn, "msisdn");
    Validate.notEmpty(stackName, STACK_NAME_ARG_VARIABLE_NAME);

    vceIdentifyModuleMetricReporter.onUpConrepo(stackName);
    String srcMsisdnString = msisdn.toString();
    logger.debug("Trying to identify device in connectivity repository by msisdn [{}]", srcMsisdnString);
    return conrepoLookupClient.lookupByMsisdn(srcMsisdnString).map(this::insertDeviceInfo);
  }

  Optional<DeviceInfo> trySatellite(SatelliteId satelliteId, String stackName) {
    Validate.notNull(satelliteId, "satelliteId");
    Validate.notEmpty(stackName, STACK_NAME_ARG_VARIABLE_NAME);

    vceIdentifyModuleMetricReporter.onUpConrepo(stackName);
    String srcSatelliteIdString = satelliteId.toString();
    logger.debug("Trying to identify device in connectivity repository by satid [{}]", srcSatelliteIdString);
    return conrepoLookupClient.lookupBySatId(srcSatelliteIdString).map(this::insertDeviceInfo);
  }

  Optional<DeviceInfo> tryVpi(Vpi vpi, String stackName) {
    Validate.notNull(vpi, "vpi");
    Validate.notEmpty(stackName, STACK_NAME_ARG_VARIABLE_NAME);

    vceIdentifyModuleMetricReporter.onDownConrepo(stackName);
    String vpiString = vpi.toString();
    logger.debug("Trying to identify device in connectivity repository by vpi [{}]", vpiString);
    return conrepoLookupClient.lookupByVehiclePlatformId(vpiString).map(this::insertDeviceInfo);
  }

  private DeviceInfo insertDeviceInfo(DeviceDetailedEntry deviceDetailedEntry) {
    Either<IllegalArgumentException, DeviceInfo> deviceInfoEither = DeviceDetailedEntryInputConverterFunction.INSTANCE
        .apply(deviceDetailedEntry);

    if (deviceInfoEither.isLeft()) {
      throw new EngineRuntimeException("Error converting deviceDetailedEntry to deviceinfo locally", deviceInfoEither.getLeft());
    }

    insertDeviceInfo(deviceInfoEither.getRight());

    return deviceInfoEither.getRight();
  }

  private void insertDeviceInfo(DeviceInfo deviceInfo) {
    try (DeviceInfoWriter deviceInfoWriter = deviceInfoWriterFactory.createReadCommitted()) {
      Either<InsertionFailure, DeviceInfoId> deviceInfoIdEither = deviceInfoWriter.insertDeviceInfo(deviceInfo);

      if (deviceInfoIdEither.isLeft()) {
        throw new EngineRuntimeException("Error inserting Device locally", deviceInfoIdEither.getLeft().getRuntimeException());
      }

      Either<InsertionFailure, DeviceSequenceId> deviceSequenceIdEither = deviceInfoWriter.insertDeviceSequence(
          createDefaultDeviceSequence(deviceInfoIdEither.getRight()));

      if (deviceSequenceIdEither.isLeft()) {
        throw new EngineRuntimeException("Error inserting Device sequence locally", deviceSequenceIdEither.getLeft().getRuntimeException());
      }
    }
  }
}
