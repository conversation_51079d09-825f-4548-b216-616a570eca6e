package com.wirelesscar.tcevce.module.identify;

import java.util.Optional;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.volvo.tisp.vc.common.dto.lib.vehicle.Ipv4Address;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Msisdn;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.wirelesscar.tce.db.common.db.api.MessageFields;
import com.wirelesscar.tce.module.api.Message;
import com.wirelesscar.tce.module.api.MetaData;
import com.wirelesscar.tcevce.wecu.device.info.database.model.DeviceInfo;
import com.wirelesscar.tcevce.wecu.device.info.database.model.Handle;
import com.wirelesscar.tcevce.wecu.device.info.database.model.MobileNetworkOperator;
import com.wirelesscar.tcevce.wecu.device.info.database.model.SatelliteId;
import com.wirelesscar.tcevce.wecu.device.info.database.model.SimInfo;

final class VceIdentifyMessagePropertiesUtil {
  private static final String DEVICE_INFO_ARG_VARIABLE_NAME = "deviceInfo";
  private static final String MESSAGE_ARG_VARIABLE_NAME = "message";
  private static final Logger logger = LoggerFactory.getLogger(VceIdentifyMessagePropertiesUtil.class);

  private VceIdentifyMessagePropertiesUtil() {
    throw new IllegalStateException();
  }

  static Optional<Handle> getHandle(Message message) {
    Validate.notNull(message, MESSAGE_ARG_VARIABLE_NAME);

    return Optional.ofNullable(message.getProperty(MetaData.HANDLE))
        .map(Handle::ofString);
  }

  static Optional<Ipv4Address> getSourceIpv4Address(Message message) {
    Validate.notNull(message, MESSAGE_ARG_VARIABLE_NAME);

    return Optional.ofNullable(message.getProperty(MetaData.IP_SRC_ADDRESS))
        .map(Ipv4Address::ofString);
  }

  static Optional<Msisdn> getSourceMsisdn(Message message) {
    Validate.notNull(message, MESSAGE_ARG_VARIABLE_NAME);

    return Optional.ofNullable(message.getProperty(MessageFields.msisdn.name()))
        .or(() -> Optional.ofNullable(message.getProperties().get(MetaData.SMPP_SOURCE_ADDRESS.name())))
        .map(Msisdn::ofString);
  }

  static Optional<SatelliteId> getSourceSatelliteId(Message message) {
    Validate.notNull(message, MESSAGE_ARG_VARIABLE_NAME);

    return Optional.ofNullable(message.getProperty(MetaData.SATELLITE_SOURCE_ADDRESS))
        .map(SatelliteId::ofString);
  }

  static Optional<Vpi> getVpi(Message message) {
    Validate.notNull(message, MESSAGE_ARG_VARIABLE_NAME);

    return Optional.ofNullable(message.getProperty(MetaData.VPI))
        .map(Vpi::ofString);
  }

  static void setProperties(Message message, DeviceInfo deviceInfo, boolean isDown) {
    Validate.notNull(message, MESSAGE_ARG_VARIABLE_NAME);
    Validate.notNull(deviceInfo, DEVICE_INFO_ARG_VARIABLE_NAME);

    logger.debug("Setting properties for {}", message);

    setVehicleId(message, deviceInfo);
    setVpi(message, deviceInfo);
    setHandle(message, deviceInfo);

    if (isDown) {
      setDestinationAddress(message, deviceInfo);
    } else {
      setSourceAddress(message, deviceInfo);
    }

    setMobileOperator(message, deviceInfo);

    logger.debug("After setting properties: {}", message);
  }

  static void setVehicleId(Message message, DeviceInfo deviceInfo) {
    Validate.notNull(message, MESSAGE_ARG_VARIABLE_NAME);
    Validate.notNull(deviceInfo, DEVICE_INFO_ARG_VARIABLE_NAME);

    String vehicleID = deviceInfo.getVpi().map(Vpi::toString)
        .orElseGet(() -> deviceInfo.getHandle().toString());

    message.setVehicleID(vehicleID);
    logger.trace("Set vehicleId={} for {}", vehicleID, message);
  }

  private static void setDestinationAddress(Message message, DeviceInfo deviceInfo) {
    deviceInfo.getSimInfo()
        .ifPresent(simInfo -> {
          setProperty(message, MetaData.IP_DST_ADDRESS, simInfo.getIpv4Address().toString());
          setProperty(message, MetaData.IP_DST_PORT, simInfo.getIpv4Port().toString());
          setProperty(message, MetaData.SMPP_DEST_ADDRESS, simInfo.getMsisdn().toString());
        });

    setSatelliteId(message, deviceInfo);
  }

  private static void setHandle(Message message, DeviceInfo deviceInfo) {
    String handleString = deviceInfo.getHandle().toString();

    setProperty(message, MessageFields.handle, handleString);
    setProperty(message, MetaData.HANDLE, handleString);
  }

  private static void setMobileOperator(Message message, DeviceInfo deviceInfo) {
    deviceInfo.getSimInfo()
        .map(SimInfo::getMobileNetworkOperator)
        .map(MobileNetworkOperator::toString)
        .ifPresent(operatorString -> setProperty(message, MetaData.OPERATOR, operatorString));
  }

  private static void setProperty(Message message, MessageFields messageFields, String value) {
    if (value != null) {
      logger.trace("Set {}={}", messageFields, value);
      message.setProperty(messageFields.name(), value);
    }
  }

  private static void setProperty(Message message, MetaData metaData, String value) {
    if (value != null) {
      logger.trace("Set {}={}", metaData, value);
      message.setProperty(metaData, value);
    }
  }

  private static void setSatelliteId(Message message, DeviceInfo deviceInfo) {
    deviceInfo.getSatelliteId()
        .map(SatelliteId::toString)
        .ifPresent(satelliteIdString -> setProperty(message, MetaData.SATELLITE_DEST_ADDRESS, satelliteIdString));
  }

  private static void setSourceAddress(Message message, DeviceInfo deviceInfo) {
    MetaData smppSourceAddress = MetaData.SMPP_SOURCE_ADDRESS;

    Optional<Msisdn> messageMsisdnOptional = Optional.ofNullable(message.getProperty(smppSourceAddress))
        .map(Msisdn::ofString);

    if (messageMsisdnOptional.isEmpty()) {
      String sourceAddress = deviceInfo.getSimInfo()
          .map(SimInfo::getMsisdn)
          .map(Msisdn::toString)
          .orElse(null);

      setProperty(message, smppSourceAddress, sourceAddress);
    }
  }

  private static void setVpi(Message message, DeviceInfo deviceInfo) {
    String vpiString = deviceInfo.getVpi()
        .map(Vpi::toString)
        .orElse(null);

    setProperty(message, MetaData.VPI, vpiString);
  }
}
