package com.wirelesscar.tcevce.module.identify.metrics;

import java.time.Duration;

import org.springframework.stereotype.Component;

import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.wirelesscar.tce.module.api.ModuleTypeConstants;

import io.micrometer.core.instrument.MeterRegistry;

@Component
public class VceIdentifyModuleMetricReporter {
  static final String DOWN = "module." + ModuleTypeConstants.VCE_IDENTIFY + ".down.";
  static final String DOWN_CONREPO = "module." + ModuleTypeConstants.VCE_IDENTIFY + ".down-conrepo";
  static final String DOWN_FAIL = "module." + ModuleTypeConstants.VCE_IDENTIFY + ".down.-fail";
  static final String ID_BY_HANDLR = "module." + ModuleTypeConstants.VCE_IDENTIFY + ".id-by-handle";
  static final String ID_BY_IP = "module." + ModuleTypeConstants.VCE_IDENTIFY + ".id-by-ip";
  static final String ID_BY_MSISDN = "module." + ModuleTypeConstants.VCE_IDENTIFY + ".id-by-msisdn";
  static final String ID_BY_SAT = "module." + ModuleTypeConstants.VCE_IDENTIFY + ".id-by-sat";
  static final String ID_BY_VID = "module." + ModuleTypeConstants.VCE_IDENTIFY + ".id-by-vid";
  static final String STACK_VAR_NAME = "stackName";

  static final String UP = "module." + ModuleTypeConstants.VCE_IDENTIFY + ".up.";
  static final String UP_CONREPO = "module." + ModuleTypeConstants.VCE_IDENTIFY + ".up-conrepo";
  static final String UP_FAIL = "module." + ModuleTypeConstants.VCE_IDENTIFY + ".up.-fail";
  static final String UP_FAILURE_SAT = "module." + ModuleTypeConstants.VCE_IDENTIFY + ".up.failure.sat";
  static final String UP_FAILURE_SMS = "module." + ModuleTypeConstants.VCE_IDENTIFY + ".up.failure.sms";

  static final String UP_FAILURE_UDP = "module." + ModuleTypeConstants.VCE_IDENTIFY + ".up.failure.udp";

  private final MeterRegistry meterRegistry;

  public VceIdentifyModuleMetricReporter(MeterRegistry meterRegistry) {
    this.meterRegistry = meterRegistry;
  }

  public void onDown(Duration duration, String stackName) {
    Validate.notNegative(duration, "duration");
    Validate.notEmpty(stackName, STACK_VAR_NAME);

    meterRegistry.timer(DOWN, STACK_VAR_NAME, stackName).record(duration);
  }

  public void onDownConrepo(String stackName) {
    Validate.notEmpty(stackName, STACK_VAR_NAME);

    meterRegistry.counter(DOWN_CONREPO, STACK_VAR_NAME, stackName).increment();
  }

  public void onDownFail(String stackName) {
    Validate.notEmpty(stackName, STACK_VAR_NAME);

    meterRegistry.counter(DOWN_FAIL, STACK_VAR_NAME, stackName).increment();
  }

  public void onIdByHandle(String stackName) {
    Validate.notEmpty(stackName, STACK_VAR_NAME);

    meterRegistry.counter(ID_BY_HANDLR, STACK_VAR_NAME, stackName).increment();
  }

  public void onIdByIp(String stackName) {
    Validate.notEmpty(stackName, STACK_VAR_NAME);

    meterRegistry.counter(ID_BY_IP, STACK_VAR_NAME, stackName).increment();
  }

  public void onIdByMsisdn(String stackName) {
    Validate.notEmpty(stackName, STACK_VAR_NAME);

    meterRegistry.counter(ID_BY_MSISDN, STACK_VAR_NAME, stackName).increment();
  }

  public void onIdBySat(String stackName) {
    Validate.notEmpty(stackName, STACK_VAR_NAME);

    meterRegistry.counter(ID_BY_SAT, STACK_VAR_NAME, stackName).increment();
  }

  public void onIdByVid(String stackName) {
    Validate.notEmpty(stackName, STACK_VAR_NAME);

    meterRegistry.counter(ID_BY_VID, STACK_VAR_NAME, stackName).increment();
  }

  public void onUp(Duration duration, String stackName) {
    Validate.notNegative(duration, "duration");
    Validate.notEmpty(stackName, STACK_VAR_NAME);

    meterRegistry.timer(UP, STACK_VAR_NAME, stackName).record(duration);
  }

  public void onUpConrepo(String stackName) {
    Validate.notEmpty(stackName, STACK_VAR_NAME);

    meterRegistry.counter(UP_CONREPO, STACK_VAR_NAME, stackName).increment();
  }

  public void onUpFail(String stackName) {
    Validate.notEmpty(stackName, STACK_VAR_NAME);

    meterRegistry.counter(UP_FAIL, STACK_VAR_NAME, stackName).increment();
  }

  public void onUpFailureSat(String stackName) {
    Validate.notEmpty(stackName, STACK_VAR_NAME);

    meterRegistry.counter(UP_FAILURE_SAT, STACK_VAR_NAME, stackName).increment();
  }

  public void onUpFailureSms(String stackName) {
    Validate.notEmpty(stackName, STACK_VAR_NAME);

    meterRegistry.counter(UP_FAILURE_SMS, STACK_VAR_NAME, stackName).increment();
  }

  public void onUpFailureUdp(String stackName) {
    Validate.notEmpty(stackName, STACK_VAR_NAME);

    meterRegistry.counter(UP_FAILURE_UDP, STACK_VAR_NAME, stackName).increment();
  }
}
