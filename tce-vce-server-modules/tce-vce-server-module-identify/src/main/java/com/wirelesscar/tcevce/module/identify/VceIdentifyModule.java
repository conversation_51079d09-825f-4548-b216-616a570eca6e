package com.wirelesscar.tcevce.module.identify;

import java.time.Duration;
import java.util.Optional;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import com.wirelesscar.tce.core.conf.TceConfig;
import com.wirelesscar.tce.module.api.Message;
import com.wirelesscar.tce.module.api.MetaData;
import com.wirelesscar.tce.module.api.ModuleBase;
import com.wirelesscar.tce.module.api.ModuleTypeConstants;
import com.wirelesscar.tce.module.api.TransportType;
import com.wirelesscar.tce.module.api.exception.EngineRuntimeException;
import com.wirelesscar.tce.module.metrics.ModuleMetricReporter;
import com.wirelesscar.tce.utils.MetadataUtils;
import com.wirelesscar.tcevce.module.identify.metrics.VceIdentifyModuleMetricReporter;
import com.wirelesscar.tcevce.wecu.device.info.database.model.DeviceInfo;

@Component(ModuleTypeConstants.VCE_IDENTIFY)
@Scope("prototype")
public class VceIdentifyModule extends ModuleBase {
  private static final Logger logger = LoggerFactory.getLogger(VceIdentifyModule.class);

  @TceConfig(configKey = "activate-down", defaultValue = "true")
  private boolean isActiveDown;

  @TceConfig(configKey = "activate-up", defaultValue = "true")
  private boolean isActiveUp;

  private MetaData[] requiredMetadata;

  @TceConfig(configKey = "metadata", defaultValue = "")
  private String requiredMetadataStr = "";

  private final VceIdentifyModuleMetricReporter vceIdentifyModuleMetricReporter;
  private final VceIdentifyService vceIdentifyService;

  public VceIdentifyModule(ModuleMetricReporter moduleMetricReporter, VceIdentifyModuleMetricReporter vceIdentifyModuleMetricReporter,
      VceIdentifyService vceIdentifyService) {
    super(moduleMetricReporter);
    this.vceIdentifyModuleMetricReporter = vceIdentifyModuleMetricReporter;
    this.vceIdentifyService = vceIdentifyService;
  }

  private static void checkMtTag(Message message) {
    if (message.getProperty(MetaData.MT) == null) {
      message.setProperty(MetaData.MT, Boolean.TRUE.toString());
    }
  }

  @Override
  public void deploy() {
    requiredMetadata = MetadataUtils.parseMetaData(requiredMetadataStr);

    logger.info("Active up: {}", isActiveUp);
    logger.info("Active down: {}", isActiveDown);
  }

  @Override
  public void down(Message message) {
    MetadataUtils.validateRequiredMetaData(message, requiredMetadata);

    if (isActiveDown) {
      logger.debug("DOWN: About to identify message: {}", message);

      long ts0 = System.nanoTime();
      checkMtTag(message);

      Optional<DeviceInfo> deviceInfo = identifyDeviceDown(message);

      if (deviceInfo.isEmpty()) {
        vceIdentifyModuleMetricReporter.onDownFail(getStackName());
        logger.warn("DOWN: Dropping message, Not able to identify device for: {}", message);
        return;
      }

      VceIdentifyMessagePropertiesUtil.setProperties(message, deviceInfo.get(), true);
      vceIdentifyModuleMetricReporter.onDown(Duration.ofNanos(System.nanoTime() - ts0), getStackName());
    }

    sendDown(message);
  }

  @Override
  public void up(Message message) {
    MetadataUtils.validateRequiredMetaData(message, requiredMetadata);

    if (isActiveUp) {
      logger.debug("UP: About to identify message: {}", message);
      long startTime = System.nanoTime();

      identifyDeviceUp(message).ifPresentOrElse(
          deviceInfo -> VceIdentifyMessagePropertiesUtil.setProperties(message, deviceInfo, false),
          () -> message.setProperty(MetaData.VEHICLE_NOT_FOUND, Boolean.TRUE.toString()));

      if (message.getVehicleID() == null) {
        vceIdentifyModuleMetricReporter.onUpFail(getStackName());

        reportUnknownMessages(message);

        // Set MetaData indicating that vehicle doesn't exists, used by segmentation and splitAndAck
        // modules
        if (message.getProperty(MetaData.VEHICLE_NOT_FOUND) == null) {
          logger.info("UP: About to drop message: {}", message);
          return;
        }
      }
      vceIdentifyModuleMetricReporter.onUp(Duration.ofNanos(System.nanoTime() - startTime), getStackName());
    }

    logger.debug("UP: Sending message: {}", message);
    sendUp(message);
  }

  private Optional<DeviceInfo> identifyDeviceDown(Message message) {
    try {
      return vceIdentifyService.identifyDeviceDown(message, getStackName());
    } catch (Exception e) {
      throw new EngineRuntimeException("DOWN: Error identifying message: " + message, e);
    }
  }

  private Optional<DeviceInfo> identifyDeviceUp(Message message) {
    try {
      return vceIdentifyService.identifyDeviceUp(message, getStackName());
    } catch (Exception e) {
      throw new EngineRuntimeException("UP: Error identifying message: " + message, e);
    }
  }

  private void reportUnknownMessages(Message message) {
    String transportType = message.getProperty(MetaData.TRANSPORT_TYPE);
    if (transportType == null || transportType.isEmpty()) {
      return;
    }
    if (transportType.equals(TransportType.UDP.name())) {
      vceIdentifyModuleMetricReporter.onUpFailureUdp(getStackName());
      return;
    }
    if (transportType.equals(TransportType.SMS.name())) {
      vceIdentifyModuleMetricReporter.onUpFailureSms(getStackName());
      return;
    }
    if (transportType.equals(TransportType.SATELLITE.name())) {
      vceIdentifyModuleMetricReporter.onUpFailureSat(getStackName());
    }
  }
}
