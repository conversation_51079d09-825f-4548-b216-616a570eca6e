package com.wirelesscar.tcevce.module.identify.metrics;

import org.springframework.stereotype.Component;

import com.volvo.tisp.vc.main.utils.lib.Validate;

import io.micrometer.core.instrument.MeterRegistry;

@Component
public class ConrepoClientMetricReporter {
  static final String BY = "by";
  static final String CONREPO_LOOKUP = "conrepo-lookup";
  static final String FAILED = "failed";
  static final String STATUS = "status";

  private final MeterRegistry meterRegistry;

  public ConrepoClientMetricReporter(MeterRegistry meterRegistry) {
    this.meterRegistry = meterRegistry;
  }

  public void onLookupFailure(String lookupBy) {
    Validate.notEmpty(lookupBy, "lookupBy");

    meterRegistry.counter(CONREPO_LOOKUP, BY, lookupBy, STATUS, FAILED).increment();
  }
}