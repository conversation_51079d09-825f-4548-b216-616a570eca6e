package com.wirelesscar.tcevce.module.identify;

import java.util.Optional;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.volvo.tisp.vc.common.dto.lib.vehicle.Ipv4Address;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Msisdn;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.wirelesscar.tce.module.api.Message;
import com.wirelesscar.tcevce.wecu.device.info.database.model.DeviceInfo;
import com.wirelesscar.tcevce.wecu.device.info.database.model.Handle;
import com.wirelesscar.tcevce.wecu.device.info.database.model.SatelliteId;

@Component
class VceIdentifyService {
  private static final String MESSAGE_ARG_VARIABLE_NAME = "message";
  private static final String STACK_NAME_ARG_VARIABLE_NAME = "stackName";
  private static final Logger logger = LoggerFactory.getLogger(VceIdentifyService.class);

  private final ConrepoIdentifier conrepoIdentifier;
  private final VceLocalIdentifier vceLocalIdentifier;

  VceIdentifyService(VceLocalIdentifier vceLocalIdentifier, ConrepoIdentifier conrepoIdentifier) {
    this.vceLocalIdentifier = vceLocalIdentifier;
    this.conrepoIdentifier = conrepoIdentifier;
  }

  Optional<DeviceInfo> identifyDeviceDown(Message message, String stackName) {
    Validate.notNull(message, MESSAGE_ARG_VARIABLE_NAME);
    Validate.notEmpty(stackName, STACK_NAME_ARG_VARIABLE_NAME);

    Optional<Vpi> vpiOptional = VceIdentifyMessagePropertiesUtil.getVpi(message);
    if (vpiOptional.isPresent()) {
      logger.debug("Try to identify device by VPI, stackName={}, {}", stackName, message);
      Vpi vpi = vpiOptional.get();
      return vceLocalIdentifier.tryVpi(vpi, stackName).or(() -> conrepoIdentifier.tryVpi(vpi, stackName));
    }

    logger.debug("Try to identify device by HANDLE, stackName={}, {}", stackName, message);
    Optional<Handle> handle = VceIdentifyMessagePropertiesUtil.getHandle(message);
    if (handle.isPresent()) {
      return vceLocalIdentifier.tryHandle(handle.get(), stackName).or(() -> conrepoIdentifier.tryHandle(handle.get(), stackName));
    }
    return Optional.empty();
  }

  Optional<DeviceInfo> identifyDeviceUp(Message message, String stackName) {
    Validate.notNull(message, MESSAGE_ARG_VARIABLE_NAME);
    Validate.notEmpty(stackName, STACK_NAME_ARG_VARIABLE_NAME);

    Optional<Ipv4Address> ipv4AddressOptional = VceIdentifyMessagePropertiesUtil.getSourceIpv4Address(message);
    if (ipv4AddressOptional.isPresent()) {
      logger.debug("Try to identify device by IPV4_ADDRESS, stackName={}, {}", stackName, message);
      Ipv4Address ipv4Address = ipv4AddressOptional.get();
      return vceLocalIdentifier.tryIp(ipv4Address, stackName).or(() -> conrepoIdentifier.tryIp(ipv4Address, stackName));
    }

    Optional<Msisdn> msisdnOptional = VceIdentifyMessagePropertiesUtil.getSourceMsisdn(message);
    if (msisdnOptional.isPresent()) {
      logger.debug("Try to identify device by MSISDN, stackName={}, {}", stackName, message);
      Msisdn msisdn = msisdnOptional.get();
      return vceLocalIdentifier.tryMsisdn(msisdn, stackName).or(() -> conrepoIdentifier.tryMsisdn(msisdn, stackName));
    }

    Optional<SatelliteId> satelliteIdOptional = VceIdentifyMessagePropertiesUtil.getSourceSatelliteId(message);
    if (satelliteIdOptional.isPresent()) {
      logger.debug("Try to identify device by SATELLITE_ID, stackName={}, {}", stackName, message);
      SatelliteId satelliteId = satelliteIdOptional.get();
      return vceLocalIdentifier.trySatellite(satelliteId, stackName).or(() -> conrepoIdentifier.trySatellite(satelliteId, stackName));
    }

    throw new IllegalStateException("no identifier (ip/msisdn/satId) available for identifying message: " + message);
  }
}
