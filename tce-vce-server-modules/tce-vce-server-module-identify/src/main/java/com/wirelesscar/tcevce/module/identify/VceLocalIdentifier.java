package com.wirelesscar.tcevce.module.identify;

import java.util.Optional;
import java.util.function.Function;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.volvo.tisp.vc.common.dto.lib.vehicle.Ipv4Address;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Msisdn;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.wirelesscar.tcevce.module.identify.metrics.VceIdentifyModuleMetricReporter;
import com.wirelesscar.tcevce.wecu.device.info.database.api.DeviceInfoReader;
import com.wirelesscar.tcevce.wecu.device.info.database.api.DeviceInfoReaderFactory;
import com.wirelesscar.tcevce.wecu.device.info.database.model.DeviceInfo;
import com.wirelesscar.tcevce.wecu.device.info.database.model.Handle;
import com.wirelesscar.tcevce.wecu.device.info.database.model.PersistedDeviceInfo;
import com.wirelesscar.tcevce.wecu.device.info.database.model.SatelliteId;

@Component
class VceLocalIdentifier {
  private static final String STACK_NAME_ARG_VARIABLE_NAME = "stackName";
  private static final Logger logger = LoggerFactory.getLogger(VceLocalIdentifier.class);

  private final DeviceInfoReaderFactory deviceInfoReaderFactory;
  private final VceIdentifyModuleMetricReporter vceIdentifyModuleMetricReporter;

  VceLocalIdentifier(VceIdentifyModuleMetricReporter vceIdentifyModuleMetricReporter, DeviceInfoReaderFactory deviceInfoReaderFactory) {
    this.vceIdentifyModuleMetricReporter = vceIdentifyModuleMetricReporter;
    this.deviceInfoReaderFactory = deviceInfoReaderFactory;
  }

  Optional<DeviceInfo> tryHandle(Handle handle, String stackName) {
    Validate.notNull(handle, "handle");
    Validate.notEmpty(stackName, STACK_NAME_ARG_VARIABLE_NAME);

    Optional<DeviceInfo> deviceInfoFound = findDeviceInfo(deviceInfoReader -> deviceInfoReader.findDeviceInfoByHandle(handle));

    deviceInfoFound.ifPresentOrElse(deviceInfo -> {
          logger.debug("Identified device by HANDLE, stackName={}, {}", stackName, handle);
          vceIdentifyModuleMetricReporter.onIdByHandle(stackName);
        },
        () -> logger.info("Can't identify device by HANDLE, stackName={}, {}", stackName, handle));

    return deviceInfoFound;
  }

  Optional<DeviceInfo> tryIp(Ipv4Address ipv4Address, String stackName) {
    Validate.notNull(ipv4Address, "ipv4Address");
    Validate.notEmpty(stackName, STACK_NAME_ARG_VARIABLE_NAME);

    Optional<DeviceInfo> deviceInfoFound = findDeviceInfo(deviceInfoReader -> deviceInfoReader.findDeviceInfoByIpv4Address(ipv4Address));

    deviceInfoFound.ifPresentOrElse(deviceInfo -> {
          logger.debug("Identified device by IPV4_ADDRESS, stackName={}, {}", stackName, ipv4Address);
          vceIdentifyModuleMetricReporter.onIdByIp(stackName);
        },
        () -> logger.info("Can't identify device by IPV4_ADDRESS, stackName={}, {}", stackName, ipv4Address));

    return deviceInfoFound;
  }

  Optional<DeviceInfo> tryMsisdn(Msisdn msisdn, String stackName) {
    Validate.notNull(msisdn, "msisdn");
    Validate.notEmpty(stackName, STACK_NAME_ARG_VARIABLE_NAME);

    Optional<DeviceInfo> deviceInfoFound = findDeviceInfo(deviceInfoReader -> deviceInfoReader.findDeviceInfoByMsisdn(msisdn));

    deviceInfoFound.ifPresentOrElse(deviceInfo -> {
          logger.debug("Identified device by MSISDN, stackName={}, {}", stackName, msisdn);
          vceIdentifyModuleMetricReporter.onIdByMsisdn(stackName);
        },
        () -> logger.info("Can't identify device by MSISDN, stackName={}, {}", stackName, msisdn));

    return deviceInfoFound;
  }

  Optional<DeviceInfo> trySatellite(SatelliteId satelliteId, String stackName) {
    Validate.notNull(satelliteId, "satelliteId");
    Validate.notEmpty(stackName, STACK_NAME_ARG_VARIABLE_NAME);

    Optional<DeviceInfo> deviceInfoFound = findDeviceInfo(deviceInfoReader -> deviceInfoReader.findDeviceInfoBySatelliteId(satelliteId));

    deviceInfoFound.ifPresentOrElse(deviceInfo -> {
          logger.debug("Identified device by SATELLITE_ID, stackName={}, {}", stackName, satelliteId);
          vceIdentifyModuleMetricReporter.onIdBySat(stackName);
        },
        () -> logger.info("Can't identify device by SATELLITE_ID, stackName={}, {}", stackName, satelliteId));

    return deviceInfoFound;
  }

  Optional<DeviceInfo> tryVpi(Vpi vpi, String stackName) {
    Validate.notNull(vpi, "vpi");
    Validate.notEmpty(stackName, STACK_NAME_ARG_VARIABLE_NAME);

    Optional<DeviceInfo> deviceInfoFound = findDeviceInfo(deviceInfoReader -> deviceInfoReader.findDeviceInfoByVpi(vpi));

    deviceInfoFound.ifPresentOrElse(deviceInfo -> {
          logger.debug("Identified device by VPI, stackName={}, {}", stackName, vpi);
          vceIdentifyModuleMetricReporter.onIdByVid(stackName);
        },
        () -> logger.info("Can't identify device by VPI, stackName={}, {}", stackName, vpi));

    return deviceInfoFound;
  }

  private Optional<DeviceInfo> findDeviceInfo(Function<DeviceInfoReader, Optional<PersistedDeviceInfo>> readerOptionalFunction) {
    try (DeviceInfoReader deviceInfoReader = deviceInfoReaderFactory.create()) {
      return readerOptionalFunction.apply(deviceInfoReader).map(PersistedDeviceInfo::getDeviceInfo);
    }
  }
}
