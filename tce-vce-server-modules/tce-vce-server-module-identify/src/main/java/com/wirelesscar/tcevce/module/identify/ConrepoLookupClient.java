package com.wirelesscar.tcevce.module.identify;

import java.net.URI;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.wirelesscar.conrepo.ContentTypes;
import com.wirelesscar.conrepo.api.v1.DeviceDetailedEntry;
import com.wirelesscar.tcevce.module.identify.metrics.ConrepoClientMetricReporter;

public class ConrepoLookupClient {
  private static final String HANDLE = "handle";
  private static final String IP = "ip";
  private static final String MSISDN = "msisdn";
  private static final String SAT_ID = "satId";
  private static final String VEHICLE_PLATFORM_ID = "vehiclePlatformId";

  private final String baseUrl;
  private final ConrepoClientMetricReporter conrepoClientMetricReporter;
  private final RestTemplate restTemplate;

  public ConrepoLookupClient(RestTemplate restTemplate, ConrepoClientMetricReporter conrepoClientMetricReporter, String baseUrl) {
    Validate.notNull(restTemplate, "restTemplate");
    Validate.notNull(conrepoClientMetricReporter, "conrepoClientMetricReporter");
    Validate.notEmpty(baseUrl, "baseUrl");

    this.restTemplate = restTemplate;
    this.conrepoClientMetricReporter = conrepoClientMetricReporter;
    this.baseUrl = baseUrl;
  }

  private static HttpEntity<Void> createHttpEntity() {
    HttpHeaders httpHeaders = new HttpHeaders();
    httpHeaders.setAccept(List.of(MediaType.parseMediaType(ContentTypes.CONREPO_1_0_XML), MediaType.parseMediaType(ContentTypes.CONREPO_1_0_JSON)));
    return new HttpEntity<>(httpHeaders);
  }

  public Optional<DeviceDetailedEntry> lookupByMsisdn(String msisdn) {
    Validate.notEmpty(msisdn, MSISDN);

    return get(buildUriWithPathParameters("/conrepo/msisdn/{msisdn}", Map.of(MSISDN, msisdn)), MSISDN);
  }

  Optional<DeviceDetailedEntry> lookupByHandle(String handle) {
    Validate.notEmpty(handle, HANDLE);

    return get(buildUriWithOneQueryParameter("/conrepo/handle", HANDLE, handle), HANDLE);
  }

  Optional<DeviceDetailedEntry> lookupByIp(String ip) {
    Validate.notEmpty(ip, IP);

    return get(buildUriWithOneQueryParameter("/conrepo/ip", IP, ip), IP);
  }

  Optional<DeviceDetailedEntry> lookupBySatId(String satId) {
    Validate.notEmpty(satId, SAT_ID);

    return get(buildUriWithPathParameters("/conrepo/satid/{satId}", Map.of(SAT_ID, satId)), SAT_ID);
  }

  Optional<DeviceDetailedEntry> lookupByVehiclePlatformId(String vehiclePlatformId) {
    Validate.notEmpty(vehiclePlatformId, VEHICLE_PLATFORM_ID);

    return get(buildUriWithPathParameters("/conrepo/vehicleplatformid/{id}", Map.of("id", vehiclePlatformId)), VEHICLE_PLATFORM_ID);
  }

  private URI buildUriWithOneQueryParameter(String path, String queryParameterName, String queryParameterValue) {
    return UriComponentsBuilder.fromHttpUrl(baseUrl)
        .path(path)
        .queryParam(queryParameterName, queryParameterValue)
        .build(false)
        .toUri();
  }

  private URI buildUriWithPathParameters(String path, Map<String, Object> parameters) {
    return UriComponentsBuilder.fromHttpUrl(baseUrl)
        .path(path)
        .uriVariables(parameters)
        .build(false)
        .toUri();
  }

  private Optional<DeviceDetailedEntry> get(URI uri, String micrometerMetricName) {
    ResponseEntity<DeviceDetailedEntry> responseEntity = restTemplate.exchange(uri, HttpMethod.GET, createHttpEntity(), DeviceDetailedEntry.class);
    HttpStatus httpStatus = HttpStatus.valueOf(responseEntity.getStatusCode().value());
    if (httpStatus.isError()) {
      conrepoClientMetricReporter.onLookupFailure(micrometerMetricName);
      if (httpStatus != HttpStatus.NOT_FOUND) {
        // the message should be not acked if it could not be identified due to conrepo error.
        throw new IllegalStateException("httpStatus from conrepo is " + httpStatus);
      }
    }
    return Optional.ofNullable(responseEntity.getBody());
  }
}
