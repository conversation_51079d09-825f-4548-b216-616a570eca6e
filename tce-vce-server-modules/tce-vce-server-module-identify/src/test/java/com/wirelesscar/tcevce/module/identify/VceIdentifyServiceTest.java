package com.wirelesscar.tcevce.module.identify;

import java.util.Optional;
import java.util.function.BiConsumer;
import java.util.function.BiFunction;
import java.util.function.Consumer;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import com.volvo.tisp.vc.test.utils.lib.AssertThrows;
import com.wirelesscar.tce.module.api.Message;
import com.wirelesscar.tce.module.api.MetaData;
import com.wirelesscar.tcevce.wecu.device.info.database.model.DeviceInfo;

class VceIdentifyServiceTest {
  private static final String STACK = "stack";

  private static void identifyDeviceTest(Optional<DeviceInfo> expectOptional, BiConsumer<ConrepoIdentifier, VceLocalIdentifier> identifierMockConsumer,
      Consumer<Message> messageConsumer,
      BiFunction<VceIdentifyService, Message, Optional<DeviceInfo>> testBiFunction,
      BiConsumer<ConrepoIdentifier, VceLocalIdentifier> identifierVerifyConsumer) {
    ConrepoIdentifier conrepoIdentifier = Mockito.mock(ConrepoIdentifier.class);
    VceLocalIdentifier vceLocalIdentifier = Mockito.mock(VceLocalIdentifier.class);
    identifierMockConsumer.accept(conrepoIdentifier, vceLocalIdentifier);

    Message message = new Message("messageId", null);
    messageConsumer.accept(message);

    VceIdentifyService vceIdentifyService = new VceIdentifyService(vceLocalIdentifier, conrepoIdentifier);
    Optional<DeviceInfo> actualOptional = testBiFunction.apply(vceIdentifyService, message);

    Assertions.assertSame(expectOptional, actualOptional);
    identifierVerifyConsumer.accept(conrepoIdentifier, vceLocalIdentifier);
    Mockito.verifyNoMoreInteractions(conrepoIdentifier, vceLocalIdentifier);
  }

  @Test
  void identifyDeviceDownHandleDeviceExistLocallyTest() {
    Optional<DeviceInfo> optional = Optional.of(TestUtils.createDeviceInfo());

    identifyDeviceTest(optional,
        (conrepoIdentifier, vceLocalIdentifier) -> Mockito.when(vceLocalIdentifier.tryHandle(TestUtils.HANDLE, STACK)).thenReturn(optional),
        message -> message.setProperty(MetaData.HANDLE, TestUtils.HANDLE.toString()),
        (vceIdentifyService, message) -> vceIdentifyService.identifyDeviceDown(message, STACK),
        (conrepoIdentifier, vceLocalIdentifier) -> {
          Mockito.verify(vceLocalIdentifier).tryHandle(TestUtils.HANDLE, STACK);
        });
  }

  @Test
  void identifyDeviceDownHandleDeviceFoundByConrepoTest() {
    Optional<DeviceInfo> optional = Optional.of(TestUtils.createDeviceInfo());
    identifyDeviceTest(optional,
        (conrepoIdentifier, vceLocalIdentifier) -> {
          Mockito.when(vceLocalIdentifier.tryHandle(TestUtils.HANDLE, STACK)).thenReturn(Optional.empty());
          Mockito.when(conrepoIdentifier.tryHandle(TestUtils.HANDLE, STACK)).thenReturn(optional);
        },
        message -> message.setProperty(MetaData.HANDLE, TestUtils.HANDLE.toString()),
        (vceIdentifyService, message) -> vceIdentifyService.identifyDeviceDown(message, STACK),
        (conrepoIdentifier, vceLocalIdentifier) -> {
          Mockito.verify(vceLocalIdentifier).tryHandle(TestUtils.HANDLE, STACK);
          Mockito.verify(conrepoIdentifier).tryHandle(TestUtils.HANDLE, STACK);
        });
  }

  @Test
  void identifyDeviceDownHandleDeviceNotFoundTest() {
    identifyDeviceTest(Optional.empty(),
        (conrepoIdentifier, vceLocalIdentifier) -> {
          Mockito.when(vceLocalIdentifier.tryHandle(TestUtils.HANDLE, STACK)).thenReturn(Optional.empty());
          Mockito.when(conrepoIdentifier.tryHandle(TestUtils.HANDLE, STACK)).thenReturn(Optional.empty());
        },
        message -> message.setProperty(MetaData.HANDLE, TestUtils.HANDLE.toString()),
        (vceIdentifyService, message) -> vceIdentifyService.identifyDeviceDown(message, STACK),
        (conrepoIdentifier, vceLocalIdentifier) -> {
          Mockito.verify(vceLocalIdentifier).tryHandle(TestUtils.HANDLE, STACK);
          Mockito.verify(conrepoIdentifier).tryHandle(TestUtils.HANDLE, STACK);
        });
  }

  @Test
  void identifyDeviceDownInvalidParametesTest() {
    VceIdentifyService vceIdentifyService = new VceIdentifyService(Mockito.mock(VceLocalIdentifier.class), Mockito.mock(ConrepoIdentifier.class));

    AssertThrows.illegalArgumentException(() -> vceIdentifyService.identifyDeviceDown(null, STACK), "message must not be null");
    AssertThrows.illegalArgumentException(() -> vceIdentifyService.identifyDeviceDown(new Message("messageId", null), null), "stackName must not be null");
    AssertThrows.illegalArgumentException(() -> vceIdentifyService.identifyDeviceDown(new Message("messageId", null), ""), "stackName must not be empty");
  }

  @Test
  void identifyDeviceDownNoIdentifierTest() {
    ConrepoIdentifier conrepoIdentifier = Mockito.mock(ConrepoIdentifier.class);
    VceLocalIdentifier vceLocalIdentifier = Mockito.mock(VceLocalIdentifier.class);
    VceIdentifyService vceIdentifyService = new VceIdentifyService(vceLocalIdentifier, conrepoIdentifier);

    Message message = new Message("messageId", null);

    Optional<DeviceInfo> deviceInfo = vceIdentifyService.identifyDeviceDown(message, STACK);
    Assertions.assertTrue(deviceInfo.isEmpty());

    Mockito.verifyNoMoreInteractions(vceLocalIdentifier, conrepoIdentifier);
  }

  @Test
  void identifyDeviceDownVpiDeviceExistLocallyTest() {
    Optional<DeviceInfo> optional = Optional.of(TestUtils.createDeviceInfo());
    identifyDeviceTest(optional,
        (conrepoIdentifier, vceLocalIdentifier) -> {
          Mockito.when(vceLocalIdentifier.tryVpi(TestUtils.VPI, STACK)).thenReturn(optional);
        },
        message -> message.setProperty(MetaData.VPI, TestUtils.VPI.toString()),
        (vceIdentifyService, message) -> vceIdentifyService.identifyDeviceDown(message, STACK),
        (conrepoIdentifier, vceLocalIdentifier) -> {
          Mockito.verify(vceLocalIdentifier).tryVpi(TestUtils.VPI, STACK);
        });
  }

  @Test
  void identifyDeviceDownVpiDeviceFoundByConrepoTest() {
    Optional<DeviceInfo> optional = Optional.of(TestUtils.createDeviceInfo());
    identifyDeviceTest(optional,
        (conrepoIdentifier, vceLocalIdentifier) -> {
          Mockito.when(vceLocalIdentifier.tryVpi(TestUtils.VPI, STACK)).thenReturn(Optional.empty());
          Mockito.when(conrepoIdentifier.tryVpi(TestUtils.VPI, STACK)).thenReturn(optional);
        },
        message -> message.setProperty(MetaData.VPI, TestUtils.VPI.toString()),
        (vceIdentifyService, message) -> vceIdentifyService.identifyDeviceDown(message, STACK),
        (conrepoIdentifier, vceLocalIdentifier) -> {
          Mockito.verify(vceLocalIdentifier).tryVpi(TestUtils.VPI, STACK);
          Mockito.verify(conrepoIdentifier).tryVpi(TestUtils.VPI, STACK);
        });
  }

  @Test
  void identifyDeviceDownVpiDeviceNotFoundTest() {
    identifyDeviceTest(Optional.empty(),
        (conrepoIdentifier, vceLocalIdentifier) -> {
          Mockito.when(vceLocalIdentifier.tryVpi(TestUtils.VPI, STACK)).thenReturn(Optional.empty());
          Mockito.when(conrepoIdentifier.tryVpi(TestUtils.VPI, STACK)).thenReturn(Optional.empty());
        },
        message -> message.setProperty(MetaData.VPI, TestUtils.VPI.toString()),
        (vceIdentifyService, message) -> vceIdentifyService.identifyDeviceDown(message, STACK),
        (conrepoIdentifier, vceLocalIdentifier) -> {
          Mockito.verify(vceLocalIdentifier).tryVpi(TestUtils.VPI, STACK);
          Mockito.verify(conrepoIdentifier).tryVpi(TestUtils.VPI, STACK);
        });
  }

  @Test
  void identifyDeviceUpInvalidParametesTest() {
    VceIdentifyService vceIdentifyService = new VceIdentifyService(Mockito.mock(VceLocalIdentifier.class), Mockito.mock(ConrepoIdentifier.class));

    AssertThrows.illegalArgumentException(() -> vceIdentifyService.identifyDeviceUp(null, STACK), "message must not be null");
    AssertThrows.illegalArgumentException(() -> vceIdentifyService.identifyDeviceUp(new Message("messageId", null), null), "stackName must not be null");
    AssertThrows.illegalArgumentException(() -> vceIdentifyService.identifyDeviceUp(new Message("messageId", null), ""), "stackName must not be empty");
  }

  @Test
  void identifyDeviceUpNoIdentifierTest() {
    ConrepoIdentifier conrepoIdentifier = Mockito.mock(ConrepoIdentifier.class);
    VceLocalIdentifier vceLocalIdentifier = Mockito.mock(VceLocalIdentifier.class);
    VceIdentifyService vceIdentifyService = new VceIdentifyService(vceLocalIdentifier, conrepoIdentifier);

    Message message = new Message("messageId", null);

    AssertThrows.illegalStateException(() -> vceIdentifyService.identifyDeviceUp(message, STACK),
        "no identifier (ip/msisdn/satId) available for identifying message: Message[messageId=messageId, vehicleID=null, Type=null, payloadSize=0, Properties={MESSAGE_ID=messageId}]");

    Mockito.verifyNoMoreInteractions(vceLocalIdentifier, conrepoIdentifier);
  }

  @Test
  void identifyDeviceUpSatDeviceExistLocallyTest() {
    Optional<DeviceInfo> optional = Optional.of(TestUtils.createDeviceInfo());
    identifyDeviceTest(optional,
        (conrepoIdentifier, vceLocalIdentifier) -> {
          Mockito.when(vceLocalIdentifier.trySatellite(TestUtils.SATELLITE_ID, STACK)).thenReturn(optional);
        },
        message -> message.setProperty(MetaData.SATELLITE_SOURCE_ADDRESS, TestUtils.SATELLITE_ID.toString()),
        (vceIdentifyService, message) -> vceIdentifyService.identifyDeviceUp(message, STACK),
        (conrepoIdentifier, vceLocalIdentifier) -> {
          Mockito.verify(vceLocalIdentifier).trySatellite(TestUtils.SATELLITE_ID, STACK);
        });
  }

  @Test
  void identifyDeviceUpSatDeviceFoundByConrepoTest() {
    Optional<DeviceInfo> optional = Optional.of(TestUtils.createDeviceInfo());
    identifyDeviceTest(optional,
        (conrepoIdentifier, vceLocalIdentifier) -> {
          Mockito.when(vceLocalIdentifier.trySatellite(TestUtils.SATELLITE_ID, STACK)).thenReturn(Optional.empty());
          Mockito.when(conrepoIdentifier.trySatellite(TestUtils.SATELLITE_ID, STACK)).thenReturn(optional);
        },
        message -> message.setProperty(MetaData.SATELLITE_SOURCE_ADDRESS, TestUtils.SATELLITE_ID.toString()),
        (vceIdentifyService, message) -> vceIdentifyService.identifyDeviceUp(message, STACK),
        (conrepoIdentifier, vceLocalIdentifier) -> {
          Mockito.verify(vceLocalIdentifier).trySatellite(TestUtils.SATELLITE_ID, STACK);
          Mockito.verify(conrepoIdentifier).trySatellite(TestUtils.SATELLITE_ID, STACK);
        });
  }

  @Test
  void identifyDeviceUpSatDeviceNotFoundTest() {
    identifyDeviceTest(Optional.empty(),
        (conrepoIdentifier, vceLocalIdentifier) -> {
          Mockito.when(vceLocalIdentifier.trySatellite(TestUtils.SATELLITE_ID, STACK)).thenReturn(Optional.empty());
          Mockito.when(conrepoIdentifier.trySatellite(TestUtils.SATELLITE_ID, STACK)).thenReturn(Optional.empty());
        },
        message -> message.setProperty(MetaData.SATELLITE_SOURCE_ADDRESS, TestUtils.SATELLITE_ID.toString()),
        (vceIdentifyService, message) -> vceIdentifyService.identifyDeviceUp(message, STACK),
        (conrepoIdentifier, vceLocalIdentifier) -> {
          Mockito.verify(vceLocalIdentifier).trySatellite(TestUtils.SATELLITE_ID, STACK);
          Mockito.verify(conrepoIdentifier).trySatellite(TestUtils.SATELLITE_ID, STACK);
        });
  }

  @Test
  void identifyDeviceUpSmsDeviceExistLocallyTest() {
    Optional<DeviceInfo> optional = Optional.of(TestUtils.createDeviceInfo());
    identifyDeviceTest(optional,
        (conrepoIdentifier, vceLocalIdentifier) -> {
          Mockito.when(vceLocalIdentifier.tryMsisdn(TestUtils.MSISDN, STACK)).thenReturn(optional);
        },
        message -> message.setProperty(MetaData.SMPP_SOURCE_ADDRESS, TestUtils.MSISDN.toString()),
        (vceIdentifyService, message) -> vceIdentifyService.identifyDeviceUp(message, STACK),
        (conrepoIdentifier, vceLocalIdentifier) -> {
          Mockito.verify(vceLocalIdentifier).tryMsisdn(TestUtils.MSISDN, STACK);
        });
  }

  @Test
  void identifyDeviceUpSmsDeviceFoundByConrepoTest() {
    Optional<DeviceInfo> optional = Optional.of(TestUtils.createDeviceInfo());
    identifyDeviceTest(optional,
        (conrepoIdentifier, vceLocalIdentifier) -> {
          Mockito.when(vceLocalIdentifier.tryMsisdn(TestUtils.MSISDN, STACK)).thenReturn(Optional.empty());
          Mockito.when(conrepoIdentifier.tryMsisdn(TestUtils.MSISDN, STACK)).thenReturn(optional);
        },
        message -> message.setProperty(MetaData.SMPP_SOURCE_ADDRESS, TestUtils.MSISDN.toString()),
        (vceIdentifyService, message) -> vceIdentifyService.identifyDeviceUp(message, STACK),
        (conrepoIdentifier, vceLocalIdentifier) -> {
          Mockito.verify(vceLocalIdentifier).tryMsisdn(TestUtils.MSISDN, STACK);
          Mockito.verify(conrepoIdentifier).tryMsisdn(TestUtils.MSISDN, STACK);
        });
  }

  @Test
  void identifyDeviceUpSmsDeviceNotFoundTest() {
    identifyDeviceTest(Optional.empty(),
        (conrepoIdentifier, vceLocalIdentifier) -> {
          Mockito.when(vceLocalIdentifier.tryMsisdn(TestUtils.MSISDN, STACK)).thenReturn(Optional.empty());
          Mockito.when(conrepoIdentifier.tryMsisdn(TestUtils.MSISDN, STACK)).thenReturn(Optional.empty());
        },
        message -> message.setProperty(MetaData.SMPP_SOURCE_ADDRESS, TestUtils.MSISDN.toString()),
        (vceIdentifyService, message) -> vceIdentifyService.identifyDeviceUp(message, STACK),
        (conrepoIdentifier, vceLocalIdentifier) -> {
          Mockito.verify(vceLocalIdentifier).tryMsisdn(TestUtils.MSISDN, STACK);
          Mockito.verify(conrepoIdentifier).tryMsisdn(TestUtils.MSISDN, STACK);
        });
  }

  @Test
  void identifyDeviceUpUdpDeviceExistLocallyTest() {
    Optional<DeviceInfo> optional = Optional.of(TestUtils.createDeviceInfo());
    identifyDeviceTest(optional,
        (conrepoIdentifier, vceLocalIdentifier) -> {
          Mockito.when(vceLocalIdentifier.tryIp(TestUtils.IPV4_ADDRESS, STACK)).thenReturn(optional);
        },
        message -> message.setProperty(MetaData.IP_SRC_ADDRESS, TestUtils.IPV4_ADDRESS.toString()),
        (vceIdentifyService, message) -> vceIdentifyService.identifyDeviceUp(message, STACK),
        (conrepoIdentifier, vceLocalIdentifier) -> {
          Mockito.verify(vceLocalIdentifier).tryIp(TestUtils.IPV4_ADDRESS, STACK);
        });
  }

  @Test
  void identifyDeviceUpUdpDeviceFoundByConrepoTest() {
    Optional<DeviceInfo> optional = Optional.of(TestUtils.createDeviceInfo());
    identifyDeviceTest(optional,
        (conrepoIdentifier, vceLocalIdentifier) -> {
          Mockito.when(vceLocalIdentifier.tryIp(TestUtils.IPV4_ADDRESS, STACK)).thenReturn(Optional.empty());
          Mockito.when(conrepoIdentifier.tryIp(TestUtils.IPV4_ADDRESS, STACK)).thenReturn(optional);
        },
        message -> message.setProperty(MetaData.IP_SRC_ADDRESS, TestUtils.IPV4_ADDRESS.toString()),
        (vceIdentifyService, message) -> vceIdentifyService.identifyDeviceUp(message, STACK),
        (conrepoIdentifier, vceLocalIdentifier) -> {
          Mockito.verify(vceLocalIdentifier).tryIp(TestUtils.IPV4_ADDRESS, STACK);
          Mockito.verify(conrepoIdentifier).tryIp(TestUtils.IPV4_ADDRESS, STACK);
        });
  }

  @Test
  void identifyDeviceUpUdpDeviceNotFoundTest() {
    identifyDeviceTest(Optional.empty(),
        (conrepoIdentifier, vceLocalIdentifier) -> {
          Mockito.when(vceLocalIdentifier.tryIp(TestUtils.IPV4_ADDRESS, STACK)).thenReturn(Optional.empty());
          Mockito.when(conrepoIdentifier.tryIp(TestUtils.IPV4_ADDRESS, STACK)).thenReturn(Optional.empty());
        },
        message -> message.setProperty(MetaData.IP_SRC_ADDRESS, TestUtils.IPV4_ADDRESS.toString()),
        (vceIdentifyService, message) -> vceIdentifyService.identifyDeviceUp(message, STACK),
        (conrepoIdentifier, vceLocalIdentifier) -> {
          Mockito.verify(vceLocalIdentifier).tryIp(TestUtils.IPV4_ADDRESS, STACK);
          Mockito.verify(conrepoIdentifier).tryIp(TestUtils.IPV4_ADDRESS, STACK);
        });
  }
}
