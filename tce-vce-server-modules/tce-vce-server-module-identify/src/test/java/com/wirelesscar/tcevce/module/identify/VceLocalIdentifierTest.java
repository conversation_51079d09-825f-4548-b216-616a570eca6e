package com.wirelesscar.tcevce.module.identify;

import java.util.Optional;
import java.util.function.BiConsumer;
import java.util.function.Consumer;
import java.util.function.Function;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import com.volvo.tisp.vc.test.utils.lib.AssertThrows;
import com.wirelesscar.tcevce.module.identify.metrics.VceIdentifyModuleMetricReporter;
import com.wirelesscar.tcevce.wecu.device.info.database.api.DeviceInfoReader;
import com.wirelesscar.tcevce.wecu.device.info.database.api.DeviceInfoReaderFactory;
import com.wirelesscar.tcevce.wecu.device.info.database.model.DeviceInfo;
import com.wirelesscar.tcevce.wecu.device.info.database.model.PersistedDeviceInfo;

class VceLocalIdentifierTest {
  private static final String STACK = "stack";

  private static void performTry(Function<DeviceInfoReader, Optional<PersistedDeviceInfo>> deviceInfoReaderMockFunction,
      Function<VceLocalIdentifier, Optional<DeviceInfo>> testFunction,
      BiConsumer<DeviceInfoReader, VceIdentifyModuleMetricReporter> verifyConsumer) {
    final VceIdentifyModuleMetricReporter vceIdentifyModuleMetricReporter = Mockito.mock(VceIdentifyModuleMetricReporter.class);

    DeviceInfoReader deviceInfoReader = Mockito.mock(DeviceInfoReader.class);
    Optional<PersistedDeviceInfo> persistedDeviceInfoOptional = deviceInfoReaderMockFunction.apply(deviceInfoReader);

    final DeviceInfoReaderFactory deviceInfoReaderFactory = Mockito.mock(DeviceInfoReaderFactory.class);
    Mockito.when(deviceInfoReaderFactory.create()).thenReturn(deviceInfoReader);
    VceLocalIdentifier vceLocalIdentifier = new VceLocalIdentifier(vceIdentifyModuleMetricReporter, deviceInfoReaderFactory);

    Optional<DeviceInfo> deviceInfoOptional = testFunction.apply(vceLocalIdentifier);

    if (persistedDeviceInfoOptional.isPresent() && deviceInfoOptional.isPresent()) {
      Assertions.assertSame(persistedDeviceInfoOptional.get().getDeviceInfo(), deviceInfoOptional.get());
    } else {
      Assertions.assertTrue(deviceInfoOptional.isEmpty());
    }

    Mockito.verify(deviceInfoReader).close();
    Mockito.verify(deviceInfoReaderFactory).create();
    verifyConsumer.accept(deviceInfoReader, vceIdentifyModuleMetricReporter);
    Mockito.verifyNoMoreInteractions(vceIdentifyModuleMetricReporter, deviceInfoReaderFactory, deviceInfoReader);
  }

  private static void performTryInvalid(Consumer<VceLocalIdentifier> vceLocalIdentifierConsumer) {
    DeviceInfoReaderFactory deviceInfoReaderFactory = Mockito.mock(DeviceInfoReaderFactory.class);
    VceIdentifyModuleMetricReporter vceIdentifyModuleMetricReporter = Mockito.mock(VceIdentifyModuleMetricReporter.class);

    VceLocalIdentifier vceLocalIdentifier = new VceLocalIdentifier(vceIdentifyModuleMetricReporter, deviceInfoReaderFactory);

    vceLocalIdentifierConsumer.accept(vceLocalIdentifier);

    Mockito.verifyNoInteractions(deviceInfoReaderFactory, vceIdentifyModuleMetricReporter);
  }

  @Test
  void testTryHandleInvalid() {
    performTryInvalid(vceLocalIdentifier -> {
      AssertThrows.illegalArgumentException(() -> vceLocalIdentifier.tryHandle(null, STACK), "handle must not be null");
      AssertThrows.illegalArgumentException(() -> vceLocalIdentifier.tryHandle(TestUtils.HANDLE, null), "stackName must not be null");
      AssertThrows.illegalArgumentException(() -> vceLocalIdentifier.tryHandle(TestUtils.HANDLE, ""), "stackName must not be empty");
    });
  }

  @Test
  void tryHandleDeviceNotFoundTest() {
    performTry(
        deviceInfoReader -> {
          Mockito.when(deviceInfoReader.findDeviceInfoByHandle(TestUtils.HANDLE)).thenReturn(Optional.empty());
          return Optional.empty();
        },
        vceLocalIdentifier -> vceLocalIdentifier.tryHandle(TestUtils.HANDLE, STACK),
        (deviceInfoReader, vceIdentifyModuleMetricReporter) -> Mockito.verify(deviceInfoReader).findDeviceInfoByHandle(TestUtils.HANDLE));
  }

  @Test
  void tryHandleTest() {
    performTry(
        deviceInfoReader -> {
          Optional<PersistedDeviceInfo> persistedDeviceInfoOptional = Optional.of(TestUtils.createPersistedDeviceInfo());
          Mockito.when(deviceInfoReader.findDeviceInfoByHandle(TestUtils.HANDLE)).thenReturn(persistedDeviceInfoOptional);
          return persistedDeviceInfoOptional;
        },
        vceLocalIdentifier -> vceLocalIdentifier.tryHandle(TestUtils.HANDLE, STACK),
        (deviceInfoReader, vceIdentifyModuleMetricReporter) -> {
          Mockito.verify(deviceInfoReader).findDeviceInfoByHandle(TestUtils.HANDLE);
          Mockito.verify(vceIdentifyModuleMetricReporter).onIdByHandle(STACK);
        });
  }

  @Test
  void tryIpDeviceNotFoundTest() {
    performTry(
        deviceInfoReader -> {
          Mockito.when(deviceInfoReader.findDeviceInfoByIpv4Address(TestUtils.IPV4_ADDRESS)).thenReturn(Optional.empty());
          return Optional.empty();
        },
        vceLocalIdentifier -> vceLocalIdentifier.tryIp(TestUtils.IPV4_ADDRESS, STACK),
        (deviceInfoReader, vceIdentifyModuleMetricReporter) -> Mockito.verify(deviceInfoReader).findDeviceInfoByIpv4Address(TestUtils.IPV4_ADDRESS));
  }

  @Test
  void tryIpInvalidTest() {
    performTryInvalid(vceLocalIdentifier -> {
      AssertThrows.illegalArgumentException(() -> vceLocalIdentifier.tryIp(null, STACK), "ipv4Address must not be null");
      AssertThrows.illegalArgumentException(() -> vceLocalIdentifier.tryIp(TestUtils.IPV4_ADDRESS, null), "stackName must not be null");
      AssertThrows.illegalArgumentException(() -> vceLocalIdentifier.tryIp(TestUtils.IPV4_ADDRESS, ""), "stackName must not be empty");
    });
  }

  @Test
  void tryIpTest() {
    performTry(
        deviceInfoReader -> {
          Optional<PersistedDeviceInfo> persistedDeviceInfoOptional = Optional.of(TestUtils.createPersistedDeviceInfo());
          Mockito.when(deviceInfoReader.findDeviceInfoByIpv4Address(TestUtils.IPV4_ADDRESS)).thenReturn(persistedDeviceInfoOptional);
          return persistedDeviceInfoOptional;
        },
        vceLocalIdentifier -> vceLocalIdentifier.tryIp(TestUtils.IPV4_ADDRESS, STACK),
        (deviceInfoReader, vceIdentifyModuleMetricReporter) -> {
          Mockito.verify(deviceInfoReader).findDeviceInfoByIpv4Address(TestUtils.IPV4_ADDRESS);
          Mockito.verify(vceIdentifyModuleMetricReporter).onIdByIp(STACK);
        });

    performTry(
        deviceInfoReader -> {
          Mockito.when(deviceInfoReader.findDeviceInfoByIpv4Address(TestUtils.IPV4_ADDRESS)).thenReturn(Optional.empty());
          return Optional.empty();
        },
        vceLocalIdentifier -> vceLocalIdentifier.tryIp(TestUtils.IPV4_ADDRESS, STACK),
        (deviceInfoReader, vceIdentifyModuleMetricReporter) -> Mockito.verify(deviceInfoReader).findDeviceInfoByIpv4Address(TestUtils.IPV4_ADDRESS));
  }

  @Test
  void tryMsisdnDeviceNotFoundTest() {
    performTry(
        deviceInfoReader -> {
          Mockito.when(deviceInfoReader.findDeviceInfoByMsisdn(TestUtils.MSISDN)).thenReturn(Optional.empty());
          return Optional.empty();
        },
        vceLocalIdentifier -> vceLocalIdentifier.tryMsisdn(TestUtils.MSISDN, STACK),
        (deviceInfoReader, vceIdentifyModuleMetricReporter) -> Mockito.verify(deviceInfoReader).findDeviceInfoByMsisdn(TestUtils.MSISDN));
  }

  @Test
  void tryMsisdnInvalidTest() {
    performTryInvalid(vceLocalIdentifier -> {
      AssertThrows.illegalArgumentException(() -> vceLocalIdentifier.tryMsisdn(null, STACK), "msisdn must not be null");
      AssertThrows.illegalArgumentException(() -> vceLocalIdentifier.tryMsisdn(TestUtils.MSISDN, null), "stackName must not be null");
      AssertThrows.illegalArgumentException(() -> vceLocalIdentifier.tryMsisdn(TestUtils.MSISDN, ""), "stackName must not be empty");
    });
  }

  @Test
  void tryMsisdnTest() {
    performTry(
        deviceInfoReader -> {
          Optional<PersistedDeviceInfo> persistedDeviceInfoOptional = Optional.of(TestUtils.createPersistedDeviceInfo());
          Mockito.when(deviceInfoReader.findDeviceInfoByMsisdn(TestUtils.MSISDN)).thenReturn(persistedDeviceInfoOptional);
          return persistedDeviceInfoOptional;
        },
        vceLocalIdentifier -> vceLocalIdentifier.tryMsisdn(TestUtils.MSISDN, STACK),
        (deviceInfoReader, vceIdentifyModuleMetricReporter) -> {
          Mockito.verify(deviceInfoReader).findDeviceInfoByMsisdn(TestUtils.MSISDN);
          Mockito.verify(vceIdentifyModuleMetricReporter).onIdByMsisdn(STACK);
        });
  }

  @Test
  void trySatelliteDeviceNotFoundTest() {
    performTry(
        deviceInfoReader -> {
          Mockito.when(deviceInfoReader.findDeviceInfoBySatelliteId(TestUtils.SATELLITE_ID)).thenReturn(Optional.empty());
          return Optional.empty();
        },
        vceLocalIdentifier -> vceLocalIdentifier.trySatellite(TestUtils.SATELLITE_ID, STACK),
        (deviceInfoReader, vceIdentifyModuleMetricReporter) -> Mockito.verify(deviceInfoReader).findDeviceInfoBySatelliteId(TestUtils.SATELLITE_ID));
  }

  @Test
  void trySatelliteInvalidTest() {
    performTryInvalid(vceLocalIdentifier -> {
      AssertThrows.illegalArgumentException(() -> vceLocalIdentifier.trySatellite(null, STACK), "satelliteId must not be null");
      AssertThrows.illegalArgumentException(() -> vceLocalIdentifier.trySatellite(TestUtils.SATELLITE_ID, null), "stackName must not be null");
      AssertThrows.illegalArgumentException(() -> vceLocalIdentifier.trySatellite(TestUtils.SATELLITE_ID, ""), "stackName must not be empty");
    });
  }

  @Test
  void trySatelliteTest() {
    performTry(
        deviceInfoReader -> {
          Optional<PersistedDeviceInfo> persistedDeviceInfoOptional = Optional.of(TestUtils.createPersistedDeviceInfo());
          Mockito.when(deviceInfoReader.findDeviceInfoBySatelliteId(TestUtils.SATELLITE_ID)).thenReturn(persistedDeviceInfoOptional);
          return persistedDeviceInfoOptional;
        },
        vceLocalIdentifier -> vceLocalIdentifier.trySatellite(TestUtils.SATELLITE_ID, STACK),
        (deviceInfoReader, vceIdentifyModuleMetricReporter) -> {
          Mockito.verify(deviceInfoReader).findDeviceInfoBySatelliteId(TestUtils.SATELLITE_ID);
          Mockito.verify(vceIdentifyModuleMetricReporter).onIdBySat(STACK);
        });
  }

  @Test
  void tryVpiDeviceNotFoundTest() {
    performTry(
        deviceInfoReader -> {
          Mockito.when(deviceInfoReader.findDeviceInfoByVpi(TestUtils.VPI)).thenReturn(Optional.empty());
          return Optional.empty();
        },
        vceLocalIdentifier -> vceLocalIdentifier.tryVpi(TestUtils.VPI, STACK),
        (deviceInfoReader, vceIdentifyModuleMetricReporter) -> Mockito.verify(deviceInfoReader).findDeviceInfoByVpi(TestUtils.VPI));
  }

  @Test
  void tryVpiTest() {
    performTry(
        deviceInfoReader -> {
          Optional<PersistedDeviceInfo> persistedDeviceInfoOptional = Optional.of(TestUtils.createPersistedDeviceInfo());
          Mockito.when(deviceInfoReader.findDeviceInfoByVpi(TestUtils.VPI)).thenReturn(persistedDeviceInfoOptional);
          return persistedDeviceInfoOptional;
        },
        vceLocalIdentifier -> vceLocalIdentifier.tryVpi(TestUtils.VPI, STACK),
        (deviceInfoReader, vceIdentifyModuleMetricReporter) -> {
          Mockito.verify(deviceInfoReader).findDeviceInfoByVpi(TestUtils.VPI);
          Mockito.verify(vceIdentifyModuleMetricReporter).onIdByVid(STACK);
        });
  }
}
