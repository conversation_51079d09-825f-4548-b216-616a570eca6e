package com.wirelesscar.tcevce.module.identify.metrics;

import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tags;

class ConrepoClientMetricReporterTest {
  private static final String BY = "by";
  private static final String CONREPO_LOOKUP = "conrepo-lookup";
  private static final String FAILED = "failed";
  private static final String STATUS = "status";
  private static final String TEST = "TEST";

  @Test
  void metricWithInvalidParametersTest() {
    MeterRegistry meterRegistry = Mockito.mock(MeterRegistry.class);
    ConrepoClientMetricReporter conrepoClientMetricReporter = new ConrepoClientMetricReporter(meterRegistry);

    AssertThrows.illegalArgumentException(() -> conrepoClientMetricReporter.onLookupFailure(null), "lookupBy must not be null");
  }

  @Test
  void onLookupFailureTest() {
    MetricReporterUtil.initReporterAndTest(ConrepoClientMetricReporter::new, (meterRegistry, conrepoClientMetricReporter) -> {
      conrepoClientMetricReporter.onLookupFailure(TEST);
      MetricReporterUtil.checkCounter(meterRegistry, 1, CONREPO_LOOKUP, Tags.of(BY, TEST, STATUS, FAILED));
    });
  }
}
