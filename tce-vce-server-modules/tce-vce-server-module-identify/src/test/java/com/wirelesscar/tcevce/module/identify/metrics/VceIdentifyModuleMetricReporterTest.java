package com.wirelesscar.tcevce.module.identify.metrics;

import static com.wirelesscar.tcevce.module.identify.metrics.VceIdentifyModuleMetricReporter.DOWN;
import static com.wirelesscar.tcevce.module.identify.metrics.VceIdentifyModuleMetricReporter.DOWN_CONREPO;
import static com.wirelesscar.tcevce.module.identify.metrics.VceIdentifyModuleMetricReporter.DOWN_FAIL;
import static com.wirelesscar.tcevce.module.identify.metrics.VceIdentifyModuleMetricReporter.ID_BY_HANDLR;
import static com.wirelesscar.tcevce.module.identify.metrics.VceIdentifyModuleMetricReporter.ID_BY_IP;
import static com.wirelesscar.tcevce.module.identify.metrics.VceIdentifyModuleMetricReporter.ID_BY_MSISDN;
import static com.wirelesscar.tcevce.module.identify.metrics.VceIdentifyModuleMetricReporter.ID_BY_SAT;
import static com.wirelesscar.tcevce.module.identify.metrics.VceIdentifyModuleMetricReporter.ID_BY_VID;
import static com.wirelesscar.tcevce.module.identify.metrics.VceIdentifyModuleMetricReporter.STACK_VAR_NAME;
import static com.wirelesscar.tcevce.module.identify.metrics.VceIdentifyModuleMetricReporter.UP;
import static com.wirelesscar.tcevce.module.identify.metrics.VceIdentifyModuleMetricReporter.UP_CONREPO;
import static com.wirelesscar.tcevce.module.identify.metrics.VceIdentifyModuleMetricReporter.UP_FAIL;
import static com.wirelesscar.tcevce.module.identify.metrics.VceIdentifyModuleMetricReporter.UP_FAILURE_SAT;
import static com.wirelesscar.tcevce.module.identify.metrics.VceIdentifyModuleMetricReporter.UP_FAILURE_SMS;
import static com.wirelesscar.tcevce.module.identify.metrics.VceIdentifyModuleMetricReporter.UP_FAILURE_UDP;

import java.time.Duration;

import org.junit.jupiter.api.Test;

import io.micrometer.core.instrument.Tags;

class VceIdentifyModuleMetricReporterTest {
  private static final String STACK_TEST = "stack";

  @Test
  void onDown() {
    MetricReporterUtil.initReporterAndTest(VceIdentifyModuleMetricReporter::new, (meterRegistry, vceSplitAckModuleMetricReporter) -> {
      vceSplitAckModuleMetricReporter.onDown(Duration.ofMillis(10), STACK_TEST);
      MetricReporterUtil.checkTimer(meterRegistry, Duration.ofMillis(10), 1, DOWN, Tags.of(STACK_VAR_NAME, STACK_TEST));
    });
  }

  @Test
  void onDownConrepoTest() {
    MetricReporterUtil.initReporterAndTest(VceIdentifyModuleMetricReporter::new, (meterRegistry, vceSplitAckModuleMetricReporter) -> {
      vceSplitAckModuleMetricReporter.onDownConrepo(STACK_TEST);
      MetricReporterUtil.checkCounter(meterRegistry, 1, DOWN_CONREPO, Tags.of(STACK_VAR_NAME, STACK_TEST));
    });
  }

  @Test
  void onDownFailTest() {
    MetricReporterUtil.initReporterAndTest(VceIdentifyModuleMetricReporter::new, (meterRegistry, vceSplitAckModuleMetricReporter) -> {
      vceSplitAckModuleMetricReporter.onDownFail(STACK_TEST);
      MetricReporterUtil.checkCounter(meterRegistry, 1, DOWN_FAIL, Tags.of(STACK_VAR_NAME, STACK_TEST));
    });
  }

  @Test
  void onIdByHandleTest() {
    MetricReporterUtil.initReporterAndTest(VceIdentifyModuleMetricReporter::new, (meterRegistry, vceSplitAckModuleMetricReporter) -> {
      vceSplitAckModuleMetricReporter.onIdByHandle(STACK_TEST);
      MetricReporterUtil.checkCounter(meterRegistry, 1, ID_BY_HANDLR, Tags.of(STACK_VAR_NAME, STACK_TEST));
    });
  }

  @Test
  void onIdByIpTest() {
    MetricReporterUtil.initReporterAndTest(VceIdentifyModuleMetricReporter::new, (meterRegistry, vceSplitAckModuleMetricReporter) -> {
      vceSplitAckModuleMetricReporter.onIdByIp(STACK_TEST);
      MetricReporterUtil.checkCounter(meterRegistry, 1, ID_BY_IP, Tags.of(STACK_VAR_NAME, STACK_TEST));
    });
  }

  @Test
  void onIdByMsisdnTest() {
    MetricReporterUtil.initReporterAndTest(VceIdentifyModuleMetricReporter::new, (meterRegistry, vceSplitAckModuleMetricReporter) -> {
      vceSplitAckModuleMetricReporter.onIdByMsisdn(STACK_TEST);
      MetricReporterUtil.checkCounter(meterRegistry, 1, ID_BY_MSISDN, Tags.of(STACK_VAR_NAME, STACK_TEST));
    });
  }

  @Test
  void onIdBySatTest() {
    MetricReporterUtil.initReporterAndTest(VceIdentifyModuleMetricReporter::new, (meterRegistry, vceSplitAckModuleMetricReporter) -> {
      vceSplitAckModuleMetricReporter.onIdBySat(STACK_TEST);
      MetricReporterUtil.checkCounter(meterRegistry, 1, ID_BY_SAT, Tags.of(STACK_VAR_NAME, STACK_TEST));
    });
  }

  @Test
  void onIdByVidTest() {
    MetricReporterUtil.initReporterAndTest(VceIdentifyModuleMetricReporter::new, (meterRegistry, vceSplitAckModuleMetricReporter) -> {
      vceSplitAckModuleMetricReporter.onIdByVid(STACK_TEST);
      MetricReporterUtil.checkCounter(meterRegistry, 1, ID_BY_VID, Tags.of(STACK_VAR_NAME, STACK_TEST));
    });
  }

  @Test
  void onProducedAckTest() {
    MetricReporterUtil.initReporterAndTest(VceIdentifyModuleMetricReporter::new, (meterRegistry, vceSplitAckModuleMetricReporter) -> {
      vceSplitAckModuleMetricReporter.onUpFail(STACK_TEST);
      MetricReporterUtil.checkCounter(meterRegistry, 1, UP_FAIL, Tags.of(STACK_VAR_NAME, STACK_TEST));
    });
  }

  @Test
  void onUpConrepoTest() {
    MetricReporterUtil.initReporterAndTest(VceIdentifyModuleMetricReporter::new, (meterRegistry, vceSplitAckModuleMetricReporter) -> {
      vceSplitAckModuleMetricReporter.onUpConrepo(STACK_TEST);
      MetricReporterUtil.checkCounter(meterRegistry, 1, UP_CONREPO, Tags.of(STACK_VAR_NAME, STACK_TEST));
    });
  }

  @Test
  void onUpFailureSatTest() {
    MetricReporterUtil.initReporterAndTest(VceIdentifyModuleMetricReporter::new, (meterRegistry, vceSplitAckModuleMetricReporter) -> {
      vceSplitAckModuleMetricReporter.onUpFailureSat(STACK_TEST);
      MetricReporterUtil.checkCounter(meterRegistry, 1, UP_FAILURE_SAT, Tags.of(STACK_VAR_NAME, STACK_TEST));
    });
  }

  @Test
  void onUpFailureSmsTest() {
    MetricReporterUtil.initReporterAndTest(VceIdentifyModuleMetricReporter::new, (meterRegistry, vceSplitAckModuleMetricReporter) -> {
      vceSplitAckModuleMetricReporter.onUpFailureSms(STACK_TEST);
      MetricReporterUtil.checkCounter(meterRegistry, 1, UP_FAILURE_SMS, Tags.of(STACK_VAR_NAME, STACK_TEST));
    });
  }

  @Test
  void onUpFailureUdpTest() {
    MetricReporterUtil.initReporterAndTest(VceIdentifyModuleMetricReporter::new, (meterRegistry, vceSplitAckModuleMetricReporter) -> {
      vceSplitAckModuleMetricReporter.onUpFailureUdp(STACK_TEST);
      MetricReporterUtil.checkCounter(meterRegistry, 1, UP_FAILURE_UDP, Tags.of(STACK_VAR_NAME, STACK_TEST));
    });
  }

  @Test
  void onUpTest() {
    MetricReporterUtil.initReporterAndTest(VceIdentifyModuleMetricReporter::new, (meterRegistry, vceSplitAckModuleMetricReporter) -> {
      vceSplitAckModuleMetricReporter.onUp(Duration.ofMillis(10), STACK_TEST);
      MetricReporterUtil.checkTimer(meterRegistry, Duration.ofMillis(10), 1, UP, Tags.of(STACK_VAR_NAME, STACK_TEST));
    });
  }
}
