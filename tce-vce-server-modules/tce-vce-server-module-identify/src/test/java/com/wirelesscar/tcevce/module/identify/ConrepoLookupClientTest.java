package com.wirelesscar.tcevce.module.identify;

import java.net.URI;
import java.util.List;
import java.util.Optional;
import java.util.function.Consumer;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import com.volvo.tisp.vc.test.utils.lib.AssertThrows;
import com.wirelesscar.config.mock.MockConfiguration;
import com.wirelesscar.conrepo.ContentTypes;
import com.wirelesscar.conrepo.api.v1.DeviceDetailedEntry;
import com.wirelesscar.tcevce.module.identify.metrics.ConrepoClientMetricReporter;

class ConrepoLookupClientTest {
  private static final String BASE_URL = "http://localhost:1111";
  private static final RestTemplate REST_TEMPLATE = Mockito.mock(RestTemplate.class);

  private static URI buildUri(String path) {
    return UriComponentsBuilder.fromHttpUrl(BASE_URL).path(path).build(false).toUri();
  }

  private static URI buildUriWithOneQueryParameter(String path, String queryParameterName, String queryParameterValue) {
    return UriComponentsBuilder.fromHttpUrl(BASE_URL).path(path).queryParam(queryParameterName, queryParameterValue).build(false).toUri();
  }

  private static HttpEntity<Void> createHttpEntity() {
    HttpHeaders httpHeaders = new HttpHeaders();
    httpHeaders.setAccept(List.of(MediaType.parseMediaType(ContentTypes.CONREPO_1_0_XML), MediaType.parseMediaType(ContentTypes.CONREPO_1_0_JSON)));
    return new HttpEntity<>(httpHeaders);
  }

  private static void performWhenConrepoError(URI uri, String micrometerMetricName, Consumer<ConrepoLookupClient> conrepoLookupClientConsumer) {
    setupConfig();
    stubErrorRestTemplateExchange(uri, HttpStatus.BAD_REQUEST);

    ConrepoClientMetricReporter conrepoClientMetricReporterMock = Mockito.mock(ConrepoClientMetricReporter.class);
    ConrepoLookupClient conrepoLookupClient = new ConrepoLookupClient(REST_TEMPLATE, conrepoClientMetricReporterMock, BASE_URL);

    conrepoLookupClientConsumer.accept(conrepoLookupClient);

    Mockito.verify(conrepoClientMetricReporterMock).onLookupFailure(micrometerMetricName);
    Mockito.verifyNoMoreInteractions(conrepoClientMetricReporterMock);
  }

  private static void setupConfig() {
    MockConfiguration mockConfiguration = MockConfiguration.getConfig();
    mockConfiguration.deleteAllProperties();

    mockConfiguration.setPlatformProperty("servicediscovery.conrepo", BASE_URL);
  }

  private static void stubErrorRestTemplateExchange(URI uri, HttpStatus responseHttpStatus) {
    ResponseEntity<DeviceDetailedEntry> responseEntity = new ResponseEntity<>(null, null, responseHttpStatus);
    Mockito.when(REST_TEMPLATE.exchange(uri, HttpMethod.GET, createHttpEntity(), DeviceDetailedEntry.class)).thenReturn(responseEntity);
  }

  private static void stubOkRestTemplateExchange(URI uri) {
    ResponseEntity<DeviceDetailedEntry> responseEntity = new ResponseEntity<>(TestUtils.createDeviceDetailedEntry(), null, HttpStatus.OK);
    Mockito.when(REST_TEMPLATE.exchange(uri, HttpMethod.GET, createHttpEntity(), DeviceDetailedEntry.class)).thenReturn(responseEntity);
  }

  @Test
  void invalidConstructorTest() {
    ConrepoClientMetricReporter conrepoClientMetricReporter = Mockito.mock(ConrepoClientMetricReporter.class);
    String baseUrl = "testBaseUrl";

    AssertThrows.illegalArgumentException(() -> new ConrepoLookupClient(null, conrepoClientMetricReporter, baseUrl), "restTemplate must not be null");
    AssertThrows.illegalArgumentException(() -> new ConrepoLookupClient(REST_TEMPLATE, null, baseUrl), "conrepoClientMetricReporter must not be null");
    AssertThrows.illegalArgumentException(() -> new ConrepoLookupClient(REST_TEMPLATE, conrepoClientMetricReporter, null), "baseUrl must not be null");
    AssertThrows.illegalArgumentException(() -> new ConrepoLookupClient(REST_TEMPLATE, conrepoClientMetricReporter, ""), "baseUrl must not be empty");
  }

  @Test
  void lookupByHandleFailureTest() {
    setupConfig();
    stubErrorRestTemplateExchange(buildUriWithOneQueryParameter("/conrepo/handle", "handle", TestUtils.HANDLE.toString()), HttpStatus.NOT_FOUND);

    ConrepoClientMetricReporter conrepoClientMetricReporterMock = Mockito.mock(ConrepoClientMetricReporter.class);
    ConrepoLookupClient conrepoLookupClient = new ConrepoLookupClient(REST_TEMPLATE, conrepoClientMetricReporterMock, BASE_URL);

    conrepoLookupClient.lookupByHandle(TestUtils.HANDLE.toString());

    Mockito.verify(conrepoClientMetricReporterMock).onLookupFailure("handle");
  }

  @Test
  void lookupByHandleTest() {
    setupConfig();
    stubOkRestTemplateExchange(buildUriWithOneQueryParameter("/conrepo/handle", "handle", TestUtils.HANDLE.toString()));

    ConrepoClientMetricReporter conrepoClientMetricReporterMock = Mockito.mock(ConrepoClientMetricReporter.class);
    ConrepoLookupClient conrepoLookupClient = new ConrepoLookupClient(REST_TEMPLATE, conrepoClientMetricReporterMock, BASE_URL);

    Optional<DeviceDetailedEntry> optionalDeviceDetailedEntry = conrepoLookupClient.lookupByHandle(TestUtils.HANDLE.toString());
    Assertions.assertEquals(TestUtils.HANDLE.toString(), optionalDeviceDetailedEntry.get().getHandle());

    Mockito.verifyNoMoreInteractions(conrepoClientMetricReporterMock);
  }

  @Test
  void lookupByHandleWhenConrepoErrorTest() {
    performWhenConrepoError(buildUriWithOneQueryParameter("/conrepo/handle", "handle", TestUtils.HANDLE.toString()), "handle",
        conrepoLookupClient -> AssertThrows.illegalStateException(() -> conrepoLookupClient.lookupByHandle(TestUtils.HANDLE.toString()),
            "httpStatus from conrepo is 400 BAD_REQUEST"));
  }

  @Test
  void lookupByIpFailureTest() {
    setupConfig();
    stubErrorRestTemplateExchange(buildUriWithOneQueryParameter("/conrepo/ip", "ip", TestUtils.IPV4_ADDRESS.toString()), HttpStatus.NOT_FOUND);

    ConrepoClientMetricReporter conrepoClientMetricReporterMock = Mockito.mock(ConrepoClientMetricReporter.class);
    ConrepoLookupClient conrepoLookupClient = new ConrepoLookupClient(REST_TEMPLATE, conrepoClientMetricReporterMock, BASE_URL);

    conrepoLookupClient.lookupByIp(TestUtils.IPV4_ADDRESS.toString());

    Mockito.verify(conrepoClientMetricReporterMock).onLookupFailure("ip");
  }

  @Test
  void lookupByIpTest() {
    setupConfig();
    stubOkRestTemplateExchange(buildUriWithOneQueryParameter("/conrepo/ip", "ip", TestUtils.IPV4_ADDRESS.toString()));

    ConrepoClientMetricReporter conrepoClientMetricReporterMock = Mockito.mock(ConrepoClientMetricReporter.class);
    ConrepoLookupClient conrepoLookupClient = new ConrepoLookupClient(REST_TEMPLATE, conrepoClientMetricReporterMock, BASE_URL);

    Optional<DeviceDetailedEntry> optionalDeviceDetailedEntry = conrepoLookupClient.lookupByIp(TestUtils.IPV4_ADDRESS.toString());
    Assertions.assertEquals(TestUtils.IPV4_ADDRESS.toString(), optionalDeviceDetailedEntry.get().getSimEntry().getIp());

    Mockito.verifyNoMoreInteractions(conrepoClientMetricReporterMock);
  }

  @Test
  void lookupByIpWhenConrepoErrorTest() {
    performWhenConrepoError(buildUriWithOneQueryParameter("/conrepo/ip", "ip", TestUtils.IPV4_ADDRESS.toString()), "ip",
        conrepoLookupClient -> AssertThrows.illegalStateException(() -> conrepoLookupClient.lookupByIp(TestUtils.IPV4_ADDRESS.toString()),
            "httpStatus from conrepo is 400 BAD_REQUEST"));
  }

  @Test
  void lookupByMsisdnFailureTest() {
    setupConfig();
    stubErrorRestTemplateExchange(buildUri("/conrepo/msisdn/" + TestUtils.MSISDN), HttpStatus.NOT_FOUND);

    ConrepoClientMetricReporter conrepoClientMetricReporterMock = Mockito.mock(ConrepoClientMetricReporter.class);
    ConrepoLookupClient conrepoLookupClient = new ConrepoLookupClient(REST_TEMPLATE, conrepoClientMetricReporterMock, BASE_URL);

    conrepoLookupClient.lookupByMsisdn(TestUtils.MSISDN.toString());

    Mockito.verify(conrepoClientMetricReporterMock).onLookupFailure("msisdn");
  }

  @Test
  void lookupByMsisdnTest() {
    setupConfig();

    stubOkRestTemplateExchange(buildUri("/conrepo/msisdn/" + TestUtils.MSISDN));

    ConrepoClientMetricReporter conrepoClientMetricReporterMock = Mockito.mock(ConrepoClientMetricReporter.class);
    ConrepoLookupClient conrepoLookupClient = new ConrepoLookupClient(REST_TEMPLATE, conrepoClientMetricReporterMock, BASE_URL);

    Optional<DeviceDetailedEntry> optionalDeviceDetailedEntry = conrepoLookupClient.lookupByMsisdn(TestUtils.MSISDN.toString());
    Assertions.assertEquals(TestUtils.MSISDN.toString(), optionalDeviceDetailedEntry.get().getSimEntry().getMsisdn());

    Mockito.verifyNoMoreInteractions(conrepoClientMetricReporterMock);
  }

  @Test
  void lookupByMsisdnWhenConrepoErrorTest() {
    performWhenConrepoError(buildUri("/conrepo/msisdn/" + TestUtils.MSISDN), "msisdn",
        conrepoLookupClient -> AssertThrows.illegalStateException(() -> conrepoLookupClient.lookupByMsisdn(TestUtils.MSISDN.toString()),
            "httpStatus from conrepo is 400 BAD_REQUEST"));
  }

  @Test
  void lookupBySatIdFailureTest() {
    setupConfig();
    stubErrorRestTemplateExchange(buildUri("/conrepo/satid/" + TestUtils.SATELLITE_ID), HttpStatus.NOT_FOUND);

    ConrepoClientMetricReporter conrepoClientMetricReporterMock = Mockito.mock(ConrepoClientMetricReporter.class);
    ConrepoLookupClient conrepoLookupClient = new ConrepoLookupClient(REST_TEMPLATE, conrepoClientMetricReporterMock, BASE_URL);

    conrepoLookupClient.lookupBySatId(TestUtils.SATELLITE_ID.toString());

    Mockito.verify(conrepoClientMetricReporterMock).onLookupFailure("satId");
  }

  @Test
  void lookupBySatIdTest() {
    setupConfig();
    stubOkRestTemplateExchange(buildUri("/conrepo/satid/" + TestUtils.SATELLITE_ID));

    ConrepoClientMetricReporter conrepoClientMetricReporterMock = Mockito.mock(ConrepoClientMetricReporter.class);
    ConrepoLookupClient conrepoLookupClient = new ConrepoLookupClient(REST_TEMPLATE, conrepoClientMetricReporterMock, BASE_URL);

    Optional<DeviceDetailedEntry> optionalDeviceDetailedEntry = conrepoLookupClient.lookupBySatId(TestUtils.SATELLITE_ID.toString());
    Assertions.assertEquals(TestUtils.SATELLITE_ID.toString(), optionalDeviceDetailedEntry.get().getSatelliteId());

    Mockito.verifyNoMoreInteractions(conrepoClientMetricReporterMock);
  }

  @Test
  void lookupBySatIdWhenConrepoErrorTest() {
    performWhenConrepoError(buildUri("/conrepo/satid/" + TestUtils.SATELLITE_ID), "satId",
        conrepoLookupClient -> AssertThrows.illegalStateException(() -> conrepoLookupClient.lookupBySatId(TestUtils.SATELLITE_ID.toString()),
            "httpStatus from conrepo is 400 BAD_REQUEST"));
  }

  @Test
  void lookupByVehiclePlatformIdFailureTest() {
    setupConfig();
    stubErrorRestTemplateExchange(buildUri("/conrepo/vehicleplatformid/" + TestUtils.VPI), HttpStatus.NOT_FOUND);

    ConrepoClientMetricReporter conrepoClientMetricReporterMock = Mockito.mock(ConrepoClientMetricReporter.class);
    ConrepoLookupClient conrepoLookupClient = new ConrepoLookupClient(REST_TEMPLATE, conrepoClientMetricReporterMock, BASE_URL);

    conrepoLookupClient.lookupByVehiclePlatformId(TestUtils.VPI.toString());

    Mockito.verify(conrepoClientMetricReporterMock).onLookupFailure("vehiclePlatformId");
  }

  @Test
  void lookupByVehiclePlatformIdTest() {
    setupConfig();
    stubOkRestTemplateExchange(buildUri("/conrepo/vehicleplatformid/" + TestUtils.VPI));

    ConrepoClientMetricReporter conrepoClientMetricReporterMock = Mockito.mock(ConrepoClientMetricReporter.class);
    ConrepoLookupClient conrepoLookupClient = new ConrepoLookupClient(REST_TEMPLATE, conrepoClientMetricReporterMock, BASE_URL);

    Optional<DeviceDetailedEntry> optionalDeviceDetailedEntry = conrepoLookupClient.lookupByVehiclePlatformId(TestUtils.VPI.toString());
    Assertions.assertEquals(TestUtils.VPI.toString(), optionalDeviceDetailedEntry.get().getVehiclePlatformId());

    Mockito.verifyNoMoreInteractions(conrepoClientMetricReporterMock);
  }

  @Test
  void lookupByVpiWhenConrepoErrorTest() {
    performWhenConrepoError(buildUri("/conrepo/vehicleplatformid/" + TestUtils.VPI), "vehiclePlatformId",
        conrepoLookupClient -> AssertThrows.illegalStateException(() -> conrepoLookupClient.lookupByVehiclePlatformId(TestUtils.VPI.toString()),
            "httpStatus from conrepo is 400 BAD_REQUEST"));
  }
}
