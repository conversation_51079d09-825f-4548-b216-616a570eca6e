package com.wirelesscar.tcevce.module.identify;

import java.util.Optional;
import java.util.function.BiConsumer;
import java.util.function.Consumer;
import java.util.function.Function;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import com.volvo.tisp.vc.main.utils.lib.type.Either;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;
import com.wirelesscar.conrepo.api.v1.DeviceDetailedEntry;
import com.wirelesscar.conrepo.api.v1.DeviceSim;
import com.wirelesscar.tcevce.module.identify.metrics.VceIdentifyModuleMetricReporter;
import com.wirelesscar.tcevce.wecu.device.info.database.api.DeviceInfoWriter;
import com.wirelesscar.tcevce.wecu.device.info.database.api.DeviceInfoWriterFactory;
import com.wirelesscar.tcevce.wecu.device.info.database.model.DeviceInfo;
import com.wirelesscar.tcevce.wecu.device.info.database.model.DeviceInfoId;
import com.wirelesscar.tcevce.wecu.device.info.database.model.DeviceSequenceId;
import com.wirelesscar.tcevce.wecu.device.info.database.model.InsertionFailure;
import com.wirelesscar.tcevce.wecu.device.info.database.model.InsertionFailureReason;
import com.wirelesscar.tcevce.wecu.device.info.database.model.SatelliteId;
import com.wirelesscar.tcevce.wecu.device.info.database.model.SimInfo;

class ConrepoIdentifierTest {
  private static final String STACK = "stack";

  private static InsertionFailure createInsertionFailure() {
    return new InsertionFailure(InsertionFailureReason.DUPLICATE_KEY, new RuntimeException("test"));
  }

  private static BiConsumer<ConrepoLookupClient, DeviceDetailedEntry> getMockConsumerForTryHandle() {
    return (conrepoLookupClient, deviceDetailedEntry) -> Mockito
        .when(conrepoLookupClient.lookupByHandle(TestUtils.HANDLE.toString()))
        .thenReturn(Optional.of(deviceDetailedEntry));
  }

  private static BiConsumer<ConrepoLookupClient, DeviceDetailedEntry> getMockConsumerForTryIp() {
    return (conrepoLookupClient, deviceDetailedEntry) -> Mockito
        .when(conrepoLookupClient.lookupByIp(TestUtils.IPV4_ADDRESS.toString()))
        .thenReturn(Optional.of(deviceDetailedEntry));
  }

  private static BiConsumer<ConrepoLookupClient, DeviceDetailedEntry> getMockConsumerForTrySat() {
    return (conrepoLookupClient, deviceDetailedEntry) -> Mockito
        .when(conrepoLookupClient.lookupBySatId(TestUtils.SATELLITE_ID.toString()))
        .thenReturn(Optional.of(deviceDetailedEntry));
  }

  private static BiConsumer<ConrepoLookupClient, DeviceDetailedEntry> getMockConsumerForTryVpi() {
    return (conrepoLookupClient, deviceDetailedEntry) -> Mockito
        .when(conrepoLookupClient.lookupByVehiclePlatformId(TestUtils.VPI.toString()))
        .thenReturn(Optional.of(deviceDetailedEntry));
  }

  private static BiConsumer<ConrepoLookupClient, DeviceDetailedEntry> getMockConsumerFotTryMsisdn() {
    return (conrepoLookupClient, deviceDetailedEntry) -> Mockito
        .when(conrepoLookupClient.lookupByMsisdn(TestUtils.MSISDN.toString()))
        .thenReturn(Optional.of(deviceDetailedEntry));
  }

  private static BiConsumer<ConrepoLookupClient, VceIdentifyModuleMetricReporter> getVerifyConsumerForTryHandle() {
    return (conrepoLookupClient, vceIdentifyModuleMetricReporter) -> {
      Mockito.verify(conrepoLookupClient).lookupByHandle(TestUtils.HANDLE.toString());
      Mockito.verify(vceIdentifyModuleMetricReporter).onDownConrepo(STACK);
    };
  }

  private static BiConsumer<ConrepoLookupClient, VceIdentifyModuleMetricReporter> getVerifyConsumerForTryIp() {
    return (conrepoLookupClient, vceIdentifyModuleMetricReporter) -> {
      Mockito.verify(conrepoLookupClient).lookupByIp(TestUtils.IPV4_ADDRESS.toString());
      Mockito.verify(vceIdentifyModuleMetricReporter).onUpConrepo(STACK);
    };
  }

  private static BiConsumer<ConrepoLookupClient, VceIdentifyModuleMetricReporter> getVerifyConsumerForTryMsisdn() {
    return (conrepoLookupClient, vceIdentifyModuleMetricReporter) -> {
      Mockito.verify(conrepoLookupClient).lookupByMsisdn(TestUtils.MSISDN.toString());
      Mockito.verify(vceIdentifyModuleMetricReporter).onUpConrepo(STACK);
    };
  }

  private static BiConsumer<ConrepoLookupClient, VceIdentifyModuleMetricReporter> getVerifyConsumerForTrySat() {
    return (conrepoLookupClient, vceIdentifyModuleMetricReporter) -> {
      Mockito.verify(conrepoLookupClient).lookupBySatId(TestUtils.SATELLITE_ID.toString());
      Mockito.verify(vceIdentifyModuleMetricReporter).onUpConrepo(STACK);
    };
  }

  private static BiConsumer<ConrepoLookupClient, VceIdentifyModuleMetricReporter> getVerifyConsumerForTryVpi() {
    return (conrepoLookupClient, vceIdentifyModuleMetricReporter) -> {
      Mockito.verify(conrepoLookupClient).lookupByVehiclePlatformId(TestUtils.VPI.toString());
      Mockito.verify(vceIdentifyModuleMetricReporter).onDownConrepo(STACK);
    };
  }

  private static void performTryInvalid(Consumer<ConrepoIdentifier> conrepoIdentifierConsumer) {
    ConrepoLookupClient conrepoLookupClient = Mockito.mock(ConrepoLookupClient.class);
    DeviceInfoWriterFactory deviceInfoWriterFactory = Mockito.mock(DeviceInfoWriterFactory.class);
    VceIdentifyModuleMetricReporter vceIdentifyModuleMetricReporter = Mockito.mock(VceIdentifyModuleMetricReporter.class);

    ConrepoIdentifier conrepoIdentifier = new ConrepoIdentifier(conrepoLookupClient, deviceInfoWriterFactory, vceIdentifyModuleMetricReporter);

    conrepoIdentifierConsumer.accept(conrepoIdentifier);

    Mockito.verifyNoInteractions(conrepoLookupClient, deviceInfoWriterFactory, vceIdentifyModuleMetricReporter);
  }

  private static void tryConrepoLookupClientException(Consumer<ConrepoLookupClient> conrepoLookupClientConsumer,
      Function<ConrepoIdentifier, DeviceInfo> testBiFunction,
      BiConsumer<ConrepoLookupClient, VceIdentifyModuleMetricReporter> verifyConsumer) {
    final ConrepoLookupClient conrepoLookupClient = Mockito.mock(ConrepoLookupClient.class);
    conrepoLookupClientConsumer.accept(conrepoLookupClient);

    final DeviceInfoWriterFactory deviceInfoWriterFactory = Mockito.mock(DeviceInfoWriterFactory.class);
    VceIdentifyModuleMetricReporter vceIdentifyModuleMetricReporter = Mockito.mock(VceIdentifyModuleMetricReporter.class);

    ConrepoIdentifier conrepoIdentifier = new ConrepoIdentifier(conrepoLookupClient, deviceInfoWriterFactory, vceIdentifyModuleMetricReporter);
    AssertThrows.exception(() -> testBiFunction.apply(conrepoIdentifier), "test", RuntimeException.class);

    verifyConsumer.accept(conrepoLookupClient, vceIdentifyModuleMetricReporter);

    Mockito.verifyNoMoreInteractions(conrepoLookupClient, deviceInfoWriterFactory);
  }

  private static void tryEmpty(Consumer<ConrepoLookupClient> conrepoLookupClientConsumer,
      Function<ConrepoIdentifier, Optional<DeviceInfo>> testBiFunction,
      BiConsumer<ConrepoLookupClient, VceIdentifyModuleMetricReporter> verifyConsumer) {
    final ConrepoLookupClient conrepoLookupClient = Mockito.mock(ConrepoLookupClient.class);
    conrepoLookupClientConsumer.accept(conrepoLookupClient);

    final DeviceInfoWriterFactory deviceInfoWriterFactory = Mockito.mock(DeviceInfoWriterFactory.class);

    VceIdentifyModuleMetricReporter vceIdentifyModuleMetricReporter = Mockito.mock(VceIdentifyModuleMetricReporter.class);
    ConrepoIdentifier conrepoIdentifier = new ConrepoIdentifier(conrepoLookupClient, deviceInfoWriterFactory, vceIdentifyModuleMetricReporter);

    Optional<DeviceInfo> optional = testBiFunction.apply(conrepoIdentifier);
    Assertions.assertTrue(optional.isEmpty());

    verifyConsumer.accept(conrepoLookupClient, vceIdentifyModuleMetricReporter);

    Mockito.verifyNoMoreInteractions(conrepoLookupClient, deviceInfoWriterFactory);
  }

  private static void tryFailToInsertDeviceException(BiConsumer<ConrepoLookupClient, DeviceDetailedEntry> conrepoLookupClientConsumer,
      Function<ConrepoIdentifier, DeviceInfo> testBiFunction,
      BiConsumer<ConrepoLookupClient, VceIdentifyModuleMetricReporter> verifyConsumer) {
    final ConrepoLookupClient conrepoLookupClient = Mockito.mock(ConrepoLookupClient.class);
    conrepoLookupClientConsumer.accept(conrepoLookupClient, TestUtils.createDeviceDetailedEntry());

    DeviceInfoWriter deviceInfoWriter = Mockito.mock(DeviceInfoWriter.class);
    Mockito.when(deviceInfoWriter.insertDeviceInfo(TestUtils.createDeviceInfo())).thenReturn(Either.left(createInsertionFailure()));

    final DeviceInfoWriterFactory deviceInfoWriterFactory = Mockito.mock(DeviceInfoWriterFactory.class);
    Mockito.when(deviceInfoWriterFactory.createReadCommitted()).thenReturn(deviceInfoWriter);

    VceIdentifyModuleMetricReporter vceIdentifyModuleMetricReporter = Mockito.mock(VceIdentifyModuleMetricReporter.class);

    ConrepoIdentifier conrepoIdentifier = new ConrepoIdentifier(conrepoLookupClient, deviceInfoWriterFactory, vceIdentifyModuleMetricReporter);
    AssertThrows.exception(() -> testBiFunction.apply(conrepoIdentifier), "Error inserting Device locally", RuntimeException.class);

    verifyConsumer.accept(conrepoLookupClient, vceIdentifyModuleMetricReporter);
    Mockito.verify(deviceInfoWriter).insertDeviceInfo(TestUtils.createDeviceInfo());
    Mockito.verify(deviceInfoWriter).close();
    Mockito.verify(deviceInfoWriterFactory).createReadCommitted();
    Mockito.verifyNoMoreInteractions(conrepoLookupClient, deviceInfoWriterFactory, deviceInfoWriter);
  }

  private static void tryFailToInsertDeviceSequenceException(BiConsumer<ConrepoLookupClient, DeviceDetailedEntry> conrepoLookupClientConsumer,
      Function<ConrepoIdentifier, DeviceInfo> testBiFunction,
      BiConsumer<ConrepoLookupClient, VceIdentifyModuleMetricReporter> verifyConsumer) {
    final ConrepoLookupClient conrepoLookupClient = Mockito.mock(ConrepoLookupClient.class);
    conrepoLookupClientConsumer.accept(conrepoLookupClient, TestUtils.createDeviceDetailedEntry());

    DeviceInfoWriter deviceInfoWriter = Mockito.mock(DeviceInfoWriter.class);
    Mockito.when(deviceInfoWriter.insertDeviceInfo(TestUtils.createDeviceInfo())).thenReturn(Either.right(DeviceInfoId.ofLong(1L)));
    Mockito.when(deviceInfoWriter.insertDeviceSequence(TestUtils.createDeviceSequence())).thenReturn(Either.left(createInsertionFailure()));

    final DeviceInfoWriterFactory deviceInfoWriterFactory = Mockito.mock(DeviceInfoWriterFactory.class);
    Mockito.when(deviceInfoWriterFactory.createReadCommitted()).thenReturn(deviceInfoWriter);

    VceIdentifyModuleMetricReporter vceIdentifyModuleMetricReporter = Mockito.mock(VceIdentifyModuleMetricReporter.class);
    ConrepoIdentifier conrepoIdentifier = new ConrepoIdentifier(conrepoLookupClient, deviceInfoWriterFactory, vceIdentifyModuleMetricReporter);

    AssertThrows.exception(() -> testBiFunction.apply(conrepoIdentifier), "Error inserting Device sequence locally", RuntimeException.class);

    verifyConsumer.accept(conrepoLookupClient, vceIdentifyModuleMetricReporter);
    Mockito.verify(deviceInfoWriter).insertDeviceInfo(TestUtils.createDeviceInfo());
    Mockito.verify(deviceInfoWriter).insertDeviceSequence(TestUtils.createDeviceSequence());
    Mockito.verify(deviceInfoWriter).close();
    Mockito.verify(deviceInfoWriterFactory).createReadCommitted();
    Mockito.verifyNoMoreInteractions(conrepoLookupClient, deviceInfoWriterFactory, deviceInfoWriter);
  }

  private static void tryNotEmpty(BiConsumer<ConrepoLookupClient, DeviceDetailedEntry> conrepoLookupClientConsumer,
      Function<ConrepoIdentifier, DeviceInfo> testBiFunction,
      BiConsumer<ConrepoLookupClient, VceIdentifyModuleMetricReporter> verifyConsumer) {
    DeviceDetailedEntry deviceDetailedEntry = TestUtils.createDeviceDetailedEntry();

    ConrepoLookupClient conrepoLookupClient = Mockito.mock(ConrepoLookupClient.class);
    conrepoLookupClientConsumer.accept(conrepoLookupClient, deviceDetailedEntry);

    DeviceInfoWriter deviceInfoWriter = Mockito.mock(DeviceInfoWriter.class);
    Mockito.when(deviceInfoWriter.insertDeviceInfo(TestUtils.createDeviceInfo())).thenReturn(Either.right(DeviceInfoId.ofLong(1L)));
    Mockito.when(deviceInfoWriter.insertDeviceSequence(TestUtils.createDeviceSequence())).thenReturn(Either.right(DeviceSequenceId.ofLong(1L)));

    DeviceInfoWriterFactory deviceInfoWriterFactory = Mockito.mock(DeviceInfoWriterFactory.class);
    Mockito.when(deviceInfoWriterFactory.createReadCommitted()).thenReturn(deviceInfoWriter);

    VceIdentifyModuleMetricReporter vceIdentifyModuleMetricReporter = Mockito.mock(VceIdentifyModuleMetricReporter.class);
    ConrepoIdentifier conrepoIdentifier = new ConrepoIdentifier(conrepoLookupClient, deviceInfoWriterFactory, vceIdentifyModuleMetricReporter);

    DeviceInfo deviceInfo = testBiFunction.apply(conrepoIdentifier);
    verifyDeviceInfo(deviceDetailedEntry, deviceInfo);

    verifyConsumer.accept(conrepoLookupClient, vceIdentifyModuleMetricReporter);
    Mockito.verify(deviceInfoWriter).insertDeviceInfo(TestUtils.createDeviceInfo());
    Mockito.verify(deviceInfoWriter).insertDeviceSequence(TestUtils.createDeviceSequence());
    Mockito.verify(deviceInfoWriter).close();
    Mockito.verify(deviceInfoWriterFactory).createReadCommitted();
    Mockito.verifyNoMoreInteractions(conrepoLookupClient, deviceInfoWriterFactory, deviceInfoWriter);
  }

  private static void verifyDeviceInfo(DeviceDetailedEntry deviceDetailedEntry, DeviceInfo deviceInfo) {
    Assertions.assertEquals(deviceDetailedEntry.getHandle(), deviceInfo.getHandle().toString());
    Assertions.assertEquals(deviceDetailedEntry.getVehiclePlatformId(), deviceInfo.getVpi().get().toString());

    verifySimInfo(deviceDetailedEntry.getSimEntry(), deviceInfo.getSimInfo());
    verifySatelliteId(deviceDetailedEntry.getSatelliteId(), deviceInfo.getSatelliteId());
  }

  private static void verifySatelliteId(String satId, Optional<SatelliteId> optionalSatelliteId) {
    if (satId != null) {
      Assertions.assertEquals(satId, optionalSatelliteId.get().toString());
    } else {
      Assertions.assertTrue(optionalSatelliteId.isEmpty());
    }
  }

  private static void verifySimInfo(DeviceSim simEntry, Optional<SimInfo> optionalSimInfo) {
    if (simEntry != null) {
      SimInfo simInfo = optionalSimInfo.get();
      Assertions.assertEquals(simEntry.getIp(), simInfo.getIpv4Address().toString());
      Assertions.assertEquals(simEntry.getMsisdn(), simInfo.getMsisdn().toString());
      Assertions.assertEquals(simEntry.getPort(), simInfo.getIpv4Port().toInt());
      Assertions.assertEquals(simEntry.getOperator(), simInfo.getMobileNetworkOperator().toString());
    } else {
      Assertions.assertTrue(optionalSimInfo.isEmpty());
    }
  }

  @Test
  void tryHandleConrepoLookupClientExceptionTest() {
    tryConrepoLookupClientException(
        (conrepoLookupClient) -> Mockito.when(conrepoLookupClient.lookupByHandle(TestUtils.HANDLE.toString())).thenThrow(new RuntimeException("test")),
        conrepoIdentifier -> conrepoIdentifier.tryHandle(TestUtils.HANDLE, STACK).get(), getVerifyConsumerForTryHandle());
  }

  @Test
  void tryHandleEmptyTest() {
    tryEmpty(
        (conrepoLookupClient) -> Mockito.when(conrepoLookupClient.lookupByHandle(TestUtils.HANDLE.toString())).thenReturn(Optional.empty()),
        conrepoIdentifier -> conrepoIdentifier.tryHandle(TestUtils.HANDLE, STACK), getVerifyConsumerForTryHandle());
  }

  @Test
  void tryHandleFailToInsertDeviceExceptionTest() {
    tryFailToInsertDeviceException(getMockConsumerForTryHandle(), conrepoIdentifier -> conrepoIdentifier.tryHandle(TestUtils.HANDLE, STACK).get(),
        getVerifyConsumerForTryHandle());
  }

  @Test
  void tryHandleFailToInsertDeviceSequenceExceptionTest() {
    tryFailToInsertDeviceSequenceException(getMockConsumerForTryHandle(), conrepoIdentifier -> conrepoIdentifier.tryHandle(TestUtils.HANDLE, STACK).get(),
        getVerifyConsumerForTryHandle());
  }

  @Test
  void tryHandleInvalidTest() {
    performTryInvalid(conrepoIdentifier -> {
      AssertThrows.illegalArgumentException(() -> conrepoIdentifier.tryHandle(null, STACK), "handle must not be null");
      AssertThrows.illegalArgumentException(() -> conrepoIdentifier.tryHandle(TestUtils.HANDLE, null), "stackName must not be null");
      AssertThrows.illegalArgumentException(() -> conrepoIdentifier.tryHandle(TestUtils.HANDLE, ""), "stackName must not be empty");
    });
  }

  @Test
  void tryHandleTest() {
    tryNotEmpty(getMockConsumerForTryHandle(), conrepoIdentifier -> conrepoIdentifier.tryHandle(TestUtils.HANDLE, STACK).get(),
        getVerifyConsumerForTryHandle());
  }

  @Test
  void tryIpConrepoLookupClientExceptionTest() {
    tryConrepoLookupClientException(
        (conrepoLookupClient) -> Mockito.when(conrepoLookupClient.lookupByIp(TestUtils.IPV4_ADDRESS.toString())).thenThrow(new RuntimeException("test")),
        conrepoIdentifier -> conrepoIdentifier.tryIp(TestUtils.IPV4_ADDRESS, STACK).get(), getVerifyConsumerForTryIp());
  }

  @Test
  void tryIpEmptyTest() {
    tryEmpty(
        (conrepoLookupClient) -> Mockito.when(conrepoLookupClient.lookupByIp(TestUtils.IPV4_ADDRESS.toString())).thenReturn(Optional.empty()),
        conrepoIdentifier -> conrepoIdentifier.tryIp(TestUtils.IPV4_ADDRESS, STACK), getVerifyConsumerForTryIp());
  }

  @Test
  void tryIpFailToInsertDeviceExceptionTest() {
    tryFailToInsertDeviceException(getMockConsumerForTryIp(),
        conrepoIdentifier -> conrepoIdentifier.tryIp(TestUtils.IPV4_ADDRESS, STACK).get(), getVerifyConsumerForTryIp());
  }

  @Test
  void tryIpFailToInsertDeviceSequenceExceptionTest() {
    tryFailToInsertDeviceSequenceException(getMockConsumerForTryIp(), conrepoIdentifier -> conrepoIdentifier.tryIp(TestUtils.IPV4_ADDRESS, STACK).get(),
        getVerifyConsumerForTryIp());
  }

  @Test
  void tryIpInvalidTest() {
    performTryInvalid(conrepoIdentifier -> {
      AssertThrows.illegalArgumentException(() -> conrepoIdentifier.tryIp(null, STACK), "ipv4Address must not be null");
      AssertThrows.illegalArgumentException(() -> conrepoIdentifier.tryIp(TestUtils.IPV4_ADDRESS, null), "stackName must not be null");
      AssertThrows.illegalArgumentException(() -> conrepoIdentifier.tryIp(TestUtils.IPV4_ADDRESS, ""), "stackName must not be empty");
    });
  }

  @Test
  void tryIpTest() {
    tryNotEmpty(getMockConsumerForTryIp(), conrepoIdentifier -> conrepoIdentifier.tryIp(TestUtils.IPV4_ADDRESS, STACK).get(), getVerifyConsumerForTryIp());
  }

  @Test
  void tryMsisdnConrepoLookupClientExceptionTest() {
    tryConrepoLookupClientException(
        (conrepoLookupClient) -> Mockito.when(conrepoLookupClient.lookupByMsisdn(TestUtils.MSISDN.toString())).thenThrow(new RuntimeException("test")),
        conrepoIdentifier -> conrepoIdentifier.tryMsisdn(TestUtils.MSISDN, STACK).get(), getVerifyConsumerForTryMsisdn());
  }

  @Test
  void tryMsisdnEmptyTest() {
    tryEmpty(
        (conrepoLookupClient) -> Mockito.when(conrepoLookupClient.lookupByMsisdn(TestUtils.MSISDN.toString())).thenReturn(Optional.empty()),
        conrepoIdentifier -> conrepoIdentifier.tryMsisdn(TestUtils.MSISDN, STACK), getVerifyConsumerForTryMsisdn());
  }

  @Test
  void tryMsisdnFailToInsertDeviceExceptionTest() {
    tryFailToInsertDeviceException(getMockConsumerFotTryMsisdn(), conrepoIdentifier -> conrepoIdentifier.tryMsisdn(TestUtils.MSISDN, STACK).get(),
        getVerifyConsumerForTryMsisdn());
  }

  @Test
  void tryMsisdnFailToInsertDeviceSequenceExceptionTest() {
    tryFailToInsertDeviceSequenceException(
        getMockConsumerFotTryMsisdn(), conrepoIdentifier -> conrepoIdentifier.tryMsisdn(TestUtils.MSISDN, STACK).get(), getVerifyConsumerForTryMsisdn());
  }

  @Test
  void tryMsisdnInvalidTest() {
    performTryInvalid(conrepoIdentifier -> {
      AssertThrows.illegalArgumentException(() -> conrepoIdentifier.tryMsisdn(null, STACK), "msisdn must not be null");
      AssertThrows.illegalArgumentException(() -> conrepoIdentifier.tryMsisdn(TestUtils.MSISDN, null), "stackName must not be null");
      AssertThrows.illegalArgumentException(() -> conrepoIdentifier.tryMsisdn(TestUtils.MSISDN, ""), "stackName must not be empty");
    });
  }

  @Test
  void tryMsisdnTest() {
    tryNotEmpty(getMockConsumerFotTryMsisdn(),
        conrepoIdentifier -> conrepoIdentifier.tryMsisdn(TestUtils.MSISDN, STACK).get(), getVerifyConsumerForTryMsisdn());
  }

  @Test
  void trySatelliteConrepoLookupClientExceptionTest() {
    tryConrepoLookupClientException(
        (conrepoLookupClient) -> Mockito.when(conrepoLookupClient.lookupBySatId(TestUtils.SATELLITE_ID.toString())).thenThrow(new RuntimeException("test")),
        conrepoIdentifier -> conrepoIdentifier.trySatellite(TestUtils.SATELLITE_ID, STACK).get(), getVerifyConsumerForTrySat());
  }

  @Test
  void trySatelliteEmptyTest() {
    tryEmpty(
        (conrepoLookupClient) -> Mockito.when(conrepoLookupClient.lookupBySatId(TestUtils.SATELLITE_ID.toString())).thenReturn(Optional.empty()),
        conrepoIdentifier -> conrepoIdentifier.trySatellite(TestUtils.SATELLITE_ID, STACK), getVerifyConsumerForTrySat());
  }

  @Test
  void trySatelliteFailToInsertDeviceExceptionTest() {
    tryFailToInsertDeviceException(getMockConsumerForTrySat(), conrepoIdentifier -> conrepoIdentifier.trySatellite(TestUtils.SATELLITE_ID, STACK).get(),
        getVerifyConsumerForTrySat());
  }

  @Test
  void trySatelliteFailToInsertDeviceSequenceExceptionTest() {
    tryFailToInsertDeviceSequenceException(getMockConsumerForTrySat(),
        conrepoIdentifier -> conrepoIdentifier.trySatellite(TestUtils.SATELLITE_ID, STACK).get(), getVerifyConsumerForTrySat());
  }

  @Test
  void trySatelliteInvalidTest() {
    performTryInvalid(conrepoIdentifier -> {
      AssertThrows.illegalArgumentException(() -> conrepoIdentifier.trySatellite(null, STACK), "satelliteId must not be null");
      AssertThrows.illegalArgumentException(() -> conrepoIdentifier.trySatellite(TestUtils.SATELLITE_ID, null), "stackName must not be null");
      AssertThrows.illegalArgumentException(() -> conrepoIdentifier.trySatellite(TestUtils.SATELLITE_ID, ""), "stackName must not be empty");
    });
  }

  @Test
  void trySatelliteTest() {
    tryNotEmpty(getMockConsumerForTrySat(), conrepoIdentifier -> conrepoIdentifier.trySatellite(TestUtils.SATELLITE_ID, STACK).get(),
        getVerifyConsumerForTrySat());
  }

  @Test
  void tryVpiConrepoLookupClientExceptionTest() {
    tryConrepoLookupClientException(
        (conrepoLookupClient) -> Mockito.when(conrepoLookupClient.lookupByVehiclePlatformId(TestUtils.VPI.toString())).thenThrow(new RuntimeException("test")),
        conrepoIdentifier -> conrepoIdentifier.tryVpi(TestUtils.VPI, STACK).get(), getVerifyConsumerForTryVpi());
  }

  @Test
  void tryVpiEmptyTest() {
    tryEmpty(
        (conrepoLookupClient) -> Mockito.when(conrepoLookupClient.lookupByVehiclePlatformId(TestUtils.VPI.toString())).thenReturn(Optional.empty()),
        conrepoIdentifier -> conrepoIdentifier.tryVpi(TestUtils.VPI, STACK),
        getVerifyConsumerForTryVpi());
  }

  @Test
  void tryVpiFailToInsertDeviceExceptionTest() {
    tryFailToInsertDeviceException(getMockConsumerForTryVpi(), conrepoIdentifier -> conrepoIdentifier.tryVpi(TestUtils.VPI, STACK).get(),
        getVerifyConsumerForTryVpi());
  }

  @Test
  void tryVpiFailToInsertDeviceSequenceExceptionTest() {
    tryFailToInsertDeviceSequenceException(getMockConsumerForTryVpi(), conrepoIdentifier -> conrepoIdentifier.tryVpi(TestUtils.VPI, STACK).get(),
        getVerifyConsumerForTryVpi());
  }

  @Test
  void tryVpiTest() {
    tryNotEmpty(getMockConsumerForTryVpi(), conrepoIdentifier -> conrepoIdentifier.tryVpi(TestUtils.VPI, STACK).get(), getVerifyConsumerForTryVpi());
  }
}
