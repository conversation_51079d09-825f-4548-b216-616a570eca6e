package com.wirelesscar.tcevce.module.identify;

import java.util.Collections;
import java.util.Optional;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentMatchers;
import org.mockito.InOrder;
import org.mockito.Mockito;

import com.volvo.tisp.vc.test.utils.lib.AssertThrows;
import com.wirelesscar.tce.core.conf.TceConfigException;
import com.wirelesscar.tce.core.conf.TceConfigParser;
import com.wirelesscar.tce.module.api.Message;
import com.wirelesscar.tce.module.api.MetaData;
import com.wirelesscar.tce.module.api.Module;
import com.wirelesscar.tce.module.api.TransportType;
import com.wirelesscar.tce.module.api.exception.EngineRuntimeException;
import com.wirelesscar.tce.module.metrics.ModuleMetricReporter;
import com.wirelesscar.tcevce.module.identify.metrics.VceIdentifyModuleMetricReporter;
import com.wirelesscar.tcevce.wecu.device.info.database.model.DeviceInfo;
import com.wirelesscar.tcevce.wecu.device.info.database.model.SimInfo;

class VceIdentifyModuleTest {
  private static void setModuleConfig(VceIdentifyModule vceIdentifyModule, Module lowerModule, Module upperModule) throws TceConfigException {
    TceConfigParser.setConfig(vceIdentifyModule, Collections.emptyMap());
    vceIdentifyModule.setDown(lowerModule);
    vceIdentifyModule.setUp(upperModule);
  }

  private static void verifyDownMessageWithPropertiesNotSet(Message message) {
    Assertions.assertNull(message.getProperty(MetaData.IP_DST_ADDRESS));
    Assertions.assertNull(message.getProperty(MetaData.IP_DST_PORT));
    Assertions.assertNull(message.getProperty(MetaData.SMPP_DEST_ADDRESS));
    Assertions.assertNull(message.getProperty(MetaData.SATELLITE_DEST_ADDRESS));
    Assertions.assertNull(message.getProperty(MetaData.VPI));
    Assertions.assertNull(message.getProperty(MetaData.HANDLE));
    Assertions.assertNull(message.getProperty(MetaData.OPERATOR));
    Assertions.assertNull(message.getVehicleID());
  }

  private static void verifyMessageAfterDown(DeviceInfo expectedDeviceInfo, Message message) {
    Assertions.assertEquals(expectedDeviceInfo.getSatelliteId().get().toString(), message.getProperty(MetaData.SATELLITE_DEST_ADDRESS));

    Assertions.assertEquals(expectedDeviceInfo.getVpi().get().toString(), message.getProperty(MetaData.VPI));
    Assertions.assertEquals(expectedDeviceInfo.getHandle().toString(), message.getProperty(MetaData.HANDLE));
    Assertions.assertEquals(expectedDeviceInfo.getVpi().get().toString(), message.getVehicleID());

    SimInfo simInfo = expectedDeviceInfo.getSimInfo().get();
    Assertions.assertEquals(simInfo.getIpv4Address().toString(), message.getProperty(MetaData.IP_DST_ADDRESS));
    Assertions.assertEquals(simInfo.getIpv4Port().toString(), message.getProperty(MetaData.IP_DST_PORT));
    Assertions.assertEquals(simInfo.getMsisdn().toString(), message.getProperty(MetaData.SMPP_DEST_ADDRESS));
    Assertions.assertEquals(simInfo.getMobileNetworkOperator().toString(), message.getProperty(MetaData.OPERATOR));
  }

  private static void verifyMessageAfterUp(DeviceInfo expectedDeviceInfo, Message message) {
    Assertions.assertEquals(expectedDeviceInfo.getVpi().get().toString(), message.getProperty(MetaData.VPI));
    Assertions.assertEquals(expectedDeviceInfo.getHandle().toString(), message.getProperty(MetaData.HANDLE));
    Assertions.assertEquals(expectedDeviceInfo.getVpi().get().toString(), message.getVehicleID());

    SimInfo expectedSimInfo = expectedDeviceInfo.getSimInfo().get();
    Assertions.assertEquals(expectedSimInfo.getMobileNetworkOperator().toString(), message.getProperty(MetaData.OPERATOR));
  }

  private static void verifyUpMessageWithPropertiesNotSet(Message message) {
    Assertions.assertNull(message.getProperty(MetaData.VPI));
    Assertions.assertNull(message.getProperty(MetaData.HANDLE));
    Assertions.assertNull(message.getProperty(MetaData.OPERATOR));
    Assertions.assertNull(message.getVehicleID());
  }

  @Test
  void downDeviceNotExistTest() throws TceConfigException {
    Message message = new Message("", null);
    verifyDownMessageWithPropertiesNotSet(message);

    ModuleMetricReporter moduleMetricReporter = Mockito.mock(ModuleMetricReporter.class);
    VceIdentifyService vceIdentifyService = Mockito.mock(VceIdentifyService.class);
    VceIdentifyModuleMetricReporter vceIdentifyModuleMetricReporter = Mockito.mock(VceIdentifyModuleMetricReporter.class);
    Module lowerModule = Mockito.mock(Module.class);
    Module upperModule = Mockito.mock(Module.class);

    VceIdentifyModule vceIdentifyModule = new VceIdentifyModule(moduleMetricReporter, vceIdentifyModuleMetricReporter, vceIdentifyService);
    setModuleConfig(vceIdentifyModule, lowerModule, upperModule);

    Mockito.when(vceIdentifyService.identifyDeviceDown(message, vceIdentifyModule.getStackName())).thenReturn(Optional.empty());

    vceIdentifyModule.down(message);
    verifyDownMessageWithPropertiesNotSet(message);

    InOrder inOrder = Mockito.inOrder(moduleMetricReporter, vceIdentifyModuleMetricReporter, vceIdentifyService, lowerModule, upperModule);
    inOrder.verify(vceIdentifyService).identifyDeviceDown(message, vceIdentifyModule.getStackName());
    inOrder.verify(vceIdentifyModuleMetricReporter).onDownFail(vceIdentifyModule.getStackName());
    inOrder.verifyNoMoreInteractions();
  }

  @Test
  void downInvalidTest() throws TceConfigException {
    ModuleMetricReporter moduleMetricReporter = Mockito.mock(ModuleMetricReporter.class);
    VceIdentifyModuleMetricReporter vceIdentifyModuleMetricReporter = Mockito.mock(VceIdentifyModuleMetricReporter.class);
    VceIdentifyService vceIdentifyService = Mockito.mock(VceIdentifyService.class);
    Module lowerModule = Mockito.mock(Module.class);
    Module upperModule = Mockito.mock(Module.class);

    VceIdentifyModule vceIdentifyModule = new VceIdentifyModule(moduleMetricReporter, vceIdentifyModuleMetricReporter, vceIdentifyService);
    setModuleConfig(vceIdentifyModule, lowerModule, upperModule);

    AssertThrows.illegalArgumentException(() -> vceIdentifyModule.down(null), "message must not be null");

    Mockito.verifyNoMoreInteractions(moduleMetricReporter, vceIdentifyModuleMetricReporter, vceIdentifyService);
  }

  @Test
  void downOkTest() throws TceConfigException {
    ModuleMetricReporter moduleMetricReporter = Mockito.mock(ModuleMetricReporter.class);
    VceIdentifyService vceIdentifyService = Mockito.mock(VceIdentifyService.class);
    VceIdentifyModuleMetricReporter vceIdentifyModuleMetricReporter = Mockito.mock(VceIdentifyModuleMetricReporter.class);
    Module lowerModule = Mockito.mock(Module.class);
    Module upperModule = Mockito.mock(Module.class);

    VceIdentifyModule vceIdentifyModule = new VceIdentifyModule(moduleMetricReporter, vceIdentifyModuleMetricReporter, vceIdentifyService);
    setModuleConfig(vceIdentifyModule, lowerModule, upperModule);

    DeviceInfo deviceInfo = TestUtils.createDeviceInfo();

    Mockito.when(vceIdentifyService.identifyDeviceDown(ArgumentMatchers.any(Message.class), ArgumentMatchers.eq(vceIdentifyModule.getStackName())))
        .thenReturn(Optional.of(deviceInfo));

    Message message = new Message("", null);
    verifyDownMessageWithPropertiesNotSet(message);

    vceIdentifyModule.down(message);
    verifyMessageAfterDown(deviceInfo, message);

    InOrder inOrder = Mockito.inOrder(moduleMetricReporter, vceIdentifyModuleMetricReporter, vceIdentifyService, lowerModule, upperModule);
    inOrder.verify(vceIdentifyService).identifyDeviceDown(ArgumentMatchers.any(Message.class), ArgumentMatchers.eq(vceIdentifyModule.getStackName()));
    inOrder.verify(lowerModule).down(ArgumentMatchers.any(Message.class));
    inOrder.verifyNoMoreInteractions();
  }

  @Test
  void downRuntimeExceptionTest() throws TceConfigException {
    VceIdentifyModuleMetricReporter vceIdentifyModuleMetricReporter = Mockito.mock(VceIdentifyModuleMetricReporter.class);
    VceIdentifyService vceIdentifyService = Mockito.mock(VceIdentifyService.class);
    ModuleMetricReporter moduleMetricReporter = Mockito.mock(ModuleMetricReporter.class);
    Module lowerModule = Mockito.mock(Module.class);
    Module upperModule = Mockito.mock(Module.class);

    Mockito.when(vceIdentifyService.identifyDeviceDown(ArgumentMatchers.any(Message.class), ArgumentMatchers.anyString()))
        .thenThrow(new RuntimeException("test"));

    VceIdentifyModule vceIdentifyModule = new VceIdentifyModule(moduleMetricReporter, vceIdentifyModuleMetricReporter, vceIdentifyService);
    setModuleConfig(vceIdentifyModule, lowerModule, upperModule);

    Message message = new Message("", null);
    AssertThrows.exception(() -> vceIdentifyModule.down(message),
        "DOWN: Error identifying message: Message[messageId=, vehicleID=null, Type=null, payloadSize=0, Properties={MESSAGE_ID=, MT=true}]",
        EngineRuntimeException.class);

    InOrder inOrder = Mockito.inOrder(moduleMetricReporter, vceIdentifyModuleMetricReporter, vceIdentifyService, lowerModule, upperModule);
    inOrder.verify(vceIdentifyService).identifyDeviceDown(message, vceIdentifyModule.getStackName());
    inOrder.verifyNoMoreInteractions();
  }

  @Test
  void upDeviceNotExistTest() throws TceConfigException {
    ModuleMetricReporter moduleMetricReporter = Mockito.mock(ModuleMetricReporter.class);
    VceIdentifyModuleMetricReporter vceIdentifyModuleMetricReporter = Mockito.mock(VceIdentifyModuleMetricReporter.class);
    VceIdentifyService vceIdentifyService = Mockito.mock(VceIdentifyService.class);
    Module lowerModule = Mockito.mock(Module.class);
    Module upperModule = Mockito.mock(Module.class);

    Mockito.when(vceIdentifyService.identifyDeviceUp(ArgumentMatchers.any(Message.class), ArgumentMatchers.anyString())).thenReturn(Optional.empty());

    Message message = new Message("", null);
    verifyUpMessageWithPropertiesNotSet(message);

    VceIdentifyModule vceIdentifyModule = new VceIdentifyModule(moduleMetricReporter, vceIdentifyModuleMetricReporter, vceIdentifyService);
    setModuleConfig(vceIdentifyModule, lowerModule, upperModule);

    vceIdentifyModule.up(message);
    verifyUpMessageWithPropertiesNotSet(message);

    Assertions.assertEquals("true", message.getProperty(MetaData.VEHICLE_NOT_FOUND));

    InOrder inOrder = Mockito.inOrder(moduleMetricReporter, vceIdentifyModuleMetricReporter, vceIdentifyService, lowerModule, upperModule);
    inOrder.verify(vceIdentifyService).identifyDeviceUp(message, vceIdentifyModule.getStackName());
    inOrder.verify(vceIdentifyModuleMetricReporter).onUpFail(vceIdentifyModule.getStackName());
    inOrder.verify(upperModule).up(ArgumentMatchers.any(Message.class));
    inOrder.verifyNoMoreInteractions();
  }

  @Test
  void upInvalidTest() {
    VceIdentifyModuleMetricReporter vceIdentifyModuleMetricReporter = Mockito.mock(VceIdentifyModuleMetricReporter.class);
    VceIdentifyService vceIdentifyService = Mockito.mock(VceIdentifyService.class);
    ModuleMetricReporter moduleMetricReporter = Mockito.mock(ModuleMetricReporter.class);

    VceIdentifyModule vceIdentifyModule = new VceIdentifyModule(moduleMetricReporter, vceIdentifyModuleMetricReporter, vceIdentifyService);
    AssertThrows.illegalArgumentException(() -> vceIdentifyModule.up(null), "message must not be null");

    Mockito.verifyNoMoreInteractions(moduleMetricReporter, vceIdentifyModuleMetricReporter, vceIdentifyService);
  }

  @Test
  void upOkTest() throws TceConfigException {
    ModuleMetricReporter moduleMetricReporter = Mockito.mock(ModuleMetricReporter.class);
    VceIdentifyService vceIdentifyService = Mockito.mock(VceIdentifyService.class);
    VceIdentifyModuleMetricReporter vceIdentifyModuleMetricReporter = Mockito.mock(VceIdentifyModuleMetricReporter.class);
    Module lowerModule = Mockito.mock(Module.class);
    Module upperModule = Mockito.mock(Module.class);

    DeviceInfo deviceInfo = TestUtils.createDeviceInfo();

    Mockito.when(vceIdentifyService.identifyDeviceUp(ArgumentMatchers.any(Message.class), ArgumentMatchers.anyString())).thenReturn(Optional.of(deviceInfo));

    Message message = new Message("", null);
    verifyUpMessageWithPropertiesNotSet(message);

    VceIdentifyModule vceIdentifyModule = new VceIdentifyModule(moduleMetricReporter, vceIdentifyModuleMetricReporter, vceIdentifyService);
    setModuleConfig(vceIdentifyModule, lowerModule, upperModule);

    vceIdentifyModule.up(message);
    verifyMessageAfterUp(deviceInfo, message);

    InOrder inOrder = Mockito.inOrder(moduleMetricReporter, vceIdentifyModuleMetricReporter, vceIdentifyService, lowerModule, upperModule);
    inOrder.verify(vceIdentifyService).identifyDeviceUp(ArgumentMatchers.any(Message.class), ArgumentMatchers.eq(vceIdentifyModule.getStackName()));
    inOrder.verify(upperModule).up(ArgumentMatchers.any(Message.class));
    inOrder.verifyNoMoreInteractions();
  }

  @Test
  void upRuntimeExceptionTest() throws TceConfigException {
    ModuleMetricReporter moduleMetricReporter = Mockito.mock(ModuleMetricReporter.class);
    VceIdentifyService vceIdentifyService = Mockito.mock(VceIdentifyService.class);
    VceIdentifyModuleMetricReporter vceIdentifyModuleMetricReporter = Mockito.mock(VceIdentifyModuleMetricReporter.class);
    Module lowerModule = Mockito.mock(Module.class);
    Module upperModule = Mockito.mock(Module.class);

    Mockito.when(vceIdentifyService.identifyDeviceUp(ArgumentMatchers.any(Message.class), ArgumentMatchers.anyString()))
        .thenThrow(new RuntimeException("test"));

    VceIdentifyModule vceIdentifyModule = new VceIdentifyModule(moduleMetricReporter, vceIdentifyModuleMetricReporter, vceIdentifyService);
    setModuleConfig(vceIdentifyModule, lowerModule, upperModule);

    Message message = new Message("", null);

    AssertThrows.exception(() -> vceIdentifyModule.up(message),
        "UP: Error identifying message: Message[messageId=, vehicleID=null, Type=null, payloadSize=0, Properties={MESSAGE_ID=}]",
        EngineRuntimeException.class);

    InOrder inOrder = Mockito.inOrder(moduleMetricReporter, vceIdentifyModuleMetricReporter, vceIdentifyService, lowerModule, upperModule);
    inOrder.verify(vceIdentifyService).identifyDeviceUp(message, vceIdentifyModule.getStackName());
    inOrder.verifyNoMoreInteractions();
  }

  @Test
  void upWhenUnknownMessageAndTransportTypeIsSatelliteThenOkTest() throws TceConfigException {
    ModuleMetricReporter moduleMetricReporter = Mockito.mock(ModuleMetricReporter.class);
    VceIdentifyService vceIdentifyService = Mockito.mock(VceIdentifyService.class);
    VceIdentifyModuleMetricReporter vceIdentifyModuleMetricReporter = Mockito.mock(VceIdentifyModuleMetricReporter.class);
    Module lowerModule = Mockito.mock(Module.class);
    Module upperModule = Mockito.mock(Module.class);

    Mockito.when(vceIdentifyService.identifyDeviceUp(ArgumentMatchers.any(Message.class), ArgumentMatchers.anyString())).thenReturn(Optional.empty());

    Message message = new Message("", null);
    message.setProperty(MetaData.TRANSPORT_TYPE, TransportType.SATELLITE.name());
    verifyUpMessageWithPropertiesNotSet(message);

    VceIdentifyModule vceIdentifyModule = new VceIdentifyModule(moduleMetricReporter, vceIdentifyModuleMetricReporter, vceIdentifyService);
    setModuleConfig(vceIdentifyModule, lowerModule, upperModule);

    vceIdentifyModule.up(message);

    verifyUpMessageWithPropertiesNotSet(message);
    Assertions.assertEquals("true", message.getProperty(MetaData.VEHICLE_NOT_FOUND));

    InOrder inOrder = Mockito.inOrder(moduleMetricReporter, vceIdentifyModuleMetricReporter, vceIdentifyService, lowerModule, upperModule);
    inOrder.verify(vceIdentifyService).identifyDeviceUp(message, vceIdentifyModule.getStackName());
    inOrder.verify(vceIdentifyModuleMetricReporter).onUpFail(vceIdentifyModule.getStackName());
    inOrder.verify(vceIdentifyModuleMetricReporter).onUpFailureSat(vceIdentifyModule.getStackName());
    inOrder.verify(upperModule).up(ArgumentMatchers.any(Message.class));
    inOrder.verifyNoMoreInteractions();
  }

  @Test
  void upWhenUnknownMessageAndTransportTypeIsSmsThenOkTest() throws TceConfigException {
    VceIdentifyService vceIdentifyService = Mockito.mock(VceIdentifyService.class);
    VceIdentifyModuleMetricReporter vceIdentifyModuleMetricReporter = Mockito.mock(VceIdentifyModuleMetricReporter.class);
    ModuleMetricReporter moduleMetricReporter = Mockito.mock(ModuleMetricReporter.class);
    Module lowerModule = Mockito.mock(Module.class);
    Module upperModule = Mockito.mock(Module.class);

    Mockito.when(vceIdentifyService.identifyDeviceUp(ArgumentMatchers.any(Message.class), ArgumentMatchers.anyString())).thenReturn(Optional.empty());

    Message message = new Message("", null);
    message.setProperty(MetaData.TRANSPORT_TYPE, TransportType.SMS.name());
    verifyUpMessageWithPropertiesNotSet(message);

    VceIdentifyModule vceIdentifyModule = new VceIdentifyModule(moduleMetricReporter, vceIdentifyModuleMetricReporter, vceIdentifyService);
    setModuleConfig(vceIdentifyModule, lowerModule, upperModule);

    vceIdentifyModule.up(message);

    verifyUpMessageWithPropertiesNotSet(message);
    Assertions.assertEquals("true", message.getProperty(MetaData.VEHICLE_NOT_FOUND));

    InOrder inOrder = Mockito.inOrder(moduleMetricReporter, vceIdentifyModuleMetricReporter, vceIdentifyService, lowerModule, upperModule);
    inOrder.verify(vceIdentifyService).identifyDeviceUp(message, vceIdentifyModule.getStackName());
    inOrder.verify(vceIdentifyModuleMetricReporter).onUpFail(vceIdentifyModule.getStackName());
    inOrder.verify(vceIdentifyModuleMetricReporter).onUpFailureSms(vceIdentifyModule.getStackName());
    inOrder.verify(upperModule).up(ArgumentMatchers.any(Message.class));
    inOrder.verifyNoMoreInteractions();
  }

  @Test
  void upWhenUnknownMessageAndTransportTypeIsUdpThenOkTest() throws TceConfigException {
    VceIdentifyService vceIdentifyService = Mockito.mock(VceIdentifyService.class);
    ModuleMetricReporter moduleMetricReporter = Mockito.mock(ModuleMetricReporter.class);
    VceIdentifyModuleMetricReporter vceIdentifyModuleMetricReporter = Mockito.mock(VceIdentifyModuleMetricReporter.class);
    Module lowerModule = Mockito.mock(Module.class);
    Module upperModule = Mockito.mock(Module.class);

    Mockito.when(vceIdentifyService.identifyDeviceUp(ArgumentMatchers.any(Message.class), ArgumentMatchers.anyString())).thenReturn(Optional.empty());

    Message message = new Message("", null);
    message.setProperty(MetaData.TRANSPORT_TYPE, TransportType.UDP.name());
    verifyUpMessageWithPropertiesNotSet(message);

    VceIdentifyModule vceIdentifyModule = new VceIdentifyModule(moduleMetricReporter, vceIdentifyModuleMetricReporter, vceIdentifyService);
    setModuleConfig(vceIdentifyModule, lowerModule, upperModule);

    vceIdentifyModule.up(message);

    verifyUpMessageWithPropertiesNotSet(message);
    Assertions.assertEquals("true", message.getProperty(MetaData.VEHICLE_NOT_FOUND));

    InOrder inOrder = Mockito.inOrder(moduleMetricReporter, vceIdentifyModuleMetricReporter, vceIdentifyService, lowerModule, upperModule);
    inOrder.verify(vceIdentifyService).identifyDeviceUp(message, vceIdentifyModule.getStackName());
    inOrder.verify(vceIdentifyModuleMetricReporter).onUpFail(vceIdentifyModule.getStackName());
    inOrder.verify(vceIdentifyModuleMetricReporter).onUpFailureUdp(vceIdentifyModule.getStackName());
    inOrder.verify(upperModule).up(ArgumentMatchers.any(Message.class));
    inOrder.verifyNoMoreInteractions();
  }
}
