package com.wirelesscar.tcevce.module.identify;

import java.util.Optional;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.vc.test.utils.lib.AssertThrows;
import com.volvo.tisp.vc.test.utils.lib.UtilClassVerifier;
import com.wirelesscar.tce.db.common.db.api.MessageFields;
import com.wirelesscar.tce.module.api.Message;
import com.wirelesscar.tce.module.api.MetaData;
import com.wirelesscar.tcevce.wecu.device.info.database.model.DeviceInfo;
import com.wirelesscar.tcevce.wecu.device.info.database.model.SimInfo;

class VceIdentifyMessagePropertiesUtilTest {

  private static void assertHandle(DeviceInfo deviceInfo, Message message) {
    String handleString = deviceInfo.getHandle().toString();

    Assertions.assertEquals(message.getProperty(MetaData.HANDLE), handleString);
    Assertions.assertEquals(message.getProperty(MessageFields.handle.name()), handleString);
  }

  private static Message createMessage() {
    return new Message("", null);
  }

  @Test
  void getHandleInvalidTest() {
    AssertThrows.illegalArgumentException(() -> VceIdentifyMessagePropertiesUtil.getHandle(null), "message must not be null");
  }

  @Test
  void getHandleTest() {
    Message message = createMessage();

    message.setProperty(MetaData.HANDLE, TestUtils.HANDLE.toString());
    Assertions.assertEquals(TestUtils.HANDLE, VceIdentifyMessagePropertiesUtil.getHandle(message).get());
  }

  @Test
  void getHandle1Test() {
    Message message = createMessage();

    Assertions.assertEquals(Optional.empty(), VceIdentifyMessagePropertiesUtil.getHandle(message));
  }

  @Test
  void getSourceIpv4AddressInvalidTest() {
    AssertThrows.illegalArgumentException(() -> VceIdentifyMessagePropertiesUtil.getSourceIpv4Address(null), "message must not be null");
  }

  @Test
  void getSourceIpv4AddressTest() {
    Message message = createMessage();

    Assertions.assertTrue(VceIdentifyMessagePropertiesUtil.getSourceIpv4Address(message).isEmpty());

    message.setProperty(MetaData.IP_SRC_ADDRESS, TestUtils.IPV4_ADDRESS.toString());
    Assertions.assertEquals(TestUtils.IPV4_ADDRESS, VceIdentifyMessagePropertiesUtil.getSourceIpv4Address(message).get());
  }

  @Test
  void getSourceMsisdnInvalidTest() {
    AssertThrows.illegalArgumentException(() -> VceIdentifyMessagePropertiesUtil.getSourceMsisdn(null), "message must not be null");
  }

  @Test
  void getSourceMsisdnSmppSrcAddress() {
    Message message = createMessage();

    Assertions.assertTrue(VceIdentifyMessagePropertiesUtil.getSourceMsisdn(message).isEmpty());

    message.setProperty(MetaData.SMPP_SOURCE_ADDRESS, TestUtils.MSISDN.toString());
    Assertions.assertEquals(TestUtils.MSISDN, VceIdentifyMessagePropertiesUtil.getSourceMsisdn(message).get());
  }

  @Test
  void getSourceMsisdnTest() {
    Message message = createMessage();

    Assertions.assertTrue(VceIdentifyMessagePropertiesUtil.getSourceMsisdn(message).isEmpty());

    message.setProperty(MessageFields.msisdn.name(), TestUtils.MSISDN.toString());
    Assertions.assertEquals(TestUtils.MSISDN, VceIdentifyMessagePropertiesUtil.getSourceMsisdn(message).get());
  }

  @Test
  void getSourceSatelliteIdInvalidTest() {
    AssertThrows.illegalArgumentException(() -> VceIdentifyMessagePropertiesUtil.getSourceSatelliteId(null), "message must not be null");
  }

  @Test
  void getSourceSatelliteIdTest() {
    Message message = createMessage();

    Assertions.assertTrue(VceIdentifyMessagePropertiesUtil.getSourceSatelliteId(message).isEmpty());

    message.setProperty(MetaData.SATELLITE_SOURCE_ADDRESS, TestUtils.SATELLITE_ID.toString());
    Assertions.assertEquals(TestUtils.SATELLITE_ID, VceIdentifyMessagePropertiesUtil.getSourceSatelliteId(message).get());
  }

  @Test
  void getVpiInvalidTest() {
    AssertThrows.illegalArgumentException(() -> VceIdentifyMessagePropertiesUtil.getVpi(null), "message must not be null");
  }

  @Test
  void getVpiTest() {
    Message message = createMessage();

    Assertions.assertTrue(VceIdentifyMessagePropertiesUtil.getVpi(message).isEmpty());

    message.setProperty(MetaData.VPI, TestUtils.VPI.toString());
    Assertions.assertEquals(TestUtils.VPI, VceIdentifyMessagePropertiesUtil.getVpi(message).get());
  }

  @Test
  void setPropertiesInvalidTest() {
    Message message = createMessage();
    DeviceInfo deviceInfo = TestUtils.createDeviceInfo();

    AssertThrows.illegalArgumentException(() -> VceIdentifyMessagePropertiesUtil.setProperties(null, deviceInfo, false), "message must not be null");
    AssertThrows.illegalArgumentException(() -> VceIdentifyMessagePropertiesUtil.setProperties(null, deviceInfo, true), "message must not be null");

    AssertThrows.illegalArgumentException(() -> VceIdentifyMessagePropertiesUtil.setProperties(message, null, false), "deviceInfo must not be null");
    AssertThrows.illegalArgumentException(() -> VceIdentifyMessagePropertiesUtil.setProperties(message, null, true), "deviceInfo must not be null");
  }

  @Test
  void setPropertiesWhenDownTest() {
    DeviceInfo deviceInfo = TestUtils.createDeviceInfo();

    Message message = createMessage();
    VceIdentifyMessagePropertiesUtil.setProperties(message, deviceInfo, true);

    String expectedVpiString = deviceInfo.getVpi().get().toString();
    Assertions.assertEquals(expectedVpiString, message.getVehicleID());
    Assertions.assertEquals(expectedVpiString, message.getProperty(MetaData.VPI));

    assertHandle(deviceInfo, message);

    SimInfo simInfo = deviceInfo.getSimInfo().get();
    Assertions.assertEquals(message.getProperty(MetaData.IP_DST_ADDRESS), simInfo.getIpv4Address().toString());
    Assertions.assertEquals(message.getProperty(MetaData.IP_DST_PORT), simInfo.getIpv4Port().toString());
    Assertions.assertEquals(message.getProperty(MetaData.SMPP_DEST_ADDRESS), simInfo.getMsisdn().toString());
    Assertions.assertEquals(message.getProperty(MetaData.OPERATOR), simInfo.getMobileNetworkOperator().toString());
  }

  @Test
  void setPropertiesWhenUpAndMessageHasMsisdnPropertyTest() {
    DeviceInfo deviceInfo = TestUtils.createDeviceInfo();

    Message message = createMessage();
    message.setProperty(MetaData.SMPP_SOURCE_ADDRESS, TestUtils.MSISDN.toString());

    VceIdentifyMessagePropertiesUtil.setProperties(message, deviceInfo, false);

    String expectedVpiString = deviceInfo.getVpi().get().toString();
    Assertions.assertEquals(expectedVpiString, message.getVehicleID());
    Assertions.assertEquals(expectedVpiString, message.getProperty(MetaData.VPI));

    assertHandle(deviceInfo, message);

    Assertions.assertNull(message.getProperty(MetaData.IP_DST_ADDRESS));
    Assertions.assertNull(message.getProperty(MetaData.IP_DST_PORT));
    Assertions.assertNull(message.getProperty(MetaData.SMPP_DEST_ADDRESS));

    Assertions.assertEquals(message.getProperty(MetaData.OPERATOR), deviceInfo.getSimInfo().get().getMobileNetworkOperator().toString());
    Assertions.assertEquals(message.getProperty(MetaData.SMPP_SOURCE_ADDRESS), TestUtils.MSISDN.toString());
  }

  @Test
  void setPropertiesWhenUpAndMessageHasNoMsisdnPropertyTest() {
    DeviceInfo deviceInfo = TestUtils.createDeviceInfo();

    Message message = createMessage();
    VceIdentifyMessagePropertiesUtil.setProperties(message, deviceInfo, false);

    String expectedVpiString = deviceInfo.getVpi().get().toString();
    Assertions.assertEquals(expectedVpiString, message.getVehicleID());
    Assertions.assertEquals(expectedVpiString, message.getProperty(MetaData.VPI));

    assertHandle(deviceInfo, message);

    Assertions.assertNull(message.getProperty(MetaData.IP_DST_ADDRESS));
    Assertions.assertNull(message.getProperty(MetaData.IP_DST_PORT));
    Assertions.assertNull(message.getProperty(MetaData.SMPP_DEST_ADDRESS));

    SimInfo simInfo = deviceInfo.getSimInfo().get();
    Assertions.assertEquals(message.getProperty(MetaData.OPERATOR), simInfo.getMobileNetworkOperator().toString());
    Assertions.assertEquals(message.getProperty(MetaData.SMPP_SOURCE_ADDRESS), simInfo.getMsisdn().toString());
  }

  @Test
  void setVehicleIdInvalidTest() {
    AssertThrows.illegalArgumentException(() -> VceIdentifyMessagePropertiesUtil.setVehicleId(null, TestUtils.createDeviceInfo()), "message must not be null");
    AssertThrows.illegalArgumentException(() -> VceIdentifyMessagePropertiesUtil.setVehicleId(createMessage(), null), "deviceInfo must not be null");
  }

  @Test
  void setVehicleIdTest() {
    DeviceInfo deviceInfo = TestUtils.createDeviceInfo();

    Message message = createMessage();
    VceIdentifyMessagePropertiesUtil.setVehicleId(message, deviceInfo);

    Assertions.assertEquals(deviceInfo.getVpi().get().toString(), message.getVehicleID());
  }

  @Test
  void verifyIsUtilClassTest() throws ReflectiveOperationException {
    UtilClassVerifier.verifyUtilClass(VceIdentifyMessagePropertiesUtil.class);
  }
}
