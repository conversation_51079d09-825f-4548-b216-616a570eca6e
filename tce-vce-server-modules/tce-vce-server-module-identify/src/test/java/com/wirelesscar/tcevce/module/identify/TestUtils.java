package com.wirelesscar.tcevce.module.identify;

import java.time.Instant;
import java.util.Optional;

import com.volvo.tisp.vc.common.dto.lib.vehicle.Imsi;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Ipv4Address;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Msisdn;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.wirelesscar.conrepo.api.v1.DeviceDetailedEntry;
import com.wirelesscar.conrepo.api.v1.DeviceSim;
import com.wirelesscar.tcevce.wecu.device.info.database.model.DeviceInfo;
import com.wirelesscar.tcevce.wecu.device.info.database.model.DeviceInfoBuilder;
import com.wirelesscar.tcevce.wecu.device.info.database.model.DeviceInfoId;
import com.wirelesscar.tcevce.wecu.device.info.database.model.DeviceSequence;
import com.wirelesscar.tcevce.wecu.device.info.database.model.DeviceSequenceBuilder;
import com.wirelesscar.tcevce.wecu.device.info.database.model.Handle;
import com.wirelesscar.tcevce.wecu.device.info.database.model.Ipv4Port;
import com.wirelesscar.tcevce.wecu.device.info.database.model.MobileNetworkOperator;
import com.wirelesscar.tcevce.wecu.device.info.database.model.PersistedDeviceInfo;
import com.wirelesscar.tcevce.wecu.device.info.database.model.PersistedDeviceInfoBuilder;
import com.wirelesscar.tcevce.wecu.device.info.database.model.SatelliteId;
import com.wirelesscar.tcevce.wecu.device.info.database.model.SequenceNumber;
import com.wirelesscar.tcevce.wecu.device.info.database.model.SimInfo;
import com.wirelesscar.tcevce.wecu.device.info.database.model.SimInfoBuilder;

final class TestUtils {
  static final Instant CREATED = Instant.ofEpochSecond(1);
  static final DeviceInfoId DEVICE_INFO_ID = DeviceInfoId.ofLong(1);
  static final Handle HANDLE = Handle.ofString("123456");
  static final Ipv4Address IPV4_ADDRESS = Ipv4Address.ofString("*******");
  static final Ipv4Port IPV4_PORT = Ipv4Port.ofInt(9_062);
  static final Instant LAST_UPDATED = Instant.ofEpochSecond(2);
  static final MobileNetworkOperator MOBILE_NETWORK_OPERATOR = MobileNetworkOperator.ofString("testOperator");
  static final Msisdn MSISDN = Msisdn.ofString("+469123456789");
  static final SatelliteId SATELLITE_ID = SatelliteId.ofString("HQ1234567890x1");
  static final SequenceNumber SEQUENCE_NUMBER = SequenceNumber.ofByte((byte) 0);
  static final Vpi VPI = Vpi.ofString("1234567890ABCDEF1234567890ABCDEF");

  private TestUtils() {
    throw new IllegalStateException();
  }

  static DeviceDetailedEntry createDeviceDetailedEntry() {
    DeviceDetailedEntry deviceDetailedEntry = new DeviceDetailedEntry();

    deviceDetailedEntry.setHandle(HANDLE.toString());
    deviceDetailedEntry.setSimEntry(createDeviceSim());
    deviceDetailedEntry.setSatelliteId(SATELLITE_ID.toString());
    deviceDetailedEntry.setVehiclePlatformId(VPI.toString());

    return deviceDetailedEntry;
  }

  static DeviceInfo createDeviceInfo() {
    return createDeviceInfoBuilder().build();
  }

  static DeviceInfoBuilder createDeviceInfoBuilder() {
    return new DeviceInfoBuilder()
        .setHandle(HANDLE)
        .setSatelliteId(Optional.of(SATELLITE_ID))
        .setSimInfo(Optional.of(createSimInfo()))
        .setVpi(Optional.of(VPI));
  }

  static DeviceSequence createDeviceSequence() {
    return createDeviceSequenceBuilder().build();
  }

  static DeviceSequenceBuilder createDeviceSequenceBuilder() {
    return new DeviceSequenceBuilder()
        .setDeviceInfoId(DEVICE_INFO_ID)
        .setSequenceNumber(SEQUENCE_NUMBER);
  }

  static DeviceSim createDeviceSim() {
    DeviceSim deviceSim = new DeviceSim();

    deviceSim.setImsi(Imsi.ofLong(1L).toString());
    deviceSim.setMsisdn(MSISDN.toString());
    deviceSim.setIp(IPV4_ADDRESS.toString());
    deviceSim.setPort(IPV4_PORT.toInt());
    deviceSim.setOperator(MOBILE_NETWORK_OPERATOR.toString());

    return deviceSim;
  }

  static PersistedDeviceInfo createPersistedDeviceInfo() {
    return createPersistedDeviceInfoBuilder().build();
  }

  static PersistedDeviceInfoBuilder createPersistedDeviceInfoBuilder() {
    return new PersistedDeviceInfoBuilder()
        .setCreated(CREATED)
        .setDeviceInfo(createDeviceInfo())
        .setDeviceInfoId(DEVICE_INFO_ID)
        .setLastUpdated(LAST_UPDATED);
  }

  static SimInfo createSimInfo() {
    return createSimInfoBuilder().build();
  }

  static SimInfoBuilder createSimInfoBuilder() {
    return new SimInfoBuilder()
        .setImsi(Imsi.ofLong(1L))
        .setIpv4Address(IPV4_ADDRESS)
        .setIpv4Port(IPV4_PORT)
        .setMobileNetworkOperator(MOBILE_NETWORK_OPERATOR)
        .setMsisdn(MSISDN);
  }
}
