package com.wirelesscar.tcevce.module.scheduler.cache.api;

import java.sql.SQLException;

import com.wirelesscar.tce.db.common.cache.api.Cacher;
import com.wirelesscar.tcevce.module.scheduler.api.Schedule;

public interface ScheduleCache extends Cacher<Schedule> {
  /**
   * Get entry from cache by name. Will try to fetch entry from <PERSON> if not found in cache
   *
   * @param name entry.getName()
   * @throws SQLException if fetch from db failed with error
   */
  Schedule getByName(String name) throws SQLException;
}
