package com.wirelesscar.tcevce.module.scheduler.db.impl.upgrade;

import java.sql.SQLException;
import java.util.List;
import java.util.Locale;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.wirelesscar.tcevce.module.scheduler.api.Schedule;
import com.wirelesscar.tcevce.module.scheduler.api.Transition;
import com.wirelesscar.tcevce.module.scheduler.db.api.SchedulePersistanceFactory;
import com.wirelesscar.tcevce.module.scheduler.db.api.SchedulePersister;
import com.wirelesscar.tcevce.module.scheduler.db.api.TransitionPersister;

public class TransitionUpgrade {
  private static final Logger log = LoggerFactory.getLogger(TransitionUpgrade.class);

  private TransitionPersister transitionPersister;

  public void upgrade() throws SQLException {
    SchedulePersister schedulePersister = SchedulePersistanceFactory.makeSchedulePersister();
    transitionPersister = SchedulePersistanceFactory.makeTransitionPersister();

    for (Schedule schedule : schedulePersister.find(null)) {
      List<Transition> transitions = transitionPersister.findBySchedule(schedule);

      Transition currentFinal = transitions.get(transitions.size() - 1);
      if (!currentFinal.getConnEstablishedAllowed()) {
        if (schedule.getName().trim().toLowerCase(Locale.ROOT).endsWith("setup")) {
          log.info("Setup Transition discovered: {}", currentFinal);
          updateAndCreate(schedule, transitionPersister, currentFinal, 7_200 * 1_000);
        } else if (schedule.getName().trim().toLowerCase(Locale.ROOT).endsWith("short")) {
          log.info("Short Transition discovered: {}", currentFinal);
          updateAndCreate(schedule, transitionPersister, currentFinal, 500 * 1_000);
        } else if (schedule.getName().trim().toLowerCase(Locale.ROOT).endsWith("very-high")) {
          log.info("Very-high Transition discovered: {}", currentFinal);
          updateAndCreate(schedule, transitionPersister, currentFinal, 40 * 1_000);
        } else if (schedule.getName().trim().toLowerCase(Locale.ROOT).endsWith("wakeup")) {
          log.info("Wakeup Transition discovered: {}", currentFinal);
          updateAndCreate(schedule, transitionPersister, currentFinal, 4_000 * 1_000);
        } else if (schedule.getName().trim().toLowerCase(Locale.ROOT).endsWith("mid-plus")) {
          log.info("Mid-plus Transition discovered: {}", currentFinal);
          updateAndCreate(schedule, transitionPersister, currentFinal, 180 * 1_000);
        } else if (currentFinal.getName().trim().equalsIgnoreCase("UDP")) {
          log.info("UDP Transition discovered: {}", currentFinal);
          updateAndCreate(schedule, transitionPersister, currentFinal, 300 * 1_000);
        } else if (currentFinal.getName().trim().equalsIgnoreCase("SMS")) {
          log.info("SMS Transition discovered: {}", currentFinal);
          updateAndCreate(schedule, transitionPersister, currentFinal, 1_200 * 1_000);
        } else {
          log.info("Unknown Transition: {}", currentFinal);
          updateAndCreate(schedule, transitionPersister, currentFinal, 1_200 * 1_000);
        }
      } else {
        log.info("Upgrade already done for {}", currentFinal);
      }
    }
  }

  private void addNewTransition(Schedule schedule, Transition currentFinal) throws SQLException {
    long ttl = schedule.getTimeToLive() - currentFinal.getTimeToLive();

    Transition transition = new Transition();
    transition.setConnEstablishedAllowed(true);
    transition.setMaxNumberOfTriesPerMsg(1_000);
    transition.setName("WAIT");
    transition.setOrder(currentFinal.getOrder() + 10);
    transition.setSchedule(schedule);
    transition.setTimeOutRepeat(10);
    transition.setTimeToLive(ttl);

    log.info("Creating new Transition: {}", transition);
    transition = transitionPersister.persist(transition);
    log.info("Created Transition: {}", transition);
  }

  private void updateAndCreate(Schedule schedule, TransitionPersister tranPers, Transition currentFinal, int ttl) throws SQLException {
    currentFinal.setTimeToLive(ttl);
    tranPers.persist(currentFinal);

    addNewTransition(schedule, currentFinal);
  }
}
