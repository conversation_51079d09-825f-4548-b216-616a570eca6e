package com.wirelesscar.tcevce.module.scheduler.cache;

import java.sql.SQLException;
import java.util.concurrent.TimeUnit;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.wirelesscar.config.Config;
import com.wirelesscar.config.ConfigFactory;
import com.wirelesscar.tcevce.module.scheduler.api.Schedule;
import com.wirelesscar.tcevce.module.scheduler.cache.api.ScheduleCache;
import com.wirelesscar.tcevce.module.scheduler.db.api.SchedulePersistanceFactory;
import com.wirelesscar.tcevce.module.scheduler.db.api.SchedulePersister;

/** Cache of Schedule objects */
public final class ScheduleCacheImpl implements ScheduleCache {
  private static final ScheduleCache INSTANCE = new ScheduleCacheImpl();

  private Cache<String, Schedule> byId;
  private Cache<String, Schedule> byName;
  private int maxCacheMinutesUsed = MAX_RETAIN_MINUTES;
  private int maxCacheSizeUsed = MAX_SIZE;

  private ScheduleCacheImpl() {
    createCaches();
  }

  public static ScheduleCache getInstance() {
    return INSTANCE;
  }

  private static SchedulePersister getPersister() {
    return SchedulePersistanceFactory.makeSchedulePersister();
  }

  @Override
  public int cacheRetainMinutes() {
    return maxCacheMinutesUsed;
  }

  @Override
  public int cacheSizeMax() {
    return maxCacheSizeUsed;
  }

  @Override
  public void clear() {
    byId.invalidateAll();
    byName.invalidateAll();

    createCaches();
  }

  @Override
  public Schedule get(String id, boolean fetchFromDbIfNeeded) throws SQLException {
    Schedule schedule = byId.getIfPresent(id);

    if (schedule == null && fetchFromDbIfNeeded) {
      schedule = getPersister().lookup(id);
      byId.put(id, schedule);
      byName.put(schedule.getName(), schedule);
    }

    return schedule;
  }

  @Override
  public Schedule getByName(String name) throws SQLException {
    Schedule schedule = byName.getIfPresent(name);

    if (schedule == null) {
      schedule = getPersister().lookupByName(name);
      if (schedule != null) {
        byName.put(name, schedule);
        byId.put(schedule.getId(), schedule);
      }
    }

    return schedule;
  }

  @Override
  public long getCacheSize() {
    return byId.size();
  }

  @Override
  public void remove(Schedule schedule) {
    byId.invalidate(schedule.getId());
    byName.invalidate(schedule.getName());
  }

  private void createCaches() {
    Config config = ConfigFactory.getConfig();
    int maxMinutes = config.getInt("scheduler.module.cache.minutes").orElse(MAX_RETAIN_MINUTES);
    int size = config.getInt("scheduler.module.cache.size").orElse(MAX_SIZE);

    byId = CacheBuilder.newBuilder()
        .concurrencyLevel(10)
        .maximumSize(size)
        .expireAfterWrite(maxMinutes, TimeUnit.MINUTES)
        .build();

    byName = CacheBuilder.newBuilder()
        .concurrencyLevel(10)
        .maximumSize(size)
        .expireAfterWrite(maxMinutes, TimeUnit.MINUTES)
        .build();

    maxCacheMinutesUsed = maxMinutes;
    maxCacheSizeUsed = size;
  }
}
