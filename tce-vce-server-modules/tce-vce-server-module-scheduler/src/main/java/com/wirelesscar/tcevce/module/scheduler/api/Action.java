package com.wirelesscar.tcevce.module.scheduler.api;

import java.util.HashMap;
import java.util.Map;

import com.wirelesscar.tce.db.common.api.CacheableObject;
import com.wirelesscar.tce.db.common.db.api.CacheableObjectType;
import com.wirelesscar.tce.module.api.Message;
import com.wirelesscar.tcevce.module.scheduler.SchedulerModule;
import com.wirelesscar.tcevce.module.scheduler.db.api.ExternalAction;
import com.wirelesscar.tcevce.module.scheduler.db.api.FailedActionException;
import com.wirelesscar.tcevce.module.scheduler.db.api.InternalAction;

public class Action extends CacheableObject {
  private boolean isInternal = true;
  private String name;
  private int order;
  private final Map<String, String> properties = new HashMap<>();
  private Transition transition;

  /** Executes action on message, */
  public void executeAction(Message message, SchedulerModule schedulerModule) throws FailedActionException {
    if (isInternal) {
      InternalAction.execute(this, message, properties);
    } else {
      ExternalAction.execute(message, schedulerModule);
    }
  }

  public String getName() {
    return name;
  }

  @Override
  public CacheableObjectType getObjectType() {
    return CacheableObjectType.Action;
  }

  public int getOrder() {
    return order;
  }

  public Map<String, String> getProperties() {
    return properties;
  }

  public Transition getTransition() {
    return transition;
  }

  /** internal or external. External = sending on down, internal= e.g. set property */
  public boolean isInternal() {
    return isInternal;
  }

  public void setInternal(boolean isInternal) {
    this.isInternal = isInternal;
  }

  public void setName(String name) {
    this.name = name;
  }

  public void setOrder(int order) {
    this.order = order;
  }

  public void setProperty(final String key, final String value) {
    properties.put(key, value);
  }

  public void setTransition(Transition transition) {
    this.transition = transition;
  }

  @Override
  public String toString() {
    StringBuilder stringBuilder = new StringBuilder();
    stringBuilder.append(getObjectType().name());

    stringBuilder.append(" id:");
    stringBuilder.append(getId());

    stringBuilder.append(", name:");
    stringBuilder.append(getName());

    stringBuilder.append(", order:");
    stringBuilder.append(getOrder());

    stringBuilder.append(", ");
    stringBuilder.append(getTransition() != null ? getTransition().toString() : " Transition not set. ");

    stringBuilder.append(", Properties:");
    stringBuilder.append(getProperties().toString());

    return stringBuilder.toString();
  }
}
