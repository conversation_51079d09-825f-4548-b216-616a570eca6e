package com.wirelesscar.tcevce.module.scheduler;

import java.nio.charset.StandardCharsets;
import java.sql.SQLException;
import java.time.Duration;
import java.util.ArrayList;
import java.util.EnumMap;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Queue;
import java.util.TreeMap;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.TimeUnit;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.volvo.tisp.framework.context.TispContext;
import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.wirelesscar.config.Config;
import com.wirelesscar.config.ConfigFactory;
import com.wirelesscar.tce.core.event.Type;
import com.wirelesscar.tce.db.common.AbstractProcessorThread;
import com.wirelesscar.tce.db.common.api.ActivityStatus;
import com.wirelesscar.tce.db.common.db.api.MessageFields;
import com.wirelesscar.tce.db.common.db.api.MessagePersister;
import com.wirelesscar.tce.module.api.EnqueueingType;
import com.wirelesscar.tce.module.api.Message;
import com.wirelesscar.tce.module.api.MessageStatus;
import com.wirelesscar.tce.module.api.MetaData;
import com.wirelesscar.tce.utils.ClusterConfig;
import com.wirelesscar.tce.utils.LoggingHelper;
import com.wirelesscar.tce.utils.Util;
import com.wirelesscar.tcevce.module.scheduler.api.Action;
import com.wirelesscar.tcevce.module.scheduler.api.Schedule;
import com.wirelesscar.tcevce.module.scheduler.api.Transition;
import com.wirelesscar.tcevce.module.scheduler.cache.api.SchedulerCacheFactory;
import com.wirelesscar.tcevce.module.scheduler.db.api.FailedActionException;
import com.wirelesscar.tcevce.module.scheduler.db.api.SchedulePersistanceFactory;
import com.wirelesscar.tcevce.module.scheduler.metrics.SchedulerModuleMetricReporter;

public class SchedulerProcessorThread extends AbstractProcessorThread {
  private static final String DEFAULT_SCHEDULE_NAME = "common_normal";
  private static final String DEFAULT_SCHEDULE_NAME_START = "common_";
  private static final String[] defaultScheduleNameEndings = new String[] {"very-high", "high", "mid", "low", "setup", "rswdl", "short", "long"};
  private static final Logger log = LoggerFactory.getLogger(SchedulerProcessorThread.class);
  private static final String QUEUE_ID_NOT_SET = "QUEUE_ID_NOT_SET";
  private static final String SCHEDULE_PROP_NAME_BASE = "SCHDPRP_";

  private final int activeMsgCap;
  private final int activeMsgCapWaitMs;
  private final Cache<String, Queue<String>> cappedMsgListCache;
  private long cappedMsgs;
  private int connectionEstablishedMsg;
  private final Cache<String, String> connEstablishedCache;
  private long discardedDueToEnqueueType;
  private long execTransitionsTimes;
  private final String hashKeyBase;
  private final String hashKeyBaseSecondary;
  private final String hashKeyBaseTertiary;
  private final Cache<String, String> lastMsgCache;
  private final Cache<String, String> lastMsgSentCache;
  /** Log metrics for cache size */
  private long logCount;
  private final int maxIdsInCache;
  private final Cache<String, Map<String, Map<Long, String>>> msgIdsPerDeviceQ;
  private long noUsableTransition;
  private final int pollTimeOutMs;
  private long processed;
  private long processedToWait;
  private final SchedulerModuleMetricReporter schedulerModuleMetricReporter;
  private final SchedulerProcessor schedulerProcessor;
  private final int secsCappedCache;
  private final int secsConnEstCache;
  private final int secsDuplicateCheck;
  private int skippedTimeout;
  private int skippedTransitionsMaxTries;
  private int skippedTransitionsRepeat;

  public SchedulerProcessorThread(SchedulerProcessor schedulerProcessor, int number, SchedulerModuleMetricReporter schedulerModuleMetricReporter) {
    super(schedulerProcessor, number);
    Validate.notNull(schedulerProcessor, "schedulerProcessor");
    Validate.notNull(schedulerModuleMetricReporter, "schedulerModuleMetricReporter");

    this.schedulerProcessor = schedulerProcessor;
    this.schedulerModuleMetricReporter = schedulerModuleMetricReporter;

    Config config = ConfigFactory.getConfig();
    pollTimeOutMs = 500;
    maxIdsInCache = config
        .getInt(schedulerProcessor.getModule().getStackName()
            + ".scheduler.workerthread.thread.max-ids-cache")
        .orElse(50_000);
    activeMsgCap = config
        .getInt(schedulerProcessor.getModule().getStackName()
            + ".scheduler.workerthread.thread.active_msg_cap")
        .orElse(5);
    activeMsgCapWaitMs = config
        .getInt(schedulerProcessor.getModule().getStackName()
            + ".scheduler.workerthread.thread.msg_cap_wait_ms")
        .orElse(1_000 * 3_600 * 24);
    secsCappedCache = config
        .getInt(schedulerProcessor.getModule().getStackName()
            + ".scheduler.workerthread.min_secs_capped_cache")
        .orElse(5);
    secsDuplicateCheck = config
        .getInt(schedulerProcessor.getModule().getStackName()
            + ".scheduler.workerthread.min_secs_duplicate_check")
        .orElse(5);
    secsConnEstCache = config
        .getInt(schedulerProcessor.getModule().getStackName()
            + ".scheduler.workerthread.min_secs_conn_estbl_cache")
        .orElse(120);

    int hoursCacheMsgId = config
        .getInt(schedulerProcessor.getModule().getStackName()
            + ".scheduler.workerthread.cache.msgid.hours")
        .orElse(7 * 24);
    int maxCacheMsgId = config
        .getInt(schedulerProcessor.getModule().getStackName()
            + ".scheduler.workerthread.cache.msgid.max")
        .orElse(10_000);
    msgIdsPerDeviceQ = CacheBuilder.newBuilder()
        .maximumSize(maxCacheMsgId)
        .expireAfterWrite(hoursCacheMsgId, TimeUnit.HOURS)
        .build();
    cappedMsgListCache = CacheBuilder.newBuilder()
        .maximumSize(maxCacheMsgId)
        .expireAfterWrite(secsCappedCache, TimeUnit.SECONDS)
        .build();
    connEstablishedCache = CacheBuilder.newBuilder()
        .maximumSize(maxCacheMsgId)
        .expireAfterWrite(secsConnEstCache, TimeUnit.SECONDS)
        .build();
    lastMsgCache = CacheBuilder.newBuilder()
        .maximumSize(maxCacheMsgId)
        .expireAfterWrite(secsDuplicateCheck, TimeUnit.SECONDS)
        .build();
    lastMsgSentCache = CacheBuilder.newBuilder()
        .maximumSize(maxCacheMsgId)
        .expireAfterWrite(secsDuplicateCheck, TimeUnit.SECONDS)
        .build();

    hashKeyBase = ((SchedulerModule) this.schedulerProcessor.getModule()).getHashkey();
    hashKeyBaseSecondary = ((SchedulerModule) this.schedulerProcessor.getModule()).getHashkeyBaseSecondary();
    hashKeyBaseTertiary = ((SchedulerModule) this.schedulerProcessor.getModule()).getHashKeyBaseTertiary();

    log.info("Capped message limit: {}", activeMsgCap);
  }

  private static void checkAllTransitionsUsed(Message message, List<Transition> transitions) {
    boolean anyLeft = false;
    for (Transition transition : transitions) {
      String maxTriesKey = makeMaxTriesKey(transition);
      int triesSoFar = getCounter(message, maxTriesKey);

      if (triesSoFar < transition.getMaxNumberOfTriesPerMsg()) {
        anyLeft = true;
        break;
      }
    }

    if (!anyLeft) {
      message.setProperty(MessageFields.allTransitionsUsed.name(), "true");
    }
  }

  private static void discontinueProcessing(Message message, String logStr) {
    log.debug("{} {}", logStr, message.getMessageId());
  }

  private static String fetchLastTransitionName(Message message) {
    return message.getProperty(MessageFields.lastTransitionName.name());
  }

  private static Schedule fetchSchedule(Message message) throws SQLException {
    String scheduleName = message.getProperty(MetaData.SCHEDULE_NAME);

    if (scheduleName == null || scheduleName.isEmpty()) {
      scheduleName = DEFAULT_SCHEDULE_NAME;
      message.setProperty(MetaData.SCHEDULE_NAME, scheduleName);
      log.warn("WARNING: No " + MetaData.SCHEDULE_NAME + " found on message: " + message.getMessageId());
    }

    Schedule schedule = SchedulerCacheFactory.scheduleCache().getByName(scheduleName);

    if (schedule == null) {
      schedule = findBestMatchSchedule(message);
    }
    return schedule;
  }

  private static Schedule findBestMatchSchedule(Message message) throws SQLException {
    String scheduleName = message.getProperty(MetaData.SCHEDULE_NAME);
    String[] stArr = scheduleName.split("_", 2);
    String prio = (stArr.length > 1 ? stArr[1] : stArr[0]).toLowerCase(Locale.ROOT);

    for (String ending : defaultScheduleNameEndings) {
      if (ending.equals(prio)) {
        scheduleName = DEFAULT_SCHEDULE_NAME_START + ending;
        break;
      }
    }

    if (scheduleName.isEmpty()) {
      scheduleName = DEFAULT_SCHEDULE_NAME;
    }

    Schedule schedule = SchedulerCacheFactory.scheduleCache().getByName(scheduleName);

    if (schedule == null) {
      scheduleName = DEFAULT_SCHEDULE_NAME;
      schedule = SchedulerCacheFactory.scheduleCache().getByName(scheduleName);
    }

    message.setProperty(MetaData.SCHEDULE_NAME, scheduleName);
    if (log.isDebugEnabled()) {
      log.debug("Best match schedule name chosen for " + message.getMessageId() + " : " + scheduleName);
    }

    return schedule;
  }

  private static int getCounter(Message message, String maxTriesKey) {
    String triesSoFarStr = message.getProperty(maxTriesKey);
    return triesSoFarStr != null ? Integer.valueOf(triesSoFarStr) : 0;
  }

  private static Long getLongProperty(Message message, String propertyKey, Long defaultValue) {
    Long ret = null;
    if (message.getProperties().containsKey(propertyKey)) {
      ret = message.getLongProperty(propertyKey);
    } else {
      ret = System.currentTimeMillis();
      message.setProperty(propertyKey, defaultValue.toString());
    }

    return ret;
  }

  private static boolean isDelivered(Message message) {
    String stat = message.getProperty(MetaData.STATUS);
    return stat != null ? stat.equals(MessageStatus.DELIVERED.name()) : false;
  }

  private static boolean isIgnoredType(Message message) {
    String s = message.getProperty(MetaData.SCHEDULE_ENQUEUEING_TYPE);
    EnqueueingType enq = (s != null && s.length() > 0) ? EnqueueingType.valueOf(s) : EnqueueingType.NORMAL;
    return enq.equals(EnqueueingType.IGNORE);
  }

  private static String makeMaxTriesKey(Transition transition) {
    StringBuilder stringBuilder = new StringBuilder();
    stringBuilder.append(SCHEDULE_PROP_NAME_BASE);
    stringBuilder.append("MXTRIES_");
    stringBuilder.append(transition.getName());

    return stringBuilder.toString();
  }

  private static String makeQueueId(Message message) {
    String queueid = message.getProperty(MetaData.SCHEDULE_QUEUE_ID);
    queueid = queueid != null ? queueid : QUEUE_ID_NOT_SET;
    return queueid;
  }

  private static String makeRepeatKey(Transition transition) {
    StringBuilder stringBuilder = new StringBuilder();
    stringBuilder.append(SCHEDULE_PROP_NAME_BASE);
    stringBuilder.append("REPEAT_");
    stringBuilder.append(transition.getName());

    return stringBuilder.toString();
  }

  private static void markWithLastTransitionName(Message message, String lastTransitionName, Transition transition) {
    message.setProperty(MessageFields.lastTransitionName.name(), (transition != null) ? transition.getName() : lastTransitionName);
  }

  /**
   * @return null if all transitions done for Schedule
   */
  private static Transition nextTransition(String lastTransitionName, List<Transition> list, boolean firstTime, boolean connEstbl, Message message) {
    Transition ret = null;

    // find next transition
    int i = 0;
    for (Transition transition : list) {
      i++;
      if (lastTransitionName.equals(transition.getName())) {
        if (connEstbl && firstTime) {
          ret = list.get(0);
        } else if (firstTime) {
          ret = transition;
        }
        break; // i has correct number : current +1
      }
    }

    if (ret == null) {
      if (i > (list.size() - 1)) {} else {
        ret = list.get(i);
        message.setProperty(makeRepeatKey(ret), "0");
      }
    }

    if (log.isDebugEnabled()) {
      log.debug(message.getMessageId()
          + " choosing Transition "
          + (ret != null ? ret.getName() : " null"));
    }

    return ret;
  }

  /** Checks whether a message has timed out or not */
  private static boolean notTimeOut(Message message) {
    boolean ok = true;

    String srm = message.getProperty(MessageFields.removeTime.name());
    if (srm != null && srm.length() > 0) {
      long lrm = Long.parseLong(srm);

      ok = lrm > System.currentTimeMillis();
    }

    return ok;
  }

  private static void validateSize(int size, int minSize, String msgError, Object addToMsg) {
    if (size < minSize) {
      throw new RuntimeException(msgError + " " + addToMsg);
    }
  }

  public void clearCaches() {
    log.info("Clearing caches SchedulerProcessorThread: {}", myNumber);
    cappedMsgListCache.invalidateAll();
    msgIdsPerDeviceQ.invalidateAll();
  }

  public void clearConnectionEstablishedCache() {
    connEstablishedCache.invalidateAll();
  }

  public void clearLastMsgCache() {
    lastMsgCache.invalidateAll();
    lastMsgSentCache.invalidateAll();
  }

  public void clearLastMsgCache(String msgId) {
    log.trace("Removing (clear) lastMsgCache key: {}", msgId);
    lastMsgCache.invalidate(msgId);
  }

  public void clearLastMsgSentCache(String msgId) {
    log.trace("Removing (clear) lastMsgSentCache key: {}", msgId);
    lastMsgSentCache.invalidate(msgId);
  }

  /**
   * return map is <queueID, TreeMap<createTime, msgId>>. This means that for each QueueID the waiting messages are ordered. Msg sent as parameter is not
   * included.
   */
  public Map<String, Map<Long, String>> findActiveMsgIdsNowForDevice(Message message, boolean fetchFromDb) throws SQLException {
    Map<String, Map<Long, String>> mapQtoMsgId = msgIdsPerDeviceQ.getIfPresent(message.getVehicleID());

    if (mapQtoMsgId == null && fetchFromDb) {
      List<Message> msgs = findActiveMsgInDBForVehicle(message);

      mapQtoMsgId = new ConcurrentHashMap<>();
      for (Message tmp : msgs) {
        String queueid = makeQueueId(tmp);

        Map<Long, String> msgIds = mapQtoMsgId.get(queueid);
        if (msgIds == null) {
          msgIds = new TreeMap<>();
          mapQtoMsgId.put(queueid, msgIds);
        }

        Long createTime = getLongProperty(tmp, MessageFields.createTime.name(), System.currentTimeMillis() - 1);
        msgIds.put(createTime, tmp.getMessageId());
      }

      msgIdsPerDeviceQ.put(message.getVehicleID(), mapQtoMsgId);
    }

    logSize();

    return mapQtoMsgId != null ? mapQtoMsgId : new HashMap<>();
  }

  public Map<String, Map<Long, String>> findActiveMsgIdsNowForDeviceInDatabase(Message message) throws SQLException {
    Map<String, Map<Long, String>> mapQtoMsgId = new ConcurrentHashMap<>();
    List<Message> msgs = findActiveMsgInDBForVehicle(message);

    for (Message tmp : msgs) {
      String queueid = makeQueueId(tmp);

      Map<Long, String> msgIds = mapQtoMsgId.computeIfAbsent(queueid, k -> new TreeMap<>());

      Long createTime = getLongProperty(tmp, MessageFields.createTime.name(), System.currentTimeMillis() - 1);
      msgIds.put(createTime, tmp.getMessageId());
    }

    msgIdsPerDeviceQ.put(message.getVehicleID(), mapQtoMsgId);

    logSize();

    return mapQtoMsgId;
  }

  /**
   * return map is <queueID, TreeMap<createTime, msgId>>. This means that for each QueueID the waiting messages are ordered. Msg sent as parameter is not
   * included.
   */
  public Message findNextActiveMsgsNotSentAllQ(Message message) throws SQLException {
    List<String> msgQ = new ArrayList<>(getCappedMsgList(message));

    Message notSent = null;
    for (String id : msgQ) {
      Message old = findMessage(id);
      if (old != null) {
        Long sentTimes = getLongProperty(old, MessageFields.sent.name(), 0L);
        if (sentTimes == 0) {
          notSent = old;
          break;
        }
      }
    }

    return notSent;
  }

  /**
   * return map is <queueID, TreeMap<createTime, msgId>>. This means that for each QueueID the waiting messages are ordered. Msg sent as parameter is not
   * included.
   */
  public Message findNextActiveMsgsNotSentForMyQ(Message message, boolean fetchFromDb) throws SQLException {
    Map<String, Map<Long, String>> activeMsgs = findActiveMsgIdsNowForDevice(message, fetchFromDb);
    String queueid = makeQueueId(message);

    Message notSent = null;
    Map<Long, String> msgIDs = activeMsgs.get(queueid);
    if (msgIDs != null && msgIDs.size() > 0) {
      for (String id : msgIDs.values()) {
        Message old = findMessage(id);
        Long sentTimes = getLongProperty(old, MessageFields.sent.name(), 0L);
        if (sentTimes == 0) {
          notSent = old;
          break;
        }
      }
    }

    return notSent;
  }

  /** Used to control number of active message for a device, so not to breach the cap. */
  public void flagMsgFinished(Message message) throws SQLException {
    log.debug("Removing msg: {}", message.getMessageId());

    msgIdsPerDeviceQ.invalidate(message.getVehicleID());
    removeFromCappedMsgCache(message.getMessageId(), message);
    schedulerProcessor.removeMsgFromCache(message.getMessageId());

    sendNextInQ(message);
  }

  public long getCappedMessageCount() {
    return cappedMsgs;
  }

  public Queue<String> getCappedMsgList(Message message) throws SQLException {
    if (message.getVehicleID() == null) {
      return new ConcurrentLinkedQueue<>();
    }

    Queue<String> msgInQ = cappedMsgListCache.getIfPresent(message.getVehicleID());

    if (msgInQ == null) {
      msgInQ = new ConcurrentLinkedQueue<>();
      List<Message> activeMessages = findActiveMsgInDBForVehicle(message);

      for (Message activeMessage : activeMessages) {
        if (!isDelivered(activeMessage)) {
          if (log.isDebugEnabled()) {
            log.debug("adding to cappedMsgListCache"
                + activeMessage.getMessageId()
                + ", list: "
                + msgInQ);
          }
          msgInQ.add(activeMessage.getMessageId());
        } else {
          log.debug("NOT adding delivered message to cappedMsgListCache {}", activeMessage.getMessageId());
        }
      }

      cappedMsgListCache.put(message.getVehicleID(), msgInQ);
    }

    return msgInQ;
  }

  public int getConnectionEstablishedMsg() {
    return connectionEstablishedMsg;
  }

  public long getDiscardedDueToEnqueueType() {
    return discardedDueToEnqueueType;
  }

  public long getExecTransitionsTimes() {
    return execTransitionsTimes;
  }

  public long getNoUsableTransition() {
    return noUsableTransition;
  }

  public long getProcessed() {
    return processed;
  }

  public long getProcessedToWait() {
    return processedToWait;
  }

  public long getSecsCacheTime() {
    return secsCappedCache;
  }

  public int getSkippedTimeout() {
    return skippedTimeout;
  }

  public int getSkippedTransitionsMaxTries() {
    return skippedTransitionsMaxTries;
  }

  public int getSkippedTransitionsRepeat() {
    return skippedTransitionsRepeat;
  }

  /** Polls one message from Q and tries to execute all Actions for the next Transition in line. */
  @Override
  public void processOneMessage() throws Exception {
    final Message message = queue.poll(pollTimeOutMs, TimeUnit.MILLISECONDS);

    if (message != null) {
      processorMetricsReporter.onTakeFromQueue(processor.getModule().getName());
      TispContext.runInContext(() -> {
        try {
          final boolean connEstMsg = message.getProperty(Type.CONNECTION_ESTABLISHED.name()) != null;

          if (existsInLastMsgCache(message.getMessageId())) {
            discontinueProcessing(message, "Discontinue msg, just handled it: ");
            schedulerModuleMetricReporter.onDroppingJustHandled();
            return;
          } else if (!connEstMsg
              && !ClusterConfig.getInstance()
                  .isMyMessage(message, hashKeyBase, hashKeyBaseSecondary, hashKeyBaseTertiary)) {
            discontinueProcessing(message, "Discontinue msg, not my msg, is handled by other instance in cluster: ");
            schedulerModuleMetricReporter.onDroppingNotMine();
            return;
          } else if (isDelivered(message)) {
            removeFromCappedMsgCache(message.getMessageId(), message);
            discontinueProcessing(message, "Discontinue msg, already delivered: ");
            schedulerModuleMetricReporter.onDroppingJustHandledDelivered();
            return;
          } else {
            addToLastMsgCache(message.getMessageId());
            addToCappedMsgCache(message, connEstMsg);
          }

          message.setProperty(MessageFields.sent.name(), "0");
          runningEmpty = false;

          log.debug("Starting on msg: {}, Qsize: {}", message, queue.size());
          final long ts0 = System.nanoTime();

          if (!connEstMsg) {
            if (notTimeOut(message)) {
              handleMsg(message);
            } else {
              handleTimeOut(message);
            }
            processed++;
          } else {
            handleConnectionEstablished(message);
            long timeInNanos = System.nanoTime() - ts0;
            schedulerModuleMetricReporter.logConnectionEstablished(Duration.ofNanos(timeInNanos));
            schedulerModuleMetricReporter.onCacheConnectionEstablished(connEstablishedCache.size());
          }
        } catch (Exception e) {
          log.error("", e);
        }
      }, builder -> LoggingHelper.populateContextWithMessage(builder, message));
    } else {
      runningEmpty = true;
    }
  }

  @Override
  protected MessagePersister getNewMessagePersister() {
    return SchedulePersistanceFactory.makeMessagePersister();
  }

  private void addToCappedMsgCache(Message message, boolean connectionEstbl) throws SQLException {
    if (!connectionEstbl && (message.getVehicleID() != null)) {
      Queue<String> msgInQ = getCappedMsgList(message);

      if (msgInQ.contains(message.getMessageId())) {
        if (log.isDebugEnabled()) {
          log.debug("cappedMsgListCache already have message "
              + message.getMessageId()
              + ", list: "
              + msgInQ);
        }
      } else {
        if (log.isDebugEnabled()) {
          log.debug("cappedMsgListCache adding message " + message.getMessageId() + ", list: " + msgInQ);
        }
        msgInQ.add(message.getMessageId());
      }
    }
  }

  private void addToLastMsgCache(String msgId) {
    log.trace("Add to lastMsgCache {}", msgId);

    lastMsgCache.put(msgId, msgId);
  }

  private void addToLastMsgSentCache(String msgId) {
    log.trace("Add to lastMsgSentCache {}", msgId);

    lastMsgSentCache.put(msgId, msgId);
  }

  /**
   * checks number of simultaneous message being send to device, and cap's at level set by activeMsgCap
   */
  private boolean checkActiveMsgCapExceeded(Message message) throws SQLException {
    String s = message.getProperty(MetaData.SCHEDULE_ENQUEUEING_TYPE);
    EnqueueingType enq = (s != null && s.length() > 0) ? EnqueueingType.valueOf(s) : EnqueueingType.NORMAL;
    if (enq.equals(EnqueueingType.IMMEDIATE) || enq.equals(EnqueueingType.OVERRIDE_IMMEDIATE)) {
      return false;
    }

    int activeMsg = countActiveMsgsExcludingThis(message);
    boolean capMsg = activeMsg >= activeMsgCap;

    if (log.isDebugEnabled()) {
      log.debug("Message (" + message.getMessageId() + ") capped? " + capMsg);
    }

    return capMsg;
  }

  /** checks if previous msgs with same serviceID should be overridden */
  private void checkEnqueueing(Message message) throws SQLException {
    String enqTypeStr = message.getProperty(MetaData.SCHEDULE_ENQUEUEING_TYPE);

    if (enqTypeStr != null) {
      EnqueueingType type = EnqueueingType.valueOf(enqTypeStr);

      switch (type) {
        case NORMAL: // Message placed in end of queue.
        case IMMEDIATE: // Send message immediate regardless of queues and throttling.
          break;

        case IGNORE: // If queue contains one (1) or more messages the new one is dropped. If not it
          // is placed in queue.
          break;

        case OVERRIDE: // Removes any messages in queue and replaces with this one.
        case OVERRIDE_IMMEDIATE: // Removes any messages in queue and replaces with this one. Sent
          // immediate regardless of throttling
          findAndDeletePreviousMsgs(message);
          break;

        default:
          throw new RuntimeException("Not implemented");
      }
    }
  }

  private boolean clearOlderIgnoreMessages(Message message) throws SQLException {
    log.debug("clearOlderIgnoreMessages for {}", message);

    boolean wasIgnored = false;

    Map<String, Map<Long, String>> msgQueueMsgIds = findActiveMsgIdsNowForDeviceInDatabase(message);
    String queueid = makeQueueId(message);

    Map<Long, String> msgIDs = msgQueueMsgIds.get(queueid);
    if (msgIDs != null && msgIDs.size() > 0) {
      List<String> list = new ArrayList<>(msgIDs.values()); // to avoid concurrent change exception
      log.info("before messages in the list {}", list);
      // Skip first message
      list.remove(0);
      log.info("after messages in the list {}", list);
      for (String msgId : list) {
        Message tmpMessage = findMessage(msgId);
        if (isIgnoredType(tmpMessage)) {
          // Set status to null on messages with EnqueueingType.IGNORE
          tmpMessage.setProperty(MessageFields.status.name(), ActivityStatus.d.name());

          log.info("Removing message {} due to IGNORE by message {}", msgId, message.getMessageId());

          getPersister().updateStatusAndProps(tmpMessage);
          discardedDueToEnqueueType++;
          schedulerModuleMetricReporter.onDiscarded();

          if (msgId.equals(message.getMessageId())) {
            wasIgnored = true;
          }
        } else {
          log.info(" message {} will not be ignored", msgId);
        }
      }

      flagQueueRemoveMsg(message);
    }
    return wasIgnored;
  }

  private int countActiveMsgsExcludingThis(Message message) throws SQLException {
    int sentMsgs = 0;
    List<String> msgQ = new ArrayList<>(getCappedMsgList(message));

    if (log.isDebugEnabled()) {
      log.debug("--- countActiveMsgsExcludingThis size:  " + msgQ.size());
    }

    for (String id : msgQ) {
      if (!id.equals(message.getMessageId())) {
        Message foundMessage = findMessage(id);
        if (foundMessage != null) {
          Long sent = getLongProperty(foundMessage, MessageFields.sent.name(), 0L);
          sentMsgs += sent > 0 ? 1 : 0;
        } else {
          removeFromCappedMsgCache(id, message);
        }
      }
    }

    if (log.isDebugEnabled()) {
      log.debug("Message (" + message.getMessageId() + ") countActiveMsgsExcludingThis? " + sentMsgs);
    }

    return sentMsgs;
  }

  /**
   * Executes all actions for this transition
   *
   * @param transition - null if no Transition to execute
   * @throws SQLException if error, leave as is and try again next time according to schedule
   * @throws FailedActionException if failed, leave as is and try again next time according to schedule
   */
  private void doActionsForTransition(Transition transition, Message message) throws SQLException, FailedActionException {
    schedulerModuleMetricReporter.onDown();

    // get actions exec in order
    List<Action> actions = SchedulerCacheFactory.actionCache().findByTransition(transition);
    if (log.isDebugEnabled()) {
      log.debug("Found actions for message ({}) {}", message.getMessageId(), actions);
    }

    for (Action action : actions) {
      action.executeAction(message, (SchedulerModule) processor.getModule());
    }
  }

  private boolean existsInLastMsgCache(String msgId) {
    if (msgId == null) {
      return false;
    }

    boolean exists = lastMsgCache.getIfPresent(msgId) != null;
    exists = exists ? exists : lastMsgSentCache.getIfPresent(msgId) != null;

    log.trace("Found in lastMsgCache? {} {}", msgId, exists);

    return exists;
  }

  private List<Message> fetchActiveMsgs(Map<String, Map<Long, String>> map, boolean onlyEligbleForConnEstbl) throws SQLException {
    List<Message> ret = new ArrayList<>();

    if (map != null) {
      Map<Long, String> orderdMap = new TreeMap<>();

      for (Map<Long, String> qmap : map.values()) {
        orderdMap.putAll(qmap);
      }

      for (String id : orderdMap.values()) {
        Message message = findMessage(id);
        if (onlyEligbleForConnEstbl) {
          String lastTransitionName = fetchLastTransitionName(message);
          Schedule schedule = fetchSchedule(message);
          Transition transition = null;

          if (lastTransitionName != null) {
            List<Transition> transitions = SchedulerCacheFactory.transitionCache().findBySchedule(schedule);
            for (Transition tmp : transitions) {
              if (tmp.getName().equals(lastTransitionName)) {
                transition = tmp;
                break;
              }
            }
          } else {
            transition = SchedulerCacheFactory.transitionCache().findBySchedule(schedule).get(0);
          }

          if (transition.getConnEstablishedAllowed()) {
            ret.add(message);
          }
        } else {
          ret.add(message);
        }
      }
    }

    return ret;
  }

  private List<Message> findActiveMsgInDBForVehicle(Message message) throws SQLException {
    Map<MessageFields, String> criterias = new EnumMap<>(MessageFields.class);
    criterias.put(MessageFields.vehicleID, message.getVehicleID());
    criterias.put(MessageFields.status, ActivityStatus.a.name());

    return getPersister().findForMe(criterias, "IX_SCHDLD_MSSG_VHCL_STTS_LOCAL");
  }

  private void findAndDeletePreviousMsgs(Message message) throws SQLException {
    log.debug("findAndDeletePreviousMsgs for {}", message);

    Map<String, Map<Long, String>> msgQueueMsgIds = findActiveMsgIdsNowForDevice(message, true);
    String queueid = makeQueueId(message);

    Map<Long, String> msgIDs = msgQueueMsgIds.get(queueid);
    if (msgIDs != null && msgIDs.size() > 0) {
      List<String> list = new ArrayList<>(msgIDs.values()); // to avoid concurrent change exception

      for (String msgId : list) {
        if (!msgId.equals(message.getMessageId())) {
          Message tmpMessage = findMessage(msgId);
          if (tmpMessage == null) {
            tmpMessage = new Message();
            tmpMessage.setVehicleID(message.getVehicleID());
            tmpMessage.getProperties().putAll(message.getProperties());
            tmpMessage.setMessageId(msgId);
            tmpMessage.setProperty(MetaData.SCHEDULE_QUEUE_ID, queueid);
          }

          tmpMessage.setProperty(MessageFields.status.name(), ActivityStatus.d.name());

          log.info("Removing message {} due to OVERRIDE by message {}", msgId, message.getMessageId());

          getPersister().updateStatusAndProps(tmpMessage);
          discardedDueToEnqueueType++;
          schedulerModuleMetricReporter.onDiscarded();
        }
      }

      flagQueueRemoveMsg(message);
    }
  }

  /** Finds message either in cache or in DB */
  private Message findMessage(String id) throws SQLException {
    return schedulerProcessor.findMessage(id);
  }

  private Transition findNextTransition(Message message) throws SQLException {
    Schedule schedule = fetchSchedule(message);

    String lastTransitionName = fetchLastTransitionName(message);
    Transition transition = getNextTransition(lastTransitionName, schedule, message);
    markWithLastTransitionName(message, lastTransitionName, transition);

    if (transition != null && transition.getRepeatFrom() != null) {
      log.debug("RepeatFrom detected: {} from msg: {}", transition, message);
      lastTransitionName = transition.getRepeatFrom().getName();
      message.setProperty(makeRepeatKey(transition.getRepeatFrom()), "0");
      transition = getNextTransition(lastTransitionName, schedule, message);
      markWithLastTransitionName(message, lastTransitionName, transition);
      log.debug("RepeatFrom: {}", transition);
    }

    return transition;
  }

  /** Used to control number of active message for a device, so not to breach the cap. */
  private void flagQueueRemoveMsg(Message message) throws SQLException {
    Map<String, Map<Long, String>> msgPerQueue = findActiveMsgIdsNowForDevice(message, false);
    Map<Long, String> msgInOrder = msgPerQueue != null ? msgPerQueue.get(makeQueueId(message)) : new HashMap<>();

    for (String msgId : msgInOrder.values()) {
      if (!msgId.equals(message.getMessageId())) {
        schedulerProcessor.removeMsgFromCache(msgId);
        removeFromCappedMsgCache(msgId, message);
      }
    }

    msgInOrder.clear();
  }

  /**
   * @param transitionName - name of last executed transition
   * @return null if all transitions done for Schedule
   */
  private Transition getNextTransition(String transitionName, Schedule schedule, Message message) throws SQLException {
    Transition retTransition = null;

    List<Transition> transitions = SchedulerCacheFactory.transitionCache().findBySchedule(schedule);
    validateSize(transitions.size(), 1, "Missing Transition configuration for Schedule", schedule);

    boolean connEstbl = connEstablishedCache.getIfPresent(message.getVehicleID()) != null;
    boolean tryAgain = true;
    boolean first = true;
    int laps = 0;

    while (tryAgain) {
      retTransition = transitionName == null
          ? transitions.get(0)
          : nextTransition(transitionName, transitions, first, connEstbl, message);
      first = false;
      laps++;

      if (retTransition != null) {
        // check max tries
        String maxTriesKey = makeMaxTriesKey(retTransition);
        int triesSoFar = getCounter(message, maxTriesKey);

        if (triesSoFar >= retTransition.getMaxNumberOfTriesPerMsg()) {
          log.info("Skipped Transition {} due to too many tries: {}, msg: {}", retTransition.getName(), triesSoFar, message.getMessageId());
          skippedTransitionsMaxTries++;
          transitionName = retTransition.getName();
          retTransition = null;
          tryAgain = laps < transitions.size();
          checkAllTransitionsUsed(message, transitions);
        } else {
          // check repeats
          int repeats = 0;
          String repeatKey = makeRepeatKey(retTransition);
          if (connEstablishedCache.getIfPresent(message.getVehicleID()) == null) {
            repeats = getCounter(message, repeatKey);
          }

          if (repeats > (retTransition.getTimeOutRepeat() - 1)) {
            log.info("Skipped Transition {} due to too many repeats: {}, message: {}", retTransition.getName(), repeats, message.getMessageId());
            skippedTransitionsRepeat++;
            transitionName = retTransition.getName();
            retTransition = null;
            schedulerModuleMetricReporter.onTooManyRepeats();
          } else {
            execTransitionsTimes++;
            message.setProperty(repeatKey, Integer.toString(++repeats));
            message.setProperty(maxTriesKey, Integer.toString(++triesSoFar));
            message.setProperty(repeatKey + "_exec", Util.getHumanTimeStamp() + "-" + ConfigFactory.getConfig().getComponentShortRuntimeName());

            tryAgain = false;
          }
        }
      } else {
        tryAgain = false;
      }
    }

    if (retTransition == null) {
      log.info("No usable transition found for msg: {}", message);
      message.setProperty("NoUsableTransitionFound", "true");
    }

    return retTransition;
  }

  /** Execute all actions for the next transition */
  private void handleActiveMsg(Message message) {
    log.debug("Start Handle active message: {}", message);

    schedulerProcessor.getAbstractDeviceService().refreshCommdata(message);

    Transition transition = null;
    try {
      transition = findNextTransition(message);

      if (transition != null) {
        doActionsForTransition(transition, message);
      } else if (message.getProperty(MessageFields.allTransitionsUsed.name()) != null) {
        noUsableTransition++;
        log.info("No usable transition found, will discontinue message {}", message.getMessageId());
        handleTimeOut(message);
      } else {
        log.debug("Msg sent to wait. {}", message.getMessageId());
        processedToWait++;
      }
    } catch (Exception e) {
      log.error("", e);
    } finally {
      addToLastMsgSentCache(message.getMessageId());
      Long sent = getLongProperty(message, MessageFields.sent.name(), 0L);
      sent++;
      message.setProperty(MessageFields.sent.name(), sent.toString());
      updateNextCheckTimeAndStatus(message, transition);

      log.debug("Finished Handled active msg: {}", message);
    }
  }

  private void handleCappedMsg(Message message) {
    try {
      schedulerModuleMetricReporter.onCapped();
      cappedMsgs++;
      updateNextCheckTimeAndStatus(message, null);
    } catch (Exception e) {
      log.error("", e);
    }
  }

  private void handleConnectionEstablished(Message message) throws SQLException {
    log.debug("ConnectionEstablished: {}", message.getVehicleID());

    connEstablishedCache.put(message.getVehicleID(), message.getVehicleID());

    // queueID, <createTime, msgId>
    Map<String, Map<Long, String>> msgMap = findActiveMsgIdsNowForDevice(message, true);
    List<Message> eligibleForConnEstSend = fetchActiveMsgs(msgMap, true);

    log.debug("Found eligibleForConnEstSend: {}", eligibleForConnEstSend.size());

    if (!eligibleForConnEstSend.isEmpty()) {
      getPersister().updateForConnectionEstablished(eligibleForConnEstSend);
    }

    connectionEstablishedMsg++;
  }

  /** Handle message that needs to be processed */
  private void handleMsg(Message message) throws SQLException {
    checkEnqueueing(message);

    boolean wasIgnored = false;
    if (isIgnoredType(message)) {
      log.debug("Ignored msg: {}", message);
      wasIgnored = clearOlderIgnoreMessages(message);
    }
    if (wasIgnored) {
      return;
    }
    if (checkActiveMsgCapExceeded(message)) {
      log.debug("Capped msg: {}", message);
      handleCappedMsg(message);
    } else {
      message.setProperty(MessageFields.status.name(), ActivityStatus.a.name());
      handleActiveMsg(message);
    }
  }

  /** Handle timeout message by sending notification up */
  private void handleTimeOut(Message message) throws SQLException {
    skippedTimeout++;

    log.trace("handleTimeOut of message: {}", message);

    String props = message.getProperties().toString();

    if (props.indexOf(MessageStatus.TIMEOUT.name()) < 0) {
      Message timeoutMsg = Message.createStatusMessage(MessageStatus.TIMEOUT);
      try {
        timeoutMsg.addProperties(message.getProperties());
        timeoutMsg.setMessageId(message.getMessageId());
        timeoutMsg.setPayload("".getBytes(StandardCharsets.UTF_8));
        timeoutMsg.setVehicleID(message.getVehicleID());
        log.trace("First time, send Notification: {}", timeoutMsg);

        ((SchedulerModule) schedulerProcessor.getModule()).handleAck(timeoutMsg);
      } catch (Exception e) {
        log.error("", e);
      } finally {
        timeoutMsg.setProperty(MessageFields.status.name(), ActivityStatus.d.name());
        getPersister().updateStatusAndProps(timeoutMsg);
      }
    } else {
      log.trace("Notification been sent before: {}", message);

      message.setProperty(MessageFields.status.name(), ActivityStatus.d.name());
      getPersister().updateStatusAndProps(message);
    }
  }

  private void logSize() {
    if (logCount % 10 == 0) {
      int entries = 0;
      for (Map<String, Map<Long, String>> maplvl1 : msgIdsPerDeviceQ.asMap().values()) {
        for (Map<Long, String> maplvl2 : maplvl1.values()) {
          entries += maplvl2.size();
        }
      }

      if (entries >= maxIdsInCache) {
        log.info("Max ID's in cache reached, clearing cache. {}", entries);
        msgIdsPerDeviceQ.invalidateAll();
      }

      schedulerModuleMetricReporter.onCacheMsgIds();
    }
  }

  private void removeFromCappedMsgCache(String msgId, Message message) throws SQLException {
    Queue<String> msgInQ = getCappedMsgList(message);

    if (msgInQ.contains(msgId)) {
      if (log.isDebugEnabled()) {
        log.debug("cappedMsgListCache have message to remove " + msgId + ", list: " + msgInQ);
      }
      msgInQ.remove(msgId);
      if (log.isDebugEnabled()) {
        log.debug("... after remove: " + msgId + ", list: " + msgInQ);
      }
    } else {
      if (log.isDebugEnabled()) {
        log.debug("cappedMsgListCache dont have message " + msgId + ", list: " + msgInQ);
      }
    }
  }

  private void sendNextInQ(Message message) throws SQLException {
    Message toSend = findNextActiveMsgsNotSentAllQ(message);
    if (toSend != null) {
      clearLastMsgCache(toSend.getMessageId());
      addToQueue(toSend);
    }
  }

  /** Stores message in processor cache and message ID in internal cache */
  private void storeMsgInCache(Message message) throws SQLException {
    Map<String, Map<Long, String>> queueMsgIds = findActiveMsgIdsNowForDevice(message, true);
    String queueId = makeQueueId(message);

    if (message.getMessageId() != null) {
      Map<Long, String> orderedIds = queueMsgIds.get(queueId);
      if (orderedIds == null) {
        orderedIds = new TreeMap<>();
        queueMsgIds.put(queueId, orderedIds);
      }

      if (!orderedIds.containsValue(message.getMessageId())) {
        orderedIds.put(getLongProperty(message, MessageFields.createTime.name(), System.currentTimeMillis()), message.getMessageId());
      }
      schedulerProcessor.addMsgToCache(message);
    }
  }

  /**
   * Updates message with status and nextCheckTime
   *
   * @param transition - if null, message waits for ack or timeout
   */
  private void updateNextCheckTimeAndStatus(Message message, Transition transition) {
    try {
      long nextCheckTime = Long.MAX_VALUE;

      if (transition != null) {
        message.setProperty(MessageFields.lastTransitionName.name(), transition.getName());
        nextCheckTime = System.currentTimeMillis() + transition.getTimeToLive();

        String sct = message.getProperty(MessageFields.createTime.name());
        long lct = (sct != null && sct.length() > 0) ? Long.valueOf(sct) : System.currentTimeMillis();
        Long removeTime = lct + transition.getSchedule().getTimeToLive();
        message.setProperty(MessageFields.removeTime.name(), removeTime.toString());

        storeMsgInCache(message);

        if (message.getProperty(MessageFields.status.name()).equals(ActivityStatus.a.name())) {
          getPersister().updateNextCheckTime(message, nextCheckTime, removeTime);
        } else {
          getPersister().updateStatusAndProps(message);
        }
      } else {
        storeMsgInCache(message);
        nextCheckTime = System.currentTimeMillis() + activeMsgCapWaitMs;
        getPersister().updateNextCheckTime(message, nextCheckTime, 0);
      }
    } catch (Exception e) {
      log.error("", e);
    }
  }
}
