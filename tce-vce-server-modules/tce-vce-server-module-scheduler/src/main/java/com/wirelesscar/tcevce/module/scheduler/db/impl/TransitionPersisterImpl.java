package com.wirelesscar.tcevce.module.scheduler.db.impl;

import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Collections;
import java.util.List;

import com.wirelesscar.tce.db.common.db.api.MessageFields;
import com.wirelesscar.tce.db.common.db.api.TableName;
import com.wirelesscar.tce.db.standard.sql.impl.AbstractSqlPersister;
import com.wirelesscar.tce.db.standard.sql.impl.IdGenerator;
import com.wirelesscar.tcevce.module.scheduler.api.Schedule;
import com.wirelesscar.tcevce.module.scheduler.api.Transition;
import com.wirelesscar.tcevce.module.scheduler.cache.api.SchedulerCacheFactory;
import com.wirelesscar.tcevce.module.scheduler.db.api.TransitionPersister;

public class TransitionPersisterImpl extends AbstractSqlPersister<Transition> implements TransitionPersister {
  private static final String INSERT_SQL_STRING = createInsertSql();
  private static final String SELECT_BY_SCHEDULE_ID_SQL_STRING = createSelectByScheduleIdSql();
  private static final String UPDATE_SQL_STRING = createUpdateSql();

  public TransitionPersisterImpl() {
    super(TableName.SCHD_TRANSITION.name());
  }

  private static String createInsertSql() {
    StringBuilder stringBuilder = new StringBuilder(500);

    stringBuilder.append("insert into ");
    stringBuilder.append(TableName.SCHD_TRANSITION);
    stringBuilder.append(" (");
    stringBuilder.append(MessageFields.id);
    stringBuilder.append(", ");
    stringBuilder.append(MessageFields.scheduleId);
    stringBuilder.append(", ");
    stringBuilder.append(MessageFields.name);
    stringBuilder.append(", ");
    stringBuilder.append(MessageFields.orderNumber);
    stringBuilder.append(", ");
    stringBuilder.append(MessageFields.timeToLive);
    stringBuilder.append(", ");
    stringBuilder.append(MessageFields.maxTriesPerMsg);
    stringBuilder.append(", ");
    stringBuilder.append(MessageFields.timeOutRepeat);
    stringBuilder.append(", ");
    stringBuilder.append(MessageFields.connectionEstablished);
    stringBuilder.append(", ");
    stringBuilder.append(MessageFields.repeatFrom);
    stringBuilder.append(") values (");
    stringBuilder.append("?");
    stringBuilder.append(", ?");
    stringBuilder.append(", ?");
    stringBuilder.append(", ?");
    stringBuilder.append(", ?");
    stringBuilder.append(", ?");
    stringBuilder.append(", ?");
    stringBuilder.append(", ?");
    stringBuilder.append(", ?");
    stringBuilder.append(")");

    return stringBuilder.toString();
  }

  private static String createSelectByScheduleIdSql() {
    StringBuilder stringBuilder = new StringBuilder(500);

    stringBuilder.append("select * from ");
    stringBuilder.append(TableName.SCHD_TRANSITION);

    stringBuilder.append(" where ");
    stringBuilder.append(MessageFields.scheduleId);
    stringBuilder.append(" = ? ");

    stringBuilder.append(" order by ");
    stringBuilder.append(MessageFields.orderNumber);

    return stringBuilder.toString();
  }

  private static String createUpdateSql() {
    StringBuilder stringBuilder = new StringBuilder(500);

    stringBuilder.append("update ");
    stringBuilder.append(TableName.SCHD_TRANSITION);
    stringBuilder.append(" set ");

    stringBuilder.append(MessageFields.scheduleId);
    stringBuilder.append("= ? ,");
    stringBuilder.append(MessageFields.name);
    stringBuilder.append("= ? ,");
    stringBuilder.append(MessageFields.orderNumber);
    stringBuilder.append("= ? ,");
    stringBuilder.append(MessageFields.timeToLive);
    stringBuilder.append("= ?, ");
    stringBuilder.append(MessageFields.maxTriesPerMsg);
    stringBuilder.append("= ?, ");
    stringBuilder.append(MessageFields.timeOutRepeat);
    stringBuilder.append("= ?, ");
    stringBuilder.append(MessageFields.connectionEstablished);
    stringBuilder.append("= ?, ");
    stringBuilder.append(MessageFields.repeatFrom);
    stringBuilder.append("= ? ");

    stringBuilder.append(" where ");
    stringBuilder.append(MessageFields.id);
    stringBuilder.append(" = ? ");

    return stringBuilder.toString();
  }

  @Override
  public List<Transition> findBySchedule(Schedule schedule) throws SQLException {
    List<Object> values = Collections.singletonList(schedule.getId());

    return find(SELECT_BY_SCHEDULE_ID_SQL_STRING, values);
  }

  @Override
  protected String getInsertSql() {
    return INSERT_SQL_STRING;
  }

  @Override
  protected String getUpdateSql() {
    return UPDATE_SQL_STRING;
  }

  @Override
  protected Transition insert(PreparedStatement preparedStatement, Transition transition) throws SQLException {
    transition.setId(IdGenerator.newId());

    Schedule schedule = transition.getSchedule();
    validateNotNull(schedule, "Missing mandatory field Schedule");

    int i = 1;
    preparedStatement.setString(i++, transition.getId());
    preparedStatement.setString(i++, schedule.getId());

    preparedStatement.setString(i++, transition.getName() != null ? transition.getName() : "");
    preparedStatement.setInt(i++, transition.getOrder());
    preparedStatement.setLong(i++, transition.getTimeToLive());
    preparedStatement.setInt(i++, transition.getMaxNumberOfTriesPerMsg());
    preparedStatement.setInt(i++, transition.getTimeOutRepeat());
    preparedStatement.setBoolean(i++, transition.getConnEstablishedAllowed());
    preparedStatement.setString(i++, transition.getRepeatFrom() != null ? transition.getRepeatFrom().getId() : null);

    preparedStatement.executeUpdate();

    return transition;
  }

  @Override
  protected Transition populateObject(ResultSet resultSet) throws SQLException {
    Transition transition = new Transition();

    transition.setId(resultSet.getString(MessageFields.id.name()));

    String schId = resultSet.getString(MessageFields.scheduleId.name());
    transition.setSchedule(SchedulerCacheFactory.scheduleCache().get(schId, true));
    transition.setName(resultSet.getString(MessageFields.name.name()));
    transition.setOrder(resultSet.getInt(MessageFields.orderNumber.name()));
    transition.setTimeToLive(resultSet.getLong(MessageFields.timeToLive.name()));
    transition.setMaxNumberOfTriesPerMsg(resultSet.getInt(MessageFields.maxTriesPerMsg.name()));
    transition.setTimeOutRepeat(resultSet.getInt(MessageFields.timeOutRepeat.name()));
    transition.setConnEstablishedAllowed(resultSet.getBoolean(MessageFields.connectionEstablished.name()));

    String repeatFromId = resultSet.getString(MessageFields.repeatFrom.name());
    if (repeatFromId != null && repeatFromId.length() > 0) {
      transition.setRepeatFrom(SchedulerCacheFactory.transitionCache().get(repeatFromId, true));
    }

    return transition;
  }

  @Override
  protected Transition update(PreparedStatement preparedStatement, Transition transition) throws SQLException {
    int i = 1;
    preparedStatement.setString(i++, transition.getSchedule().getId());
    preparedStatement.setString(i++, transition.getName());
    preparedStatement.setInt(i++, transition.getOrder());
    preparedStatement.setLong(i++, transition.getTimeToLive());
    preparedStatement.setInt(i++, transition.getMaxNumberOfTriesPerMsg());
    preparedStatement.setInt(i++, transition.getTimeOutRepeat());
    preparedStatement.setBoolean(i++, transition.getConnEstablishedAllowed());
    preparedStatement.setString(i++, transition.getRepeatFrom() != null ? transition.getRepeatFrom().getId() : null);

    // where values
    preparedStatement.setString(i++, transition.getId());

    preparedStatement.executeUpdate();

    return transition;
  }
}
