package com.wirelesscar.tcevce.module.scheduler.api;

import com.wirelesscar.tce.db.common.api.CacheableObject;
import com.wirelesscar.tce.db.common.db.api.CacheableObjectType;

public class Transition extends CacheableObject {
  private static final int DEFAULT_MAX_NUMBER_OF_RETRIES_PER_MESSAGE = 999;
  private static final int DEFAULT_TIME_OUT_REPEATS = 1;
  private static final long DEFAULT_TTL_IN_MS = 1_000 * 60 * 60L;

  /** If startover from first Transition for ConnectionEstablished event */
  private boolean connEstablishedAllowed;

  /** max number of tries for a message by this transition, regardless everything else */
  private int maxNumberOfTriesPerMsg = DEFAULT_MAX_NUMBER_OF_RETRIES_PER_MESSAGE;

  /** Name of transition */
  private String name;

  /** Order in which they are executed */
  private int order;

  /** Transition to start over from, if MT not acked from vehicle */
  private Transition repeatFrom;

  /** Parent schedule that transition belongs to */
  private Schedule schedule;

  /** How many times to repeat this transition before moving on to next */
  private int timeOutRepeat = DEFAULT_TIME_OUT_REPEATS;

  /** Timeout in milliseconds on transition. Used to calculate NextCheckTime on message. */
  private long timeToLive = DEFAULT_TTL_IN_MS;

  /**
   * Returns the OK to go to for ConnectionEstablished event
   *
   * @return boolean to go to for ConnectionEstablished event
   */
  public boolean getConnEstablishedAllowed() {
    return connEstablishedAllowed;
  }

  /**
   * Returns max number of tries for a message by this transition, regardless of everything else.
   *
   * <p>
   * Default value if not set is {@value #DEFAULT_MAX_NUMBER_OF_RETRIES_PER_MESSAGE}.
   *
   * @return max number of tries for a message by this transition, regardless of everything else.
   */
  public int getMaxNumberOfTriesPerMsg() {
    return maxNumberOfTriesPerMsg;
  }

  /**
   * @return name of transition
   */
  public String getName() {
    return name;
  }

  @Override
  public CacheableObjectType getObjectType() {
    return CacheableObjectType.Transition;
  }

  /**
   * @return the order in which this transition is executed in the schema.
   */
  public int getOrder() {
    return order;
  }

  /** Transition to startover from. Null value (default) mean use next in order instead. */
  public Transition getRepeatFrom() {
    return repeatFrom;
  }

  /**
   * @return the schedule that this transition belongs to.
   */
  public Schedule getSchedule() {
    return schedule;
  }

  /**
   * @return how many times to repeat this transition before moving on to next
   */
  public int getTimeOutRepeat() {
    return timeOutRepeat;
  }

  /**
   * @return timeout (in milliseconds) for this transition.
   */
  public long getTimeToLive() {
    return timeToLive;
  }

  /**
   * Sets the ok/not to go to for ConnectionEstablished event
   *
   * @param connEstablishedAllowed the transition to go to for ConnectionEstablished event
   */
  public void setConnEstablishedAllowed(boolean connEstablishedAllowed) {
    this.connEstablishedAllowed = connEstablishedAllowed;
  }

  /**
   * Sets max number of tries for a message by this transition, regardless of everything else.
   *
   * @param maxNumberOfTriesPerMsg max number of tries for a message by this transition, regardless of everything else.
   */
  public void setMaxNumberOfTriesPerMsg(int maxNumberOfTriesPerMsg) {
    this.maxNumberOfTriesPerMsg = maxNumberOfTriesPerMsg;
  }

  /**
   * Sets a name for the transition.
   *
   * @param name name of transition
   */
  public void setName(String name) {
    this.name = name;
  }

  /**
   * Sets the order in which this transition is executed in the schema.
   *
   * @param order the order in which this transition is executed in the schema.
   */
  public void setOrder(int order) {
    this.order = order;
  }

  /** Set transition to startover from */
  public void setRepeatFrom(Transition repeatFrom) {
    this.repeatFrom = repeatFrom;
  }

  /**
   * @param schedule the schedule that this transition belongs to.
   */
  public void setSchedule(Schedule schedule) {
    this.schedule = schedule;
  }

  /**
   * Sets how many times to repeat this transition before moving on to next.
   *
   * <p>
   * Default value if not set is {@value #DEFAULT_TIME_OUT_REPEATS}.
   *
   * @param timeOutRepeat how many times to repeat this transition before moving on to next
   */
  public void setTimeOutRepeat(int timeOutRepeat) {
    this.timeOutRepeat = timeOutRepeat;
  }

  /**
   * Sets timeout for this transition.
   *
   * <p>
   * Default value if not set is {@value #DEFAULT_TTL_IN_MS}.
   *
   * @param timeToLive timeout (in milliseconds) for this transition.
   */
  public void setTimeToLive(long timeToLive) {
    this.timeToLive = timeToLive;
  }

  @Override
  public String toString() {
    StringBuilder stringBuilder = new StringBuilder();
    stringBuilder.append(getObjectType().name());

    stringBuilder.append(" id:");
    stringBuilder.append(getId());

    stringBuilder.append(", name:");
    stringBuilder.append(getName());

    stringBuilder.append(", order:");
    stringBuilder.append(getOrder());

    stringBuilder.append(", TimeOutRepeat:");
    stringBuilder.append(getTimeOutRepeat());

    stringBuilder.append(", ConnEstablish:");
    stringBuilder.append(getConnEstablishedAllowed());

    stringBuilder.append(", maxNumberOfTriesPerMsg:");
    stringBuilder.append(getMaxNumberOfTriesPerMsg());

    stringBuilder.append(", RepeatFrom:");
    stringBuilder.append(getRepeatFrom() != null ? getRepeatFrom() : "null");

    stringBuilder.append(", TTL:");
    stringBuilder.append(getTimeToLive());

    stringBuilder.append(", ");
    stringBuilder.append(getSchedule() != null ? getSchedule().toString() : " Schedule not set. ");

    return stringBuilder.toString();
  }
}
