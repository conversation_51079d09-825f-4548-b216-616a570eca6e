package com.wirelesscar.tcevce.module.scheduler.db.api;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.wirelesscar.tce.db.common.db.api.MessagePersister;
import com.wirelesscar.tce.db.standard.sql.impl.SqlPersistanceFactory;
import com.wirelesscar.tce.db.standard.sql.impl.migration.DB_UPGRADES;
import com.wirelesscar.tcevce.module.scheduler.db.impl.ActionPersisterImpl;
import com.wirelesscar.tcevce.module.scheduler.db.impl.SchedulePersisterImpl;
import com.wirelesscar.tcevce.module.scheduler.db.impl.ScheduledMessagePersisterImpl;
import com.wirelesscar.tcevce.module.scheduler.db.impl.TransitionPersisterImpl;
import com.wirelesscar.tcevce.module.scheduler.db.impl.upgrade.TransitionUpgrade;

/** Factory for all persistence */
public final class SchedulePersistanceFactory {
  private static final Logger logger = LoggerFactory.getLogger(SchedulePersistanceFactory.class);

  static {
    SqlPersistanceFactory.getConnection();
    if (SqlPersistanceFactory.getModuleSpecificUpgrades()
        .contains(DB_UPGRADES.TRANSITION_UPGRADE)) {
      try {
        logger.info("Initiating DB upgrade: {}", DB_UPGRADES.TRANSITION_UPGRADE);
        final TransitionUpgrade transitionUpgrade = new TransitionUpgrade();
        transitionUpgrade.upgrade();
        logger.info("Finished DB upgrade: {}", DB_UPGRADES.TRANSITION_UPGRADE);
      } catch (final Exception e) {
        logger.error("Fatal error upgrading DB", e);
      }
    }
  }

  private SchedulePersistanceFactory() {}

  public static ActionPersister makeActionPersister() {
    return new ActionPersisterImpl();
  }

  public static MessagePersister makeMessagePersister() {
    return new ScheduledMessagePersisterImpl();
  }

  public static SchedulePersister makeSchedulePersister() {
    return new SchedulePersisterImpl();
  }

  public static TransitionPersister makeTransitionPersister() {
    return new TransitionPersisterImpl();
  }
}
