package com.wirelesscar.tcevce.module.scheduler.api;

import java.util.concurrent.TimeUnit;

import com.wirelesscar.tce.db.common.api.ActivityStatus;
import com.wirelesscar.tce.db.common.api.CacheableObject;
import com.wirelesscar.tce.db.common.db.api.CacheableObjectType;

public class Schedule extends CacheableObject {
  private static final long DEFAULT_TTL_IN_MS = TimeUnit.DAYS.toMillis(7);

  private String name;
  private ActivityStatus state = ActivityStatus.a;
  private long timeToLive = DEFAULT_TTL_IN_MS;

  /**
   * @return unique name for the schedule
   */
  public String getName() {
    return name;
  }

  @Override
  public CacheableObjectType getObjectType() {
    return CacheableObjectType.Schedule;
  }

  /**
   * Gets activity state for schedule.
   *
   * @return {@link ActivityStatus#a} if active; {@link ActivityStatus#d} if deleted
   */
  public ActivityStatus getState() {
    return state;
  }

  /**
   * @return time in milliseconds until message is removed if not delivered
   */
  public long getTimeToLive() {
    return timeToLive;
  }

  /**
   * Sets a unique name for the schedule
   *
   * @param name unique name
   */
  public void setName(String name) {
    this.name = name;
  }

  /**
   * Sets activity state for schedule.
   *
   * @param state {@link ActivityStatus#a} if active; {@link ActivityStatus#d} if deleted
   */
  public void setState(ActivityStatus state) {
    this.state = state;
  }

  /**
   * Sets time until message is removed if not delivered.
   *
   * @param timeToLive TTL in milliseconds
   */
  public void setTimeToLive(long timeToLive) {
    this.timeToLive = timeToLive;
  }

  @Override
  public String toString() {
    StringBuilder stringBuilder = new StringBuilder();
    stringBuilder.append(getObjectType().name());

    stringBuilder.append(" id:");
    stringBuilder.append(getId());

    stringBuilder.append(", name:");
    stringBuilder.append(getName());

    stringBuilder.append(", TTL:");
    stringBuilder.append(getTimeToLive());

    stringBuilder.append(", Status:");
    stringBuilder.append(getState().name());

    return stringBuilder.toString();
  }
}
