package com.wirelesscar.tcevce.module.scheduler.metrics;

import java.time.Duration;

import org.springframework.stereotype.Component;

import com.volvo.tisp.vc.main.utils.lib.Validate;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;

@Component
public class SchedulerModuleMetricReporter {
  static final String METRIC_CACHE_CONN_ESTBL = "module.scheduler.worker.cache-conn-estbl-size";
  static final String METRIC_CACHE_MSG_IDS = "module.scheduler.worker.cache-msg-ids";
  static final String METRIC_CACHE_SIZE = "module.scheduler.msg_cache_size";
  static final String METRIC_CANCELLED = "module.scheduler.ack-cancelled";
  static final String METRIC_CAPPED = "module.scheduler.worker.capped";
  static final String METRIC_CONNECTION_ESTABLISHED = "module.scheduler.worker.connEstbl";
  static final String METRIC_DELIVERED = "module.scheduler.ack-delivered";
  static final String METRIC_DISCARDED = "module.scheduler.worker.discarded";
  static final String METRIC_DOWN = "module.scheduler.worker.down";
  static final String METRIC_DROPPING_JUST_HANDLED = "module.scheduler.worker.dropping-just-handled";
  static final String METRIC_DROPPING_JUST_HANDLED_DELIVERED = "module.scheduler.worker.dropping-just-handledDelivered";
  static final String METRIC_DROPPING_NOT_MINE = "module.scheduler.worker.dropping-not-mine";
  static final String METRIC_SEND_DOWN = "module.scheduler.down";
  static final String METRIC_SMS = "module.scheduler.mt-sms";
  static final String METRIC_TIMEOUT = "module.scheduler.ack-timeout";
  static final String METRIC_TOO_MANY_REPEATS = "module.scheduler.worker.to_many_repeats";
  static final String METRIC_UDP = "module.scheduler.mt-udp";
  static final String SIZE_VAR_NAME = "size";
  static final String STACK_VAR_NAME = "stackName";
  private static final String DURATION_VAR_NAME = "duration";

  private final Counter cacheMsgIdsCounter;
  private final Counter cacheSizeCounter;
  private final Counter cappedCounter;
  private final Counter connectionEstablishedCounter;
  private final Timer connectionEstablishedTimer;
  private final Counter discardedCounter;
  private final Counter downCounter;
  private final Counter droppingJustHandledCounter;
  private final Counter droppingJustHandledDeliveredCounter;
  private final Counter droppingNotMineCounter;
  private final MeterRegistry meterRegistry;
  private final Counter tooManyRepeatsCounter;

  public SchedulerModuleMetricReporter(MeterRegistry meterRegistry) {
    this.meterRegistry = meterRegistry;

    cacheMsgIdsCounter = meterRegistry.counter(METRIC_CACHE_MSG_IDS);
    cacheSizeCounter = meterRegistry.counter(METRIC_CACHE_SIZE);
    cappedCounter = meterRegistry.counter(METRIC_CAPPED);
    connectionEstablishedCounter = meterRegistry.counter(METRIC_CACHE_CONN_ESTBL);
    connectionEstablishedTimer = meterRegistry.timer(METRIC_CONNECTION_ESTABLISHED);
    discardedCounter = meterRegistry.counter(METRIC_DISCARDED);
    downCounter = meterRegistry.counter(METRIC_DOWN);
    droppingJustHandledCounter = meterRegistry.counter(METRIC_DROPPING_JUST_HANDLED);
    droppingJustHandledDeliveredCounter = meterRegistry.counter(METRIC_DROPPING_JUST_HANDLED_DELIVERED);
    droppingNotMineCounter = meterRegistry.counter(METRIC_DROPPING_NOT_MINE);
    tooManyRepeatsCounter = meterRegistry.counter(METRIC_TOO_MANY_REPEATS);
  }

  public void logCacheSize(long size) {
    Validate.notNegative(size, SIZE_VAR_NAME);

    cacheSizeCounter.increment(size);
  }

  public void logConnectionEstablished(Duration duration) {
    Validate.notNegative(duration, DURATION_VAR_NAME);

    connectionEstablishedTimer.record(duration);
  }

  public void logSendDown(String stackName, Duration duration) {
    Validate.notEmpty(stackName, STACK_VAR_NAME);
    Validate.notNegative(duration, DURATION_VAR_NAME);

    meterRegistry.timer(METRIC_SEND_DOWN, STACK_VAR_NAME, stackName).record(duration);
  }

  public void onCacheConnectionEstablished(long size) {
    Validate.notNegative(size, SIZE_VAR_NAME);

    connectionEstablishedCounter.increment(size);
  }

  public void onCacheMsgIds() {
    cacheMsgIdsCounter.increment();
  }

  public void onCanceled(String stackName) {
    Validate.notEmpty(stackName, STACK_VAR_NAME);

    meterRegistry.counter(METRIC_CANCELLED, STACK_VAR_NAME, stackName).increment();
  }

  public void onCapped() {
    cappedCounter.increment();
  }

  public void onDelivered(String stackName) {
    Validate.notEmpty(stackName, STACK_VAR_NAME);

    meterRegistry.counter(METRIC_DELIVERED, STACK_VAR_NAME, stackName).increment();
  }

  public void onDiscarded() {
    discardedCounter.increment();
  }

  public void onDown() {
    downCounter.increment();
  }

  public void onDroppingJustHandled() {
    droppingJustHandledCounter.increment();
  }

  public void onDroppingJustHandledDelivered() {
    droppingJustHandledDeliveredCounter.increment();
  }

  public void onDroppingNotMine() {
    droppingNotMineCounter.increment();
  }

  public void onSms(String stackName) {
    Validate.notEmpty(stackName, STACK_VAR_NAME);

    meterRegistry.counter(METRIC_SMS, STACK_VAR_NAME, stackName).increment();
  }

  public void onTimeout(String stackName) {
    Validate.notEmpty(stackName, STACK_VAR_NAME);

    meterRegistry.counter(METRIC_TIMEOUT, STACK_VAR_NAME, stackName).increment();
  }

  public void onTooManyRepeats() {
    tooManyRepeatsCounter.increment();
  }

  public void onUdp(String stackName) {
    Validate.notEmpty(stackName, STACK_VAR_NAME);

    meterRegistry.counter(METRIC_UDP, STACK_VAR_NAME, stackName).increment();
  }
}
