package com.wirelesscar.tcevce.module.scheduler.db.api;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.wirelesscar.tce.module.api.Message;
import com.wirelesscar.tcevce.module.scheduler.SchedulerModule;

public final class ExternalAction {
  private static final Logger log = LoggerFactory.getLogger(ExternalAction.class);

  private ExternalAction() {
    throw new UnsupportedOperationException();
  }

  public static void execute(Message message, SchedulerModule schedulerModule) {
    log.debug("Sending message down: {}", message);
    schedulerModule.processDown(message);
  }
}
