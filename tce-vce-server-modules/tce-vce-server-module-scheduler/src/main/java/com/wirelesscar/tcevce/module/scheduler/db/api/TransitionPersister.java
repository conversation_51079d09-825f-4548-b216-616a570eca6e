package com.wirelesscar.tcevce.module.scheduler.db.api;

import java.sql.SQLException;
import java.util.List;

import com.wirelesscar.tce.db.common.db.api.BasicPersister;
import com.wirelesscar.tcevce.module.scheduler.api.Schedule;
import com.wirelesscar.tcevce.module.scheduler.api.Transition;

public interface TransitionPersister extends BasicPersister<Transition> {
  /** Returns all entries connected to parent */
  List<Transition> findBySchedule(Schedule schedule) throws SQLException;
}
