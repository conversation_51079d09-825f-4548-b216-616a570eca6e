package com.wirelesscar.tcevce.module.scheduler.cache;

import java.sql.SQLException;
import java.util.List;
import java.util.concurrent.TimeUnit;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.wirelesscar.config.Config;
import com.wirelesscar.config.ConfigFactory;
import com.wirelesscar.tcevce.module.scheduler.api.Action;
import com.wirelesscar.tcevce.module.scheduler.api.Transition;
import com.wirelesscar.tcevce.module.scheduler.cache.api.ActionCache;
import com.wirelesscar.tcevce.module.scheduler.db.api.ActionPersister;
import com.wirelesscar.tcevce.module.scheduler.db.api.SchedulePersistanceFactory;

public final class ActionCacheImpl implements ActionCache {
  private static final ActionCache INSTANCE = new ActionCacheImpl();

  private Cache<String, Action> byId;
  private Cache<String, List<Action>> byTransitionId;
  private int maxCacheMinutesUsed = MAX_RETAIN_MINUTES;
  private int maxCacheSizeUsed = MAX_SIZE;

  private ActionCacheImpl() {
    createCaches();
  }

  public static ActionCache getInstance() {
    return INSTANCE;
  }

  private static ActionPersister getPersister() {
    return SchedulePersistanceFactory.makeActionPersister();
  }

  @Override
  public int cacheRetainMinutes() {
    return maxCacheMinutesUsed;
  }

  @Override
  public int cacheSizeMax() {
    return maxCacheSizeUsed;
  }

  @Override
  public void clear() {
    byId.invalidateAll();
    byTransitionId.invalidateAll();

    createCaches();
  }

  @Override
  public List<Action> findByTransition(Transition transition) throws SQLException {
    List<Action> actions = byTransitionId.getIfPresent(transition.getId());

    if (actions == null) {
      actions = getPersister().findByTransition(transition);
      byTransitionId.put(transition.getId(), actions);

      for (Action action : actions) {
        byId.put(action.getId(), action);
      }
    }

    return actions;
  }

  @Override
  public Action get(String id, boolean fetchFromDbIfNeeded) throws SQLException {
    Action action = byId.getIfPresent(id);

    if (action == null && fetchFromDbIfNeeded) {
      action = getPersister().lookup(id);
      byId.put(id, action);
    }

    return action;
  }

  @Override
  public long getCacheSize() {
    return byId.size();
  }

  @Override
  public void remove(Action action) {
    byId.invalidate(action.getId());
    byTransitionId.invalidate(action.getTransition().getId());
  }

  private void createCaches() {
    Config config = ConfigFactory.getConfig();
    int maxMinutes = config.getInt("scheduler.module.cache.minutes").orElse(MAX_RETAIN_MINUTES);
    int size = config.getInt("scheduler.module.cache.size").orElse(MAX_SIZE);

    byId = CacheBuilder.newBuilder()
        .concurrencyLevel(10)
        .maximumSize(size)
        .expireAfterWrite(maxMinutes, TimeUnit.MINUTES)
        .build();

    byTransitionId = CacheBuilder.newBuilder()
        .concurrencyLevel(10)
        .maximumSize(size)
        .expireAfterWrite(maxMinutes, TimeUnit.MINUTES)
        .build();

    maxCacheMinutesUsed = maxMinutes;
    maxCacheSizeUsed = size;
  }
}
