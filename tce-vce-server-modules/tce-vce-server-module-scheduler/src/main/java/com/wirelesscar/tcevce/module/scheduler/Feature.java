package com.wirelesscar.tcevce.module.scheduler;

import com.wirelesscar.config.Config;
import com.wirelesscar.config.ConfigFactory;

/**
 * Feature toogles for TCE.
 *
 * <p>
 * Usage: in env.properties
 *
 * <pre>
 * &lt;mycompshortname&gt;.FEATURE_TEST_CONFIGURED_TO_DISABLED=false
 * &lt;mycompshortname&gt;.FEATURE_TEST_CONFIGURED_TO_ENABLED=true
 * </pre>
 */
public enum Feature {
  /**
   * When a vehicle sends eg an ack update the next check time for the active messages in the mt queue in the database.
   */
  FEATURE_MT_UPDATE_NEXTCHECKTIME_WHEN_VEHICLE_COMMUNICATES;

  private static final Config config = ConfigFactory.getConfig();

  public boolean isActive() {
    return config.getBoolean(this.name()).orElse(false);
  }
}
