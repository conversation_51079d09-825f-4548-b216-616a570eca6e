package com.wirelesscar.tcevce.module.scheduler.cache;

import java.sql.SQLException;
import java.util.concurrent.TimeUnit;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.wirelesscar.tce.db.common.db.api.CacheUpdatePersister;
import com.wirelesscar.tce.db.standard.sql.impl.SqlCacheUpdatePersisterImpl;
import com.wirelesscar.tcevce.module.scheduler.CacheUpdater;

/**
 * Continually cleans CacheUpdate table. Only run on one instance per cluster, controlled via configuration
 */
public class CacheTableCleaner implements Runnable {
  private static final Logger log = LoggerFactory.getLogger(CacheUpdater.class);

  private final boolean activeCacheCleaner = true; // get from config
  private final long pauseMs = 30_000;
  private boolean run;
  private long totalDeleted;

  @Override
  public void run() {
    log.info("CacheTableCleaner start");

    CacheUpdatePersister cacheUpdatePersister = new SqlCacheUpdatePersisterImpl();
    processEvents(cacheUpdatePersister);
  }

  /** permanently stops this thread */
  public void stopMe() {
    this.run = false;
  }

  /** Total deleted cache entries since start of JVM */
  public long totalDeleted() {
    return this.totalDeleted;
  }

  /**
   * Should get result from dynamic config to enable shared environment. Only one active per cluster.
   */
  private boolean isActiveCacheCleaner() {
    return this.activeCacheCleaner;
  }

  /**
   * Continually deletes old cache update entries in table. Sleeps in between for <pauseMs> millis.
   */
  private void processEvents(CacheUpdatePersister cacheUpdatePersister) {
    this.run = true;

    while (this.run) {
      try {
        runOnce(cacheUpdatePersister);

        TimeUnit.MILLISECONDS.sleep(this.pauseMs);
      } catch (final Exception e) {
        log.error("", e);
      }
    }
  }

  private void runOnce(CacheUpdatePersister cacheUpdatePersister) throws SQLException {
    if (isActiveCacheCleaner()) {
      final long deleted = cacheUpdatePersister.deleteOldEntries();
      this.totalDeleted += deleted;
    }
  }
}
