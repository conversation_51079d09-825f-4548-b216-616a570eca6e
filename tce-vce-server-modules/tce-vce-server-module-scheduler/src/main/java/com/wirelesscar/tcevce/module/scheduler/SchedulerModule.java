package com.wirelesscar.tcevce.module.scheduler;

import java.nio.charset.StandardCharsets;
import java.sql.SQLException;
import java.time.Duration;
import java.time.Instant;
import java.util.HashMap;
import java.util.HexFormat;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.wirelesscar.componentbase.logging.Logging.Direction;
import com.wirelesscar.componentbase.logging.Logging.Status;
import com.wirelesscar.config.Config;
import com.wirelesscar.config.ConfigFactory;
import com.wirelesscar.tce.core.conf.TceConfig;
import com.wirelesscar.tce.core.event.Event;
import com.wirelesscar.tce.core.event.EventListener;
import com.wirelesscar.tce.core.event.EventService;
import com.wirelesscar.tce.core.event.SynchronousEventListener;
import com.wirelesscar.tce.core.event.Type;
import com.wirelesscar.tce.core.event.events.AckEvent;
import com.wirelesscar.tce.core.event.events.ConnectionEstablishedEvent;
import com.wirelesscar.tce.db.common.AbstractProcessor;
import com.wirelesscar.tce.db.common.api.ActivityStatus;
import com.wirelesscar.tce.db.common.db.api.MessageFields;
import com.wirelesscar.tce.db.common.db.api.MessagePersister;
import com.wirelesscar.tce.db.common.metrics.ProcessorMetricsReporter;
import com.wirelesscar.tce.db.standard.sql.impl.IdGenerator;
import com.wirelesscar.tce.device.AbstractDeviceService;
import com.wirelesscar.tce.module.api.Message;
import com.wirelesscar.tce.module.api.MessageStatus;
import com.wirelesscar.tce.module.api.MetaData;
import com.wirelesscar.tce.module.api.ModuleBase;
import com.wirelesscar.tce.module.api.ModuleTypeConstants;
import com.wirelesscar.tce.module.api.StackDirection;
import com.wirelesscar.tce.module.api.TransportType;
import com.wirelesscar.tce.module.api.exception.EngineRuntimeException;
import com.wirelesscar.tce.module.metrics.ModuleMetricReporter;
import com.wirelesscar.tce.utils.ClusterConfig;
import com.wirelesscar.tce.utils.LoggingHelper;
import com.wirelesscar.tce.utils.MetadataUtils;
import com.wirelesscar.tce.utils.Util;
import com.wirelesscar.tcevce.module.scheduler.api.AdditionalMetaData;
import com.wirelesscar.tcevce.module.scheduler.db.api.SchedulePersistanceFactory;
import com.wirelesscar.tcevce.module.scheduler.metrics.SchedulerModuleMetricReporter;
import com.wirelesscar.tcevce.module.segmentation.encryption.VceEncryptionHandler;

@Component(ModuleTypeConstants.SCHEDULER)
@Scope("prototype")
public class SchedulerModule extends ModuleBase {
  private static final String ACK = "ack";
  private static final String CONNECTION_ESTABLISHED = "connection.established";
  private static final String DELETE_AFTER_PUBLISH = "delete-after-publish";
  private static final String HASH_KEY = "hash.key";
  private static final String HASH_KEY_SECONDARY = "hash.key-secondary";
  private static final String HASH_KEY_TERTIARY = "hash.key-tertiary";
  private static final String INPROCESS_DOWN = "inprocessdown";
  private static final String INSTACK_DOWN = "instackdown";
  private static final String INTEGRATION_LOGGING_USE_TRANSPORT_TYPE = "integration-logging.use.transport-type";
  private static final String METADATA = "metadata";
  private static final String PERSIST_FOR_DOWN = "persistfordown";
  private static final String READER_PROCESS_START = "readprocess.start";
  private static final String STACKNAME_STATUS_MSG = "stackname-status-msg";
  private static final Logger log = LoggerFactory.getLogger(SchedulerModule.class);
  private final AbstractDeviceService abstractDeviceService;
  private CacheUpdater cacheUpdater;

  @TceConfig(configKey = DELETE_AFTER_PUBLISH, defaultValue = "false")
  private boolean deleteAfterPublish;

  private EventListener eventListener;
  private final EventService eventService;

  @TceConfig(configKey = HASH_KEY, defaultValue = "IP_DST_ADDRESS")
  private MetaData hashKey;

  @TceConfig(configKey = HASH_KEY_SECONDARY, defaultValue = "SMPP_DEST_ADDRESS")
  private MetaData hashKeyBaseSecondary;

  @TceConfig(configKey = HASH_KEY_TERTIARY, defaultValue = "SATELLITE_DEST_ADDRESS")
  private MetaData hashKeyBaseTertiary;

  @TceConfig(configKey = INPROCESS_DOWN, defaultValue = "true")
  private boolean inProcessDown = true;

  @TceConfig(configKey = INSTACK_DOWN, defaultValue = "true")
  private boolean inStackDown = true;

  @TceConfig(configKey = INTEGRATION_LOGGING_USE_TRANSPORT_TYPE, defaultValue = "false")
  private boolean integrationLoggingUseTransportType;

  private final byte[] noPayload = "NO_PAYLOAD".getBytes(StandardCharsets.UTF_8);
  private final Cache<Thread, MessagePersister> persistCache = CacheBuilder.newBuilder().maximumSize(1_000).expireAfterAccess(30, TimeUnit.SECONDS).build();

  @TceConfig(configKey = PERSIST_FOR_DOWN, defaultValue = "true")
  private boolean persistForDown = true;

  private SchedulerProcessor processor;
  private final ProcessorMetricsReporter processorMetricsReporter;
  private MetaData[] requiredMetadata;

  @TceConfig(configKey = METADATA, defaultValue = "MESSAGE_ID;SERVICE_ID;SCHEDULE_NAME")
  private String requiredMetadataStr;

  private final SchedulerModuleMetricReporter schedulerModuleMetricReporter;
  private final SchedulerRegistry schedulerRegistry;
  private Thread spThread;

  @TceConfig(configKey = STACKNAME_STATUS_MSG, defaultValue = "")
  private String stackNameStatusMsg;

  @TceConfig(configKey = READER_PROCESS_START, defaultValue = "true")
  private boolean startReadProcess = true;

  private SynchronousEventListener syncedEventListener;

  @TceConfig(configKey = ACK, defaultValue = "false")
  private boolean useAck;

  @TceConfig(configKey = CONNECTION_ESTABLISHED, defaultValue = "false")
  private boolean useConnectionEstablished;

  private final VceEncryptionHandler vceEncryptionHandler;

  public SchedulerModule(EventService eventService, SchedulerRegistry schedulerRegistry, AbstractDeviceService abstractDeviceService,
      ProcessorMetricsReporter processorMetricsReporter, SchedulerModuleMetricReporter schedulerModuleMetricReporter,
      ModuleMetricReporter moduleMetricReporter, VceEncryptionHandler vceEncryptionHandler) {
    super(moduleMetricReporter);
    this.eventService = eventService;
    this.schedulerRegistry = schedulerRegistry;
    this.abstractDeviceService = abstractDeviceService;
    this.processorMetricsReporter = processorMetricsReporter;
    this.schedulerModuleMetricReporter = schedulerModuleMetricReporter;
    this.vceEncryptionHandler = vceEncryptionHandler;
  }

  private static void logIntegrationAck(Message message, Status status) {
    LoggingHelper.integrationLogging(message, null, status, Direction.CLIENT_OUT, MetaData.MT.name() + "/" + message.getStatus().name(),
        LoggingHelper.createExtraMetaDataBuilder()
            .putIfNotNull(LoggingHelper.ExtraMetaData.KEYS.mobileDirection, MetaData.MT.name())
            .putIfNotNull(LoggingHelper.ExtraMetaData.KEYS.serviceId, message.getProperty(MetaData.SERVICE_ID))
            .putIfNotNull(LoggingHelper.ExtraMetaData.KEYS.scheduleName, message.getProperty(MetaData.SCHEDULE_NAME))
            .getAsOptional());
  }

  private static void logIntegrationPersist(Message message, Status status, String desc) {
    LoggingHelper.integrationLogging(message, null, status, Direction.CLIENT_OUT, MetaData.MT.name() + desc, LoggingHelper.createExtraMetaDataBuilder()
        .putIfNotNull(LoggingHelper.ExtraMetaData.KEYS.mobileDirection, "MT")
        .putIfNotNull(LoggingHelper.ExtraMetaData.KEYS.serviceId, message.getProperty(MetaData.SERVICE_ID))
        .putIfNotNull(LoggingHelper.ExtraMetaData.KEYS.scheduleName, message.getProperty(MetaData.SCHEDULE_NAME))
        .getAsOptional());
  }

  private static boolean msgIsAck(Message message) {
    return message.isStatus() || message.getProperty(MetaData.ACK) != null;
  }

  /**
   * contains MetaData.MT in props
   */
  private static boolean msgIsMT(Message message) {
    return message.getProperty(MetaData.MT) != null;
  }

  @Override
  public void deploy() {
    requiredMetadata = MetadataUtils.parseMetaData(requiredMetadataStr);

    final String stackName = getStackName();
    final String name = getName();
    log.info("{}.{} config startReadProcess: {}", stackName, name, startReadProcess);
    log.info("{}.{} config persistForDown: {}", stackName, name, persistForDown);
    log.info("{}.{} config inProcessDown: {}", stackName, name, inProcessDown);
    log.info("{}.{} config inStackDown: {}", stackName, name, inStackDown);
    log.info("{}.{} config useConnectionEstablished: {}", stackName, name, useConnectionEstablished);
    log.info("{}.{} config useAck: {}", stackName, name, useAck);
    log.info("{}.{} config deleteAfterPublish: {}", stackName, name, deleteAfterPublish);
    log.info("{}.{} config HashKey: {}", stackName, name, hashKey);
    log.info("{}.{} config HashKeyBaseSecondary: {}", stackName, name, hashKeyBaseSecondary);
    log.info("{}.{} config stackNameStatusMsg: {}", stackName, name, stackNameStatusMsg);
  }

  @Override
  public void down(Message message) {
    validateBeforeDown(message);

    byte[] payload = Optional.ofNullable(message.getPayload()).orElse(new byte[0]);
    log.debug("stackname={}, message={}, string_payload={}, hex_payload={}", getStackName(), message, new String(payload),
        HexFormat.of().withDelimiter(" ").withUpperCase().formatHex(payload));

    Instant startTime = Instant.now();

    try {
      if (msgIsMT(message)) {
        setHashKeyBase(message);

        if (persistForDown) {
          persistMessage(message);
        }

        if (inProcessDown) {
          getProcessor().addToQueue(message);
        } else if (inStackDown
            && ClusterConfig.getInstance()
            .isMyMessage(message, hashKey.name(), hashKeyBaseSecondary.name(), hashKeyBaseTertiary.name())) {
          sendDown(message);
        }
      } else {
        handleMO(message);
      }
    } catch (SQLException e) {
      throw new RuntimeException(e);
    }

    Duration duration = Duration.between(startTime, Instant.now());
    schedulerModuleMetricReporter.logSendDown(getStackName(), duration);
  }

  public AbstractDeviceService getAbstractDeviceService() {
    return abstractDeviceService;
  }

  public String getHashKeyBaseTertiary() {
    return hashKeyBaseTertiary != null
        ? hashKeyBaseTertiary.name()
        : MetaData.SATELLITE_DEST_ADDRESS.name();
  }

  public String getHashkey() {
    return hashKey != null ? hashKey.name() : MetaData.IP_DST_ADDRESS.name();
  }

  public String getHashkeyBaseSecondary() {
    return hashKeyBaseSecondary != null
        ? hashKeyBaseSecondary.name()
        : MetaData.IP_DST_ADDRESS.name();
  }

  public SchedulerProcessor getProcessor() {
    if (processor == null) {
      // if called by down() first, avoid to create several instances of SchedulerProcessor .
      synchronized (this) {
        if (processor == null) {
          processor = new SchedulerProcessor(this, processorMetricsReporter, schedulerModuleMetricReporter);
          schedulerRegistry.register(processor);
        }
      }
    }
    return processor;
  }

  /**
   * Updates message in DB with new status
   */
  public void handleAck(Message message) throws SQLException {
    String upd = (message.isStatus() ? message.getStatus().name() : MetaData.ACK.name())
        + " arrived at "
        + Util.getHumanTimeStamp();
    log.trace("Ack arrived: {}: {}", upd, message);

    ActivityStatus state = null;
    MessageStatus msgStatus = null;
    switch (message.getStatus()) {
      case CANCELED:
        schedulerModuleMetricReporter.onCanceled(getStackName());
        logIntegrationAck(message, Status.FAILED);
        msgStatus = MessageStatus.CANCELED;
        state = ActivityStatus.a;
        // WTP failed. Trigger next transition.
        Message originalMessage = getProcessor().findMessage(message.getMessageId());
        if (originalMessage != null) {
          getPersister().updateNextCheckTimeOnly(originalMessage, System.currentTimeMillis());
        }
        log.trace("CANCELED ACK: {}: {}: {}", upd, message, message.getProperty(MetaData.STATUS_CANCELED_REASON));
        break;

      case DELIVERED:
        schedulerModuleMetricReporter.onDelivered(getStackName());
        logIntegrationAck(message, Status.SUCCESS);
        msgStatus = MessageStatus.DELIVERED;
        log.trace("DELIVERED ACK: {}: {}", upd, message);
        break;

      case TIMEOUT:
        logIntegrationAck(message, Status.FAILED);
        schedulerModuleMetricReporter.onTimeout(getStackName());
        msgStatus = MessageStatus.TIMEOUT;
        log.trace("TIMEOUT ACK: {}: {}", upd, message);
        break;

      case REJECTED:
        logIntegrationAck(message, Status.FAILED);
        schedulerModuleMetricReporter.onTimeout(getStackName());
        msgStatus = MessageStatus.REJECTED;
        log.trace("ABORTED ACK: {}: {}", upd, message);
        break;

      case ACCEPTED:
      case NONE:
      case THROTTLED:
      default:
        log.trace("Default ACK: {}: {}", upd, message);
        state = ActivityStatus.a;
        break;
    }

    // if state change on msg, update it.
    if (state == null || !state.equals(ActivityStatus.a)) {
      String prop = message.getProperty(MetaData.ACK);
      prop = prop != null ? prop + " " + upd : upd;
      message.setProperty(MetaData.ACK, prop);
      message.getProperties().remove(MetaData.IP_SRC_ADDRESS, null);
      if (deleteAfterPublish) {
        getPersister().delete(message);

        Map<String, String> properties = message.getProperties();
        for (Entry<String, String> property : properties.entrySet()) {
          MDC.put(property.getKey(), property.getValue());
        }

        MDC.put(MessageFields.messageID.name(), message.getMessageId());
        MDC.put(MessageFields.vehicleID.name(), message.getVehicleID());

        log.info("MtMessage");
        MDC.clear();
      } else {
        getPersister().updateForAck(message, state);
      }
      if (Feature.FEATURE_MT_UPDATE_NEXTCHECKTIME_WHEN_VEHICLE_COMMUNICATES.isActive()) {
        if (message.getStatus() == MessageStatus.DELIVERED) {
          checkMessgesForVehicleNow(message.getVehicleID());
        }
      }
      upd = upd + sendNotification(msgStatus, message);
      if (message.getVehicleID() != null) {
        log.debug("About to flag message finished in module {}", message.getMessageId());
        SchedulerProcessorThread worker = getProcessor().getProcessorThreadForDevice(message.getVehicleID());
        if (worker != null) {
          worker.flagMsgFinished(message);
        }
      } else {
        String errorMessage = processor == null ? "No processor present" : "No vehicleID set on message";
        log.debug("{}, can not flag message finished in module.", errorMessage);
      }
    } else {
      log.debug("Ack received but no action configured for {}", message);
    }
  }

  public void processDown(Message message) {
    // TISP - to be changed to also use MetaData.TRANSPORT_TYPE instead of MetaData.STACK_ROUTING
    String channel = message.getProperty(MetaData.STACK_ROUTING);

    // VCE
    if (channel == null || integrationLoggingUseTransportType) {
      channel = message.getProperty(MetaData.TRANSPORT_TYPE);
    }
    if (channel == null) {
      // TODO, UDP is configured in default stack routing. This has the potential to be fixed
      // better.
      channel = TransportType.UDP.name();
    }

    if (channel.equals(TransportType.SMS.name())) {
      schedulerModuleMetricReporter.onSms(getStackName());
    } else {
      schedulerModuleMetricReporter.onUdp(getStackName());
    }

    //This property should be set as an action in the database
    boolean checkEncryption = Optional.ofNullable(message.getProperty(AdditionalMetaData.CHECK_ENCRYPTION.name())).map(Boolean::parseBoolean).orElse(false);

    if (channel.equals(TransportType.SMS.name()) && checkEncryption) {
      vceEncryptionHandler.handleEncryption(message, true);
      message.getProperties().remove(AdditionalMetaData.CHECK_ENCRYPTION.name());
    }

    LoggingHelper.integrationLogging(message, null, Status.SUCCESS, Direction.CLIENT_OUT, MetaData.MT.name() + "/Send/" + channel,
        LoggingHelper.createExtraMetaDataBuilder()
            .putIfNotNull(LoggingHelper.ExtraMetaData.KEYS.mobileDirection, MetaData.MT.name())
            .putIfNotNull(LoggingHelper.ExtraMetaData.KEYS.serviceId, message.getProperty(MetaData.SERVICE_ID))
            .putIfNotNull(LoggingHelper.ExtraMetaData.KEYS.stackName, channel)
            .putIfNotNull(LoggingHelper.ExtraMetaData.KEYS.scheduleName, message.getProperty(MetaData.SCHEDULE_NAME))
            .getAsOptional());

    sendDown(message);
  }

  @Override
  public void start() {
    log.info("Starting {}", getClass().getName());

    if (cacheUpdater == null) {
      cacheUpdater = new CacheUpdater();
      Thread thread = new Thread(cacheUpdater);
      thread.start();
    }

    if (startReadProcess) {
      processor = getProcessor();
      if (spThread == null) {
        spThread = new Thread(processor);
        spThread.start();
      }
    }

    if (useConnectionEstablished && eventListener == null) {
      registerConnectionEstablishedListener();
    }

    if (useAck && syncedEventListener == null) {
      registerAckListener();
    }
  }

  @Override
  public void stop() {
    log.info("Stopping {}", getClass().getName());

    if (cacheUpdater != null) {
      cacheUpdater.stopMe();
    }

    if (processor != null) {
      processor.stopMe();
      schedulerRegistry.unRegister(processor);
      processor = null;
      spThread = null;
    }

    if (eventListener != null) {
      eventService.unregister(eventListener);
    }
  }

  @Override
  public void up(Message message) {
    sendUp(message);
  }

  @Override
  protected void setOldModuleConfig(Map<String, String> oldConfigProperties) {
    final String stackName = getStackName();
    Config config = ConfigFactory.getConfig();
    oldConfigProperties.put(METADATA, config
        .getString(stackName + ".scheduler.module." + METADATA)
        .orElse("MESSAGE_ID;SERVICE_ID;SCHEDULE_NAME"));
    oldConfigProperties.put(READER_PROCESS_START, config.getString(stackName + ".scheduler.module." + READER_PROCESS_START).orElse("true"));
    oldConfigProperties.put(PERSIST_FOR_DOWN, config.getString(stackName + ".scheduler.module." + PERSIST_FOR_DOWN).orElse("true"));
    oldConfigProperties.put(INPROCESS_DOWN, config.getString(stackName + ".scheduler.module." + INPROCESS_DOWN).orElse("true"));
    oldConfigProperties.put(INSTACK_DOWN, config.getString(stackName + ".scheduler.module." + INSTACK_DOWN).orElse("true"));
    oldConfigProperties.put(CONNECTION_ESTABLISHED, config
        .getString(stackName + ".scheduler.module." + CONNECTION_ESTABLISHED)
        .orElse("false"));
    oldConfigProperties.put(ACK, config.getString(stackName + ".scheduler.module." + ACK).orElse("false"));
    oldConfigProperties.put(DELETE_AFTER_PUBLISH, config.getString(stackName + ".scheduler.module." + DELETE_AFTER_PUBLISH).orElse("false"));
    oldConfigProperties.put(STACKNAME_STATUS_MSG, config.getString(stackName + ".scheduler.module." + STACKNAME_STATUS_MSG).orElse(""));
    oldConfigProperties.put(HASH_KEY, config.getString("scheduler.module." + HASH_KEY).orElse(MetaData.IP_DST_ADDRESS.name()));
    oldConfigProperties.put(HASH_KEY_SECONDARY, config
        .getString(stackName + ".scheduler.module." + HASH_KEY_SECONDARY)
        .orElse(MetaData.SMPP_DEST_ADDRESS.name()));

    oldConfigProperties.put(AbstractProcessor.NUMBER_OF_THREADS, config
        .getString(stackName + ".scheduler." + AbstractProcessor.NUMBER_OF_THREADS)
        .orElse("10"));
    oldConfigProperties.put(AbstractProcessor.PROCESSOR_QUEUESIZE_MAX, config
        .getString(stackName + ".scheduler." + AbstractProcessor.PROCESSOR_QUEUESIZE_MAX)
        .orElse("20000"));
    oldConfigProperties.put(AbstractProcessor.PROCESSOR_KEY_MAXAGE_MS, config
        .getString(stackName + ".scheduler." + AbstractProcessor.PROCESSOR_KEY_MAXAGE_MS)
        .orElse("25000"));
    oldConfigProperties.put(SchedulerProcessor.PROCESSOR_CACHE_MSG_MINUTES, config
        .getString(stackName + ".scheduler." + SchedulerProcessor.PROCESSOR_CACHE_MSG_MINUTES)
        .orElse("1440"));
    oldConfigProperties.put(SchedulerProcessor.PROCESSOR_CACHE_MSG_MAX, config
        .getString(stackName + ".scheduler." + SchedulerProcessor.PROCESSOR_CACHE_MSG_MAX)
        .orElse("1440"));
    oldConfigProperties.put(AbstractProcessor.NEXT_CHECKTIME_ADD_MS, config
        .getString(stackName + ".scheduler." + AbstractProcessor.NEXT_CHECKTIME_ADD_MS)
        .orElse("10800000"));
  }

  /**
   * Update next check time for all active messages to this vehicle so they will be picked up now regardless of which server handles them.
   */
  private void checkMessgesForVehicleNow(String vehicleid) throws SQLException {
    getPersister().updateNextCheckTime(vehicleid, System.currentTimeMillis());
    log.debug("Next check time updated for vehicle with id: {}", vehicleid);
  }

  private MessagePersister getPersister() {
    MessagePersister messagePersister = persistCache.getIfPresent(Thread.currentThread());

    if (messagePersister == null) {
      messagePersister = SchedulePersistanceFactory.makeMessagePersister();
      persistCache.put(Thread.currentThread(), messagePersister);
      log.debug("Made MessagePerister for {}", Thread.currentThread().getId());
    }

    return messagePersister;
  }

  /**
   * Checks if message is "Connection Established" or "Ack" and initiates next process
   */
  private void handleMO(Message message) throws SQLException {
    if (msgIsAck(message)) {
      handleAck(message);
    }
  }

  private Message persistMessage(Message message) {
    try {
      message.getProperties().remove(MetaData.IP_SRC_ADDRESS.name(), null);
      Message returnMessage = getPersister().persist(message, inProcessDown);
      String scheduleName = message.getProperty(MetaData.SCHEDULE_NAME);
      logIntegrationPersist(message, Status.SUCCESS, "/" + scheduleName);
      return returnMessage;
    } catch (Exception e) {
      logIntegrationPersist(message, Status.FAILED, "/Failed to persist");
      throw new EngineRuntimeException("Error persisting message " + message, e);
    }
  }

  private void registerAckListener() {
    syncedEventListener = event -> {
      if (event.getType().equals(Type.ACK)) {
        try {
          handleAck(((AckEvent) event).getAckMessage());
        } catch (SQLException e) {
          throw new RuntimeException("Db error handleAck", e);
        }
      }
    };

    eventService.registerAsListener(syncedEventListener, Type.ACK);
  }

  private void registerConnectionEstablishedListener() {
    eventListener = new EventListener() {
      @Override
      public int getQueueDepth() {
        return 0;
      }

      @Override
      public void handleEvent(Event event) {
        if (event.getType().equals(Type.CONNECTION_ESTABLISHED)) {
          ConnectionEstablishedEvent ceEvent = (ConnectionEstablishedEvent) event;
          Message message = new Message(IdGenerator.newId(), noPayload);
          message.setVehicleID(ceEvent.getPlatformVehicleIdentifier());
          message.setProperty(Type.CONNECTION_ESTABLISHED.name(), Type.CONNECTION_ESTABLISHED.name());

          log.trace("Recieved ConnectionEstablished for {}", message.getVehicleID());

          getProcessor().addToQueue(message);
        }
      }
    };

    eventService.registerAsListener(eventListener, Type.CONNECTION_ESTABLISHED);
  }

  private String sendNotification(MessageStatus status, Message statusMessage) throws SQLException {
    String addAsProp = "";
    if (statusMessage.getProperty(MetaData.JMS_REPLY_TO) != null) {
      log.debug("About to send status message for: {}", statusMessage.getMessageId());

      Message orig = getProcessor().findMessage(statusMessage.getMessageId());
      if (orig != null) {
        log.debug("Found orig msg: {}", orig);
        Map<String, String> props = new HashMap<>(orig.getProperties());
        props.putAll(statusMessage.getProperties());
        statusMessage.getProperties().putAll(props);
      } else {
        log.warn("Did not find original message for StatusMessage: {}", statusMessage);
      }

      statusMessage.setProperty(MetaData.STACK_ROUTING_DIRECTION, StackDirection.UP.name());
      statusMessage.setProperty(MetaData.STACK_ROUTING, stackNameStatusMsg);
      sendDown(statusMessage);
      addAsProp = " Status (" + status.name() + ") message sent upwards at " + Util.getHumanTimeStamp();

      log.debug("Routing MT status message: {}, based on message: {}", statusMessage, orig);
    } else {
      log.debug("Will not send status message up, no JMS_REPLY_TO: {}, based on message: {}", statusMessage, statusMessage);
    }

    return addAsProp;
  }

  private void setHashKeyBase(Message message) {
    if (message.getProperty(hashKey.name()) != null) {
      message.setProperty(MessageFields.hashKey.name(), message.getProperty(hashKey.name()));
    } else if (message.getProperty(hashKeyBaseSecondary.name()) != null) {
      message.setProperty(MessageFields.hashKey.name(), message.getProperty(hashKeyBaseSecondary.name()));
    } else if (message.getProperty(hashKeyBaseTertiary.name()) != null) {
      message.setProperty(MessageFields.hashKey.name(), message.getProperty(hashKeyBaseTertiary.name()));
    } else {
      log.warn("could not find a valid properties to set hashKey, and the hashkey set to vehicleID");
      message.setProperty(MessageFields.hashKey.name(), message.getVehicleID());
    }
  }

  private void validateBeforeDown(Message message) {
    if (getPersister() == null) {
      throw new IllegalStateException("No persister in place, can not handle message: " + message);
    }

    if (message.getVehicleID() == null) {
      throw new IllegalStateException("Message has no vehicleID");
    }

    MetadataUtils.validateRequiredMetaData(message, requiredMetadata);
  }
}
