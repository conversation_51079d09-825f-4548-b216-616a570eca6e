package com.wirelesscar.tcevce.module.scheduler.db.impl;

import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Collections;
import java.util.List;

import com.wirelesscar.tce.db.common.api.ActivityStatus;
import com.wirelesscar.tce.db.common.db.api.MessageFields;
import com.wirelesscar.tce.db.common.db.api.TableName;
import com.wirelesscar.tce.db.standard.sql.impl.AbstractSqlPersister;
import com.wirelesscar.tce.db.standard.sql.impl.IdGenerator;
import com.wirelesscar.tcevce.module.scheduler.api.Schedule;
import com.wirelesscar.tcevce.module.scheduler.db.api.SchedulePersister;

public class SchedulePersisterImpl extends AbstractSqlPersister<Schedule> implements SchedulePersister {
  private static final String INSERT_SQL_STRING = createInsertSql();
  private static final String SELECT_BY_NAME_SQL_STRING = createSelectByNameSql();
  private static final String UPDATE_SQL_STRING = createUpdateSql();

  public SchedulePersisterImpl() {
    super(TableName.SCHD_SCHEDULE.name());
  }

  private static String createInsertSql() {
    StringBuilder stringBuilder = new StringBuilder(500);

    stringBuilder.append("insert into ");
    stringBuilder.append(TableName.SCHD_SCHEDULE);
    stringBuilder.append(" (");
    stringBuilder.append(MessageFields.id);
    stringBuilder.append(", ");
    stringBuilder.append(MessageFields.name);
    stringBuilder.append(", ");
    stringBuilder.append(MessageFields.status);
    stringBuilder.append(", ");
    stringBuilder.append(MessageFields.timeToLive);
    stringBuilder.append(") values (");
    stringBuilder.append("?");
    stringBuilder.append(", ?");
    stringBuilder.append(", ?");
    stringBuilder.append(", ?");
    stringBuilder.append(")");

    return stringBuilder.toString();
  }

  private static String createSelectByNameSql() {
    StringBuilder stringBuilder = new StringBuilder(500);

    stringBuilder.append("select * from ");
    stringBuilder.append(TableName.SCHD_SCHEDULE);

    stringBuilder.append(" where ");
    stringBuilder.append(MessageFields.name);
    stringBuilder.append(" = ? ");

    return stringBuilder.toString();
  }

  private static String createUpdateSql() {
    StringBuilder stringBuilder = new StringBuilder(500);

    stringBuilder.append("update ");
    stringBuilder.append(TableName.SCHD_SCHEDULE);
    stringBuilder.append(" set ");

    stringBuilder.append(MessageFields.name);
    stringBuilder.append("= ? ,");
    stringBuilder.append(MessageFields.status);
    stringBuilder.append("= ? ,");
    stringBuilder.append(MessageFields.timeToLive);
    stringBuilder.append("= ? ");

    stringBuilder.append(" where ");
    stringBuilder.append(MessageFields.id);
    stringBuilder.append(" = ? ");

    return stringBuilder.toString();
  }

  @Override
  public Schedule lookupByName(String name) throws SQLException {
    List<Object> values = Collections.singletonList(name);

    List<Schedule> schedules = find(SELECT_BY_NAME_SQL_STRING, values);

    if (schedules.size() > 1) {
      throw new SQLException("More than one result found for query by name: " + name);
    }

    return schedules.isEmpty() ? null : schedules.get(0);
  }

  @Override
  protected String getInsertSql() {
    return INSERT_SQL_STRING;
  }

  @Override
  protected String getUpdateSql() {
    return UPDATE_SQL_STRING;
  }

  @Override
  protected Schedule insert(PreparedStatement preparedStatement, Schedule schedule) throws SQLException {
    schedule.setId(IdGenerator.newId());
    String status = schedule.getState() != null ? schedule.getState().name() : ActivityStatus.a.name();

    int i = 1;
    preparedStatement.setString(i++, schedule.getId());
    preparedStatement.setString(i++, schedule.getName() != null ? schedule.getName() : "");
    preparedStatement.setString(i++, status);
    preparedStatement.setLong(i++, schedule.getTimeToLive());

    preparedStatement.executeUpdate();

    return schedule;
  }

  @Override
  protected Schedule populateObject(ResultSet resultSet) throws SQLException {
    Schedule schedule = new Schedule();

    schedule.setId(resultSet.getString(MessageFields.id.name()));
    schedule.setName(resultSet.getString(MessageFields.name.name()));
    schedule.setState(ActivityStatus.valueOf(resultSet.getString(MessageFields.status.name())));
    schedule.setTimeToLive(resultSet.getLong(MessageFields.timeToLive.name()));

    return schedule;
  }

  @Override
  protected Schedule update(PreparedStatement preparedStatement, Schedule schedule) throws SQLException {
    String status = schedule.getState() != null ? schedule.getState().name() : ActivityStatus.a.name();

    int i = 1;
    preparedStatement.setString(i++, schedule.getName() != null ? schedule.getName() : "");
    preparedStatement.setString(i++, status);
    preparedStatement.setLong(i++, schedule.getTimeToLive());

    // where values
    preparedStatement.setString(i++, schedule.getId());

    preparedStatement.executeUpdate();

    return schedule;
  }
}
