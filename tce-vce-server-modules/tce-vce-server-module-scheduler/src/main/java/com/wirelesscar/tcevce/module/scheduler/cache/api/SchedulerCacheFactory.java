package com.wirelesscar.tcevce.module.scheduler.cache.api;

import com.wirelesscar.tcevce.module.scheduler.cache.ActionCacheImpl;
import com.wirelesscar.tcevce.module.scheduler.cache.ScheduleCacheImpl;
import com.wirelesscar.tcevce.module.scheduler.cache.TransitionCacheImpl;

public final class SchedulerCacheFactory {
  private SchedulerCacheFactory() {
    throw new IllegalStateException();
  }

  public static ActionCache actionCache() {
    return ActionCacheImpl.getInstance();
  }

  public static ScheduleCache scheduleCache() {
    return ScheduleCacheImpl.getInstance();
  }

  public static TransitionCache transitionCache() {
    return TransitionCacheImpl.getInstance();
  }
}
