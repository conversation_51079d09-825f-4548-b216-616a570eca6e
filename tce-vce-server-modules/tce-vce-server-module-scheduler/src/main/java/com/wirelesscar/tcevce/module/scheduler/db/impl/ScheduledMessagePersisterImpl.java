package com.wirelesscar.tcevce.module.scheduler.db.impl;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.wirelesscar.tce.db.common.db.api.TableName;
import com.wirelesscar.tce.db.standard.sql.impl.AbstractSqlMessagePersister;

public class ScheduledMessagePersisterImpl extends AbstractSqlMessagePersister {
  private static final Logger log = LoggerFactory.getLogger(ScheduledMessagePersisterImpl.class);

  public ScheduledMessagePersisterImpl() {
    super(TableName.SCHD_SCHEDULED_MESSAGE.name());
  }

  @Override
  protected String getHintIndxedName() {
    return "IX_SCHMSSG_NXT_HSH_STS_LOCAL";
  }

  @Override
  protected Logger getLogger() {
    return log;
  }
}
