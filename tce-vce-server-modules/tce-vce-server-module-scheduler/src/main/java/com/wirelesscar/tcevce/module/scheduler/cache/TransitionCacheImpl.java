package com.wirelesscar.tcevce.module.scheduler.cache;

import java.sql.SQLException;
import java.util.List;
import java.util.concurrent.TimeUnit;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.wirelesscar.config.Config;
import com.wirelesscar.config.ConfigFactory;
import com.wirelesscar.tcevce.module.scheduler.api.Schedule;
import com.wirelesscar.tcevce.module.scheduler.api.Transition;
import com.wirelesscar.tcevce.module.scheduler.cache.api.TransitionCache;
import com.wirelesscar.tcevce.module.scheduler.db.api.SchedulePersistanceFactory;
import com.wirelesscar.tcevce.module.scheduler.db.api.TransitionPersister;

public final class TransitionCacheImpl implements TransitionCache {
  private static final TransitionCache INSTANCE = new TransitionCacheImpl();

  private Cache<String, Transition> byId;
  private Cache<String, List<Transition>> byScheduleId;
  private int maxCacheMinutesUsed = MAX_RETAIN_MINUTES;
  private int maxCacheSizeUsed = MAX_SIZE;

  private TransitionCacheImpl() {
    createCaches();
  }

  public static TransitionCache getInstance() {
    return INSTANCE;
  }

  private static TransitionPersister getPersister() {
    return SchedulePersistanceFactory.makeTransitionPersister();
  }

  @Override
  public int cacheRetainMinutes() {
    return maxCacheMinutesUsed;
  }

  @Override
  public int cacheSizeMax() {
    return maxCacheSizeUsed;
  }

  @Override
  public void clear() {
    byId.invalidateAll();
    byScheduleId.invalidateAll();

    createCaches();
  }

  @Override
  public List<Transition> findBySchedule(Schedule schedule) throws SQLException {
    List<Transition> transitions = byScheduleId.getIfPresent(schedule.getId());

    if (transitions == null) {
      transitions = getPersister().findBySchedule(schedule);
      byScheduleId.put(schedule.getId(), transitions);

      for (Transition t : transitions) {
        byId.put(t.getId(), t);
      }
    }

    return transitions;
  }

  @Override
  public Transition get(String id, boolean fetchFromDbIfNeeded) throws SQLException {
    Transition transition = null;
    if (id != null) {
      transition = byId.getIfPresent(id);

      if (transition == null && fetchFromDbIfNeeded) {
        transition = getPersister().lookup(id);
        byId.put(id, transition);
      }
    }

    return transition;
  }

  @Override
  public long getCacheSize() {
    return byId.size();
  }

  @Override
  public void remove(Transition transition) {
    byId.invalidate(transition.getId());
    byScheduleId.invalidate(transition.getSchedule().getId());
  }

  private void createCaches() {
    Config config = ConfigFactory.getConfig();
    int maxMinutes = config.getInt("scheduler.module.cache.minutes").orElse(MAX_RETAIN_MINUTES);
    int size = config.getInt("scheduler.module.cache.size").orElse(MAX_SIZE);

    byId = CacheBuilder.newBuilder()
        .concurrencyLevel(10)
        .maximumSize(size)
        .expireAfterWrite(maxMinutes, TimeUnit.MINUTES)
        .build();

    byScheduleId = CacheBuilder.newBuilder()
        .concurrencyLevel(10)
        .maximumSize(size)
        .expireAfterWrite(maxMinutes, TimeUnit.MINUTES)
        .build();

    maxCacheMinutesUsed = maxMinutes;
    maxCacheSizeUsed = size;
  }
}
