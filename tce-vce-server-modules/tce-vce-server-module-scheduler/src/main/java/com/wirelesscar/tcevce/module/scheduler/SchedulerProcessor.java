package com.wirelesscar.tcevce.module.scheduler;

import java.sql.SQLException;
import java.util.List;
import java.util.concurrent.TimeUnit;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.wirelesscar.config.exception.ConfigurationException;
import com.wirelesscar.tce.core.conf.TceConfig;
import com.wirelesscar.tce.core.conf.TceConfigException;
import com.wirelesscar.tce.core.conf.TceConfigParser;
import com.wirelesscar.tce.db.common.AbstractProcessor;
import com.wirelesscar.tce.db.common.db.api.MessagePersister;
import com.wirelesscar.tce.db.common.metrics.ProcessorMetricsReporter;
import com.wirelesscar.tce.device.AbstractDeviceService;
import com.wirelesscar.tce.module.api.Message;
import com.wirelesscar.tce.module.api.exception.EngineRuntimeException;
import com.wirelesscar.tcevce.module.scheduler.db.api.SchedulePersistanceFactory;
import com.wirelesscar.tcevce.module.scheduler.metrics.SchedulerModuleMetricReporter;

public class SchedulerProcessor extends AbstractProcessor<SchedulerProcessorThread> {
  static final String PROCESSOR_CACHE_MSG_MAX = "processor.cache.msg.max";
  static final String PROCESSOR_CACHE_MSG_MINUTES = "processor.cache.msg.minutes";
  private static int instanceNumber;
  private static final Logger log = LoggerFactory.getLogger(SchedulerProcessor.class);

  private final AbstractDeviceService abstractDeviceService;
  private final Cache<String, Message> cachedMsgs;

  @TceConfig(configKey = PROCESSOR_CACHE_MSG_MAX, defaultValue = "20000")
  private int maxCacheMsg = 20_000;

  @TceConfig(configKey = PROCESSOR_CACHE_MSG_MINUTES, defaultValue = "10")
  private int minutesCacheMsg = 10;

  private final Cache<Thread, MessagePersister> persistCache = CacheBuilder.newBuilder().maximumSize(1_000).expireAfterAccess(30, TimeUnit.SECONDS).build();
  private final SchedulerModuleMetricReporter schedulerModuleMetricReporter;
  private int threads;

  public SchedulerProcessor(SchedulerModule schedulerModule, ProcessorMetricsReporter processorMetricsReporter,
      SchedulerModuleMetricReporter schedulerModuleMetricReporter) {
    super(schedulerModule, "SchedulerProcessor", processorMetricsReporter, true);
    Validate.notNull(schedulerModuleMetricReporter, "schedulerModuleMetricReporter");

    abstractDeviceService = schedulerModule.getAbstractDeviceService();
    this.schedulerModuleMetricReporter = schedulerModuleMetricReporter;

    ++instanceNumber;
    log.info("Creating SchedulerProcessor {}", instanceNumber);

    // Call the configuration Parser to set @TceConfig annotated values
    try {
      TceConfigParser.setConfig(this, schedulerModule.getActiveConfigProperties());
    } catch (IllegalArgumentException | TceConfigException | ConfigurationException e) {
      throw new EngineRuntimeException("Bad Configuration", e);
    }

    log.info("Config numberOfThreads: {}", numberOfThreads);
    log.info("Config maxQSize Processor: {}", maxQSize);
    log.info("Config maxQSize per worker thread: {}", (maxQSize / 10));
    log.info("Config maxAge ms: {}", maxAge);
    log.info("Config minFetchSize: {}", minFetchSize);
    log.info("Config minutesCacheMsg: {}", minutesCacheMsg);
    log.info("Config maxCacheMsg: {}", maxCacheMsg);
    log.info("Config nextChecktimeFails: {}", nextChecktimeFails);

    cachedMsgs = CacheBuilder.newBuilder()
        .maximumSize(maxCacheMsg)
        .expireAfterAccess(minutesCacheMsg, TimeUnit.MINUTES)
        .build();

    createUsedIdsCache();
  }

  /**
   * adds message to timed and sized cache. Used for fast retrieval for status updates to app layer
   */
  public void addMsgToCache(Message message) {
    if (message != null) {
      cachedMsgs.put(message.getMessageId(), message);
    }
  }

  public void clearCache() {
    log.info("Clearing cache: cachedMsgs.");
    cachedMsgs.invalidateAll();

    for (SchedulerProcessorThread worker : processorList()) {
      worker.clearCaches();
    }
  }

  /** Finds message either in cache or in DB */
  public Message findMessage(String id) throws SQLException {
    Message message = findCachedMsg(id);
    if (message == null) {
      message = getMessagePersister().lookup(id);
      if (message != null) {
        addMsgToCache(message);
      }
    }
    return message;
  }

  public AbstractDeviceService getAbstractDeviceService() {
    return abstractDeviceService;
  }

  /** returns the designated messageProcessorThread for that device */
  public SchedulerProcessorThread getProcessorThreadForDevice(String deviceID) {
    return getProcessor(deviceID);
  }

  /**
   * removes message from timed and sized cache ahead of auto removal. Use when a message is e.g delivered or cancelled.
   */
  public void removeMsgFromCache(String msgId) {
    cachedMsgs.invalidate(msgId);
  }

  @Override
  protected MessagePersister getMessagePersister() {
    MessagePersister messagePersister = persistCache.getIfPresent(Thread.currentThread());

    if (messagePersister == null) {
      messagePersister = SchedulePersistanceFactory.makeMessagePersister();
      persistCache.put(Thread.currentThread(), messagePersister);
      log.debug("Made MessagePerister for " + Thread.currentThread().getId());
    }

    return messagePersister;
  }

  @Override
  protected List<Message> getNextBatch(boolean useOrderBy) throws SQLException {
    schedulerModuleMetricReporter.logCacheSize(cachedMsgs.size());
    return super.getNextBatch(useOrderBy);
  }

  @Override
  protected SchedulerProcessorThread makeNewProcessorThread() {
    return new SchedulerProcessorThread(this, threads++, schedulerModuleMetricReporter);
  }

  /** Find message in cache if cached, if not look in DB for msg. */
  private Message findCachedMsg(String msgId) {
    return msgId != null ? cachedMsgs.getIfPresent(msgId) : null;
  }
}
