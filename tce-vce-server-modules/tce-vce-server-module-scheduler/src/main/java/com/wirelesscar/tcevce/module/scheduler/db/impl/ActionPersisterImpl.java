package com.wirelesscar.tcevce.module.scheduler.db.impl;

import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

import com.wirelesscar.tce.db.common.db.api.MessageFields;
import com.wirelesscar.tce.db.common.db.api.TableName;
import com.wirelesscar.tce.db.standard.sql.impl.AbstractSqlPersister;
import com.wirelesscar.tce.db.standard.sql.impl.IdGenerator;
import com.wirelesscar.tce.utils.Util;
import com.wirelesscar.tcevce.module.scheduler.api.Action;
import com.wirelesscar.tcevce.module.scheduler.api.Transition;
import com.wirelesscar.tcevce.module.scheduler.cache.api.SchedulerCacheFactory;
import com.wirelesscar.tcevce.module.scheduler.db.api.ActionPersister;

public class ActionPersisterImpl extends AbstractSqlPersister<Action> implements ActionPersister {
  private static final String INSERT_SQL_STRING = createInsertSql();
  private static final String SELECT_BY_TRANSITION_ID_SQL_STRING = createSelectByTransitionIdSql();
  private static final String UPDATE_SQL_STRING = createUpdateSql();

  public ActionPersisterImpl() {
    super(TableName.SCHD_ACTION.name());
  }

  private static String createInsertSql() {
    StringBuilder stringBuilder = new StringBuilder(500);

    stringBuilder.append("insert into ");
    stringBuilder.append(TableName.SCHD_ACTION);
    stringBuilder.append(" (");
    stringBuilder.append(MessageFields.id);
    stringBuilder.append(", ");
    stringBuilder.append(MessageFields.transitionId);
    stringBuilder.append(", ");
    stringBuilder.append(MessageFields.name);
    stringBuilder.append(", ");
    stringBuilder.append(MessageFields.orderNumber);
    stringBuilder.append(", ");
    stringBuilder.append(MessageFields.isInternal);
    stringBuilder.append(", ");
    stringBuilder.append(MessageFields.properties);
    stringBuilder.append(") values (");
    stringBuilder.append("?");
    stringBuilder.append(", ?");
    stringBuilder.append(", ?");
    stringBuilder.append(", ?");
    stringBuilder.append(", ?");
    stringBuilder.append(", ?");
    stringBuilder.append(")");

    return stringBuilder.toString();
  }

  private static String createSelectByTransitionIdSql() {
    StringBuilder stringBuilder = new StringBuilder(500);

    stringBuilder.append("select * from ");
    stringBuilder.append(TableName.SCHD_ACTION);

    stringBuilder.append(" where ");
    stringBuilder.append(MessageFields.transitionId);
    stringBuilder.append(" = ? ");

    stringBuilder.append(" order by ");
    stringBuilder.append(MessageFields.orderNumber);

    return stringBuilder.toString();
  }

  private static String createUpdateSql() {
    StringBuilder stringBuilder = new StringBuilder();

    stringBuilder.append("update ");
    stringBuilder.append(TableName.SCHD_ACTION);
    stringBuilder.append(" set ");

    stringBuilder.append(MessageFields.transitionId);
    stringBuilder.append("= ? ,");
    stringBuilder.append(MessageFields.name);
    stringBuilder.append("= ? ,");
    stringBuilder.append(MessageFields.orderNumber);
    stringBuilder.append("= ? ,");
    stringBuilder.append(MessageFields.isInternal);
    stringBuilder.append("= ? ,");
    stringBuilder.append(MessageFields.properties);
    stringBuilder.append("= ? ");

    stringBuilder.append(" where ");
    stringBuilder.append(MessageFields.id);
    stringBuilder.append(" = ? ");

    return stringBuilder.toString();
  }

  @Override
  public List<Action> findByTransition(Transition transition) throws SQLException {
    List<Object> values = Collections.singletonList(transition.getId());

    return find(SELECT_BY_TRANSITION_ID_SQL_STRING, values);
  }

  @Override
  protected String getInsertSql() {
    return INSERT_SQL_STRING;
  }

  @Override
  protected String getUpdateSql() {
    return UPDATE_SQL_STRING;
  }

  @Override
  protected Action insert(PreparedStatement preparedStatement, Action action) throws SQLException {
    action.setId(IdGenerator.newId());

    Transition transition = action.getTransition();
    validateNotNull(transition, "Missing mandatory field Transition");

    int i = 1;
    preparedStatement.setString(i++, action.getId());
    preparedStatement.setString(i++, transition.getId());

    preparedStatement.setString(i++, action.getName() != null ? action.getName() : "");
    preparedStatement.setInt(i++, action.getOrder());
    preparedStatement.setString(i++, Boolean.toString(action.isInternal()));
    preparedStatement.setString(i++, Util.mapToString(action.getProperties()));

    preparedStatement.executeUpdate();

    return action;
  }

  @Override
  protected Action populateObject(ResultSet resultSet) throws SQLException {
    Action action = new Action();

    action.setId(resultSet.getString(MessageFields.id.name()));

    String trId = resultSet.getString(MessageFields.transitionId.name());
    action.setTransition(SchedulerCacheFactory.transitionCache().get(trId, true));
    action.setName(resultSet.getString(MessageFields.name.name()));
    action.setOrder(resultSet.getInt(MessageFields.orderNumber.name()));
    action.setInternal(Boolean.valueOf(resultSet.getString(MessageFields.isInternal.name())));

    Map<String, String> props = Util.stringToMap(resultSet.getString(MessageFields.properties.name()));

    for (Entry<String, String> entry : props.entrySet()) {
      action.setProperty(entry.getKey(), entry.getValue());
    }

    return action;
  }

  @Override
  protected Action update(PreparedStatement preparedStatement, Action action) throws SQLException {
    int i = 1;
    preparedStatement.setString(i++, action.getTransition().getId());
    preparedStatement.setString(i++, action.getName());
    preparedStatement.setInt(i++, action.getOrder());
    preparedStatement.setString(i++, Boolean.toString(action.isInternal()));
    preparedStatement.setString(i++, Util.mapToString(action.getProperties()));

    // where values
    preparedStatement.setString(i++, action.getId());

    preparedStatement.executeUpdate();

    return action;
  }
}
