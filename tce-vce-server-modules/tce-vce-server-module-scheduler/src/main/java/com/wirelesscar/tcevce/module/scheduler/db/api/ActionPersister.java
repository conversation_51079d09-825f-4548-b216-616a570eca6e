package com.wirelesscar.tcevce.module.scheduler.db.api;

import java.sql.SQLException;
import java.util.List;

import com.wirelesscar.tce.db.common.db.api.BasicPersister;
import com.wirelesscar.tcevce.module.scheduler.api.Action;
import com.wirelesscar.tcevce.module.scheduler.api.Transition;

public interface ActionPersister extends BasicPersister<Action> {
  /**
   * Returns all entries connected to parent
   *
   * @param transition - parent
   */
  List<Action> findByTransition(Transition transition) throws SQLException;
}
