package com.wirelesscar.tcevce.module.scheduler.cache.api;

import java.sql.SQLException;
import java.util.List;

import com.wirelesscar.tce.db.common.cache.api.Cacher;
import com.wirelesscar.tcevce.module.scheduler.api.Action;
import com.wirelesscar.tcevce.module.scheduler.api.Transition;

public interface ActionCache extends Cacher<Action> {
  /**
   * Get List<> from cache by parent. Will try to fetch entry from DB if not found in cache
   *
   * @param transition parent
   * @throws SQLException if fetch from db failed with error
   */
  List<Action> findByTransition(Transition transition) throws SQLException;
}
