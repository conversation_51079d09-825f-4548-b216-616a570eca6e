package com.wirelesscar.tcevce.module.scheduler;

import java.util.HashSet;
import java.util.Set;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * Keeps track of schedulers to be able to reference them later on, for example in REST controllers.
 */
@Component
public class SchedulerRegistry {
  private static final Logger log = LoggerFactory.getLogger(SchedulerRegistry.class);

  private final Set<SchedulerProcessor> schedulers = new HashSet<>();

  public Set<SchedulerProcessor> getSchedulers() {
    return schedulers;
  }

  public void register(SchedulerProcessor processor) {
    boolean wasAdded = schedulers.add(processor);
    log.info("Registered processor, was a new processor: {}, number of registered processors: {}", wasAdded, schedulers.size());
  }

  public void unRegister(SchedulerProcessor processor) {
    boolean remove = schedulers.remove(processor);
    log.info("Unregistered processor: {}, number of registered processors: {}", remove, schedulers.size());
  }
}
