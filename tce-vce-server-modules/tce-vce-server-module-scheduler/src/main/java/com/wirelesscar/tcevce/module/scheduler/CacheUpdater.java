package com.wirelesscar.tcevce.module.scheduler;

import java.sql.SQLException;
import java.util.List;
import java.util.concurrent.TimeUnit;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.wirelesscar.config.Config;
import com.wirelesscar.config.ConfigFactory;
import com.wirelesscar.tce.db.common.api.CacheUpdateEntry;
import com.wirelesscar.tce.db.common.db.api.CacheUpdatePersister;
import com.wirelesscar.tce.db.standard.sql.impl.SqlCacheUpdatePersisterImpl;
import com.wirelesscar.tcevce.module.scheduler.api.Action;
import com.wirelesscar.tcevce.module.scheduler.api.Schedule;
import com.wirelesscar.tcevce.module.scheduler.api.Transition;
import com.wirelesscar.tcevce.module.scheduler.cache.CacheTableCleaner;
import com.wirelesscar.tcevce.module.scheduler.cache.api.SchedulerCacheFactory;

public class CacheUpdater implements Runnable {
  private static final Config config = ConfigFactory.getConfig();
  private static final Logger log = LoggerFactory.getLogger(CacheUpdater.class);

  private CacheUpdatePersister cacheUpdatePersister;
  private CacheTableCleaner cleaner;
  private int maxFetch = 500;
  private long pauseMs = 1_000;
  private int processedMsgSize;
  private boolean run;
  private String stackName = "notset";

  private static void removeAction(Action action) throws SQLException {
    if (action != null) {
      log.info("Removing from cache: {}", action);
      SchedulerCacheFactory.actionCache().remove(action);

      Transition transition = SchedulerCacheFactory.transitionCache().get(action.getTransition().getId(), false);
      removeTransition(transition);
    }
  }

  private static void removeSchedule(Schedule schedule) throws SQLException {
    if (schedule != null) {
      log.info("Removing from cache: {}", schedule);
      SchedulerCacheFactory.scheduleCache().remove(schedule);
    }
  }

  private static void removeTransition(Transition transition) throws SQLException {
    if (transition != null) {
      log.info("Removing from cache: {}", transition);
      SchedulerCacheFactory.transitionCache().remove(transition);
    }

    Schedule schedule = SchedulerCacheFactory.scheduleCache().get(transition.getSchedule().getId(), false);
    removeSchedule(schedule);
  }

  public int processedMsgSize() {
    return processedMsgSize;
  }

  public void processEvents() {
    run = true;

    while (run) {
      int size = 0;
      try {
        size = processOneBatch();
      } catch (Exception e) {
        log.error("", e);
      }

      try {
        if (size < maxFetch) {
          TimeUnit.MILLISECONDS.sleep(pauseMs);
        }
      } catch (Exception e) {
        log.error("", e);
      }
    }
  }

  @Override
  public void run() {
    log.info("CacheUpdater start");

    // ToDo: read values from config

    cacheUpdatePersister = new SqlCacheUpdatePersisterImpl();
    pauseMs = config.getLong(stackName + ".scheduler.cache.pause.ms").orElse(1_000L);
    maxFetch = config.getInt(stackName + ".scheduler.cache.fetch.max").orElse(500);

    if (config.getBoolean(stackName + ".scheduler.cache.run.tablecleaner").orElse(true)) {
      cleaner = new CacheTableCleaner();
      Thread thread = new Thread(cleaner);
      thread.start();
    }

    processEvents();
  }

  public void stopMe() {
    run = false;
    if (cleaner != null) {
      cleaner.stopMe();
    }
  }

  private int processOneBatch() throws SQLException {
    List<CacheUpdateEntry> list = cacheUpdatePersister.findLatest();

    for (CacheUpdateEntry cue : list) {
      switch (cue.getObjectType()) {
        case Action:
          Action action = SchedulerCacheFactory.actionCache().get(cue.getObjectId(), false);
          removeAction(action);
          break;

        case Transition:
          Transition transition = SchedulerCacheFactory.transitionCache().get(cue.getObjectId(), false);
          removeTransition(transition);
          break;

        case Schedule:
          Schedule schedule = SchedulerCacheFactory.scheduleCache().get(cue.getObjectId(), false);
          removeSchedule(schedule);
          break;

        case Apn:
        case Bos:
        case Device:
        case DeviceProperty:
        case Satellite:
        case Selector:
        case Sim:
        default:
          // do nil, handled by other
      }

      processedMsgSize++;
    }

    return list.size();
  }
}
