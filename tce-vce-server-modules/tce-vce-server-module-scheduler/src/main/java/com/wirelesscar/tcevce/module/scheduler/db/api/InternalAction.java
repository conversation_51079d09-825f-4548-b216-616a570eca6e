package com.wirelesscar.tcevce.module.scheduler.db.api;

import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.wirelesscar.tce.db.common.db.api.MessageFields;
import com.wirelesscar.tce.module.api.Message;
import com.wirelesscar.tcevce.module.scheduler.api.Action;
import com.wirelesscar.tcevce.module.scheduler.api.ActionProperty;
import com.wirelesscar.tcevce.module.scheduler.api.ActionType;

public final class InternalAction {
  private static final Logger log = LoggerFactory.getLogger(InternalAction.class);

  private InternalAction() {
    throw new IllegalStateException();
  }

  public static void execType(Message message, Map<String, String> actionProperties, ActionType actionType) {
    switch (actionType) {
      case AddProperty:
        String key = actionProperties.get(ActionProperty.NewPropertyKey.name());
        String value = actionProperties.get(ActionProperty.NewPropertyValue.name());

        log.debug("Adding new property {}:{} to {}", key, value, message.getVehicleID());
        message.setProperty(key, value);
        break;

      case Remove:
        message.setProperty(MessageFields.removeTime.name(), "1");
        break;

      case ChangeState:
        String state = actionProperties.get(ActionProperty.NewActivityState.name());
        message.setProperty(MessageFields.status.name(), state);
        log.debug("Changing state to {} on {}", state, message.getVehicleID());
        break;

      default:
        throw new RuntimeException("Not implemented");
    }
  }

  /** Performs action on message */
  public static void execute(Action action, Message message, Map<String, String> actionProperties) throws FailedActionException {
    String actionTypeStr = actionProperties.get(ActionProperty.ActionType.name());

    if (actionTypeStr == null || actionTypeStr.isEmpty()) {
      throw new RuntimeException("Missing Configuration: "
          + ActionProperty.ActionType.name()
          + " in Action "
          + action.getName());
    }

    ActionType actionType = ActionType.valueOf(actionTypeStr);

    try {
      execType(message, actionProperties, actionType);
    } catch (Exception e) {
      throw new FailedActionException("Error executing action " + action.toString(), e);
    }
  }
}
