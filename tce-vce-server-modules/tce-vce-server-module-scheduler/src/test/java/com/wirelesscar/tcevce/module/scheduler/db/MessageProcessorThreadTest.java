package com.wirelesscar.tcevce.module.scheduler.db;

import java.nio.charset.StandardCharsets;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.EnumMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import com.volvo.tisp.framework.context.TispContext;
import com.volvo.tisp.identifier.TrackingIdentifier;
import com.volvo.tisp.identifier.WorkflowIdentifier;
import com.wirelesscar.config.mock.MockConfiguration;
import com.wirelesscar.tce.core.event.EventService;
import com.wirelesscar.tce.core.event.Type;
import com.wirelesscar.tce.db.common.api.ActivityStatus;
import com.wirelesscar.tce.db.common.db.api.MessageFields;
import com.wirelesscar.tce.db.common.db.api.MessagePersister;
import com.wirelesscar.tce.db.common.metrics.ProcessorMetricsReporter;
import com.wirelesscar.tce.db.source.TceDataSource;
import com.wirelesscar.tce.db.standard.sql.impl.SqlPersistanceFactory;
import com.wirelesscar.tce.device.AbstractDeviceService;
import com.wirelesscar.tce.module.api.EnqueueingType;
import com.wirelesscar.tce.module.api.Message;
import com.wirelesscar.tce.module.api.MetaData;
import com.wirelesscar.tce.module.metrics.ModuleMetricReporter;
import com.wirelesscar.tce.utils.LoggingHelper;
import com.wirelesscar.tcevce.module.scheduler.SchedulerModule;
import com.wirelesscar.tcevce.module.scheduler.SchedulerProcessor;
import com.wirelesscar.tcevce.module.scheduler.SchedulerProcessorThread;
import com.wirelesscar.tcevce.module.scheduler.SchedulerRegistry;
import com.wirelesscar.tcevce.module.scheduler.api.Schedule;
import com.wirelesscar.tcevce.module.scheduler.api.Transition;
import com.wirelesscar.tcevce.module.scheduler.db.api.SchedulePersistanceFactory;
import com.wirelesscar.tcevce.module.scheduler.metrics.SchedulerModuleMetricReporter;
import com.wirelesscar.tcevce.module.segmentation.encryption.VceEncryptionHandler;

class MessageProcessorThreadTest {
  private static final String PAYLOAD = "Hepp payload hopp";
  private static MessagePersister messagePersister;
  private Schedule schedule;
  private Schedule scheduleShortTimeout;
  private SchedulerModuleMetricReporter schedulerModuleMetricReporter = Mockito.mock(SchedulerModuleMetricReporter.class);
  private SchedulerProcessor schedulerProcessor;
  private Transition transition;
  private Transition transition2;
  private Transition transition3;

  @BeforeAll
  static void beforeAll() throws SQLException {
    MockConfiguration mockConfiguration = MockConfiguration.getConfig();
    mockConfiguration.reset();
    mockConfiguration.setPropertySpecific(mockConfiguration.getComponentShortName(), TceDataSource.TEST_DB_PROP, "true");
    mockConfiguration.setPropertySpecific(mockConfiguration.getComponentShortName(), "notset.scheduler.processor.number_of_threads", "1");

    SchedulerTestUtil.preps();

    messagePersister = SchedulePersistanceFactory.makeMessagePersister();
  }

  static void persistMessage(final Message message) {
    TispContext.runInContext(() -> {
      try {
        messagePersister.persist(message);
      } catch (SQLException e) {
        throw new RuntimeException(e);
      }
    }, builder -> LoggingHelper.populateContextWithMessage(builder, message));
  }

  static void persistMessage(final Message message, final boolean inProcessCallsEnabled) {
    TispContext.runInContext(() -> {
      try {
        messagePersister.persist(message, inProcessCallsEnabled);
      } catch (SQLException e) {
        throw new RuntimeException(e);
      }
    }, builder -> LoggingHelper.populateContextWithMessage(builder, message));
  }

  private static void ackOneMessage(int counterMsg, int counterAckOrder, int messagesSize, SchedulerProcessorThread procThread, MessagePersister persister,
      List<String> ids, List<Message> messages, Message message) throws Exception {
    ackOneMessage(counterMsg, counterAckOrder, messagesSize, procThread, persister, ids, messages, message, true);
  }

  private static void ackOneMessage(int counterMsg, int counterAckOrder, int messagesSize, SchedulerProcessorThread procThread, MessagePersister persister,
      List<String> ids, List<Message> messages, Message message, boolean runProcThread) throws Exception {
    messages.get(counterMsg).setProperty(MessageFields.status.name(), ActivityStatus.d.name());
    persister.updateForAck(messages.get(counterMsg), null);
    procThread.clearLastMsgCache(ids.get(counterMsg));
    procThread.flagMsgFinished(messages.get(counterMsg));

    if (runProcThread && counterAckOrder <= 4) { // still message to process
      Assertions.assertEquals(1, procThread.getQSize());
    }

    if (runProcThread) {
      // process that msg
      procThread.processOneMessage();

      // check one more is sent
      if (counterAckOrder <= 3) { // still capped msg
        Assertions.assertEquals(ids.get(counterAckOrder + 6), procThread.findNextActiveMsgsNotSentAllQ(message).getMessageId(),
            "Wrong message first in Q should be number " + (7 + counterAckOrder));
      } else {
        Assertions.assertNull(procThread.findNextActiveMsgsNotSentAllQ(message), "Should be empty ");
      }

      int countForAssert = counterAckOrder <= 4 ? counterAckOrder : 4;
      Map<String, Map<Long, String>> map = procThread.findActiveMsgIdsNowForDevice(message, true);
      Set<String> set = makeIdSet(map);
      Assertions.assertEquals((messagesSize - (counterAckOrder + 1)), set.size());
      Assertions.assertEquals(0L, procThread.getDiscardedDueToEnqueueType());
      Assertions.assertEquals((messagesSize + (countForAssert + 2)), (int) procThread.getProcessed());
      Assertions.assertEquals((countForAssert + 7), (int) procThread.getExecTransitionsTimes()); // 5 + 1
      Assertions.assertEquals(5, procThread.getCappedMessageCount(), "Wrong number of messages capped, should be 5");
      Assertions.assertEquals((9 - counterAckOrder), procThread.getCappedMsgList(message).size(),
          "Wrong number of messages in activeMessageCache, should be 10 -" + (counterAckOrder + 1));
    }
  }

  private static Set<String> makeIdSet(Map<String, Map<Long, String>> map) {
    Set<String> set = new HashSet<>();

    if (map != null) {
      for (Map<Long, String> qmap : map.values()) {
        for (String value : qmap.values()) {
          set.add(value);
        }
      }
    }
    return set;
  }

  private static void processOneMsgOnce(SchedulerProcessorThread procThread, Message message) throws Exception {
    procThread.addToQueue(message);
    procThread.processOneMessage();
    procThread.clearLastMsgCache();
  }

  @AfterEach
  void afterEach() throws SQLException {
    if (transition3 != null) {
      SchedulePersistanceFactory.makeTransitionPersister().delete(transition3);
      transition3 = null;
    }

    if (transition2 != null) {
      SchedulePersistanceFactory.makeTransitionPersister().delete(transition2);
      transition2 = null;
    }

    SchedulePersistanceFactory.makeTransitionPersister().delete(transition);
    SchedulePersistanceFactory.makeSchedulePersister().delete(schedule);
    schedulerProcessor.stopMe();
  }

  @BeforeEach
  void beforeEach() throws Exception {
    SqlPersistanceFactory.cleanDB();

    MockConfiguration mockConfiguration = MockConfiguration.getConfig();
    mockConfiguration.setPropertySpecific(mockConfiguration.getComponentShortName(), "notset.scheduler.workerthread.min_future_ms_for_connection_estbl", "1");
    mockConfiguration.setPropertySpecific(mockConfiguration.getComponentShortName(), "notset.scheduler.workerthread.min_secs_capped_cache", "5");

    AbstractDeviceService abstractDeviceService = new AbstractDeviceService() {
      @Override
      public void refreshCommdata(Message message) {
        message.setProperty(MetaData.IP_DST_ADDRESS, "*******");
        message.setProperty(MetaData.IP_DST_PORT, "500");
        message.setProperty(MetaData.SMPP_DEST_ADDRESS, "msisdn");
        message.setProperty(MetaData.OPERATOR, "operator");
      }
    };
    SchedulerModule schedulerModule = new SchedulerModule(Mockito.mock(EventService.class), new SchedulerRegistry(), abstractDeviceService,
        Mockito.mock(ProcessorMetricsReporter.class), schedulerModuleMetricReporter, Mockito.mock(ModuleMetricReporter.class),
        Mockito.mock(VceEncryptionHandler.class));
    schedulerModule.setOldConfigProperties();
    schedulerProcessor = schedulerModule.getProcessor();
    schedule = SchedulePersistanceFactory.makeSchedulePersister().persist(SchedulerTestUtil.createSchedule());
    scheduleShortTimeout = SchedulePersistanceFactory.makeSchedulePersister().persist(SchedulerTestUtil.createSchedule(1));
    Schedule scheduleHigh = SchedulePersistanceFactory.makeSchedulePersister().persist(SchedulerTestUtil.createSchedule("common_high"));
    Schedule scheduleVeryHigh = SchedulePersistanceFactory.makeSchedulePersister()
        .persist(SchedulerTestUtil.createSchedule("common_very-high"));
    transition = SchedulePersistanceFactory.makeTransitionPersister()
        .persist(SchedulerTestUtil.createTransition(schedule, 0, null));
    SchedulePersistanceFactory.makeTransitionPersister()
        .persist(SchedulerTestUtil.createTransition(scheduleHigh, 0, null));
    SchedulePersistanceFactory.makeTransitionPersister()
        .persist(SchedulerTestUtil.createTransition(scheduleVeryHigh, 0, null));
    SchedulePersistanceFactory.makeTransitionPersister()
        .persist(SchedulerTestUtil.createTransition(scheduleShortTimeout, 0, null));
  }

  // send once with each transition
  @Test
  void test2TransitionsRepeat1() throws Exception {
    transition.setTimeOutRepeat(1);
    transition = SchedulePersistanceFactory.makeTransitionPersister().persist(transition);
    transition2 = SchedulerTestUtil.createTransition(schedule, 1, transition);
    transition2.setTimeOutRepeat(1);
    transition2.setMaxNumberOfTriesPerMsg(10);
    transition2 = SchedulePersistanceFactory.makeTransitionPersister().persist(transition2);
    SchedulerProcessorThread procThread = new SchedulerProcessorThread(schedulerProcessor, 0, schedulerModuleMetricReporter);

    Message message = createMessage();
    message.setMessageId("Sonny" + Math.random());
    boolean ok = procThread.addToQueue(message);
    Assertions.assertTrue(ok);
    procThread.processOneMessage();
    procThread.clearLastMsgCache();
    procThread.clearLastMsgCache();
    Assertions.assertEquals(transition.getName(), message.getProperty(MessageFields.lastTransitionName.name()));

    processOneMsgOnce(procThread, message);

    Assertions.assertEquals(transition2.getName(), message.getProperty(MessageFields.lastTransitionName.name()));
  }

  // send once with each transition, then start over with first again
  @Test
  void test2TransitionsRepeat1AndNotStartOver() throws Exception {
    schedule.setTimeToLive(50_000);
    schedule = SchedulePersistanceFactory.makeSchedulePersister().persist(schedule);

    transition.setTimeOutRepeat(1);
    transition = SchedulePersistanceFactory.makeTransitionPersister().persist(transition);
    transition2 = SchedulerTestUtil.createTransition(schedule, 1, transition);
    transition2.setTimeOutRepeat(1);
    transition2.setTimeToLive(50_000);
    transition2.setMaxNumberOfTriesPerMsg(10);
    transition2 = SchedulePersistanceFactory.makeTransitionPersister().persist(transition2);
    SchedulerProcessorThread procThread = new SchedulerProcessorThread(schedulerProcessor, 0, schedulerModuleMetricReporter);

    Message message = createMessage();
    message.setMessageId("Sonny" + Math.random());
    boolean ok = procThread.addToQueue(message);
    Assertions.assertTrue(ok);
    procThread.processOneMessage();
    procThread.clearLastMsgCache();
    Assertions.assertEquals(transition.getName(), message.getProperty(MessageFields.lastTransitionName.name()));
    Assertions.assertEquals(0, procThread.getSkippedTransitionsMaxTries());
    Assertions.assertEquals(0, procThread.getSkippedTransitionsRepeat()); // one for each transition

    processOneMsgOnce(procThread, message);

    Assertions.assertEquals(transition2.getName(), message.getProperty(MessageFields.lastTransitionName.name()));
    Assertions.assertEquals(0, procThread.getSkippedTransitionsMaxTries());
    Assertions.assertEquals(1, procThread.getSkippedTransitionsRepeat()); // one for each transition

    processOneMsgOnce(procThread, message);

    Assertions.assertEquals(transition2.getName(), message.getProperty(MessageFields.lastTransitionName.name()));
    Assertions.assertEquals(0, procThread.getSkippedTransitionsMaxTries());
    Assertions.assertEquals(2, procThread.getSkippedTransitionsRepeat()); // one for each transition

    processOneMsgOnce(procThread, message);

    Assertions.assertEquals(transition2.getName(), message.getProperty(MessageFields.lastTransitionName.name()));
    Assertions.assertEquals(0, procThread.getSkippedTransitionsMaxTries());
    Assertions.assertEquals(3, procThread.getSkippedTransitionsRepeat()); // one for each transition
  }

  // send once with each transition, then start over with first again
  @Test
  void test2TransitionsRepeat1AndStartOver() throws Exception {
    schedule.setTimeToLive(50_000);
    schedule = SchedulePersistanceFactory.makeSchedulePersister().persist(schedule);

    transition.setTimeOutRepeat(1);
    transition = SchedulePersistanceFactory.makeTransitionPersister().persist(transition);
    transition2 = SchedulerTestUtil.createTransition(schedule, 1, transition);
    transition2.setTimeOutRepeat(1);
    transition2.setTimeToLive(50_000);
    transition2.setMaxNumberOfTriesPerMsg(10);
    transition2 = SchedulePersistanceFactory.makeTransitionPersister().persist(transition2);
    SchedulerProcessorThread procThread = new SchedulerProcessorThread(schedulerProcessor, 0, schedulerModuleMetricReporter);

    Message message = createMessage();
    message.setMessageId("Sonny" + Math.random());
    boolean ok = procThread.addToQueue(message);
    Assertions.assertTrue(ok);
    procThread.processOneMessage();
    procThread.clearLastMsgCache();
    Assertions.assertEquals(transition.getName(), message.getProperty(MessageFields.lastTransitionName.name()));

    processOneMsgOnce(procThread, message);

    Assertions.assertEquals(transition2.getName(), message.getProperty(MessageFields.lastTransitionName.name()));

    message.setProperty(Type.CONNECTION_ESTABLISHED.name(), Type.CONNECTION_ESTABLISHED.name());
    processOneMsgOnce(procThread, message);

    message.setProperty(Type.CONNECTION_ESTABLISHED.name(), null);
    processOneMsgOnce(procThread, message);

    Assertions.assertEquals(transition.getName(), message.getProperty(MessageFields.lastTransitionName.name()));
  }

  // send twice with each transition
  @Test
  void test2TransitionsRepeat2() throws Exception {
    transition.setTimeOutRepeat(2);
    transition = SchedulePersistanceFactory.makeTransitionPersister().persist(transition);
    transition2 = SchedulerTestUtil.createTransition(schedule, 1, transition);
    transition2.setTimeOutRepeat(2);
    transition2.setMaxNumberOfTriesPerMsg(10);
    transition2 = SchedulePersistanceFactory.makeTransitionPersister().persist(transition2);
    SchedulerProcessorThread procThread = new SchedulerProcessorThread(schedulerProcessor, 0, schedulerModuleMetricReporter);

    Message message = createMessage();
    message.setMessageId("Sonny" + Math.random());
    boolean ok = procThread.addToQueue(message);
    Assertions.assertTrue(ok);
    procThread.processOneMessage();
    procThread.clearLastMsgCache();
    Assertions.assertEquals(transition.getName(), message.getProperty(MessageFields.lastTransitionName.name()));

    processOneMsgOnce(procThread, message);

    Assertions.assertEquals(transition.getName(), message.getProperty(MessageFields.lastTransitionName.name()));

    message.setProperty(MessageFields.removeTime.name(), Long.MAX_VALUE + "");
    processOneMsgOnce(procThread, message);

    Assertions.assertEquals(transition2.getName(), message.getProperty(MessageFields.lastTransitionName.name()));

    processOneMsgOnce(procThread, message);

    Assertions.assertEquals(transition2.getName(), message.getProperty(MessageFields.lastTransitionName.name()));
  }

  // Five transitions, repeat from the second when reached fourth
  @Test
  void test2TransitionsRepeatFromSecond() throws Exception {
    transition.setTimeOutRepeat(1);
    transition = SchedulePersistanceFactory.makeTransitionPersister().persist(transition);
    transition2 = SchedulerTestUtil.createTransition(schedule, 2, null);
    transition2.setTimeOutRepeat(1);
    transition2.setMaxNumberOfTriesPerMsg(10);
    transition2 = SchedulePersistanceFactory.makeTransitionPersister().persist(transition2);

    Transition transition3 = SchedulerTestUtil.createTransition(schedule, 3, null);
    transition3.setTimeOutRepeat(1);
    transition3.setMaxNumberOfTriesPerMsg(10);
    transition3 = SchedulePersistanceFactory.makeTransitionPersister().persist(transition3);

    Transition transition4 = SchedulerTestUtil.createTransition(schedule, 4, null);
    transition4.setTimeOutRepeat(1);
    transition4.setMaxNumberOfTriesPerMsg(2);
    transition4.setRepeatFrom(transition2);
    transition4 = SchedulePersistanceFactory.makeTransitionPersister().persist(transition4);

    Transition transition5 = SchedulerTestUtil.createTransition(schedule, 5, null);
    transition5.setTimeOutRepeat(1);
    transition5.setMaxNumberOfTriesPerMsg(10);
    transition5 = SchedulePersistanceFactory.makeTransitionPersister().persist(transition5);

    try {
      SchedulerProcessorThread procThread = new SchedulerProcessorThread(schedulerProcessor, 0, schedulerModuleMetricReporter);

      Message message = createMessage();
      message.setMessageId("Sonny" + Math.random());

      // process transition 1
      procThread.addToQueue(message);
      procThread.processOneMessage();
      procThread.clearLastMsgCache();
      Assertions.assertEquals(transition.getName(), message.getProperty(MessageFields.lastTransitionName.name()));

      // process transition 2
      processOneMsgOnce(procThread, message);
      Assertions.assertEquals(transition2.getName(), message.getProperty(MessageFields.lastTransitionName.name()));

      // process transition 3
      processOneMsgOnce(procThread, message);
      Assertions.assertEquals(transition3.getName(), message.getProperty(MessageFields.lastTransitionName.name()));

      // process transition 4, should trigger RepeatFrom to transition 2
      processOneMsgOnce(procThread, message);
      Assertions.assertEquals(transition2.getName(), message.getProperty(MessageFields.lastTransitionName.name()));

      // process transition 3
      processOneMsgOnce(procThread, message);
      Assertions.assertEquals(transition3.getName(), message.getProperty(MessageFields.lastTransitionName.name()));

      // process transition 4, should trigger RepeatFroim to transition 2 again
      processOneMsgOnce(procThread, message);
      Assertions.assertEquals(transition2.getName(), message.getProperty(MessageFields.lastTransitionName.name()));

      // process transition 3
      processOneMsgOnce(procThread, message);
      Assertions.assertEquals(transition3.getName(), message.getProperty(MessageFields.lastTransitionName.name()));

      // process transition 5, as transition 4 is exhausted
      processOneMsgOnce(procThread, message);
      Assertions.assertEquals(transition5.getName(), message.getProperty(MessageFields.lastTransitionName.name()));

      // process again, no result
      processOneMsgOnce(procThread, message);
      Assertions.assertEquals(transition5.getName(), message.getProperty(MessageFields.lastTransitionName.name()));

    } finally {
      SchedulePersistanceFactory.makeTransitionPersister().delete(transition3);
      SchedulePersistanceFactory.makeTransitionPersister().delete(transition4);
      SchedulePersistanceFactory.makeTransitionPersister().delete(transition5);
    }
  }

  @Test
  void test2TransitionsThenWaitThenConnEst() throws Exception {
    transition.setTimeOutRepeat(1);
    transition.setMaxNumberOfTriesPerMsg(3);
    transition = SchedulePersistanceFactory.makeTransitionPersister().persist(transition);
    transition2 = SchedulerTestUtil.createTransition(schedule, 1, transition);
    transition2.setName(2 + "-" + transition2.getName());
    transition2.setTimeOutRepeat(1);
    transition2.setMaxNumberOfTriesPerMsg(2);
    transition2 = SchedulePersistanceFactory.makeTransitionPersister().persist(transition2);
    SchedulerProcessorThread procThread = new SchedulerProcessorThread(schedulerProcessor, 0, schedulerModuleMetricReporter);

    Message message = createMessage();
    message.setMessageId("Sonny" + Math.random());

    // process UDP
    boolean ok = procThread.addToQueue(message);
    Assertions.assertTrue(ok);
    procThread.processOneMessage();
    procThread.clearLastMsgCache();
    Assertions.assertEquals(transition.getName(), message.getProperty(MessageFields.lastTransitionName.name()));
    Assertions.assertEquals(1, procThread.getExecTransitionsTimes());
    Assertions.assertEquals(0, procThread.getNoUsableTransition());

    processOneMsgOnce(procThread, message);

    Assertions.assertEquals(transition2.getName(), message.getProperty(MessageFields.lastTransitionName.name()));
    Assertions.assertEquals(2, procThread.getExecTransitionsTimes());
    Assertions.assertEquals(0, procThread.getNoUsableTransition());
    Assertions.assertEquals(0, procThread.getProcessedToWait());
    Assertions.assertEquals(0, procThread.getSkippedTransitionsMaxTries());

    processOneMsgOnce(procThread, message);

    Assertions.assertEquals(transition2.getName(), message.getProperty(MessageFields.lastTransitionName.name()));
    Assertions.assertEquals(2, procThread.getExecTransitionsTimes());
    Assertions.assertEquals(0, procThread.getNoUsableTransition());
    Assertions.assertEquals(1, procThread.getProcessedToWait());
    Assertions.assertEquals(0, procThread.getSkippedTransitionsMaxTries());
    Assertions.assertEquals(0, procThread.getConnectionEstablishedMsg());

    // Connection Established
    Message msg2 = createMessage();
    msg2.setVehicleID(message.getVehicleID());
    msg2.setProperty(Type.CONNECTION_ESTABLISHED.name(), "true");
    processOneMsgOnce(procThread, msg2);

    Assertions.assertEquals(1, procThread.getConnectionEstablishedMsg());

    processOneMsgOnce(procThread, message);

    Assertions.assertEquals(transition.getName(), message.getProperty(MessageFields.lastTransitionName.name()));
    Assertions.assertEquals(3, procThread.getExecTransitionsTimes());
    Assertions.assertEquals(0, procThread.getNoUsableTransition());
    Assertions.assertEquals(1, procThread.getProcessedToWait());
    Assertions.assertEquals(0, procThread.getSkippedTransitionsMaxTries());
    Assertions.assertEquals(1, procThread.getConnectionEstablishedMsg());

    // Process to SMS 2
    procThread.clearConnectionEstablishedCache();
    processOneMsgOnce(procThread, message);

    Assertions.assertEquals(transition2.getName(), message.getProperty(MessageFields.lastTransitionName.name()));
    Assertions.assertEquals(4, procThread.getExecTransitionsTimes());
    Assertions.assertEquals(0, procThread.getNoUsableTransition());
    Assertions.assertEquals(1, procThread.getProcessedToWait());
    Assertions.assertEquals(0, procThread.getSkippedTransitionsMaxTries());
    Assertions.assertEquals(1, procThread.getConnectionEstablishedMsg());

    processOneMsgOnce(procThread, message);

    Assertions.assertEquals(transition2.getName(), message.getProperty(MessageFields.lastTransitionName.name()));
    Assertions.assertEquals(4, procThread.getExecTransitionsTimes());
    Assertions.assertEquals(0, procThread.getNoUsableTransition());
    Assertions.assertEquals(2, procThread.getProcessedToWait());
    Assertions.assertEquals(1, procThread.getSkippedTransitionsMaxTries());
    Assertions.assertEquals(1, procThread.getConnectionEstablishedMsg());

    // Last rounds, first UDP, then to timeout

    // Connection Established
    msg2 = createMessage();
    msg2.setVehicleID(message.getVehicleID());
    msg2.setProperty(Type.CONNECTION_ESTABLISHED.name(), "true");
    processOneMsgOnce(procThread, msg2);

    Assertions.assertEquals(2, procThread.getConnectionEstablishedMsg());

    processOneMsgOnce(procThread, message);

    Assertions.assertEquals(transition.getName(), message.getProperty(MessageFields.lastTransitionName.name()));
    Assertions.assertEquals(5, procThread.getExecTransitionsTimes());
    Assertions.assertEquals(0, procThread.getNoUsableTransition());
    Assertions.assertEquals(2, procThread.getProcessedToWait());
    Assertions.assertEquals(1, procThread.getSkippedTransitionsMaxTries());
    Assertions.assertEquals(2, procThread.getConnectionEstablishedMsg());

    processOneMsgOnce(procThread, message);

    Assertions.assertEquals(transition.getName(), message.getProperty(MessageFields.lastTransitionName.name()));
    Assertions.assertEquals(5, procThread.getExecTransitionsTimes());
    Assertions.assertEquals(1, procThread.getNoUsableTransition());
    Assertions.assertEquals(2, procThread.getProcessedToWait());
    Assertions.assertEquals(3, procThread.getSkippedTransitionsMaxTries());
    Assertions.assertEquals(2, procThread.getConnectionEstablishedMsg());
  }

  @Test
  void testAddOne() {
    SchedulerProcessorThread procThread = new SchedulerProcessorThread(schedulerProcessor, 0, schedulerModuleMetricReporter);

    Message message = createMessage();
    boolean ok = procThread.addToQueue(message);

    Assertions.assertEquals(1, procThread.getQSize());
    Assertions.assertTrue(ok);
  }

  @Test
  void testCappedCacheConfig() {
    final int cs = 6;

    MockConfiguration mockConfiguration = MockConfiguration.getConfig();
    mockConfiguration.setPropertySpecific(mockConfiguration.getComponentShortName(), "notset.scheduler.workerthread.min_secs_capped_cache", String.valueOf(cs));

    SchedulerProcessorThread procThread = new SchedulerProcessorThread(schedulerProcessor, 0, schedulerModuleMetricReporter);

    Assertions.assertEquals(cs, procThread.getSecsCacheTime());
  }

  @Test
  void testCappedCacheConfigBase() {
    SchedulerProcessorThread procThread = new SchedulerProcessorThread(schedulerProcessor, 0, schedulerModuleMetricReporter);

    Assertions.assertEquals(5, procThread.getSecsCacheTime());
  }

  @Test
  void testDeliverOne() throws Exception {
    SchedulerProcessorThread procThread = new SchedulerProcessorThread(schedulerProcessor, 0, schedulerModuleMetricReporter);

    Message message = createMessage();
    message.setProperty(MetaData.SCHEDULE_NAME, schedule.getName());
    message.setMessageId("1");
    message.setVehicleID("10");
    boolean ok = procThread.addToQueue(message);

    Assertions.assertEquals(1, procThread.getQSize());
    Assertions.assertTrue(ok);

    procThread.processOneMessage();
    procThread.clearLastMsgCache();
    TimeUnit.MILLISECONDS.sleep(10);

    Assertions.assertEquals(0, procThread.getQSize());

    Map<String, Map<Long, String>> map = procThread.findActiveMsgIdsNowForDevice(message, true);
    Set<String> set = makeIdSet(map);
    Assertions.assertTrue(set.contains(message.getMessageId()));

    procThread.flagMsgFinished(message);
    map = procThread.findActiveMsgIdsNowForDevice(message, true);
    set = makeIdSet(map);
    Assertions.assertEquals(0, set.size());

    procThread.stopMe();
  }

  @Test
  void testEnqueueTypeIgnore() throws Exception {
    SchedulerProcessorThread procThread = new SchedulerProcessorThread(schedulerProcessor, 0, schedulerModuleMetricReporter);
    String queueid = "Q1";

    Message message1 = createMessage();
    message1.setProperty(MetaData.SCHEDULE_NAME, schedule.getName());
    message1.setVehicleID("10");
    message1.setProperty(MetaData.SCHEDULE_QUEUE_ID, queueid);
    message1.setProperty(MetaData.SCHEDULE_ENQUEUEING_TYPE, EnqueueingType.IGNORE.name());
    persistMessage(message1);
    boolean ok = procThread.addToQueue(message1);

    Assertions.assertEquals(1, procThread.getQSize());
    Assertions.assertTrue(ok);

    Message message2 = createMessage();
    message2.setProperty(MetaData.SCHEDULE_NAME, schedule.getName());
    message2.setVehicleID("10");
    message2.setProperty(MetaData.SCHEDULE_QUEUE_ID, queueid);
    message2.setProperty(MetaData.SCHEDULE_ENQUEUEING_TYPE, EnqueueingType.IGNORE.name());
    persistMessage(message2);
    ok = procThread.addToQueue(message2);

    Assertions.assertEquals(2, procThread.getQSize());
    Assertions.assertTrue(ok);

    // process all messages
    for (int i = 0; i < 2; i++) {
      procThread.processOneMessage();
      procThread.clearLastMsgCache();
    }

    TimeUnit.MILLISECONDS.sleep(200);
    Assertions.assertEquals(0, procThread.getQSize());

    List<Message> messages = messagePersister.findForMe(null);
    int active = 0;
    int deleted = 0;
    for (Message message : messages) {
      String state = message.getProperty(MessageFields.status.name());
      active = state != null && state.equals(ActivityStatus.a.name()) ? active + 1 : active;
      deleted = state == null || state.equals(ActivityStatus.d.name()) ? deleted + 1 : deleted;
      messagePersister.delete(message);
    }
    TimeUnit.MILLISECONDS.sleep(200);
    Assertions.assertEquals(1, active);
    Assertions.assertEquals(1, deleted);
    Assertions.assertEquals(1L, procThread.getDiscardedDueToEnqueueType());

    procThread.stopMe();
  }

  @Test
  void testEnqueueTypeIgnoreTwoQueues() throws Exception {
    SchedulerProcessorThread procThread = new SchedulerProcessorThread(schedulerProcessor, 0, schedulerModuleMetricReporter);
    String queueid = "Q1";
    String queueid2 = "Q2";

    Message message1 = createMessage();
    message1.setProperty(MetaData.SCHEDULE_NAME, schedule.getName());
    message1.setVehicleID("10");
    message1.setProperty(MetaData.SCHEDULE_QUEUE_ID, queueid);
    message1.setProperty(MetaData.SCHEDULE_ENQUEUEING_TYPE, EnqueueingType.IGNORE.name());
    persistMessage(message1);
    boolean ok = procThread.addToQueue(message1);

    Assertions.assertEquals(1, procThread.getQSize());
    Assertions.assertTrue(ok);

    Message message2 = createMessage();
    message2.setProperty(MetaData.SCHEDULE_NAME, schedule.getName());
    message2.setVehicleID("10");
    message2.setProperty(MetaData.SCHEDULE_QUEUE_ID, queueid2);
    message2.setProperty(MetaData.SCHEDULE_ENQUEUEING_TYPE, EnqueueingType.IGNORE.name());
    persistMessage(message2);
    ok = procThread.addToQueue(message2);

    Assertions.assertEquals(2, procThread.getQSize());
    Assertions.assertTrue(ok);

    Message message3 = createMessage();
    message3.setProperty(MetaData.SCHEDULE_NAME, schedule.getName());
    message3.setVehicleID("10");
    message3.setProperty(MetaData.SCHEDULE_QUEUE_ID, queueid);
    message3.setProperty(MetaData.SCHEDULE_ENQUEUEING_TYPE, EnqueueingType.IGNORE.name());
    persistMessage(message3);
    ok = procThread.addToQueue(message3);

    Assertions.assertEquals(3, procThread.getQSize());
    Assertions.assertTrue(ok);

    // process all messages
    for (int i = 0; i < 3; i++) {
      procThread.processOneMessage();
      procThread.clearLastMsgCache();
    }

    TimeUnit.MILLISECONDS.sleep(200);
    Assertions.assertEquals(0, procThread.getQSize());

    List<Message> messages = messagePersister.findForMe(null);
    int active = 0;
    int deleted = 0;
    for (Message message : messages) {
      String state = message.getProperty(MessageFields.status.name());
      active = state != null && state.equals(ActivityStatus.a.name()) ? active + 1 : active;
      deleted = state == null || state.equals(ActivityStatus.d.name()) ? deleted + 1 : deleted;
      messagePersister.delete(message);
    }
    TimeUnit.MILLISECONDS.sleep(200);
    Assertions.assertEquals(2, active);
    Assertions.assertEquals(1, deleted);
    Assertions.assertEquals(1L, procThread.getDiscardedDueToEnqueueType());

    procThread.stopMe();
  }

  @Test
  void testEnqueueTypeImmediate() throws Exception {
    SchedulerProcessorThread procThread = new SchedulerProcessorThread(schedulerProcessor, 0, schedulerModuleMetricReporter);
    String serviceid = "SERVICEID_1";
    String queueid = "Q1";

    for (int i = 0; i < 10; i++) {
      Message message = createMessage();
      message.setProperty(MetaData.SCHEDULE_NAME, schedule.getName());
      message.setMessageId(null);
      message.setVehicleID("10");
      message.setProperty(MetaData.SCHEDULE_ENQUEUEING_TYPE, EnqueueingType.NORMAL.name());
      message.setProperty(MetaData.SERVICE_ID, serviceid);
      message.setProperty(MetaData.SCHEDULE_QUEUE_ID, queueid);
      persistMessage(message);
      procThread.addToQueue(message);
    }

    Message message = createMessage();
    message.setProperty(MetaData.SCHEDULE_NAME, schedule.getName());
    message.setMessageId(null);
    message.setVehicleID("10");
    message.setProperty(MetaData.SCHEDULE_ENQUEUEING_TYPE, EnqueueingType.IMMEDIATE.name());
    message.setProperty(MetaData.SERVICE_ID, serviceid);
    message.setProperty(MetaData.SCHEDULE_QUEUE_ID, queueid);
    persistMessage(message);
    boolean ok = procThread.addToQueue(message);

    Assertions.assertEquals(11, procThread.getQSize());
    Assertions.assertTrue(ok);

    int rounds = 0;
    while ((procThread.getQSize() > 0) && rounds++ < 20) {
      procThread.processOneMessage();
      procThread.clearLastMsgCache();
      TimeUnit.MILLISECONDS.sleep(10);
    }

    TimeUnit.MILLISECONDS.sleep(100);
    Assertions.assertEquals(0, procThread.getQSize());

    Map<String, Map<Long, String>> map = procThread.findActiveMsgIdsNowForDevice(message, true);

    Set<String> set = makeIdSet(map);

    Assertions.assertTrue(set.contains(message.getMessageId()));

    map = procThread.findActiveMsgIdsNowForDevice(message, true);
    set = makeIdSet(map);
    Assertions.assertEquals(0L, procThread.getDiscardedDueToEnqueueType());

    List<Message> list = messagePersister.findForMe(null);

    int active = 0;
    int delete = 0;
    for (Message tmp : list) {
      String state = tmp.getProperty(MessageFields.status.name());
      active = state != null && state.equals(ActivityStatus.a.name()) ? active + 1 : active;
      delete = state == null || state.equals(ActivityStatus.d.name()) ? delete + 1 : delete;
      messagePersister.delete(tmp);
    }

    Assertions.assertEquals(11, active);
    Assertions.assertEquals(0, delete);
    Assertions.assertEquals(11, procThread.getProcessed());
    Assertions.assertTrue(procThread.getCappedMessageCount() > 0);

    procThread.stopMe();
  }

  @Test
  void testEnqueueTypeNormal() throws Exception {
    SchedulerProcessorThread procThread = new SchedulerProcessorThread(schedulerProcessor, 0, schedulerModuleMetricReporter);

    Message message1 = createMessage();
    message1.setProperty(MetaData.SCHEDULE_NAME, schedule.getName());
    message1.setVehicleID("10");
    message1.setProperty(MetaData.SCHEDULE_ENQUEUEING_TYPE, EnqueueingType.NORMAL.name());
    persistMessage(message1);
    boolean ok = procThread.addToQueue(message1);

    Message message2 = createMessage();
    message2.setProperty(MetaData.SCHEDULE_NAME, schedule.getName());
    message2.setVehicleID("10");
    message2.setProperty(MetaData.SCHEDULE_ENQUEUEING_TYPE, EnqueueingType.NORMAL.name());
    persistMessage(message2);
    ok = procThread.addToQueue(message2);

    Assertions.assertEquals(2, procThread.getQSize());
    Assertions.assertTrue(ok);

    // process both messages
    procThread.processOneMessage();
    procThread.clearLastMsgCache();
    Assertions.assertEquals(1, procThread.getQSize());
    TimeUnit.MILLISECONDS.sleep(200);
    procThread.processOneMessage();
    procThread.clearLastMsgCache();
    Assertions.assertEquals(0, procThread.getQSize());

    Map<String, Map<Long, String>> map = procThread.findActiveMsgIdsNowForDevice(message1, true);

    Set<String> set = makeIdSet(map);
    Assertions.assertTrue(set.contains(message1.getMessageId()));

    // ack msg
    messagePersister.updateForAck(message1, ActivityStatus.d);
    procThread.flagMsgFinished(message1);

    map = procThread.findActiveMsgIdsNowForDevice(message1, true);
    set = makeIdSet(map);
    Assertions.assertEquals(1, set.size());
    Assertions.assertEquals(0L, procThread.getDiscardedDueToEnqueueType());

    procThread.stopMe();
  }

  @Test
  void testEnqueueTypeOverride() throws Exception {
    SchedulerProcessorThread procThread = new SchedulerProcessorThread(schedulerProcessor, 0, schedulerModuleMetricReporter);
    String serviceid = "SERVICEID_1";
    String queueid = "Q1";

    Message message1 = createMessage();
    message1.setProperty(MetaData.SCHEDULE_NAME, schedule.getName());
    message1.setMessageId(null);
    message1.setVehicleID("10");
    message1.setProperty(MetaData.SCHEDULE_ENQUEUEING_TYPE, EnqueueingType.NORMAL.name());
    message1.setProperty(MetaData.SERVICE_ID, serviceid);
    message1.setProperty(MetaData.SCHEDULE_QUEUE_ID, queueid);
    persistMessage(message1);
    boolean ok = procThread.addToQueue(message1);

    Message message2 = createMessage();
    message2.setProperty(MetaData.SCHEDULE_NAME, schedule.getName());
    message2.setMessageId(null);
    message2.setVehicleID("10");
    message2.setProperty(MetaData.SCHEDULE_ENQUEUEING_TYPE, EnqueueingType.OVERRIDE.name());
    message2.setProperty(MetaData.SERVICE_ID, serviceid);
    message2.setProperty(MetaData.SCHEDULE_QUEUE_ID, queueid);
    persistMessage(message2);
    ok = procThread.addToQueue(message2);

    Assertions.assertEquals(2, procThread.getQSize());
    Assertions.assertTrue(ok);

    for (int i = 0; i < 2; i++) {
      procThread.processOneMessage();
      procThread.clearLastMsgCache();
    }

    TimeUnit.MILLISECONDS.sleep(200);
    Assertions.assertEquals(0, procThread.getQSize());

    Map<String, Map<Long, String>> map = procThread.findActiveMsgIdsNowForDevice(message1, true);

    Set<String> set = makeIdSet(map);

    Assertions.assertFalse(set.contains(message1.getMessageId()));
    Assertions.assertTrue(set.contains(message2.getMessageId()));

    procThread.flagMsgFinished(message1);

    map = procThread.findActiveMsgIdsNowForDevice(message1, true);
    set = makeIdSet(map);
    Assertions.assertEquals(1, set.size());
    Assertions.assertEquals(1L, procThread.getDiscardedDueToEnqueueType());

    List<Message> messages = messagePersister.findForMe(null);

    int active = 0;
    int delete = 0;
    for (Message message : messages) {
      String state = message.getProperty(MessageFields.status.name());
      active = state != null && state.equals(ActivityStatus.a.name()) ? active + 1 : active;
      delete = state == null || state.equals(ActivityStatus.d.name()) ? delete + 1 : delete;
      messagePersister.delete(message);
    }

    Assertions.assertEquals(1, active);
    Assertions.assertEquals(1, delete);

    procThread.stopMe();
  }

  @Test
  void testEnqueueTypeOverrideImmediate() throws Exception {
    SchedulerProcessorThread procThread = new SchedulerProcessorThread(schedulerProcessor, 0, schedulerModuleMetricReporter);
    String serviceid = "SERVICEID_1";
    String queueid = "Q1";

    for (int i = 0; i < 10; i++) {
      Message message = createMessage();
      message.setProperty(MetaData.SCHEDULE_NAME, schedule.getName());
      message.setMessageId(null);
      message.setVehicleID("10");
      message.setProperty(MetaData.SCHEDULE_ENQUEUEING_TYPE, EnqueueingType.NORMAL.name());
      message.setProperty(MetaData.SERVICE_ID, serviceid);
      message.setProperty(MetaData.SCHEDULE_QUEUE_ID, queueid);
      persistMessage(message);
      procThread.addToQueue(message);
    }

    Message message = createMessage();
    message.setProperty(MetaData.SCHEDULE_NAME, schedule.getName());
    message.setMessageId(null);
    message.setVehicleID("10");
    message.setProperty(MetaData.SCHEDULE_ENQUEUEING_TYPE, EnqueueingType.OVERRIDE_IMMEDIATE.name());
    message.setProperty(MetaData.SERVICE_ID, serviceid);
    message.setProperty(MetaData.SCHEDULE_QUEUE_ID, queueid);
    persistMessage(message);
    boolean ok = procThread.addToQueue(message);

    Assertions.assertEquals(11, procThread.getQSize());
    Assertions.assertTrue(ok);

    int rounds = 0;
    while ((procThread.getQSize() > 0) && rounds++ < 20) {
      procThread.processOneMessage();
      procThread.clearLastMsgCache();
      TimeUnit.MILLISECONDS.sleep(10);
    }

    TimeUnit.MILLISECONDS.sleep(10);
    Assertions.assertEquals(0, procThread.getQSize());

    Map<String, Map<Long, String>> map = procThread.findActiveMsgIdsNowForDevice(message, true);

    Set<String> set = makeIdSet(map);

    Assertions.assertEquals(1, set.size()); // the rest deleted
    Assertions.assertTrue(set.contains(message.getMessageId()));

    map = procThread.findActiveMsgIdsNowForDevice(message, true);
    set = makeIdSet(map);
    Assertions.assertEquals(1, set.size());
    Assertions.assertEquals(10L, procThread.getDiscardedDueToEnqueueType());

    List<Message> list = messagePersister.findForMe(null);

    int active = 0;
    int delete = 0;
    for (Message tmp : list) {
      String state = tmp.getProperty(MessageFields.status.name());
      active = state != null && state.equals(ActivityStatus.a.name()) ? active + 1 : active;
      delete = state == null || state.equals(ActivityStatus.d.name()) ? delete + 1 : delete;
      messagePersister.delete(tmp);
    }

    Assertions.assertEquals(1, active);
    Assertions.assertEquals(10, delete);
    Assertions.assertTrue(procThread.getCappedMessageCount() > 0);
    Assertions.assertEquals(11, procThread.getProcessed());
    Assertions.assertEquals(message.getMessageId(), procThread.getCappedMsgList(message).peek());

    procThread.stopMe();
  }

  @Test
  void testEnqueueTypeOverrideNoQueueId() throws Exception {
    SchedulerProcessorThread procThread = new SchedulerProcessorThread(schedulerProcessor, 0, schedulerModuleMetricReporter);

    Message message1 = createMessage();
    message1.setProperty(MetaData.SCHEDULE_NAME, schedule.getName());
    message1.setMessageId(null);
    message1.setVehicleID("10");
    message1.setProperty(MetaData.SCHEDULE_ENQUEUEING_TYPE, EnqueueingType.NORMAL.name());
    persistMessage(message1);
    boolean ok = procThread.addToQueue(message1);

    Message message2 = createMessage();
    message2.setProperty(MetaData.SCHEDULE_NAME, schedule.getName());
    message2.setMessageId(null);
    message2.setVehicleID("10");
    message2.setProperty(MetaData.SCHEDULE_ENQUEUEING_TYPE, EnqueueingType.OVERRIDE.name());
    persistMessage(message2);
    ok = procThread.addToQueue(message2);

    Assertions.assertEquals(2, procThread.getQSize());
    Assertions.assertTrue(ok);

    for (int i = 0; i < 2; i++) {
      procThread.processOneMessage();
      procThread.clearLastMsgCache();
    }
    TimeUnit.MILLISECONDS.sleep(10);

    // i testet blir det partitionsförflytting

    TimeUnit.MILLISECONDS.sleep(200);
    Assertions.assertEquals(0, procThread.getQSize());

    Map<String, Map<Long, String>> map = procThread.findActiveMsgIdsNowForDevice(message1, true);

    Set<String> set = makeIdSet(map);
    Assertions.assertFalse(set.contains(message1.getMessageId()));
    Assertions.assertTrue(set.contains(message2.getMessageId()));

    procThread.flagMsgFinished(message1);

    map = procThread.findActiveMsgIdsNowForDevice(message1, true);
    set = makeIdSet(map);
    Assertions.assertEquals(1, set.size());
    Assertions.assertEquals(1L, procThread.getDiscardedDueToEnqueueType());

    List<Message> messages = messagePersister.findForMe(null);

    int active = 0;
    int delete = 0;
    for (Message message : messages) {
      String state = message.getProperty(MessageFields.status.name());
      active = state != null && state.equals(ActivityStatus.a.name()) ? active + 1 : active;
      delete = state == null || state.equals(ActivityStatus.d.name()) ? delete + 1 : delete;
      messagePersister.delete(message);
    }

    Assertions.assertEquals(1, active);
    Assertions.assertEquals(1, delete);

    procThread.stopMe();
  }

  @Test
  void testEnqueueTypeOverrideTwoQueues() throws Exception {
    SchedulerProcessorThread procThread = new SchedulerProcessorThread(schedulerProcessor, 0, schedulerModuleMetricReporter);
    String queueid = "Q1";
    String queueid2 = "Q2";
    String vid = "10" + Math.random();

    Message message1 = createMessage();
    message1.setProperty(MetaData.SCHEDULE_NAME, schedule.getName());
    message1.setVehicleID(vid);
    message1.setProperty(MetaData.SCHEDULE_ENQUEUEING_TYPE, EnqueueingType.NORMAL.name());
    message1.setProperty(MetaData.SCHEDULE_QUEUE_ID, queueid);
    persistMessage(message1);
    boolean ok = procThread.addToQueue(message1);

    Message message2 = createMessage();
    message2.setProperty(MetaData.SCHEDULE_NAME, schedule.getName());
    message2.setVehicleID(vid);
    message2.setProperty(MetaData.SCHEDULE_ENQUEUEING_TYPE, EnqueueingType.NORMAL.name());
    message2.setProperty(MetaData.SCHEDULE_QUEUE_ID, queueid);
    persistMessage(message2);
    ok = procThread.addToQueue(message2);

    Message message3 = createMessage();
    message3.setProperty(MetaData.SCHEDULE_NAME, schedule.getName());
    message3.setVehicleID(vid);
    message3.setProperty(MetaData.SCHEDULE_ENQUEUEING_TYPE, EnqueueingType.OVERRIDE.name());
    message3.setProperty(MetaData.SCHEDULE_QUEUE_ID, queueid);
    persistMessage(message3);
    ok = procThread.addToQueue(message3);

    Message message4 = createMessage();
    message4.setProperty(MetaData.SCHEDULE_NAME, schedule.getName());
    message4.setVehicleID(vid);
    message4.setProperty(MetaData.SCHEDULE_ENQUEUEING_TYPE, EnqueueingType.NORMAL.name());
    message4.setProperty(MetaData.SCHEDULE_QUEUE_ID, queueid2);
    persistMessage(message4);
    ok = procThread.addToQueue(message4);

    Assertions.assertEquals(4, procThread.getQSize());
    Assertions.assertTrue(ok);

    for (int i = 0; i < 4; i++) {
      procThread.processOneMessage();
      procThread.clearLastMsgCache();
    }

    TimeUnit.MILLISECONDS.sleep(10);
    Assertions.assertEquals(0, procThread.getQSize());

    Map<String, Map<Long, String>> map = procThread.findActiveMsgIdsNowForDevice(message1, true);

    Set<String> set = makeIdSet(map);

    Assertions.assertFalse(set.contains(message1.getMessageId()));
    Assertions.assertTrue(set.contains(message3.getMessageId()));

    procThread.flagMsgFinished(message1);

    map = procThread.findActiveMsgIdsNowForDevice(message1, true);
    set = makeIdSet(map);
    Assertions.assertEquals(2, set.size());
    Assertions.assertEquals(2L, procThread.getDiscardedDueToEnqueueType());

    List<Message> messages = messagePersister.findForMe(null);

    int active = 0;
    int delete = 0;
    for (Message message : messages) {
      String state = message.getProperty(MessageFields.status.name());
      active = state != null && state.equals(ActivityStatus.a.name()) ? active + 1 : active;
      delete = state == null || state.equals(ActivityStatus.d.name()) ? delete + 1 : delete;
      messagePersister.delete(message);
    }

    Assertions.assertEquals(2, active);
    Assertions.assertEquals(2, delete);

    procThread.stopMe();
  }

  @Test
  void testEqualsInQ() {
    SchedulerProcessorThread procThread = new SchedulerProcessorThread(schedulerProcessor, 0, schedulerModuleMetricReporter);

    String tmpname = "Nisse_very-high";
    Message message = createMessage();
    message.setProperty(MetaData.SCHEDULE_NAME, tmpname);
    boolean ok = procThread.addToQueue(message);

    Assertions.assertEquals(1, procThread.getQSize());
    Assertions.assertTrue(ok);

    ok = procThread.addToQueue(message);
    Assertions.assertEquals(1, procThread.getQSize());
    Assertions.assertTrue(ok);

    Message msg2 = createMessage();
    msg2.setProperty(MetaData.SCHEDULE_NAME, tmpname);
    ok = procThread.addToQueue(msg2);
    Assertions.assertEquals(2, procThread.getQSize());
    Assertions.assertTrue(ok);
  }

  /**
   * Send <br>
   * 1. Transition 1 (en repeat) <br>
   * 2. Transition 2 (två repeat) <br>
   * 3. Transition 2 <br>
   * 4. Transition 1 (en repeat) <br>
   * 5. ingen Transition <br>
   */
  @Test
  void testProcess2TransitionsRepeat() throws Exception {
    transition2 = SchedulerTestUtil.createTransition(schedule, 1, transition);
    transition2.setName("2-" + transition2.getName());
    transition2.setTimeOutRepeat(2);
    transition2.setMaxNumberOfTriesPerMsg(10);
    transition2 = SchedulePersistanceFactory.makeTransitionPersister().persist(transition2);
    SchedulerProcessorThread procThread = new SchedulerProcessorThread(schedulerProcessor, 0, schedulerModuleMetricReporter);

    Message message = createMessage();
    message.setMessageId("Sonny" + Math.random());
    boolean ok = procThread.addToQueue(message);
    Assertions.assertTrue(ok);
    // first transition
    procThread.processOneMessage();
    procThread.clearLastMsgCache();
    procThread.clearLastMsgCache();
    TimeUnit.MILLISECONDS.sleep(10); // time out transition
    Assertions.assertEquals(transition.getName(), message.getProperty(MessageFields.lastTransitionName.name()));

    // then second transition twice
    for (int k = 0; k < 2; k++) {
      message.setProperty(MessageFields.removeTime.name(), "" + (System.currentTimeMillis() + 3_600_000));
      processOneMsgOnce(procThread, message);
      procThread.clearLastMsgCache();
      TimeUnit.MILLISECONDS.sleep(10); // time out transition
    }
    Assertions.assertEquals(transition2.getName(), message.getProperty(MessageFields.lastTransitionName.name()));

    // and again to wait
    message.setProperty(MessageFields.removeTime.name(), "" + (System.currentTimeMillis() + 3_600_000));
    processOneMsgOnce(procThread, message);
    procThread.clearLastMsgCache();
    TimeUnit.MILLISECONDS.sleep(10); // time out transition
    Assertions.assertEquals(transition2.getName(), message.getProperty(MessageFields.lastTransitionName.name()));

    Assertions.assertEquals(0, procThread.getQSize());
    Assertions.assertEquals(0, procThread.getSkippedTransitionsMaxTries());
    Assertions.assertEquals(2, procThread.getSkippedTransitionsRepeat()); // one for each transition
    Assertions.assertEquals(1, procThread.getProcessedToWait()); // timeout at end
    Assertions.assertEquals(4, procThread.getProcessed());
    Assertions.assertEquals(3, procThread.getExecTransitionsTimes());
    Assertions.assertEquals(0, procThread.getNoUsableTransition());
  }

  @Test
  void testProcessBestMatchScheduleHigh() throws Exception {
    SchedulerProcessorThread procThread = new SchedulerProcessorThread(schedulerProcessor, 0, schedulerModuleMetricReporter);

    String tmpName = "Nisse_high";
    Message message = createMessage();
    message.setProperty(MetaData.SCHEDULE_NAME, tmpName);
    boolean ok = procThread.addToQueue(message);

    Assertions.assertEquals(1, procThread.getQSize());
    Assertions.assertTrue(ok);

    procThread.processOneMessage();
    procThread.clearLastMsgCache();
    TimeUnit.MILLISECONDS.sleep(10);

    Assertions.assertEquals(0, procThread.getQSize());
    Assertions.assertNotEquals(message.getProperty(MetaData.SCHEDULE_NAME), tmpName);
    Assertions.assertTrue(message.getProperty(MetaData.SCHEDULE_NAME).endsWith("high"));
  }

  @Test
  void testProcessBestMatchScheduleNotSet() throws Exception {
    SchedulerProcessorThread procThread = new SchedulerProcessorThread(schedulerProcessor, 0, schedulerModuleMetricReporter);

    String tmpName = "";
    Message message = createMessage();
    message.setProperty(MetaData.SCHEDULE_NAME, null);
    boolean ok = procThread.addToQueue(message);

    Assertions.assertEquals(1, procThread.getQSize());
    Assertions.assertTrue(ok);

    procThread.processOneMessage();
    procThread.clearLastMsgCache();
    TimeUnit.MILLISECONDS.sleep(10);

    Assertions.assertEquals(0, procThread.getQSize());
    Assertions.assertNotEquals(message.getProperty(MetaData.SCHEDULE_NAME), tmpName);
    Assertions.assertTrue(message.getProperty(MetaData.SCHEDULE_NAME).endsWith("common_normal"));
  }

  @Test
  void testProcessBestMatchScheduleVeryHigh() throws Exception {
    SchedulerProcessorThread procThread = new SchedulerProcessorThread(schedulerProcessor, 0, schedulerModuleMetricReporter);

    String tmpName = "Nisse_very-high";
    Message message = createMessage();
    message.setProperty(MetaData.SCHEDULE_NAME, tmpName);
    boolean ok = procThread.addToQueue(message);

    Assertions.assertEquals(1, procThread.getQSize());
    Assertions.assertTrue(ok);

    procThread.processOneMessage();
    procThread.clearLastMsgCache();
    TimeUnit.MILLISECONDS.sleep(200);

    Assertions.assertEquals(0, procThread.getQSize());
    Assertions.assertNotEquals(message.getProperty(MetaData.SCHEDULE_NAME), tmpName);
    Assertions.assertTrue(message.getProperty(MetaData.SCHEDULE_NAME).endsWith("very-high"));
  }

  @Test
  void testProcessDefaultSchedule() throws Exception {
    SchedulerProcessorThread procThread = new SchedulerProcessorThread(schedulerProcessor, 0, schedulerModuleMetricReporter);

    Message message = createMessage();
    message.setProperty(MetaData.SCHEDULE_NAME, "qwerty");
    boolean ok = procThread.addToQueue(message);

    Assertions.assertEquals(1, procThread.getQSize());
    Assertions.assertTrue(ok);

    procThread.processOneMessage();
    procThread.clearLastMsgCache();
    TimeUnit.MILLISECONDS.sleep(10);

    Assertions.assertEquals(0, procThread.getQSize());
    Assertions.assertEquals("common_normal", message.getProperty(MetaData.SCHEDULE_NAME));
  }

  @Test
  void testProcessMaxRepeatSameTransition() throws Exception {
    transition.setMaxNumberOfTriesPerMsg(100);
    transition.setTimeOutRepeat(1);
    transition = SchedulePersistanceFactory.makeTransitionPersister().persist(transition);

    SchedulerProcessorThread procThread = new SchedulerProcessorThread(schedulerProcessor, 0, schedulerModuleMetricReporter);

    Message message = createMessage();
    message.setMessageId("Sonny" + Math.random());
    boolean ok = procThread.addToQueue(message);
    Assertions.assertTrue(ok);
    procThread.processOneMessage();
    procThread.clearLastMsgCache();

    for (int i = 0; i < 19; i++) {
      processOneMsgOnce(procThread, message);
    }

    Assertions.assertEquals(0, procThread.getQSize());
    Assertions.assertEquals(0, procThread.getSkippedTransitionsMaxTries());
    Assertions.assertEquals(19, procThread.getSkippedTransitionsRepeat());
  }

  @Test
  void testProcessMaxTimes() throws Exception {
    transition.setTimeOutRepeat(10);
    transition = SchedulePersistanceFactory.makeTransitionPersister().persist(transition);

    SchedulerProcessorThread procThread = new SchedulerProcessorThread(schedulerProcessor, 0, schedulerModuleMetricReporter);

    Message message = createMessage();
    message.setMessageId("Sonny" + Math.random());
    boolean ok = procThread.addToQueue(message);
    Assertions.assertTrue(ok);
    procThread.processOneMessage();
    procThread.clearLastMsgCache();

    for (int i = 0; i < 4; i++) {
      processOneMsgOnce(procThread, message);
    }

    Assertions.assertEquals(0, procThread.getQSize());
    Assertions.assertEquals(3, procThread.getSkippedTransitionsMaxTries());
    Assertions.assertEquals(5, procThread.getProcessed());
    Assertions.assertEquals(0, procThread.getProcessedToWait());
    Assertions.assertEquals(3, procThread.getNoUsableTransition());
  }

  @Test
  void testProcessMaxTries() throws Exception {
    transition.setTimeOutRepeat(20);
    transition.setMaxNumberOfTriesPerMsg(2);
    transition = SchedulePersistanceFactory.makeTransitionPersister().persist(transition);

    SchedulerProcessorThread procThread = new SchedulerProcessorThread(schedulerProcessor, 0, schedulerModuleMetricReporter);

    Message message = createMessage();
    message.setMessageId("Sonny" + Math.random());
    boolean ok = procThread.addToQueue(message);
    Assertions.assertTrue(ok);
    procThread.processOneMessage();
    procThread.clearLastMsgCache();

    for (int i = 0; i < 2; i++) {
      message.getProperties().remove(MessageFields.lastTransitionName.name());
      processOneMsgOnce(procThread, message);
    }

    Assertions.assertEquals(2, procThread.getExecTransitionsTimes());
    Assertions.assertEquals(1, procThread.getSkippedTransitionsMaxTries());
    Assertions.assertEquals(3, procThread.getProcessed());
    Assertions.assertEquals(0, procThread.getProcessedToWait());
    Assertions.assertEquals(1, procThread.getNoUsableTransition());
  }

  @Test
  void testProcessMaxTriesSameTransition() throws Exception {
    transition.setTimeOutRepeat(10);
    transition = SchedulePersistanceFactory.makeTransitionPersister().persist(transition);

    SchedulerProcessorThread procThread = new SchedulerProcessorThread(schedulerProcessor, 0, schedulerModuleMetricReporter);

    Message message = createMessage();
    message.setMessageId("Sonny" + Math.random());
    boolean ok = procThread.addToQueue(message);
    Assertions.assertTrue(ok);
    procThread.processOneMessage();
    procThread.clearLastMsgCache();

    for (int i = 0; i < 20; i++) {
      processOneMsgOnce(procThread, message);
    }

    Assertions.assertEquals(0, procThread.getQSize());
    Assertions.assertEquals(19, procThread.getSkippedTransitionsMaxTries());
    Assertions.assertEquals(0, procThread.getSkippedTransitionsRepeat());
  }

  @Test
  void testProcessOne() throws Exception {
    SchedulerProcessorThread procThread = new SchedulerProcessorThread(schedulerProcessor, 0, schedulerModuleMetricReporter);

    Message message = createMessage();
    boolean ok = procThread.addToQueue(message);

    Assertions.assertEquals(1, procThread.getQSize());
    Assertions.assertTrue(ok);

    procThread.processOneMessage();
    procThread.clearLastMsgCache();

    TimeUnit.MILLISECONDS.sleep(10);

    Assertions.assertEquals(0, procThread.getQSize());
  }

  @Test
  void testProcessOneConnEstablishedDB() throws Exception {
    MockConfiguration mockConfiguration = MockConfiguration.getConfig();
    mockConfiguration.setPropertySpecific(mockConfiguration.getComponentShortName(), "notset.scheduler.workerthread.min_future_ms_for_connection_estbl", "1");
    mockConfiguration.setPropertySpecific(mockConfiguration.getComponentShortName(), "notset.scheduler.workerthread.thread.active_msg_cap", "5");

    transition2 = SchedulerTestUtil.createTransition(schedule, 1, transition);
    transition2.setName(2 + "-" + transition2.getName());
    transition2.setTimeOutRepeat(1);
    transition2.setMaxNumberOfTriesPerMsg(2);
    transition2.setConnEstablishedAllowed(true);
    transition2 = SchedulePersistanceFactory.makeTransitionPersister().persist(transition2);

    SchedulerProcessorThread procThread = new SchedulerProcessorThread(schedulerProcessor, 0, schedulerModuleMetricReporter);
    String id = "" + Math.random() * 1_000_000_000;

    // create message in db
    for (int i = 0; i < 3; i++) {
      Message message = createMessage();
      message.setVehicleID(id);
      persistMessage(message, true);
      procThread.addToQueue(message);
      procThread.processOneMessage();
    }
    procThread.clearLastMsgCache();

    Map<MessageFields, String> map = new EnumMap<>(MessageFields.class);
    map.put(MessageFields.status, ActivityStatus.a.name());
    map.put(MessageFields.vehicleID, id);
    List<Message> messages = messagePersister.findForMe(map);
    Assertions.assertEquals(3, messages.size(), "Did not find created messages ");
    Assertions.assertTrue(messages.get(0).getLongProperty(MessageFields.nextCheckTime.name()) > System.currentTimeMillis(), "Wrong nextCheckTime ");
    Assertions.assertTrue(messages.get(1).getLongProperty(MessageFields.nextCheckTime.name()) > System.currentTimeMillis(), "Wrong nextCheckTime ");
    Assertions.assertTrue(messages.get(2).getLongProperty(MessageFields.nextCheckTime.name()) > System.currentTimeMillis(), "Wrong nextCheckTime ");

    // run messages again
    for (Message message : messages) {
      procThread.addToQueue(message);
      procThread.processOneMessage();
    }
    procThread.clearLastMsgCache();

    Message message = createMessage();
    message.setVehicleID(id);
    message.setProperty(Type.CONNECTION_ESTABLISHED.name(), Type.CONNECTION_ESTABLISHED.name());
    boolean ok = procThread.addToQueue(message);

    Assertions.assertEquals(1, procThread.getQSize());
    Assertions.assertEquals(0, procThread.getConnectionEstablishedMsg());
    Assertions.assertTrue(ok);

    procThread.processOneMessage();
    procThread.clearLastMsgCache();

    Assertions.assertEquals(0, procThread.getQSize());
    Assertions.assertEquals(1, procThread.getConnectionEstablishedMsg());

    TimeUnit.MILLISECONDS.sleep(100);

    List<Message> msgs2 = messagePersister.findForMe(Collections.emptyMap());
    Assertions.assertEquals(3, msgs2.size(), "Did not find created messages ");

    Assertions.assertTrue(msgs2.get(0).getLongProperty(MessageFields.nextCheckTime.name()) < System.currentTimeMillis(), "Wrong nextCheckTime ");
  }

  @Test
  void testProcessOneConnEstablishedEmpty() throws Exception {
    SchedulerProcessorThread procThread = new SchedulerProcessorThread(schedulerProcessor, 0, schedulerModuleMetricReporter);

    Message message = createMessage();
    message.setProperty(Type.CONNECTION_ESTABLISHED.name(), Type.CONNECTION_ESTABLISHED.name());
    boolean ok = procThread.addToQueue(message);

    Assertions.assertEquals(1, procThread.getQSize());
    Assertions.assertEquals(0, procThread.getConnectionEstablishedMsg());
    Assertions.assertTrue(ok);

    procThread.processOneMessage();
    procThread.clearLastMsgCache();

    TimeUnit.MILLISECONDS.sleep(10);

    Assertions.assertEquals(0, procThread.getQSize());
    Assertions.assertEquals(1, procThread.getConnectionEstablishedMsg());
  }

  @Test
  void testProcessOneConnEstablishedWithRepeatMaxTwoTransitions() throws Exception {
    Message message = createMessage();
    message.setProperty(MessageFields.removeTime.name(), "" + (System.currentTimeMillis() + 3_600_000));

    transition2 = SchedulePersistanceFactory.makeTransitionPersister()
        .persist(SchedulerTestUtil.createTransition(schedule, 1, transition));
    SchedulerProcessorThread procThread = new SchedulerProcessorThread(schedulerProcessor, 0, schedulerModuleMetricReporter);

    Message messageInDb = createMessage();
    messageInDb.setVehicleID(messageInDb.getVehicleID());
    persistMessage(messageInDb, true);

    schedulerProcessor.addWorkerToList(procThread);

    Map<MessageFields, String> map = new EnumMap<>(MessageFields.class);
    map.put(MessageFields.status, ActivityStatus.a.name());
    map.put(MessageFields.vehicleID, messageInDb.getVehicleID());
    List<Message> msgs = messagePersister.findForMe(map);

    //
    // process messages first time
    //
    msgs.get(0)
        .setProperty(MessageFields.removeTime.name(), "" + (System.currentTimeMillis() + 3_600_000));

    procThread.addToQueue(msgs.get(0));
    procThread.processOneMessage();
    procThread.clearLastMsgCache();

    TimeUnit.MILLISECONDS.sleep(50);

    Assertions.assertEquals(1, procThread.getProcessed());
    Assertions.assertEquals(0, procThread.getSkippedTransitionsMaxTries());
    Assertions.assertEquals(0, procThread.getSkippedTransitionsRepeat());
    Assertions.assertEquals(1, procThread.getExecTransitionsTimes());

    message = createMessage();
    message.setProperty(MessageFields.removeTime.name(), "" + (System.currentTimeMillis() + 3_600_000));
    message.setVehicleID(messageInDb.getVehicleID());
    message.setProperty(Type.CONNECTION_ESTABLISHED.name(), Type.CONNECTION_ESTABLISHED.name());
    procThread.addToQueue(message);
    procThread.processOneMessage();
    procThread.clearLastMsgCache();

    // wait until finished
    TimeUnit.MILLISECONDS.sleep(10);

    Assertions.assertEquals(0, procThread.getQSize());
    Assertions.assertEquals(1, procThread.getConnectionEstablishedMsg());

    // first transition, no effect of conn established
    List<Message> msgs2 = messagePersister.findForMe(Collections.emptyMap());
    Assertions.assertTrue(msgs2.get(0).getLongProperty(MessageFields.nextCheckTime.name()) > System.currentTimeMillis(), "Wrong nextCheckTime ");

    //
    // process messages second time, first transition second send again
    //
    msgs2
        .get(0)
        .setProperty(MessageFields.removeTime.name(), "" + (System.currentTimeMillis() + 3_600_000));
    procThread.addToQueue(msgs2.get(0));
    procThread.processOneMessage();
    procThread.clearLastMsgCache();
    TimeUnit.MILLISECONDS.sleep(10);

    Assertions.assertEquals(transition.getName(), msgs.get(0).getProperty(MessageFields.lastTransitionName.name()));
    Assertions.assertEquals(2, procThread.getProcessed());
    Assertions.assertEquals(0, procThread.getSkippedTransitionsMaxTries());
    Assertions.assertEquals(0, procThread.getSkippedTransitionsRepeat());
    Assertions.assertEquals(2, procThread.getExecTransitionsTimes());

    //
    // Third time, second transition
    //
    message = createMessage();
    message.setVehicleID(messageInDb.getVehicleID());
    message.setProperty(Type.CONNECTION_ESTABLISHED.name(), Type.CONNECTION_ESTABLISHED.name());
    procThread.addToQueue(message);
    procThread.processOneMessage();
    procThread.clearLastMsgCache();

    // wait until finished
    TimeUnit.MILLISECONDS.sleep(10);

    Assertions.assertEquals(0, procThread.getQSize());

    List<Message> msgs3 = messagePersister.findForMe(Collections.emptyMap());

    // add messages again, as nextChecktime is
    msgs3
        .get(0)
        .setProperty(MessageFields.removeTime.name(), "" + (System.currentTimeMillis() + 3_600_000));
    procThread.addToQueue(msgs3.get(0));
    procThread.processOneMessage();
    procThread.clearLastMsgCache();
    TimeUnit.MILLISECONDS.sleep(10);

    Assertions.assertEquals(transition2.getName(), msgs3.get(0).getProperty(MessageFields.lastTransitionName.name()));
    Assertions.assertEquals(3, procThread.getProcessed());
    Assertions.assertEquals(1, procThread.getSkippedTransitionsMaxTries());
    Assertions.assertEquals(3, procThread.getExecTransitionsTimes());
    Assertions.assertEquals(0, procThread.getSkippedTransitionsRepeat());

    //
    // Fourth time, second transition send
    //
    message = createMessage();
    message.setVehicleID(messageInDb.getVehicleID());
    message.setProperty(Type.CONNECTION_ESTABLISHED.name(), Type.CONNECTION_ESTABLISHED.name());
    procThread.addToQueue(message);
    procThread.processOneMessage();
    procThread.clearLastMsgCache();

    // wait until finished
    TimeUnit.MILLISECONDS.sleep(10);

    Assertions.assertEquals(0, procThread.getQSize());

    List<Message> msgs4 = messagePersister.findForMe(Collections.emptyMap());

    // add messages again, as nextChecktime is
    msgs4
        .get(0)
        .setProperty(MessageFields.removeTime.name(), "" + (System.currentTimeMillis() + 3_600_000));
    procThread.addToQueue(msgs4.get(0));
    procThread.processOneMessage();
    procThread.clearLastMsgCache();
    TimeUnit.MILLISECONDS.sleep(10);

    Assertions.assertEquals(transition2.getName(), msgs4.get(0).getProperty(MessageFields.lastTransitionName.name()));
    Assertions.assertEquals(4, procThread.getProcessed());
    Assertions.assertEquals(2, procThread.getSkippedTransitionsMaxTries());
    Assertions.assertEquals(4, procThread.getExecTransitionsTimes());
    Assertions.assertEquals(0, procThread.getSkippedTransitionsRepeat());

    //
    // fifth time, will not be sent
    //
    message = createMessage();
    message.setVehicleID(messageInDb.getVehicleID());
    message.setProperty(Type.CONNECTION_ESTABLISHED.name(), Type.CONNECTION_ESTABLISHED.name());
    procThread.addToQueue(message);
    procThread.processOneMessage();
    procThread.clearLastMsgCache();

    // wait until finished
    TimeUnit.MILLISECONDS.sleep(10);

    Assertions.assertEquals(0, procThread.getQSize());

    List<Message> msgs5 = messagePersister.findForMe(Collections.emptyMap());

    // add messages again, as nextChecktime is
    msgs5
        .get(0)
        .setProperty(MessageFields.removeTime.name(), "" + (System.currentTimeMillis() + 3_600_000));
    procThread.addToQueue(msgs5.get(0));
    procThread.processOneMessage();
    procThread.clearLastMsgCache();
    TimeUnit.MILLISECONDS.sleep(10);

    Assertions.assertEquals(5, procThread.getProcessed());
    Assertions.assertEquals(4, procThread.getSkippedTransitionsMaxTries());
    Assertions.assertEquals(4, procThread.getExecTransitionsTimes());
    Assertions.assertEquals(0, procThread.getSkippedTransitionsRepeat());
  }

  @Test
  void testProcessSameTransitionFaulty() throws Exception {
    transition2 = SchedulerTestUtil.createTransition(schedule, 1, transition);
    transition2.setTimeOutRepeat(2);
    transition2.setMaxNumberOfTriesPerMsg(10);
    transition2 = SchedulePersistanceFactory.makeTransitionPersister().persist(transition2);
    SchedulerProcessorThread procThread = new SchedulerProcessorThread(schedulerProcessor, 0, schedulerModuleMetricReporter);

    Message message = createMessage();
    message.setMessageId("Sonny" + Math.random());
    boolean ok = procThread.addToQueue(message);
    Assertions.assertTrue(ok);
    procThread.processOneMessage();

    // then second transition three times
    for (int i = 0; i < 3; i++) {
      procThread.addToQueue(message);
      procThread.processOneMessage();
    }

    Assertions.assertEquals(0, procThread.getQSize());
    Assertions.assertEquals(0, procThread.getSkippedTransitionsMaxTries());
    Assertions.assertEquals(0, procThread.getSkippedTransitionsRepeat()); // one for each transition
    Assertions.assertEquals(0, procThread.getProcessedToWait()); // timeout at end
    Assertions.assertEquals(1, procThread.getProcessed());
    Assertions.assertEquals(0, procThread.getNoUsableTransition());
    Assertions.assertEquals(0, procThread.getSkippedTimeout());
    Assertions.assertEquals(1, procThread.getExecTransitionsTimes());
    Assertions.assertEquals(transition.getName(), message.getProperty(MessageFields.lastTransitionName.name()));
  }

  @Test
  void testProcessSameTransitionRepeat() throws Exception {
    transition2 = SchedulerTestUtil.createTransition(schedule, 1, transition);
    transition2.setTimeOutRepeat(2);
    transition2.setMaxNumberOfTriesPerMsg(10);
    transition2 = SchedulePersistanceFactory.makeTransitionPersister().persist(transition2);
    SchedulerProcessorThread procThread = new SchedulerProcessorThread(schedulerProcessor, 0, schedulerModuleMetricReporter);

    Message message = createMessage();
    message.setMessageId("Sonny" + Math.random());
    boolean ok = procThread.addToQueue(message);
    Assertions.assertTrue(ok);
    procThread.processOneMessage();
    procThread.clearLastMsgCache();
    procThread.clearLastMsgCache();

    // then second transition three times
    for (int k = 0; k < 3; k++) {
      processOneMsgOnce(procThread, message);
      procThread.clearLastMsgCache();
    }

    Assertions.assertEquals(0, procThread.getQSize());
    Assertions.assertEquals(0, procThread.getSkippedTransitionsMaxTries());
    Assertions.assertEquals(2, procThread.getSkippedTransitionsRepeat()); // one for each transition
    Assertions.assertEquals(1, procThread.getProcessedToWait()); // timeout at end
    Assertions.assertEquals(4, procThread.getProcessed());
    Assertions.assertEquals(0, procThread.getNoUsableTransition());
    Assertions.assertEquals(0, procThread.getSkippedTimeout());
    Assertions.assertEquals(3, procThread.getExecTransitionsTimes());
    Assertions.assertEquals(transition2.getName(), message.getProperty(MessageFields.lastTransitionName.name()));
  }

  @Test
  void testProcessSetSchedule() throws Exception {
    SchedulerProcessorThread procThread = new SchedulerProcessorThread(schedulerProcessor, 0, schedulerModuleMetricReporter);

    Message message = createMessage();
    message.setProperty(MetaData.SCHEDULE_NAME, schedule.getName());
    boolean ok = procThread.addToQueue(message);

    Assertions.assertEquals(1, procThread.getQSize());
    Assertions.assertTrue(ok);

    procThread.processOneMessage();
    procThread.clearLastMsgCache();
    TimeUnit.MILLISECONDS.sleep(10);

    Assertions.assertEquals(0, procThread.getQSize());
    Assertions.assertEquals(schedule.getName(), message.getProperty(MetaData.SCHEDULE_NAME));
  }

  @Test
  void testProcessTimeout() throws Exception {
    transition.setTimeOutRepeat(10);
    transition = SchedulePersistanceFactory.makeTransitionPersister().persist(transition);

    SchedulerProcessorThread procThread = new SchedulerProcessorThread(schedulerProcessor, 0, schedulerModuleMetricReporter);

    Message message1 = createMessage();
    message1.setProperty(MetaData.SCHEDULE_NAME, scheduleShortTimeout.getName());
    message1.setMessageId("Sonny" + Math.random());
    persistMessage(message1);
    boolean ok = procThread.addToQueue(message1);
    Assertions.assertTrue(ok);
    procThread.processOneMessage();
    procThread.clearLastMsgCache();
    TimeUnit.MILLISECONDS.sleep(10);
    long lastProcessed = 1;

    for (int i = 0; i < 2; i++) {
      message1.getProperties().remove(MessageFields.lastTransitionName.name());
      processOneMsgOnce(procThread, message1);
      int j = 0;
      while (procThread.getProcessed() < (lastProcessed + 1) && j++ < 10) {
        TimeUnit.MILLISECONDS.sleep(10);
      }
      lastProcessed++;
    }

    Assertions.assertEquals(0, procThread.getQSize());
    Assertions.assertEquals(2, procThread.getSkippedTimeout());
    Assertions.assertEquals(3, procThread.getProcessed());

    Message message2 = messagePersister.lookup(message1.getMessageId());
    Assertions.assertNull(message2.getProperty(MessageFields.status.name()));
  }

  @Test
  void testProcessToWait() throws Exception {
    transition.setTimeOutRepeat(1);
    transition = SchedulePersistanceFactory.makeTransitionPersister().persist(transition);

    SchedulerProcessorThread procThread = new SchedulerProcessorThread(schedulerProcessor, 0, schedulerModuleMetricReporter);

    Message message = createMessage();
    message.setMessageId("Sonny" + Math.random());
    boolean ok = procThread.addToQueue(message);
    Assertions.assertTrue(ok);
    procThread.processOneMessage();
    procThread.clearLastMsgCache();

    for (int i = 0; i < 4; i++) {
      message.getProperties().remove(MessageFields.lastTransitionName.name());
      processOneMsgOnce(procThread, message);
    }

    Assertions.assertEquals(1, procThread.getExecTransitionsTimes());
    Assertions.assertEquals(0, procThread.getSkippedTransitionsMaxTries());
    Assertions.assertEquals(5, procThread.getProcessed());
    Assertions.assertEquals(4, procThread.getProcessedToWait());
    Assertions.assertEquals(0, procThread.getNoUsableTransition());
  }

  @Test
  void testProcessToWait2() throws Exception {
    transition.setTimeOutRepeat(2);
    transition.setMaxNumberOfTriesPerMsg(5);
    transition = SchedulePersistanceFactory.makeTransitionPersister().persist(transition);

    SchedulerProcessorThread procThread = new SchedulerProcessorThread(schedulerProcessor, 0, schedulerModuleMetricReporter);

    Message message = createMessage();
    message.setMessageId("Sonny" + Math.random());
    boolean ok = procThread.addToQueue(message);
    Assertions.assertTrue(ok);
    procThread.processOneMessage();
    procThread.clearLastMsgCache();

    for (int i = 0; i < 4; i++) {
      message.getProperties().remove(MessageFields.lastTransitionName.name());
      processOneMsgOnce(procThread, message);
    }

    Assertions.assertEquals(2, procThread.getExecTransitionsTimes());
    Assertions.assertEquals(0, procThread.getSkippedTransitionsMaxTries());
    Assertions.assertEquals(5, procThread.getProcessed());
    Assertions.assertEquals(3, procThread.getProcessedToWait());
    Assertions.assertEquals(0, procThread.getNoUsableTransition());
  }

  @Test
  void testProcessTransitions() throws Exception {
    transition2 = SchedulePersistanceFactory.makeTransitionPersister()
        .persist(SchedulerTestUtil.createTransition(schedule, 1, transition));
    SchedulerProcessorThread procThread = new SchedulerProcessorThread(schedulerProcessor, 0, schedulerModuleMetricReporter);

    Message message = createMessage();
    message.setMessageId("Sonny" + Math.random());
    boolean ok = procThread.addToQueue(message);
    Assertions.assertTrue(ok);
    for (int i = 0; i < 3; i++) {
      procThread.processOneMessage();
      procThread.clearLastMsgCache();
    }
    TimeUnit.MILLISECONDS.sleep(10);

    Assertions.assertEquals(transition.getName(), message.getProperty(MessageFields.lastTransitionName.name()));

    procThread.addToQueue(message);
    for (int i = 0; i < 3; i++) {
      procThread.processOneMessage();
      procThread.clearLastMsgCache();
    }
    TimeUnit.MILLISECONDS.sleep(10);

    Assertions.assertEquals(0, procThread.getQSize());
    Assertions.assertEquals(0, procThread.getSkippedTransitionsMaxTries());
    Assertions.assertEquals(0, procThread.getProcessedToWait());
    Assertions.assertEquals(2, procThread.getProcessed());
    Assertions.assertEquals(transition2.getName(), message.getProperty(MessageFields.lastTransitionName.name()));
  }

  @Test
  void testRemoveOfOldCappedMessageThenSendNextMsg() throws Exception {
    int messagesSize = 2;
    String vid = "11111";

    MockConfiguration mockConfiguration = MockConfiguration.getConfig();
    mockConfiguration.setPropertySpecific(mockConfiguration.getComponentShortName(), "notset.scheduler.workerthread.thread.active_msg_cap", "1");

    SchedulerProcessorThread procThread = new SchedulerProcessorThread(schedulerProcessor, 0, schedulerModuleMetricReporter);
    schedulerProcessor.addWorkerToList(procThread);

    // Add messages
    List<String> ids = new ArrayList<>();
    for (int i = 0; i < messagesSize; i++) {
      Message message = createMessage();
      message.setProperty(MetaData.SCHEDULE_NAME, schedule.getName());
      message.setVehicleID(vid);
      message.setProperty(MetaData.SCHEDULE_ENQUEUEING_TYPE, EnqueueingType.NORMAL.name());
      persistMessage(message);
      procThread.addToQueue(message);
      ids.add(message.getMessageId());
      procThread.processOneMessage();
      procThread.clearLastMsgCache();
      TimeUnit.MILLISECONDS.sleep(10);
    }

    Assertions.assertEquals(0, procThread.getQSize());

    Message message = createMessage();
    message.setProperty(MetaData.SCHEDULE_NAME, schedule.getName());
    message.setVehicleID(vid);
    message.setProperty(MetaData.SCHEDULE_ENQUEUEING_TYPE, EnqueueingType.NORMAL.name());

    // get state
    Map<String, Map<Long, String>> map = procThread.findActiveMsgIdsNowForDevice(message, true);
    Set<String> set = makeIdSet(map);
    int in = 0;
    for (String id : ids) {
      in += set.contains(id) ? 1 : 0;
    }
    Assertions.assertEquals(messagesSize, in, "Wrong number of active messages");

    // check excess message is capped
    Assertions.assertEquals(ids.get(1), procThread.findNextActiveMsgsNotSentForMyQ(message, true).getMessageId(), "Wrong message first in Q ");
    map = procThread.findActiveMsgIdsNowForDevice(message, true);
    set = makeIdSet(map);
    Assertions.assertEquals(messagesSize, set.size());
    Assertions.assertEquals(1, procThread.getExecTransitionsTimes());
    Assertions.assertEquals(0L, procThread.getDiscardedDueToEnqueueType());
    Assertions.assertEquals(messagesSize, (int) procThread.getProcessed());
    Assertions.assertEquals(1, procThread.getCappedMessageCount(), "Wrong number of messages capped, should be 2-1=1");

    // try resending first one, it should be removed as it is to old (removetime < now).
    message.setMessageId(ids.get(0));
    message.setProperty(MessageFields.removeTime.name(), "" + (System.currentTimeMillis() - 1000 * 3600));
    processOneMsgOnce(procThread, message);
    TimeUnit.MILLISECONDS.sleep(10);

    Assertions.assertEquals(ids.get(1), procThread.findNextActiveMsgsNotSentAllQ(message).getMessageId(), "Should be 1 messages in Q ");
    Map<MessageFields, String> criMap = new EnumMap<>(MessageFields.class);
    criMap.put(MessageFields.status, ActivityStatus.a.name());
    List<Message> listActive = messagePersister.findForMe(criMap);
    Assertions.assertEquals(1, listActive.size());

    procThread.processOneMessage();
    procThread.clearLastMsgCache();

    // check one more is processed and discovered as too old
    // resend second message that has been capped
    Assertions.assertNull(procThread.findNextActiveMsgsNotSentAllQ(message), "Should be no messages in Q ");
    map = procThread.findActiveMsgIdsNowForDevice(message, true);
    set = makeIdSet(map);
    Assertions.assertEquals(1, set.size());
    Assertions.assertEquals(0L, procThread.getDiscardedDueToEnqueueType());
    Assertions.assertEquals(1, procThread.getSkippedTimeout());
    Assertions.assertEquals(2, procThread.getExecTransitionsTimes());
    Assertions.assertEquals((messagesSize + 2), (int) procThread.getProcessed());
    Assertions.assertEquals(1, procThread.getCappedMessageCount(), "Wrong number of messages capped, should be 1");

    procThread.stopMe();
  }

  /**
   * message that is capped when limit is one must be able to resent, i.e. the message that capps a vehicle can not be allowed to stop itself from resending.
   */
  @Test
  void testResendOfCappedMsg() throws Exception {
    int messagesSize = 2;
    String vid = "1111";

    MockConfiguration mockConfiguration = MockConfiguration.getConfig();
    mockConfiguration.setPropertySpecific(mockConfiguration.getComponentShortName(), "notset.scheduler.workerthread.thread.active_msg_cap", "1");

    SchedulerProcessorThread procThread = new SchedulerProcessorThread(schedulerProcessor, 0, schedulerModuleMetricReporter);

    // Add messages
    List<String> ids = new ArrayList<>();
    List<Message> messages = new ArrayList<>();
    for (int i = 0; i < messagesSize; i++) {
      Message message = createMessage();
      message.setProperty(MetaData.SCHEDULE_NAME, schedule.getName());
      message.setVehicleID(vid);
      message.setProperty(MetaData.SCHEDULE_ENQUEUEING_TYPE, EnqueueingType.NORMAL.name());
      procThread.addToQueue(message);
      ids.add(message.getMessageId());
      messages.add(message);
      TimeUnit.MILLISECONDS.sleep(10);
    }

    // make sure they are all processed
    int rounds = 0;
    while (rounds++ < messagesSize && (procThread.getProcessed() < messagesSize)) {
      procThread.processOneMessage();
      procThread.clearLastMsgCache();
      TimeUnit.MILLISECONDS.sleep(100);
    }
    TimeUnit.MILLISECONDS.sleep(100);

    Assertions.assertEquals(0, procThread.getQSize());

    Message message = createMessage();
    message.setProperty(MetaData.SCHEDULE_NAME, schedule.getName());
    message.setVehicleID(vid);
    message.setProperty(MetaData.SCHEDULE_ENQUEUEING_TYPE, EnqueueingType.NORMAL.name());

    // get state
    Map<String, Map<Long, String>> map = procThread.findActiveMsgIdsNowForDevice(message, true);
    Set<String> set = makeIdSet(map);
    int in = 0;
    for (String id : ids) {
      in += set.contains(id) ? 1 : 0;
    }
    Assertions.assertEquals(messagesSize, in, "Wrong number of active messages");

    // check excess message is capped
    Assertions.assertEquals(ids.get(1), procThread.findNextActiveMsgsNotSentForMyQ(message, true).getMessageId(), "Wrong message first in Q ");
    map = procThread.findActiveMsgIdsNowForDevice(message, true);
    set = makeIdSet(map);
    Assertions.assertEquals(messagesSize, set.size());
    Assertions.assertEquals(0L, procThread.getDiscardedDueToEnqueueType());
    Assertions.assertEquals(messagesSize, (int) procThread.getProcessed());
    Assertions.assertEquals(1, procThread.getCappedMessageCount(), "Wrong number of messages capped, should be 2-1=1");

    // try resending first one, it should not be stopped by the capping, as it is the message that
    // is active.
    message.setMessageId(ids.get(0));
    procThread.addToQueue(message);
    rounds = 0;
    procThread.processOneMessage();
    procThread.clearLastMsgCache();
    TimeUnit.MILLISECONDS.sleep(10);

    // check one more is sent
    Assertions.assertEquals(ids.get(1), procThread.findNextActiveMsgsNotSentAllQ(message).getMessageId(), "Wrong message first in Q ");
    map = procThread.findActiveMsgIdsNowForDevice(message, true);
    set = makeIdSet(map);
    Assertions.assertEquals((messagesSize), set.size());
    Assertions.assertEquals(0L, procThread.getDiscardedDueToEnqueueType());
    Assertions.assertEquals((messagesSize + 1), (int) procThread.getProcessed());
    Assertions.assertEquals(1, procThread.getCappedMessageCount(), "Wrong number of messages capped, should be 1");

    procThread.stopMe();
  }

  /**
   * message that is capped when limit is 5 must be able to resent, i.e. the messages that capps a vehicle can not be allowed to stop itself from resending.
   */
  @Test
  void testResendOfCappedMsgLimit5InOrder() throws Exception {
    int messagesSize = 10;
    String vid = "1119" + Math.random();

    MockConfiguration mockConfiguration = MockConfiguration.getConfig();
    mockConfiguration.setPropertySpecific(mockConfiguration.getComponentShortName(), "notset.scheduler.workerthread.thread.active_msg_cap", "5");

    SchedulerProcessorThread procThread = new SchedulerProcessorThread(schedulerProcessor, 0, schedulerModuleMetricReporter);

    // Add messages
    List<String> ids = new ArrayList<>();
    List<Message> messages = new ArrayList<>();
    for (int i = 0; i < messagesSize; i++) {
      Message message = createMessage();
      message.setMessageId(i + "-" + message.getMessageId());
      message.setProperty(MetaData.SCHEDULE_NAME, schedule.getName());
      message.setVehicleID(vid);
      message.setProperty(MetaData.SCHEDULE_ENQUEUEING_TYPE, EnqueueingType.NORMAL.name());
      persistMessage(message);
      procThread.addToQueue(message);
      ids.add(message.getMessageId());
      procThread.processOneMessage();
      messages.add(message);
      TimeUnit.MILLISECONDS.sleep(10);
    }

    Assertions.assertEquals(0, procThread.getQSize());

    Message message = createMessage();
    message.setProperty(MetaData.SCHEDULE_NAME, schedule.getName());
    message.setVehicleID(vid);
    message.setProperty(MetaData.SCHEDULE_ENQUEUEING_TYPE, EnqueueingType.NORMAL.name());

    // get state
    Map<String, Map<Long, String>> map = procThread.findActiveMsgIdsNowForDevice(message, true);
    Set<String> set = makeIdSet(map);
    int in = 0;
    for (String id : ids) {
      in += set.contains(id) ? 1 : 0;
    }
    Assertions.assertEquals(messagesSize, in, "Wrong number of active messages");

    // check excess message is capped
    Assertions.assertEquals(ids.get(5), procThread.findNextActiveMsgsNotSentForMyQ(message, true).getMessageId(),
        "Wrong message first in Q, should be message 6. ");
    map = procThread.findActiveMsgIdsNowForDevice(message, true);
    set = makeIdSet(map);
    Assertions.assertEquals(messagesSize, set.size());
    Assertions.assertEquals(0L, procThread.getDiscardedDueToEnqueueType());
    Assertions.assertEquals(messagesSize, (int) procThread.getProcessed());
    Assertions.assertEquals(5, (int) procThread.getExecTransitionsTimes());
    Assertions.assertEquals(5, procThread.getCappedMessageCount(), "Wrong number of messages capped, should be 10-5=5");
    Assertions.assertEquals(10, procThread.getCappedMsgList(message).size(), "Wrong number of messages in activeMessageCache, should be 10");

    // try resending first one, it should not be stopped by the capping, as it is one of the active
    // msgs,// but should be stopped by the lastSentMsgCache
    procThread.clearLastMsgCache(ids.get(0));
    message.setMessageId(ids.get(0));
    procThread.addToQueue(message);
    procThread.processOneMessage();
    TimeUnit.MILLISECONDS.sleep(10);

    // check no more is sent
    Assertions.assertEquals(ids.get(5), procThread.findNextActiveMsgsNotSentAllQ(message).getMessageId(), "Wrong message first in Q should be number 6 ");
    map = procThread.findActiveMsgIdsNowForDevice(message, true);
    set = makeIdSet(map);
    Assertions.assertEquals((messagesSize), set.size());
    Assertions.assertEquals(0L, procThread.getDiscardedDueToEnqueueType());
    Assertions.assertEquals((messagesSize), (int) procThread.getProcessed());
    Assertions.assertEquals(5, (int) procThread.getExecTransitionsTimes()); // 5
    Assertions.assertEquals(5, procThread.getCappedMessageCount(), "Wrong number of messages capped, should be 5");
    Assertions.assertEquals(10, procThread.getCappedMsgList(message).size(), "Wrong number of messages in activeMessageCache, should be 10");

    // try resending first one, it should not be stopped by the capping, as it is one of the active
    // msgs.
    // remove first from lastMsgSentCache, to simulate time passed
    procThread.clearLastMsgCache(ids.get(0));
    procThread.clearLastMsgSentCache(ids.get(0));
    message.setMessageId(ids.get(0));
    procThread.addToQueue(message);
    procThread.processOneMessage();
    TimeUnit.MILLISECONDS.sleep(10);

    // check one more is sent
    Assertions.assertEquals(ids.get(5), procThread.findNextActiveMsgsNotSentAllQ(message).getMessageId(), "Wrong message first in Q should be number 6 ");
    map = procThread.findActiveMsgIdsNowForDevice(message, true);
    set = makeIdSet(map);
    Assertions.assertEquals((messagesSize), set.size());
    Assertions.assertEquals(0L, procThread.getDiscardedDueToEnqueueType());
    Assertions.assertEquals((messagesSize + 1), (int) procThread.getProcessed());
    Assertions.assertEquals(6, (int) procThread.getExecTransitionsTimes()); // 5 + 1
    Assertions.assertEquals(5, procThread.getCappedMessageCount(), "Wrong number of messages capped, should be 5");
    Assertions.assertEquals(10, procThread.getCappedMsgList(message).size(), "Wrong number of messages in activeMessageCache, should be 10");

    Assertions.assertEquals(0, procThread.getQSize());

    int i = 0;
    // ack first msg, the first capped should then be queued for sending
    ackOneMessage(0, i++, messagesSize, procThread, messagePersister, ids, messages, message);

    // ack Second msg, the next capped should then be queued for sending
    ackOneMessage(1, i++, messagesSize, procThread, messagePersister, ids, messages, message);

    // ack third msg, the next capped should then be queued for sending
    ackOneMessage(2, i++, messagesSize, procThread, messagePersister, ids, messages, message);

    // ack fourth msg, the next capped should then be queued for sending
    ackOneMessage(3, i++, messagesSize, procThread, messagePersister, ids, messages, message);

    // ack fifth msg, the next capped should then be queued for sending
    ackOneMessage(4, i++, messagesSize, procThread, messagePersister, ids, messages, message);

    // ack 6'th msg
    ackOneMessage(5, i++, messagesSize, procThread, messagePersister, ids, messages, message);

    // ack 7'th msg
    ackOneMessage(6, i++, messagesSize, procThread, messagePersister, ids, messages, message);

    // ack 8'th msg
    ackOneMessage(7, i++, messagesSize, procThread, messagePersister, ids, messages, message);

    // ack 9'th msg
    ackOneMessage(8, i++, messagesSize, procThread, messagePersister, ids, messages, message);

    // ack 10'th msg
    ackOneMessage(9, i++, messagesSize, procThread, messagePersister, ids, messages, message);

    procThread.stopMe();
  }

  /**
   * message that is capped when limit is 5 must be able to resent, i.e. the messages that capps a vehicle can not be allowed to stop itself from resending.
   */
  @Test
  void testResendOfCappedMsgLimit5NotInOrder() throws Exception {
    int messagesSize = 10;
    String vid = "11199" + Math.random();

    MockConfiguration mockConfiguration = MockConfiguration.getConfig();
    mockConfiguration.setPropertySpecific(mockConfiguration.getComponentShortName(), "notset.scheduler.workerthread.thread.active_msg_cap", "5");

    SchedulerProcessorThread procThread = new SchedulerProcessorThread(schedulerProcessor, 0, schedulerModuleMetricReporter);

    // Add messages
    List<String> ids = new ArrayList<>();
    List<Message> messages = new ArrayList<>();
    for (int i = 0; i < messagesSize; i++) {
      Message message = createMessage();
      message.setMessageId(i + "-" + message.getMessageId());
      message.setProperty(MetaData.SCHEDULE_NAME, schedule.getName());
      message.setVehicleID(vid);
      message.setProperty(MetaData.SCHEDULE_ENQUEUEING_TYPE, EnqueueingType.NORMAL.name());
      persistMessage(message);
      procThread.addToQueue(message);
      ids.add(message.getMessageId());
      procThread.processOneMessage();
      messages.add(message);
      TimeUnit.MILLISECONDS.sleep(10);
    }

    Assertions.assertEquals(0, procThread.getQSize());

    Message message = createMessage();
    message.setProperty(MetaData.SCHEDULE_NAME, schedule.getName());
    message.setVehicleID(vid);
    message.setProperty(MetaData.SCHEDULE_ENQUEUEING_TYPE, EnqueueingType.NORMAL.name());

    // get state
    Map<String, Map<Long, String>> map = procThread.findActiveMsgIdsNowForDevice(message, true);
    Set<String> set = makeIdSet(map);
    int in = 0;
    for (String id : ids) {
      in += set.contains(id) ? 1 : 0;
    }
    Assertions.assertEquals(messagesSize, in, "Wrong number of active messages");

    // check excess message is capped
    Assertions.assertEquals(ids.get(5), procThread.findNextActiveMsgsNotSentForMyQ(message, true).getMessageId(),
        "Wrong message first in Q, should be message 6. ");
    map = procThread.findActiveMsgIdsNowForDevice(message, true);
    set = makeIdSet(map);
    Assertions.assertEquals(messagesSize, set.size());
    Assertions.assertEquals(0L, procThread.getDiscardedDueToEnqueueType());
    Assertions.assertEquals(messagesSize, (int) procThread.getProcessed());
    Assertions.assertEquals(5, (int) procThread.getExecTransitionsTimes());
    Assertions.assertEquals(5, procThread.getCappedMessageCount(), "Wrong number of messages capped, should be 10-5=5");
    Assertions.assertEquals(10, procThread.getCappedMsgList(message).size(), "Wrong number of messages in activeMessageCache, should be 10");

    // try resending first one, it should not be stopped by the capping, as it is one of the active
    // msgs,// but should be stopped by the lastSentMsgCache
    procThread.clearLastMsgCache(ids.get(0));
    message.setMessageId(ids.get(0));
    procThread.addToQueue(message);
    procThread.processOneMessage();
    TimeUnit.MILLISECONDS.sleep(10);

    // check no more is sent
    Assertions.assertEquals(ids.get(5), procThread.findNextActiveMsgsNotSentAllQ(message).getMessageId(), "Wrong message first in Q should be number 6 ");
    map = procThread.findActiveMsgIdsNowForDevice(message, true);
    set = makeIdSet(map);
    Assertions.assertEquals((messagesSize), set.size());
    Assertions.assertEquals(0L, procThread.getDiscardedDueToEnqueueType());
    Assertions.assertEquals((messagesSize), (int) procThread.getProcessed());
    Assertions.assertEquals(5, (int) procThread.getExecTransitionsTimes()); // 5
    Assertions.assertEquals(5, procThread.getCappedMessageCount(), "Wrong number of messages capped, should be 5");
    Assertions.assertEquals(10, procThread.getCappedMsgList(message).size(), "Wrong number of messages in activeMessageCache, should be 10");

    // try resending first one, it should not be stopped by the capping, as it is one of the active
    // messages.
    // remove first from lastMsgSentCache, to simulate time passed
    procThread.clearLastMsgCache(ids.get(0));
    procThread.clearLastMsgSentCache(ids.get(0));
    message.setMessageId(ids.get(0));
    procThread.addToQueue(message);
    procThread.processOneMessage();
    TimeUnit.MILLISECONDS.sleep(10);

    // check one more is sent
    Assertions.assertEquals(ids.get(5), procThread.findNextActiveMsgsNotSentAllQ(message).getMessageId(), "Wrong message first in Q should be number 6 ");
    map = procThread.findActiveMsgIdsNowForDevice(message, true);
    set = makeIdSet(map);
    Assertions.assertEquals((messagesSize), set.size());
    Assertions.assertEquals(0L, procThread.getDiscardedDueToEnqueueType());
    Assertions.assertEquals((messagesSize + 1), (int) procThread.getProcessed());
    Assertions.assertEquals(6, (int) procThread.getExecTransitionsTimes()); // 5 + 1
    Assertions.assertEquals(5, procThread.getCappedMessageCount(), "Wrong number of messages capped, should be 5");
    Assertions.assertEquals(10, procThread.getCappedMsgList(message).size(), "Wrong number of messages in activeMessageCache, should be 10");

    Assertions.assertEquals(0, procThread.getQSize());

    int i = 0;
    // ack third msg, the next capped should then be queued for sending
    ackOneMessage(2, i++, messagesSize, procThread, messagePersister, ids, messages, message);

    // ack Second msg, the next capped should then be queued for sending
    ackOneMessage(1, i++, messagesSize, procThread, messagePersister, ids, messages, message);

    // ack fourth msg, the next capped should then be queued for sending
    ackOneMessage(3, i++, messagesSize, procThread, messagePersister, ids, messages, message);

    // ack fifth msg, the next capped should then be queued for sending
    ackOneMessage(4, i++, messagesSize, procThread, messagePersister, ids, messages, message);

    // ack 6'th msg
    ackOneMessage(5, i++, messagesSize, procThread, messagePersister, ids, messages, message);

    // ack 7'th msg
    ackOneMessage(6, i++, messagesSize, procThread, messagePersister, ids, messages, message);

    // ack 8'th msg
    ackOneMessage(7, i++, messagesSize, procThread, messagePersister, ids, messages, message);

    // ack 9'th msg
    ackOneMessage(8, i++, messagesSize, procThread, messagePersister, ids, messages, message);

    // ack 10'th msg
    ackOneMessage(9, i++, messagesSize, procThread, messagePersister, ids, messages, message);

    // ack first msg
    ackOneMessage(0, i++, messagesSize, procThread, messagePersister, ids, messages, message);

    procThread.stopMe();
  }

  /**
   * message that is capped when limit is 5 must be able to resent, i.e. the messages that capps a vehicle can not be allowed to stop itself from resending.
   */
  @Test
  void testResendOfCappedMsgLimit5NotInOrderUsingThread() throws Exception {
    int messagesSize = 10;
    String vid = "111699" + Math.random();

    MockConfiguration mockConfiguration = MockConfiguration.getConfig();
    mockConfiguration.setPropertySpecific(mockConfiguration.getComponentShortName(), "notset.scheduler.workerthread.thread.active_msg_cap", "5");

    SchedulerProcessorThread procThread = new SchedulerProcessorThread(schedulerProcessor, 0, schedulerModuleMetricReporter);
    Thread thread = new Thread(procThread);
    thread.start();

    // Add messages
    List<String> ids = new ArrayList<>();
    List<Message> messages = new ArrayList<>();
    for (int i = 0; i < messagesSize; i++) {
      Message message = createMessage();
      message.setMessageId(i + "-" + message.getMessageId());
      message.setProperty(MetaData.SCHEDULE_NAME, schedule.getName());
      message.setVehicleID(vid);
      message.setProperty(MetaData.SCHEDULE_ENQUEUEING_TYPE, EnqueueingType.NORMAL.name());
      persistMessage(message);
      procThread.addToQueue(message);
      ids.add(message.getMessageId());
      messages.add(message);
      TimeUnit.MILLISECONDS.sleep(20);
    }

    Message message = createMessage();
    message.setProperty(MetaData.SCHEDULE_NAME, schedule.getName());
    message.setVehicleID(vid);
    message.setProperty(MetaData.SCHEDULE_ENQUEUEING_TYPE, EnqueueingType.NORMAL.name());

    TimeUnit.MILLISECONDS.sleep(20);

    // get state
    Map<String, Map<Long, String>> map = procThread.findActiveMsgIdsNowForDevice(message, true);
    Set<String> set = makeIdSet(map);
    int in = 0;
    for (String id : ids) {
      in += set.contains(id) ? 1 : 0;
    }

    TimeUnit.MILLISECONDS.sleep(20);

    Assertions.assertEquals(messagesSize, in, "Wrong number of active messages");

    // check excess message is capped
    Assertions.assertEquals(ids.get(5), procThread.findNextActiveMsgsNotSentForMyQ(message, true).getMessageId(),
        "Wrong message first in Q, should be message 6. ");
    map = procThread.findActiveMsgIdsNowForDevice(message, true);
    set = makeIdSet(map);
    Assertions.assertEquals(messagesSize, set.size());
    Assertions.assertEquals(0L, procThread.getDiscardedDueToEnqueueType());
    Assertions.assertEquals(messagesSize, (int) procThread.getProcessed());
    Assertions.assertEquals(5, (int) procThread.getExecTransitionsTimes());
    Assertions.assertEquals(5, procThread.getCappedMessageCount(), "Wrong number of messages capped, should be 10-5=5");
    Assertions.assertEquals(10, procThread.getCappedMsgList(message).size(), "Wrong number of messages in activeMessageCache, should be 10");

    // try resending first one, it should not be stopped by the capping, as it is one of the active
    // msgs,// but should be stopped by the lastSentMsgCache
    procThread.clearLastMsgCache(ids.get(0));
    message.setMessageId(ids.get(0));
    procThread.addToQueue(message);
    TimeUnit.MILLISECONDS.sleep(100);

    // check no more is sent
    Assertions.assertEquals(ids.get(5), procThread.findNextActiveMsgsNotSentAllQ(message).getMessageId(), "Wrong message first in Q should be number 6 ");
    map = procThread.findActiveMsgIdsNowForDevice(message, true);
    set = makeIdSet(map);
    Assertions.assertEquals((messagesSize), set.size());
    Assertions.assertEquals(0L, procThread.getDiscardedDueToEnqueueType());
    Assertions.assertEquals((messagesSize), (int) procThread.getProcessed());
    Assertions.assertEquals(5, (int) procThread.getExecTransitionsTimes()); // 5
    Assertions.assertEquals(5, procThread.getCappedMessageCount(), "Wrong number of messages capped, should be 5");
    Assertions.assertEquals(10, procThread.getCappedMsgList(message).size(), "Wrong number of messages in activeMessageCache, should be 10");

    // try resending first one, it should not be stopped by the capping, as it is one of the active
    // msgs.
    // remove first from lastMsgSentCache, to simulate time passed
    procThread.clearLastMsgCache(ids.get(0));
    procThread.clearLastMsgSentCache(ids.get(0));
    message.setMessageId(ids.get(0));
    procThread.addToQueue(message);
    TimeUnit.MILLISECONDS.sleep(100);

    // check one more is sent
    Assertions.assertEquals(ids.get(5), procThread.findNextActiveMsgsNotSentAllQ(message).getMessageId(), "Wrong message first in Q should be number 6 ");
    map = procThread.findActiveMsgIdsNowForDevice(message, true);
    set = makeIdSet(map);
    Assertions.assertEquals((messagesSize), set.size());
    Assertions.assertEquals(0L, procThread.getDiscardedDueToEnqueueType());
    Assertions.assertEquals((messagesSize + 1), (int) procThread.getProcessed());
    Assertions.assertEquals(6, (int) procThread.getExecTransitionsTimes()); // 5 + 1
    Assertions.assertEquals(5, procThread.getCappedMessageCount(), "Wrong number of messages capped, should be 5");
    Assertions.assertEquals(10, procThread.getCappedMsgList(message).size(), "Wrong number of messages in activeMessageCache, should be 10");

    long sleept = 5;
    int i = 0;
    // ack third msg, the next capped should then be queued for sending
    ackOneMessage(2, i++, messagesSize, procThread, messagePersister, ids, messages, message, false);
    TimeUnit.MILLISECONDS.sleep(sleept);

    // ack Second msg, the next capped should then be queued for sending
    ackOneMessage(1, i++, messagesSize, procThread, messagePersister, ids, messages, message, false);
    TimeUnit.MILLISECONDS.sleep(sleept);

    // ack fourth msg, the next capped should then be queued for sending
    ackOneMessage(3, i++, messagesSize, procThread, messagePersister, ids, messages, message, false);
    TimeUnit.MILLISECONDS.sleep(sleept);

    // ack fifth msg, the next capped should then be queued for sending
    ackOneMessage(4, i++, messagesSize, procThread, messagePersister, ids, messages, message, false);
    TimeUnit.MILLISECONDS.sleep(sleept);

    // ack 6'th msg
    ackOneMessage(5, i++, messagesSize, procThread, messagePersister, ids, messages, message, false);
    TimeUnit.MILLISECONDS.sleep(sleept);

    // ack 7'th msg
    ackOneMessage(6, i++, messagesSize, procThread, messagePersister, ids, messages, message, false);
    TimeUnit.MILLISECONDS.sleep(sleept);

    // ack 8'th msg
    ackOneMessage(7, i++, messagesSize, procThread, messagePersister, ids, messages, message, false);
    TimeUnit.MILLISECONDS.sleep(sleept);

    // ack 9'th msg
    ackOneMessage(8, i++, messagesSize, procThread, messagePersister, ids, messages, message, false);
    TimeUnit.MILLISECONDS.sleep(sleept);

    // ack first msg
    ackOneMessage(0, i++, messagesSize, procThread, messagePersister, ids, messages, message, false);
    TimeUnit.MILLISECONDS.sleep(sleept);

    // ack 10'th msg
    ackOneMessage(9, i++, messagesSize, procThread, messagePersister, ids, messages, message, false);

    TimeUnit.MILLISECONDS.sleep(200);

    int countForAssert = 4;
    map = procThread.findActiveMsgIdsNowForDevice(message, true);
    set = makeIdSet(map);
    Assertions.assertEquals((messagesSize - (i)), set.size());
    Assertions.assertEquals(0L, procThread.getDiscardedDueToEnqueueType());
    Assertions.assertEquals((messagesSize + 6), (int) procThread.getProcessed());
    Assertions.assertEquals((countForAssert + 7), (int) procThread.getExecTransitionsTimes()); // 5 + 1
    Assertions.assertEquals(5, procThread.getCappedMessageCount(), "Wrong number of messages capped, should be 5");
    Assertions.assertEquals(0, procThread.getCappedMsgList(message).size(), "Wrong number of messages in activeMessageCache, should be 10 -" + (i));

    procThread.stopMe();
  }

  @Test
  void testTech21698VersionConnEstblished() throws Exception {
    Message message = createMessage();

    transition2 = SchedulerTestUtil.createTransition(schedule, 1, transition);
    transition2.setTimeOutRepeat(1);
    transition2.setMaxNumberOfTriesPerMsg(10);
    transition2 = SchedulePersistanceFactory.makeTransitionPersister().persist(transition2);

    SchedulerProcessorThread procThread = new SchedulerProcessorThread(schedulerProcessor, 0, schedulerModuleMetricReporter);

    message.setProperty(MetaData.HANDLE, "handle");
    message.setProperty(MessageFields.lastTransitionName.name(), transition.getName());
    message.setProperty("SCHDPRP_MXTRIES_" + transition.getName(), "37");

    persistMessage(message);

    for (int i = 0; i < 20; i++) {
      message = messagePersister.lookup(message.getMessageId());
      message.setProperty(MessageFields.removeTime.name(), Long.MAX_VALUE + "");
      message.setProperty(MessageFields.lastTransitionName.name(), null);
      message.setProperty("SCHDPRP_MXTRIES_" + transition.getName(), "37");
      message.setProperty("SCHDPRP_REPEAT_" + transition.getName(), null);
      message.setProperty("SCHDPRP_REPEAT_" + transition2.getName(), null);
      processOneMsgOnce(procThread, message);
      Thread.sleep(5);
    }

    Assertions.assertEquals(0, procThread.getQSize());
    Assertions.assertEquals(10, procThread.getExecTransitionsTimes());
    Assertions.assertEquals(0, procThread.getSkippedTransitionsRepeat());
    Assertions.assertEquals(30, procThread.getSkippedTransitionsMaxTries());
  }

  @Test
  void testTech21698VersionRepeat() throws Exception {
    Message message = createMessage();

    transition2 = SchedulerTestUtil.createTransition(schedule, 1, transition);
    transition2.setTimeOutRepeat(1);
    transition2.setMaxNumberOfTriesPerMsg(10);
    transition2 = SchedulePersistanceFactory.makeTransitionPersister().persist(transition2);

    SchedulerProcessorThread procThread = new SchedulerProcessorThread(schedulerProcessor, 0, schedulerModuleMetricReporter);

    message.setProperty(MetaData.HANDLE, "handle");
    message.setProperty(MessageFields.lastTransitionName.name(), transition.getName());
    message.setProperty("SCHDPRP_MXTRIES_" + transition.getName(), "37");

    persistMessage(message);

    for (int i = 0; i < 20; i++) {
      message = messagePersister.lookup(message.getMessageId());
      message.setProperty(MessageFields.removeTime.name(), Long.MAX_VALUE + "");
      message.setProperty(MessageFields.lastTransitionName.name(), transition.getName());
      message.setProperty("SCHDPRP_MXTRIES_" + transition.getName(), "37");
      processOneMsgOnce(procThread, message);
      Thread.sleep(5);
    }

    Assertions.assertEquals(0, procThread.getQSize());
    Assertions.assertEquals(10, procThread.getExecTransitionsTimes());
    Assertions.assertEquals(0, procThread.getSkippedTransitionsRepeat());
    Assertions.assertEquals(10, procThread.getSkippedTimeout());
    Assertions.assertEquals(10, procThread.getNoUsableTransition());
  }

  private Message createMessage() {
    Message message = new Message();
    String tid = TrackingIdentifier.create().toString();
    String wid = WorkflowIdentifier.create().toString();

    message.setMessageId(tid);
    message.setProperty(MetaData.MESSAGE_ID, tid);
    message.setProperty("TRACKING_ID", tid);
    message.setProperty("WORKFLOW_ID", wid);

    message.setPayload(PAYLOAD.getBytes(StandardCharsets.UTF_8));
    message.setVehicleID("" + Math.random() * 1_000_000_000);
    message.setProperty("1", "a");
    message.setProperty("2", "b");
    message.setProperty("3", "c");
    message.setProperty(MessageFields.status.name(), ActivityStatus.a.name());
    message.setProperty(MetaData.SCHEDULE_NAME, schedule.getName());
    message.setProperty(MessageFields.removeTime.name(), "" + (System.currentTimeMillis() + 1000 * 3600 * 24));

    return message;
  }
}
