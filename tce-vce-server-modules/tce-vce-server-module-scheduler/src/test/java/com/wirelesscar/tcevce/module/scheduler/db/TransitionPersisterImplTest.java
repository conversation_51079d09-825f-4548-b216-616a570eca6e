package com.wirelesscar.tcevce.module.scheduler.db;

import java.sql.SQLException;
import java.util.List;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.wirelesscar.config.mock.MockConfiguration;
import com.wirelesscar.tce.db.source.TceDataSource;
import com.wirelesscar.tce.db.standard.sql.impl.SqlPersistanceFactory;
import com.wirelesscar.tcevce.module.scheduler.api.Schedule;
import com.wirelesscar.tcevce.module.scheduler.api.Transition;
import com.wirelesscar.tcevce.module.scheduler.cache.TransitionCacheImpl;
import com.wirelesscar.tcevce.module.scheduler.cache.api.SchedulerCacheFactory;
import com.wirelesscar.tcevce.module.scheduler.cache.api.TransitionCache;
import com.wirelesscar.tcevce.module.scheduler.db.api.SchedulePersistanceFactory;
import com.wirelesscar.tcevce.module.scheduler.db.api.TransitionPersister;

class TransitionPersisterImplTest {
  private static final int MINUTES = 500;
  private static final int SIZE = 800;

  private Schedule schedule;

  @BeforeAll
  static void beforeAll() throws SQLException {
    MockConfiguration mockConfiguration = MockConfiguration.getConfig();
    mockConfiguration.setPropertySpecific(mockConfiguration.getComponentShortName(), TceDataSource.TEST_DB_PROP, "true");
    mockConfiguration.setPropertySpecific(mockConfiguration.getComponentShortName(), "scheduler.module.cache.minutes", Integer.toString(MINUTES));
    mockConfiguration.setPropertySpecific(mockConfiguration.getComponentShortName(), "scheduler.module.cache.size", Integer.toString(SIZE));

    SchedulerTestUtil.preps();

    SchedulerCacheFactory.transitionCache().clear();
  }

  @AfterEach
  void afterEach() throws Exception {
    SqlPersistanceFactory.cleanDB();
  }

  @BeforeEach
  void beforeEach() throws SQLException {
    schedule = SchedulePersistanceFactory.makeSchedulePersister().persist(SchedulerTestUtil.createSchedule());
  }

  @Test
  void testCacheSize() {
    SchedulerCacheFactory.transitionCache().clear();
    TransitionCache transitionCache = SchedulerCacheFactory.transitionCache();
    Assertions.assertEquals(SIZE, transitionCache.cacheSizeMax());
    Assertions.assertEquals(MINUTES, transitionCache.cacheRetainMinutes());
  }

  @Test
  void testInsertLookupByCacheDelete() throws SQLException {
    TransitionCacheImpl.getInstance().clear();
    TransitionPersister transitionPersister = SchedulePersistanceFactory.makeTransitionPersister();

    Transition transition1 = SchedulerTestUtil.createTransition(schedule, 0, null);

    Assertions.assertEquals(0, TransitionCacheImpl.getInstance().getCacheSize());

    Transition transition2 = transitionPersister.persist(transition1);

    Transition transition3 = SchedulerCacheFactory.transitionCache().get(transition2.getId(), true);
    Assertions.assertEquals(1, TransitionCacheImpl.getInstance().getCacheSize());

    long deleted = transitionPersister.delete(transition3);

    Assertions.assertNotNull(transition3);
    Assertions.assertEquals(transition2.getId(), transition3.getId());
    Assertions.assertEquals(transition1.getTimeToLive(), transition3.getTimeToLive());
    Assertions.assertEquals(transition2.getMaxNumberOfTriesPerMsg(), transition3.getMaxNumberOfTriesPerMsg());
    Assertions.assertEquals(transition2.getName(), transition3.getName());
    Assertions.assertEquals(schedule.getId(), transition3.getSchedule().getId());
    Assertions.assertEquals(1, deleted);

    Transition transition4 = transitionPersister.lookup(transition2.getId());
    Assertions.assertNull(transition4);
  }

  @Test
  void testInsertLookupByNameDelete() throws SQLException {
    TransitionPersister transitionPersister = SchedulePersistanceFactory.makeTransitionPersister();

    Transition transition1 = SchedulerTestUtil.createTransition(schedule, 0, null);
    Transition transition2 = transitionPersister.persist(transition1);

    Transition transition3 = SchedulerTestUtil.createTransition(schedule, 1, transition2);
    Transition transition4 = transitionPersister.persist(transition3);

    List<Transition> transitions = transitionPersister.findBySchedule(schedule);
    Transition transition5 = transitions.get(0);
    Transition transition6 = transitions.get(1);

    Assertions.assertEquals(2, transitions.size());
    Assertions.assertNotNull(transition5);
    Assertions.assertEquals(transition2.getId(), transition5.getId());
    Assertions.assertFalse(transition2.getConnEstablishedAllowed());
    Assertions.assertFalse(transition5.getConnEstablishedAllowed());
    Assertions.assertEquals(transition2.getMaxNumberOfTriesPerMsg(), transition5.getMaxNumberOfTriesPerMsg());
    Assertions.assertEquals(transition1.getTimeToLive(), transition5.getTimeToLive());
    Assertions.assertEquals(0, transition5.getOrder());
    Assertions.assertEquals(1, transition6.getOrder());

    Assertions.assertTrue(transition4.getConnEstablishedAllowed());
    Assertions.assertTrue(transition6.getConnEstablishedAllowed());

    Assertions.assertEquals(transition2.getName(), transition5.getName());
    Assertions.assertEquals(schedule.getId(), transition5.getSchedule().getId());

    transition5.setConnEstablishedAllowed(true);
    Transition transition7 = transitionPersister.persist(transition5);
    Assertions.assertTrue(transition7.getConnEstablishedAllowed());

    transitionPersister.delete(transition5);
    transitionPersister.delete(transition6);
  }

  @Test
  void testInsertLookupDelete() throws SQLException {
    TransitionPersister transitionPersister = SchedulePersistanceFactory.makeTransitionPersister();

    Transition transition1 = SchedulerTestUtil.createTransition(schedule, 0, null);

    Transition transition2 = transitionPersister.persist(transition1);

    Transition transition3 = transitionPersister.lookup(transition2.getId());

    long deleted = transitionPersister.delete(transition3);

    Assertions.assertNotNull(transition3);
    Assertions.assertEquals(transition2.getId(), transition3.getId());
    Assertions.assertEquals(transition1.getTimeToLive(), transition3.getTimeToLive());
    Assertions.assertEquals(transition2.getName(), transition3.getName());
    Assertions.assertEquals(transition2.getMaxNumberOfTriesPerMsg(), transition3.getMaxNumberOfTriesPerMsg());
    Assertions.assertEquals(schedule.getId(), transition3.getSchedule().getId());
    Assertions.assertEquals(1, deleted);

    Transition transition4 = transitionPersister.lookup(transition2.getId());
    Assertions.assertNull(transition4);
  }

  @Test
  void testInsertLookupDeleteWithRepeatFrom() throws SQLException {
    TransitionPersister transitionPersister = SchedulePersistanceFactory.makeTransitionPersister();

    Transition transition1 = SchedulerTestUtil.createTransition(schedule, 0, null);
    Transition transition2 = SchedulerTestUtil.createTransition(schedule, 1, null);

    Transition transition3 = transitionPersister.persist(transition1);
    transition2.setRepeatFrom(transition3);
    Transition transition4 = transitionPersister.persist(transition2);

    Transition transition5 = transitionPersister.lookup(transition3.getId());
    Transition transition6 = transitionPersister.lookup(transition4.getId());

    long deleted = transitionPersister.delete(transition6);
    deleted += transitionPersister.delete(transition5);

    Assertions.assertNotNull(transition6);
    Assertions.assertEquals(transition4.getId(), transition6.getId());
    Assertions.assertNotNull(transition6.getRepeatFrom());
    Assertions.assertEquals(transition4.getRepeatFrom().getId(), transition6.getRepeatFrom().getId());
    Assertions.assertEquals(schedule.getId(), transition6.getSchedule().getId());
    Assertions.assertEquals(2, deleted);
  }
}
