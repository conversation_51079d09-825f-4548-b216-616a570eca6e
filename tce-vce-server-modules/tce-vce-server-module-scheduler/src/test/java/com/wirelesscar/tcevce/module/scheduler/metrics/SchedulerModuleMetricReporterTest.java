package com.wirelesscar.tcevce.module.scheduler.metrics;

import java.time.Duration;

import org.junit.jupiter.api.Test;

import io.micrometer.core.instrument.Tags;

class SchedulerModuleMetricReporterTest {
  private static final String STACK_TEST = "stack-test";

  @Test
  void logCacheSizeTest() {
    MetricsReporterTestUtils.initReporterAndTest(SchedulerModuleMetricReporter::new, (meterRegistry, schedulerModuleMetricReporter) -> {
      schedulerModuleMetricReporter.logCacheSize(2);
      MetricsReporterTestUtils.checkCounter(meterRegistry, 2, SchedulerModuleMetricReporter.METRIC_CACHE_SIZE, Tags.empty());
    });
  }

  @Test
  void logConnectionEstablishedTest() {
    MetricsReporterTestUtils.initReporterAndTest(SchedulerModuleMetricReporter::new, (meterRegistry, schedulerModuleMetricReporter) -> {
      schedulerModuleMetricReporter.logConnectionEstablished(Duration.ofMillis(2));
      MetricsReporterTestUtils.checkTimer(meterRegistry, Duration.ofMillis(2), 1, SchedulerModuleMetricReporter.METRIC_CONNECTION_ESTABLISHED, Tags.empty());
    });
  }

  @Test
  void logSendDownTest() {
    MetricsReporterTestUtils.initReporterAndTest(SchedulerModuleMetricReporter::new, (meterRegistry, schedulerModuleMetricReporter) -> {
      schedulerModuleMetricReporter.logSendDown(STACK_TEST, Duration.ofMillis(2));
      MetricsReporterTestUtils.checkTimer(meterRegistry, Duration.ofMillis(2), 1, SchedulerModuleMetricReporter.METRIC_SEND_DOWN,
          Tags.of(SchedulerModuleMetricReporter.STACK_VAR_NAME, STACK_TEST));
    });
  }

  @Test
  void onCacheConnectionEstablishedTest() {
    MetricsReporterTestUtils.initReporterAndTest(SchedulerModuleMetricReporter::new, (meterRegistry, schedulerModuleMetricReporter) -> {
      schedulerModuleMetricReporter.onCacheConnectionEstablished(3);
      MetricsReporterTestUtils.checkCounter(meterRegistry, 3, SchedulerModuleMetricReporter.METRIC_CACHE_CONN_ESTBL, Tags.empty());
    });
  }

  @Test
  void onCacheMsgIdsTest() {
    MetricsReporterTestUtils.initReporterAndTest(SchedulerModuleMetricReporter::new, (meterRegistry, schedulerModuleMetricReporter) -> {
      schedulerModuleMetricReporter.onCacheMsgIds();
      MetricsReporterTestUtils.checkCounter(meterRegistry, 1, SchedulerModuleMetricReporter.METRIC_CACHE_MSG_IDS, Tags.empty());
    });
  }

  @Test
  void onCanceledTest() {
    MetricsReporterTestUtils.initReporterAndTest(SchedulerModuleMetricReporter::new, (meterRegistry, schedulerModuleMetricReporter) -> {
      schedulerModuleMetricReporter.onCanceled(STACK_TEST);
      MetricsReporterTestUtils.checkCounter(meterRegistry, 1, SchedulerModuleMetricReporter.METRIC_CANCELLED,
          Tags.of(SchedulerModuleMetricReporter.STACK_VAR_NAME, STACK_TEST));
    });
  }

  @Test
  void onDeliveredTest() {
    MetricsReporterTestUtils.initReporterAndTest(SchedulerModuleMetricReporter::new, (meterRegistry, schedulerModuleMetricReporter) -> {
      schedulerModuleMetricReporter.onDelivered(STACK_TEST);
      MetricsReporterTestUtils.checkCounter(meterRegistry, 1, SchedulerModuleMetricReporter.METRIC_DELIVERED,
          Tags.of(SchedulerModuleMetricReporter.STACK_VAR_NAME, STACK_TEST));
    });
  }

  @Test
  void onDiscardedTest() {
    MetricsReporterTestUtils.initReporterAndTest(SchedulerModuleMetricReporter::new, (meterRegistry, schedulerModuleMetricReporter) -> {
      schedulerModuleMetricReporter.onDiscarded();
      MetricsReporterTestUtils.checkCounter(meterRegistry, 1, SchedulerModuleMetricReporter.METRIC_DISCARDED, Tags.empty());
    });
  }

  @Test
  void onDownTest() {
    MetricsReporterTestUtils.initReporterAndTest(SchedulerModuleMetricReporter::new, (meterRegistry, schedulerModuleMetricReporter) -> {
      schedulerModuleMetricReporter.onDown();
      MetricsReporterTestUtils.checkCounter(meterRegistry, 1, SchedulerModuleMetricReporter.METRIC_DOWN, Tags.empty());
    });
  }

  @Test
  void onDroppingJustHandledDeliveredTest() {
    MetricsReporterTestUtils.initReporterAndTest(SchedulerModuleMetricReporter::new, (meterRegistry, schedulerModuleMetricReporter) -> {
      schedulerModuleMetricReporter.onDroppingJustHandledDelivered();
      MetricsReporterTestUtils.checkCounter(meterRegistry, 1, SchedulerModuleMetricReporter.METRIC_DROPPING_JUST_HANDLED_DELIVERED, Tags.empty());
    });
  }

  @Test
  void onDroppingJustHandledTest() {
    MetricsReporterTestUtils.initReporterAndTest(SchedulerModuleMetricReporter::new, (meterRegistry, schedulerModuleMetricReporter) -> {
      schedulerModuleMetricReporter.onDroppingJustHandled();
      MetricsReporterTestUtils.checkCounter(meterRegistry, 1, SchedulerModuleMetricReporter.METRIC_DROPPING_JUST_HANDLED, Tags.empty());
    });
  }

  @Test
  void onDroppingNotMineTest() {
    MetricsReporterTestUtils.initReporterAndTest(SchedulerModuleMetricReporter::new, (meterRegistry, schedulerModuleMetricReporter) -> {
      schedulerModuleMetricReporter.onDroppingNotMine();
      MetricsReporterTestUtils.checkCounter(meterRegistry, 1, SchedulerModuleMetricReporter.METRIC_DROPPING_NOT_MINE, Tags.empty());
    });
  }

  @Test
  void onMetricCappedTest() {
    MetricsReporterTestUtils.initReporterAndTest(SchedulerModuleMetricReporter::new, (meterRegistry, schedulerModuleMetricReporter) -> {
      schedulerModuleMetricReporter.onCapped();
      MetricsReporterTestUtils.checkCounter(meterRegistry, 1, SchedulerModuleMetricReporter.METRIC_CAPPED, Tags.empty());
    });
  }

  @Test
  void onSmsTest() {
    MetricsReporterTestUtils.initReporterAndTest(SchedulerModuleMetricReporter::new, (meterRegistry, schedulerModuleMetricReporter) -> {
      schedulerModuleMetricReporter.onSms(STACK_TEST);
      MetricsReporterTestUtils.checkCounter(meterRegistry, 1, SchedulerModuleMetricReporter.METRIC_SMS, Tags.of(SchedulerModuleMetricReporter.STACK_VAR_NAME,
          STACK_TEST));
    });
  }

  @Test
  void onTimeoutTest() {
    MetricsReporterTestUtils.initReporterAndTest(SchedulerModuleMetricReporter::new, (meterRegistry, schedulerModuleMetricReporter) -> {
      schedulerModuleMetricReporter.onTimeout(STACK_TEST);
      MetricsReporterTestUtils.checkCounter(meterRegistry, 1, SchedulerModuleMetricReporter.METRIC_TIMEOUT,
          Tags.of(SchedulerModuleMetricReporter.STACK_VAR_NAME, STACK_TEST));
    });
  }

  @Test
  void onTooManyRepeatsTest() {
    MetricsReporterTestUtils.initReporterAndTest(SchedulerModuleMetricReporter::new, (meterRegistry, schedulerModuleMetricReporter) -> {
      schedulerModuleMetricReporter.onTooManyRepeats();
      MetricsReporterTestUtils.checkCounter(meterRegistry, 1, SchedulerModuleMetricReporter.METRIC_TOO_MANY_REPEATS, Tags.empty());
    });
  }

  @Test
  void onUdpTest() {
    MetricsReporterTestUtils.initReporterAndTest(SchedulerModuleMetricReporter::new, (meterRegistry, schedulerModuleMetricReporter) -> {
      schedulerModuleMetricReporter.onUdp(STACK_TEST);
      MetricsReporterTestUtils.checkCounter(meterRegistry, 1, SchedulerModuleMetricReporter.METRIC_UDP, Tags.of(SchedulerModuleMetricReporter.STACK_VAR_NAME,
          STACK_TEST));
    });
  }
}
