package com.wirelesscar.tcevce.module.scheduler.db;

import java.sql.SQLException;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import com.wirelesscar.tce.db.common.api.ActivityStatus;
import com.wirelesscar.tce.db.common.db.api.MessageFields;
import com.wirelesscar.tcevce.module.scheduler.api.Action;
import com.wirelesscar.tcevce.module.scheduler.api.Schedule;
import com.wirelesscar.tcevce.module.scheduler.api.Transition;
import com.wirelesscar.tcevce.module.scheduler.db.api.SchedulePersistanceFactory;
import com.wirelesscar.tcevce.module.scheduler.db.api.SchedulePersister;
import com.wirelesscar.tcevce.module.scheduler.db.api.TransitionPersister;

final class SchedulerTestUtil {
  private SchedulerTestUtil() {
    throw new IllegalStateException();
  }

  static Action createAction(Transition transition, int order) {
    Action action = new Action();

    action.setInternal(true);
    action.setName("Benny" + Math.random());
    action.setOrder(order);
    action.setProperty("KEY1", "VALUE1");
    action.setProperty("KEY2", "VALUE2");
    action.setTransition(transition);

    return action;
  }

  static Schedule createSchedule() {
    Schedule schedule = new Schedule();

    schedule.setName("Conny" + Math.random());
    schedule.setState(ActivityStatus.a);
    schedule.setTimeToLive(1_000_000);

    return schedule;
  }

  static Schedule createSchedule(long timeout) {
    Schedule schedule = new Schedule();

    schedule.setName("Conny" + Math.random());
    schedule.setState(ActivityStatus.a);
    schedule.setTimeToLive(timeout);

    return schedule;
  }

  static Schedule createSchedule(String nameEnding) {
    Schedule schedule = new Schedule();

    schedule.setName(nameEnding);
    schedule.setState(ActivityStatus.a);
    schedule.setTimeToLive(100);

    return schedule;
  }

  static Transition createTransition(Schedule schedule, int order, Transition connEstTransition) {
    Transition transition = new Transition();

    transition.setMaxNumberOfTriesPerMsg(2);
    transition.setName("Conny" + "-" + order + "-" + Math.random());
    transition.setOrder(order);
    transition.setSchedule(schedule);
    transition.setTimeToLive(1_000_000);

    if (connEstTransition != null) {
      transition.setConnEstablishedAllowed(true);
    }

    return transition;
  }

  static void preps() throws SQLException {
    TransitionPersister transitionPersister = SchedulePersistanceFactory.makeTransitionPersister();
    Map<String, String> query = Collections.singletonMap(MessageFields.name.name(), "Conny");
    List<Transition> transitions = transitionPersister.find(query);

    for (Transition transition : transitions) {
      transitionPersister.delete(transition);
    }

    SchedulePersister schedulePersister = SchedulePersistanceFactory.makeSchedulePersister();
    List<Schedule> schedules = schedulePersister.find(query);

    for (Schedule schedule : schedules) {
      schedulePersister.delete(schedule);
    }
  }
}
