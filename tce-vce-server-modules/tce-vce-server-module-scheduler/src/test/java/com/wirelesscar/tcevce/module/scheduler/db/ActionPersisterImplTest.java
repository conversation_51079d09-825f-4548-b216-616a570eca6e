package com.wirelesscar.tcevce.module.scheduler.db;

import java.sql.SQLException;
import java.util.List;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.wirelesscar.config.mock.MockConfiguration;
import com.wirelesscar.tce.db.source.TceDataSource;
import com.wirelesscar.tcevce.module.scheduler.api.Action;
import com.wirelesscar.tcevce.module.scheduler.api.Schedule;
import com.wirelesscar.tcevce.module.scheduler.api.Transition;
import com.wirelesscar.tcevce.module.scheduler.cache.ActionCacheImpl;
import com.wirelesscar.tcevce.module.scheduler.cache.api.ActionCache;
import com.wirelesscar.tcevce.module.scheduler.cache.api.SchedulerCacheFactory;
import com.wirelesscar.tcevce.module.scheduler.db.api.ActionPersister;
import com.wirelesscar.tcevce.module.scheduler.db.api.SchedulePersistanceFactory;

class ActionPersisterImplTest {
  private static final int MINUTES = 500;
  private static final int SIZE = 800;

  private Transition transition;

  @BeforeAll
  static void beforeAll() throws SQLException {
    MockConfiguration mockConfiguration = MockConfiguration.getConfig();
    mockConfiguration.setPropertySpecific(mockConfiguration.getComponentShortName(), TceDataSource.TEST_DB_PROP, "true");
    mockConfiguration.setPropertySpecific(mockConfiguration.getComponentShortName(), "scheduler.module.cache.minutes", Integer.toString(MINUTES));
    mockConfiguration.setPropertySpecific(mockConfiguration.getComponentShortName(), "scheduler.module.cache.size", Integer.toString(SIZE));

    SchedulerTestUtil.preps();

    SchedulerCacheFactory.actionCache().clear();
  }

  @BeforeEach
  void beforeEach() throws SQLException {
    Schedule schedule = SchedulePersistanceFactory.makeSchedulePersister().persist(SchedulerTestUtil.createSchedule());
    transition = SchedulePersistanceFactory.makeTransitionPersister()
        .persist(SchedulerTestUtil.createTransition(schedule, 0, null));
  }

  @Test
  void testCacheSize() {
    SchedulerCacheFactory.actionCache().clear();
    ActionCache actionCache = SchedulerCacheFactory.actionCache();
    Assertions.assertEquals(SIZE, actionCache.cacheSizeMax());
    Assertions.assertEquals(MINUTES, actionCache.cacheRetainMinutes());
  }

  @Test
  void testInsertLookupByCacheDelete() throws SQLException {
    ActionCacheImpl.getInstance().clear();
    ActionPersister actionPersister = SchedulePersistanceFactory.makeActionPersister();

    Action action1 = SchedulerTestUtil.createAction(transition, 0);

    Assertions.assertEquals(0, ActionCacheImpl.getInstance().getCacheSize());

    Action action2 = actionPersister.persist(action1);

    Action action3 = SchedulerCacheFactory.actionCache().get(action2.getId(), true);
    Assertions.assertEquals(1, ActionCacheImpl.getInstance().getCacheSize());

    long deleted = actionPersister.delete(action3);

    Assertions.assertNotNull(action3);
    Assertions.assertEquals(action2.getId(), action3.getId());
    Assertions.assertEquals(action2.getName(), action3.getName());
    Assertions.assertEquals(transition.getId(), action3.getTransition().getId());
    Assertions.assertEquals(1, deleted);

    Action action4 = actionPersister.lookup(action2.getId());
    Assertions.assertNull(action4);
  }

  @Test
  void testInsertLookupByNameDelete() throws SQLException {
    ActionPersister actionPersister = SchedulePersistanceFactory.makeActionPersister();

    Action action1 = SchedulerTestUtil.createAction(transition, 0);
    Action action2 = actionPersister.persist(action1);
    Action action3 = SchedulerTestUtil.createAction(transition, 1);
    actionPersister.persist(action3);

    List<Action> actions = actionPersister.findByTransition(transition);
    Action action4 = actions.get(0);
    Action action5 = actions.get(1);

    long deleted = actionPersister.delete(action4);
    actionPersister.delete(action5);

    Assertions.assertEquals(2, actions.size());
    Assertions.assertNotNull(action4);
    Assertions.assertEquals(action2.getId(), action4.getId());
    Assertions.assertEquals(0, action4.getOrder());
    Assertions.assertEquals(1, action5.getOrder());
    Assertions.assertEquals(action2.getName(), action4.getName());
    Assertions.assertEquals(transition.getId(), action4.getTransition().getId());
    Assertions.assertEquals(1, deleted);

    Action action6 = actionPersister.lookup(action2.getId());
    Assertions.assertNull(action6);
  }

  @Test
  void testInsertLookupDelete() throws SQLException {
    ActionPersister actionPersister = SchedulePersistanceFactory.makeActionPersister();

    Action action1 = SchedulerTestUtil.createAction(transition, 0);

    Action action2 = actionPersister.persist(action1);

    Action action3 = actionPersister.lookup(action2.getId());

    long deleted = actionPersister.delete(action3);

    Assertions.assertNotNull(action3);
    Assertions.assertEquals(action2.getId(), action3.getId());
    Assertions.assertEquals(0, action3.getOrder());
    Assertions.assertEquals(action2.isInternal(), action3.isInternal());
    Assertions.assertEquals(action2.getName(), action3.getName());
    Assertions.assertEquals(action2.getProperties().toString(), action3.getProperties().toString());
    Assertions.assertEquals(transition.getId(), action3.getTransition().getId());
    Assertions.assertEquals(1, deleted);

    Action action4 = actionPersister.lookup(action2.getId());

    Assertions.assertNull(action4);
  }
}
