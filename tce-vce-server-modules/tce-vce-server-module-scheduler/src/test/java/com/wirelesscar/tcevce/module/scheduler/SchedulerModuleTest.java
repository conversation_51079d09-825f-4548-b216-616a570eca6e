package com.wirelesscar.tcevce.module.scheduler;

import java.nio.charset.StandardCharsets;
import java.util.concurrent.TimeUnit;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import com.volvo.tisp.framework.context.TispContext;
import com.wirelesscar.config.exception.ConfigurationException;
import com.wirelesscar.config.mock.MockConfiguration;
import com.wirelesscar.tce.core.conf.TceConfigException;
import com.wirelesscar.tce.core.event.Event;
import com.wirelesscar.tce.core.event.EventListener;
import com.wirelesscar.tce.core.event.EventService;
import com.wirelesscar.tce.core.event.SynchronousEventListener;
import com.wirelesscar.tce.core.event.Type;
import com.wirelesscar.tce.core.event.events.AckEvent;
import com.wirelesscar.tce.db.common.api.ActivityStatus;
import com.wirelesscar.tce.db.common.db.api.MessageFields;
import com.wirelesscar.tce.db.common.db.api.MessagePersister;
import com.wirelesscar.tce.db.common.metrics.ProcessorMetricsReporter;
import com.wirelesscar.tce.db.source.TceDataSource;
import com.wirelesscar.tce.device.AbstractDeviceService;
import com.wirelesscar.tce.module.api.Message;
import com.wirelesscar.tce.module.api.MessageStatus;
import com.wirelesscar.tce.module.api.MetaData;
import com.wirelesscar.tce.module.api.ModuleBase;
import com.wirelesscar.tce.module.metrics.ModuleMetricReporter;
import com.wirelesscar.tcevce.module.scheduler.db.api.SchedulePersistanceFactory;
import com.wirelesscar.tcevce.module.scheduler.metrics.SchedulerModuleMetricReporter;
import com.wirelesscar.tcevce.module.segmentation.encryption.VceEncryptionHandler;

class SchedulerModuleTest extends ModuleBase {
  private static final MockConfiguration mockConfiguration = MockConfiguration.getConfig();

  private final EventService eventService = makeEventService();
  private Message lastDowned;
  private SchedulerModule schedulerModule;
  private int upped;
  private final VceEncryptionHandler vceEncryptionHandler = Mockito.mock(VceEncryptionHandler.class);

  SchedulerModuleTest() {
    super(Mockito.mock(ModuleMetricReporter.class));
  }

  @BeforeAll
  static void beforeAll() {
    mockConfiguration.setPropertySpecific(mockConfiguration.getComponentShortName(), TceDataSource.TEST_DB_PROP, "true");
  }

  private static Message configureMessage(String payload, Message message) {
    message.setPayload(payload.getBytes(StandardCharsets.UTF_8));
    message.setVehicleID("" + Math.random() * 1_000_000_000);
    message.setProperty("1", "a");
    message.setProperty("2", "b");
    message.setProperty("3", "c");
    message.setProperty(MessageFields.status.name(), ActivityStatus.a.name());
    message.setProperty(MetaData.SERVICE_ID, "S1");
    message.setProperty(MetaData.MESSAGE_ID, "M1");
    message.setProperty(MetaData.MT, "true");
    message.setProperty(MetaData.SCHEDULE_NAME, null);

    return message;
  }

  private static Message createMessage(String payload) {
    Message message = Message.createMessage();
    return configureMessage(payload, message);
  }

  private static Message createMessage(String payload, MessageStatus messageStatus) {
    Message message = Message.createStatusMessage(messageStatus);
    return configureMessage(payload, message);
  }

  private static EventService makeEventService() {
    return new EventService() {
      private SynchronousEventListener synchronousEventListener;

      @Override
      public void fireEvent(Event event) {
      }

      @Override
      public void fireSynchronousEvent(Event event) {
        if (event.getType().equals(Type.ACK) && synchronousEventListener != null) {
          synchronousEventListener.handleEvent(event);
        }
      }

      @Override
      public int getEventQueueSize(EventListener listener) {
        return 0;
      }

      @Override
      public void registerAsListener(EventListener listener, Type... eventTypes) {
      }

      @Override
      public void registerAsListener(SynchronousEventListener listener, Type... eventTypes) {
        synchronousEventListener = listener;
      }

      @Override
      public void unregister(EventListener listener) {
      }

      @Override
      public void unregister(SynchronousEventListener listener) {
      }
    };
  }

  @Override
  public void down(Message message) {
    lastDowned = message;
  }

  @Override
  public void up(Message message) {
    upped++;
  }

  @AfterEach
  void afterEach() {
    schedulerModule.stop();
    schedulerModule.undeploy();
  }

  @Test
  void testModuleAckCancel() throws Exception {
    String componentShortName = mockConfiguration.getComponentShortName();
    mockConfiguration.setPropertySpecific(componentShortName, "notset" + ".scheduler.module.readprocess.start", "false");
    mockConfiguration.setPropertySpecific(componentShortName, "notset" + ".scheduler.module.persistfordown", "true");
    mockConfiguration.setPropertySpecific(componentShortName, "notset" + ".scheduler.module.inprocessdown", "false");
    mockConfiguration.setPropertySpecific(componentShortName, "notset" + ".scheduler.module.instackdown", "false");
    mockConfiguration.setPropertySpecific(componentShortName, "notset" + ".scheduler.module.connection.established", "false");
    mockConfiguration.setPropertySpecific(componentShortName, "notset" + ".scheduler.module.ack", "true");
    mockConfiguration.setPropertySpecific(componentShortName, "notset" + ".scheduler.module.delete-after-publish", "false");

    Message message1 = createMessage("hepp" + Math.random(), MessageStatus.CANCELED);
    MessagePersister messagePersister = SchedulePersistanceFactory.makeMessagePersister();

    initSchedulerModule();
    setupModuleAfterInitConfig();

    TispContext.runInContext(() -> schedulerModule.down(message1));

    // setNextChecktime to Future to be able to check that it is reset
    long nextCheckTime = System.currentTimeMillis() + (3600 * 1000);
    message1.setProperty(MessageFields.nextCheckTime.name(), nextCheckTime + "");
    messagePersister.updateNextCheckTime(message1, nextCheckTime, 0);

    String fakeProp = "FAKEPROP";

    Message message2 = messagePersister.lookup(message1.getMessageId());
    Assertions.assertEquals(message1.getMessageId(), message2.getMessageId());
    Assertions.assertEquals(ActivityStatus.a.name(), message2.getProperty(MessageFields.status.name()));

    Message message3 = createMessage("hepp" + Math.random(), MessageStatus.CANCELED);
    message3.setMessageId(message1.getMessageId());
    AckEvent event = new AckEvent(message3);
    message3.setProperty(fakeProp, fakeProp);
    TispContext.runInContext(() -> eventService.fireSynchronousEvent(event));

    TimeUnit.MILLISECONDS.sleep(5);

    Message message4 = messagePersister.lookup(message1.getMessageId());
    Assertions.assertEquals(message1.getMessageId(), message4.getMessageId());
    Assertions.assertEquals("a", message4.getProperty(MessageFields.status.name()), "Wrong status on message");
    Assertions.assertTrue(Long.valueOf(message4.getProperty(MessageFields.nextCheckTime.name())) < System.currentTimeMillis(),
        "Message nextCheckTime not updated. Needed to initiate next transition");
    Assertions.assertNull(message4.getProperty(fakeProp), "Properties has been overwritten");
  }

  @Test
  void testModuleAckDelivered() throws Exception {
    String componentShortName = mockConfiguration.getComponentShortName();
    mockConfiguration.setPropertySpecific(componentShortName, "notset" + ".scheduler.module.readprocess.start", "false");
    mockConfiguration.setPropertySpecific(componentShortName, "notset" + ".scheduler.module.persistfordown", "true");
    mockConfiguration.setPropertySpecific(componentShortName, "notset" + ".scheduler.module.inprocessdown", "false");
    mockConfiguration.setPropertySpecific(componentShortName, "notset" + ".scheduler.module.instackdown", "false");
    mockConfiguration.setPropertySpecific(componentShortName, "notset" + ".scheduler.module.connection.established", "false");
    mockConfiguration.setPropertySpecific(componentShortName, "notset" + ".scheduler.module.ack", "true");
    mockConfiguration.setPropertySpecific(componentShortName, "notset" + ".scheduler.module.delete-after-publish", "false");

    Message message1 = createMessage("hepp" + Math.random());
    MessagePersister messagePersister = SchedulePersistanceFactory.makeMessagePersister();

    initSchedulerModule();
    setupModuleAfterInitConfig();

    TispContext.runInContext(() -> schedulerModule.down(message1));

    Message message2 = messagePersister.lookup(message1.getMessageId());
    Assertions.assertEquals(message1.getMessageId(), message2.getMessageId());
    Assertions.assertEquals(ActivityStatus.a.name(), message2.getProperty(MessageFields.status.name()));

    Message message3 = createMessage("hepp" + Math.random(), MessageStatus.DELIVERED);
    message3.setMessageId(message1.getMessageId());
    AckEvent event = new AckEvent(message3);
    TispContext.runInContext(() -> eventService.fireSynchronousEvent(event));

    Message message4 = messagePersister.lookup(message1.getMessageId());
    Assertions.assertEquals(message1.getMessageId(), message4.getMessageId());
    Assertions.assertNull(message4.getProperty(MessageFields.status.name()));
  }

  @Test
  void testModuleAckNotSendStatus() throws Exception {
    String componentShortName = mockConfiguration.getComponentShortName();
    mockConfiguration.setPropertySpecific(componentShortName, "notset" + ".scheduler.module.readprocess.start", "false");
    mockConfiguration.setPropertySpecific(componentShortName, "notset" + ".scheduler.module.persistfordown", "true");
    mockConfiguration.setPropertySpecific(componentShortName, "notset" + ".scheduler.module.inprocessdown", "false");
    mockConfiguration.setPropertySpecific(componentShortName, "notset" + ".scheduler.module.instackdown", "false");
    mockConfiguration.setPropertySpecific(componentShortName, "notset" + ".scheduler.module.connection.established", "false");
    mockConfiguration.setPropertySpecific(componentShortName, "notset" + ".scheduler.module.ack", "true");

    Message message1 = createMessage("hepp" + Math.random());
    MessagePersister messagePersister = SchedulePersistanceFactory.makeMessagePersister();

    initSchedulerModule();
    setupModuleAfterInitConfig();

    TispContext.runInContext(() -> schedulerModule.down(message1));

    Message message2 = messagePersister.lookup(message1.getMessageId());
    Assertions.assertEquals(message1.getMessageId(), message2.getMessageId());
    Assertions.assertEquals(ActivityStatus.a.name(), message2.getProperty(MessageFields.status.name()));

    Message message3 = createMessage("hepp" + Math.random(), MessageStatus.DELIVERED);
    message3.setMessageId(message1.getMessageId());
    AckEvent event = new AckEvent(message3);
    TispContext.runInContext(() -> eventService.fireSynchronousEvent(event));

    Message message4 = messagePersister.lookup(message1.getMessageId());
    Assertions.assertEquals(message1.getMessageId(), message4.getMessageId());
    Assertions.assertNull(message4.getProperty(MessageFields.status.name()));

    Assertions.assertNull(lastDowned, "Sent notification anyway!");
  }

  @Test
  void testModuleAckRejected() throws Exception {
    String componentShortName = mockConfiguration.getComponentShortName();
    mockConfiguration.setPropertySpecific(componentShortName, "notset" + ".scheduler.module.readprocess.start", "false");
    mockConfiguration.setPropertySpecific(componentShortName, "notset" + ".scheduler.module.persistfordown", "true");
    mockConfiguration.setPropertySpecific(componentShortName, "notset" + ".scheduler.module.inprocessdown", "false");
    mockConfiguration.setPropertySpecific(componentShortName, "notset" + ".scheduler.module.instackdown", "false");
    mockConfiguration.setPropertySpecific(componentShortName, "notset" + ".scheduler.module.connection.established", "false");
    mockConfiguration.setPropertySpecific(componentShortName, "notset" + ".scheduler.module.ack", "true");
    mockConfiguration.setPropertySpecific(componentShortName, "notset" + ".scheduler.module.delete-after-publish", "false");

    Message message1 = createMessage("hepp" + Math.random(), MessageStatus.REJECTED);
    MessagePersister messagePersister = SchedulePersistanceFactory.makeMessagePersister();

    initSchedulerModule();
    setupModuleAfterInitConfig();

    TispContext.runInContext(() -> schedulerModule.down(message1));

    Message message2 = messagePersister.lookup(message1.getMessageId());
    Assertions.assertEquals(message1.getMessageId(), message2.getMessageId());
    Assertions.assertEquals(ActivityStatus.a.name(), message2.getProperty(MessageFields.status.name()));

    Message message3 = createMessage("hepp" + Math.random(), MessageStatus.REJECTED);
    message3.setMessageId(message1.getMessageId());
    AckEvent event = new AckEvent(message3);
    TispContext.runInContext(() -> eventService.fireSynchronousEvent(event));

    TimeUnit.MILLISECONDS.sleep(5);

    Message message4 = messagePersister.lookup(message1.getMessageId());

    Assertions.assertEquals(message1.getMessageId(), message4.getMessageId());
    Assertions.assertEquals(message1.getProperty("STATUS"), message4.getProperty("STATUS"));
    Assertions.assertEquals(null, message4.getProperty(MessageFields.status.name()), "Wrong status on message");
  }

  @Test
  void testModuleAckSendStatus() throws Exception {
    String componentShortName = mockConfiguration.getComponentShortName();
    mockConfiguration.setPropertySpecific(componentShortName, "notset" + ".scheduler.module.readprocess.start", "false");
    mockConfiguration.setPropertySpecific(componentShortName, "notset" + ".scheduler.module.persistfordown", "true");
    mockConfiguration.setPropertySpecific(componentShortName, "notset" + ".scheduler.module.inprocessdown", "false");
    mockConfiguration.setPropertySpecific(componentShortName, "notset" + ".scheduler.module.instackdown", "false");
    mockConfiguration.setPropertySpecific(componentShortName, "notset" + ".scheduler.module.connection.established", "false");
    mockConfiguration.setPropertySpecific(componentShortName, "notset" + ".scheduler.module.ack", "true");
    mockConfiguration.setPropertySpecific(componentShortName, "notset" + ".scheduler.module.delete-after-publish", "false");

    Message message1 = createMessage("hepp" + Math.random());
    message1.setProperty(MetaData.JMS_REPLY_TO, "Hepp");
    MessagePersister messagePersister = SchedulePersistanceFactory.makeMessagePersister();

    initSchedulerModule();
    setupModuleAfterInitConfig();

    TispContext.runInContext(() -> schedulerModule.down(message1));

    Message message2 = messagePersister.lookup(message1.getMessageId());
    Assertions.assertEquals(message1.getMessageId(), message2.getMessageId());
    Assertions.assertEquals(ActivityStatus.a.name(), message2.getProperty(MessageFields.status.name()));

    Message message3 = createMessage("hepp" + Math.random(), MessageStatus.DELIVERED);
    message3.setMessageId(message1.getMessageId());
    AckEvent event = new AckEvent(message3);
    message3.setProperty(MetaData.JMS_REPLY_TO, "Hepp");
    TispContext.runInContext(() -> eventService.fireSynchronousEvent(event));

    Message message4 = messagePersister.lookup(message1.getMessageId());
    Assertions.assertEquals(message1.getMessageId(), message4.getMessageId());
    Assertions.assertNull(message4.getProperty(MessageFields.status.name()));

    Assertions.assertNotNull(lastDowned, "Sent notification missing.");

    Assertions.assertFalse(message4.getProperties().toString().indexOf("ACK") < 0);
  }

  @Test
  void testModuleBasic() throws Exception {
    String componentShortName = mockConfiguration.getComponentShortName();
    mockConfiguration.setPropertySpecific(componentShortName, "notset" + ".scheduler.module.readprocess.start", "false");
    mockConfiguration.setPropertySpecific(componentShortName, "notset" + ".scheduler.module.persistfordown", "true");
    mockConfiguration.setPropertySpecific(componentShortName, "notset" + ".scheduler.module.inprocessdown", "true");
    mockConfiguration.setPropertySpecific(componentShortName, "notset" + ".scheduler.module.instackdown", "true");
    mockConfiguration.setPropertySpecific(componentShortName, "notset" + ".scheduler.module.connection.established", "true");
    mockConfiguration.setPropertySpecific(componentShortName, "notset" + ".scheduler.module.delete-after-publish", "false");
    mockConfiguration.setPropertySpecific(componentShortName, "notset" + ".scheduler.module.ack", "true");

    initSchedulerModule();
    setupModuleAfterInitConfig();
  }

  @Test
  void testModuleDeleteAfterPublish() throws Exception {
    String componentShortName = mockConfiguration.getComponentShortName();
    mockConfiguration.setPropertySpecific(componentShortName, "notset" + ".scheduler.module.readprocess.start", "false");
    mockConfiguration.setPropertySpecific(componentShortName, "notset" + ".scheduler.module.persistfordown", "true");
    mockConfiguration.setPropertySpecific(componentShortName, "notset" + ".scheduler.module.inprocessdown", "false");
    mockConfiguration.setPropertySpecific(componentShortName, "notset" + ".scheduler.module.instackdown", "false");
    mockConfiguration.setPropertySpecific(componentShortName, "notset" + ".scheduler.module.connection.established", "false");
    mockConfiguration.setPropertySpecific(componentShortName, "notset" + ".scheduler.module.delete-after-publish", "true");
    mockConfiguration.setPropertySpecific(componentShortName, "notset" + ".scheduler.module.ack", "true");

    Message message1 = createMessage("hepp" + Math.random());
    MessagePersister messagePersister = SchedulePersistanceFactory.makeMessagePersister();

    initSchedulerModule();
    setupModuleAfterInitConfig();

    TispContext.runInContext(() -> schedulerModule.down(message1));

    Message message2 = messagePersister.lookup(message1.getMessageId());
    Assertions.assertEquals(message1.getMessageId(), message2.getMessageId());
    Assertions.assertEquals(ActivityStatus.a.name(), message2.getProperty(MessageFields.status.name()));

    Message message3 = createMessage("hepp" + Math.random(), MessageStatus.DELIVERED);
    message3.setMessageId(message1.getMessageId());
    AckEvent event = new AckEvent(message3);
    TispContext.runInContext(() -> eventService.fireSynchronousEvent(event));

    Message message4 = messagePersister.lookup(message1.getMessageId());
    Assertions.assertNull(message4);
  }

  @Test
  void testModuleDown() throws Exception {
    String componentShortName = mockConfiguration.getComponentShortName();
    mockConfiguration.setPropertySpecific(componentShortName, "notset" + ".scheduler.module.readprocess.start", "false");
    mockConfiguration.setPropertySpecific(componentShortName, "notset" + ".scheduler.module.persistfordown", "true");
    mockConfiguration.setPropertySpecific(componentShortName, "notset" + ".scheduler.module.inprocessdown", "false");
    mockConfiguration.setPropertySpecific(componentShortName, "notset" + ".scheduler.module.instackdown", "false");
    mockConfiguration.setPropertySpecific(componentShortName, "notset" + ".scheduler.module.connection.established", "false");
    mockConfiguration.setPropertySpecific(componentShortName, "notset" + ".scheduler.module.ack", "true");
    mockConfiguration.setPropertySpecific(componentShortName, "notset" + ".scheduler.module.delete-after-publish", "false");

    Message message1 = createMessage("hepp" + Math.random());
    MessagePersister messagePersister = SchedulePersistanceFactory.makeMessagePersister();

    initSchedulerModule();
    setupModuleAfterInitConfig();

    TispContext.runInContext(() -> schedulerModule.down(message1));

    Message message2 = messagePersister.lookup(message1.getMessageId());
    Assertions.assertNotNull(message2);
    Assertions.assertEquals(message1.getMessageId(), message2.getMessageId());
  }

  @Test
  void testModuleUp() throws Exception {
    String componentShortName = mockConfiguration.getComponentShortName();
    mockConfiguration.setPropertySpecific(componentShortName, "notset" + ".scheduler.module.readprocess.start", "false");
    mockConfiguration.setPropertySpecific(componentShortName, "notset" + ".scheduler.module.persistfordown", "false");
    mockConfiguration.setPropertySpecific(componentShortName, "notset" + ".scheduler.module.inprocessdown", "false");
    mockConfiguration.setPropertySpecific(componentShortName, "notset" + ".scheduler.module.instackdown", "false");
    mockConfiguration.setPropertySpecific(componentShortName, "notset" + ".scheduler.module.connection.established", "false");
    mockConfiguration.setPropertySpecific(componentShortName, "notset" + ".scheduler.module.delete-after-publish", "false");
    mockConfiguration.setPropertySpecific(componentShortName, "notset" + ".scheduler.module.ack", "false");

    initSchedulerModule();
    setupModuleAfterInitConfig();

    int uppedCnt = upped;
    schedulerModule.up(createMessage("hepp" + Math.random()));

    Assertions.assertEquals(uppedCnt + 1, upped);
  }

  @Test
  void testSchedulerProcessorInherritedConfigTest() throws TceConfigException, ConfigurationException {
    mockConfiguration.setPropertySpecific(mockConfiguration.getComponentShortName(), "notset" + ".scheduler.processor.queuesize.max", "15");

    ProcessorMetricsReporter processorMetricsReporter = Mockito.mock(ProcessorMetricsReporter.class);
    SchedulerModuleMetricReporter schedulerModuleMetricReporter = Mockito.mock(SchedulerModuleMetricReporter.class);
    schedulerModule = new SchedulerModule(eventService, new SchedulerRegistry(), Mockito.mock(AbstractDeviceService.class), processorMetricsReporter,
        schedulerModuleMetricReporter, Mockito.mock(ModuleMetricReporter.class), vceEncryptionHandler);
    schedulerModule.setOldConfigProperties();

    SchedulerProcessor schedulerProcessor = new SchedulerProcessor(schedulerModule, processorMetricsReporter, schedulerModuleMetricReporter);
    // maxQSize is declared i superclass check this works to set
    Assertions.assertEquals(15, schedulerProcessor.getMaxQSize());
  }

  private void initSchedulerModule() {
    schedulerModule = new SchedulerModule(eventService, new SchedulerRegistry(), Mockito.mock(AbstractDeviceService.class),
        Mockito.mock(ProcessorMetricsReporter.class), Mockito.mock(SchedulerModuleMetricReporter.class), Mockito.mock(ModuleMetricReporter.class),
        vceEncryptionHandler);
  }

  private void setupModuleAfterInitConfig() throws TceConfigException, ConfigurationException {
    schedulerModule.setDown(this);
    schedulerModule.setUp(this);
    schedulerModule.setOldConfigProperties();
    schedulerModule.deploy();
    schedulerModule.start();
  }
}
