package com.wirelesscar.tcevce.module.scheduler.db;

import java.sql.SQLException;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;

import com.wirelesscar.config.mock.MockConfiguration;
import com.wirelesscar.tce.db.common.api.ActivityStatus;
import com.wirelesscar.tce.db.source.TceDataSource;
import com.wirelesscar.tcevce.module.scheduler.api.Schedule;
import com.wirelesscar.tcevce.module.scheduler.cache.ScheduleCacheImpl;
import com.wirelesscar.tcevce.module.scheduler.cache.api.ScheduleCache;
import com.wirelesscar.tcevce.module.scheduler.cache.api.SchedulerCacheFactory;
import com.wirelesscar.tcevce.module.scheduler.db.api.SchedulePersistanceFactory;
import com.wirelesscar.tcevce.module.scheduler.db.api.SchedulePersister;

class SchedulePersisterImplTest {
  private static final int MINUTES = 500;
  private static final int SIZE = 800;

  @BeforeAll
  static void beforeAll() throws SQLException {
    MockConfiguration mockConfiguration = MockConfiguration.getConfig();
    mockConfiguration.setPropertySpecific(mockConfiguration.getComponentShortName(), TceDataSource.TEST_DB_PROP, "true");
    mockConfiguration.setPropertySpecific(mockConfiguration.getComponentShortName(), "scheduler.module.cache.minutes", Integer.toString(MINUTES));
    mockConfiguration.setPropertySpecific(mockConfiguration.getComponentShortName(), "scheduler.module.cache.size", Integer.toString(SIZE));

    SchedulerTestUtil.preps();

    SchedulerCacheFactory.scheduleCache().clear();
  }

  @Test
  void testCacheSize() {
    SchedulerCacheFactory.scheduleCache().clear();
    ScheduleCache scheduleCache = SchedulerCacheFactory.scheduleCache();
    Assertions.assertEquals(SIZE, scheduleCache.cacheSizeMax());
    Assertions.assertEquals(MINUTES, scheduleCache.cacheRetainMinutes());
  }

  @Test
  void testInsertLookupByCacheDelete() throws SQLException {
    ScheduleCacheImpl.getInstance().clear();
    SchedulePersister schedulePersister = SchedulePersistanceFactory.makeSchedulePersister();

    Schedule schedule1 = SchedulerTestUtil.createSchedule();

    Assertions.assertEquals(0, ScheduleCacheImpl.getInstance().getCacheSize());

    Schedule schedule2 = schedulePersister.persist(schedule1);

    Schedule schedule3 = SchedulerCacheFactory.scheduleCache().get(schedule2.getId(), true);
    Assertions.assertEquals(1, ScheduleCacheImpl.getInstance().getCacheSize());

    long deleted = schedulePersister.delete(schedule3);

    Assertions.assertNotNull(schedule3);
    Assertions.assertEquals(schedule2.getId(), schedule3.getId());
    Assertions.assertEquals(1_000_000, schedule3.getTimeToLive());
    Assertions.assertEquals(schedule2.getName(), schedule3.getName());
    Assertions.assertEquals(ActivityStatus.a, schedule3.getState());
    Assertions.assertEquals(1, deleted);

    Schedule schedule4 = schedulePersister.lookup(schedule2.getId());
    Assertions.assertNull(schedule4);
  }

  @Test
  void testInsertLookupByNameDelete() throws SQLException {
    SchedulePersister schedulePersister = SchedulePersistanceFactory.makeSchedulePersister();

    Schedule schedule1 = SchedulerTestUtil.createSchedule();

    Schedule schedule2 = schedulePersister.persist(schedule1);

    Schedule schedule3 = schedulePersister.lookupByName(schedule2.getName());

    long deleted = schedulePersister.delete(schedule3);

    Assertions.assertNotNull(schedule3);
    Assertions.assertEquals(schedule2.getId(), schedule3.getId());
    Assertions.assertEquals(1_000_000, schedule3.getTimeToLive());
    Assertions.assertEquals(schedule2.getName(), schedule3.getName());
    Assertions.assertEquals(ActivityStatus.a, schedule3.getState());
    Assertions.assertEquals(1, deleted);

    Schedule schedule4 = schedulePersister.lookup(schedule2.getId());
    Assertions.assertNull(schedule4);
  }

  @Test
  void testInsertLookupDelete() throws SQLException {
    SchedulePersister schedulePersister = SchedulePersistanceFactory.makeSchedulePersister();

    Schedule schedule1 = SchedulerTestUtil.createSchedule();

    Schedule schedule2 = schedulePersister.persist(schedule1);

    Schedule schedule3 = schedulePersister.lookup(schedule2.getId());

    long deleted = schedulePersister.delete(schedule3);

    Assertions.assertNotNull(schedule3);
    Assertions.assertEquals(schedule2.getId(), schedule3.getId());
    Assertions.assertEquals(1_000_000, schedule3.getTimeToLive());
    Assertions.assertEquals(schedule2.getName(), schedule3.getName());
    Assertions.assertEquals(ActivityStatus.a, schedule3.getState());
    Assertions.assertEquals(1, deleted);

    Schedule schedule4 = schedulePersister.lookup(schedule2.getId());
    Assertions.assertNull(schedule4);
  }
}
