package com.wirelesscar.tcevce.module.scheduler.db;

import java.nio.charset.StandardCharsets;
import java.sql.Date;
import java.sql.SQLException;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.EnumMap;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Map.Entry;
import java.util.TimeZone;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.framework.context.TispContext;
import com.wirelesscar.config.mock.MockConfiguration;
import com.wirelesscar.tce.db.common.api.ActivityStatus;
import com.wirelesscar.tce.db.common.db.api.MessageFields;
import com.wirelesscar.tce.db.common.db.api.MessagePersister;
import com.wirelesscar.tce.db.source.TceDataSource;
import com.wirelesscar.tce.module.api.Message;
import com.wirelesscar.tce.module.api.MessagePriority;
import com.wirelesscar.tce.module.api.MetaData;
import com.wirelesscar.tcevce.module.scheduler.db.api.SchedulePersistanceFactory;

class ScheduledMessagePersisterImplTest {
  private static final DateFormat dateFormat = dateFormat();
  private static final String PAYLOAD = "Hepp payload hopp";
  private static final String REMOVE_TIME = Long.toString(Long.MAX_VALUE);
  private static final String TRANSITION_A = "Transition A";

  @BeforeAll
  static void beforeAll() throws SQLException {
    MockConfiguration mockConfiguration = MockConfiguration.getConfig();
    mockConfiguration.setPropertySpecific(mockConfiguration.getComponentShortName(), TceDataSource.TEST_DB_PROP, "true");

    SchedulerTestUtil.preps();
  }

  private static Message createMessage() {
    return createMessage(false);
  }

  private static Message createMessage(boolean largeMsg) {
    Message message = new Message();

    message.setPayload(largeMsg ? makeLargePayload() : PAYLOAD.getBytes(StandardCharsets.UTF_8));
    message.setVehicleID("" + Math.random() * 1_000_000_000);
    message.setProperty("1", "a");
    message.setProperty("2", "b");
    message.setProperty("3", "c");
    message.setProperty(MessageFields.status.name(), ActivityStatus.a.name());
    message.setProperty(MessageFields.lastTransitionName.name(), TRANSITION_A);
    message.setProperty(MessageFields.removeTime.name(), REMOVE_TIME);
    message.setProperty(MessageFields.nextCheckTime.name(), Long.toString(System.currentTimeMillis()));
    message.setProperty(MetaData.SERVICE_ID, "S1");
    message.setProperty(MetaData.MESSAGE_ID, "M1");
    message.setProperty(MetaData.SRP_DST_VERSION, "1");

    return message;
  }

  private static DateFormat dateFormat() {
    DateFormat formatter = new SimpleDateFormat("yyyyMMdd", Locale.ROOT);
    formatter.setTimeZone(TimeZone.getTimeZone("GMT"));
    return formatter;
  }

  private static Long getPartitionDate(long timestamp) {
    return Long.valueOf(dateFormat.format(new Date(timestamp)));
  }

  private static byte[] makeLargePayload() {
    StringBuilder stringBuilder = new StringBuilder();

    while (stringBuilder.length() < 12_000) {
      stringBuilder.append("aaaasdfghjklöåäooiiuytreeqwqzxxccvvbnm,.-+09876543321zx");
    }

    return stringBuilder.toString().getBytes(StandardCharsets.UTF_8);
  }

  private static Message persist(MessagePersister messagePersister, Message message) {
    AtomicReference<Message> atomicReference = new AtomicReference<>();
    TispContext.runInContext(() -> {
      try {
        atomicReference.set(messagePersister.persist(message));
      } catch (SQLException e) {
        throw new IllegalStateException(e);
      }
    });
    return atomicReference.get();
  }

  @BeforeEach
  void beforeEach() {
    MockConfiguration mockConfiguration = MockConfiguration.getConfig();
    mockConfiguration.setPropertySpecific(mockConfiguration.getComponentShortName(), "notset.scheduler.workerthread.min_future_ms_for_connection_estbl", "1");
  }

  @Test
  void hashTest() {
    Map<String, Integer> map = new HashMap<>();

    for (int i = 0; i < 100_000; i++) {
      String string = "ABCDE00" + String.format(Locale.ROOT, "%09d", i);

      String hash = String.format(Locale.ROOT, "%05d", string.hashCode());
      hash = hash.substring(hash.length() - 2);

      Integer antal = map.get(hash);
      antal = antal != null ? ++antal : 1;
      map.put(hash, antal);
    }

    Integer max = 0;
    Integer min = 9_999_999;
    for (Entry<String, Integer> entry : map.entrySet()) {
      if (entry.getValue().compareTo(max) > 0) {
        max = entry.getValue();
      }

      if (entry.getValue().compareTo(min) < 0) {
        min = entry.getValue();
      }
    }
  }

  @Test
  void testDeleteOldMsg() throws SQLException {
    MessagePersister messagePersister = SchedulePersistanceFactory.makeMessagePersister();
    messagePersister.deleteAll();

    for (int i = 0; i < 10; i++) {
      Message message1 = createMessage();

      message1.setProperty(MessageFields.removeTime.name(), Long.toString(System.currentTimeMillis() - 999_999));
      Message message2 = persist(messagePersister, message1);
      message2.setProperty(MessageFields.status.name(), "d");
      messagePersister.updateStatusAndProps(message2);
    }

    int deleted = 0;
    List<Message> toBeDeleted = messagePersister.findMessagesToDelete();
    for (Message message : toBeDeleted) {
      deleted += messagePersister.delete(message);
    }

    Assertions.assertEquals(10, toBeDeleted.size());
    Assertions.assertEquals(10, deleted);

    List<Message> m3List = messagePersister.findForMe(Collections.emptyMap());
    Assertions.assertEquals(0, m3List.size());
  }

  @Test
  void testInsertFindAllDelete() throws SQLException {
    MessagePersister messagePersister = SchedulePersistanceFactory.makeMessagePersister();
    messagePersister.deleteAll();

    List<Message> list = new ArrayList<>();
    for (int i = 0; i < 10; i++) {
      Message message1 = createMessage();

      Message message2 = persist(messagePersister, message1);

      list.add(message2);
    }

    List<Message> messages = messagePersister.findForMe(Collections.emptyMap());

    for (Message message3 : messages) {
      long deleted = messagePersister.delete(message3);
      Assertions.assertEquals(1, deleted);

      Message message4 = messagePersister.lookup(message3.getMessageId());
      Assertions.assertNull(message4);
    }

    Assertions.assertEquals(10, messages.size());
  }

  @Test
  void testInsertFindByPriority() throws SQLException {
    MessagePersister messagePersister = SchedulePersistanceFactory.makeMessagePersister();

    Message message1 = createMessage();
    Message message2 = persist(messagePersister, message1);

    Message msgPL = createMessage();
    msgPL.setProperty(MetaData.PRIORITY, MessagePriority.Low.name());
    Message mPL = persist(messagePersister, msgPL);

    Message msgPH = createMessage();
    msgPH.setProperty(MetaData.PRIORITY, MessagePriority.Urgent.name());
    Message mPH = persist(messagePersister, msgPH);

    List<Message> m3List = messagePersister.findAllActiveAndReady(true);

    messagePersister.delete(message2);
    messagePersister.delete(mPL);
    messagePersister.delete(mPH);

    Message m3H = m3List.get(0);
    Message m3L = m3List.get(2);
    Assertions.assertEquals(3, m3List.size());

    Assertions.assertEquals(mPH.getMessageId(), m3H.getMessageId());
    Assertions.assertEquals(mPL.getMessageId(), m3L.getMessageId());
  }

  @Test
  void testInsertFindDelete() throws SQLException {
    MessagePersister messagePersister = SchedulePersistanceFactory.makeMessagePersister();
    messagePersister.deleteAll();

    Message message1 = createMessage();

    Message message2 = persist(messagePersister, message1);

    Map<MessageFields, String> map = new EnumMap<>(MessageFields.class);
    map.put(MessageFields.vehicleID, message2.getVehicleID());

    List<Message> messages = messagePersister.findForMe(Collections.emptyMap());

    Message message3 = messages.get(0);

    long deleted = messagePersister.delete(message3);

    Assertions.assertEquals(message2.getMessageId(), message3.getMessageId());
    Assertions.assertEquals(new String(message2.getPayload(), StandardCharsets.UTF_8), new String(message3.getPayload(), StandardCharsets.UTF_8));

    Assertions.assertEquals(1, deleted);

    Message message4 = messagePersister.lookup(message2.getMessageId());

    Assertions.assertNull(message4);
  }

  @Test
  void testInsertFindDeleteManyFieldsSelect() throws SQLException {
    MessagePersister messagePersister = SchedulePersistanceFactory.makeMessagePersister();
    messagePersister.deleteAll();

    Message message1 = createMessage();

    Message message2 = persist(messagePersister, message1);

    Map<MessageFields, String> map = new EnumMap<>(MessageFields.class);
    map.put(MessageFields.vehicleID, message2.getVehicleID());
    map.put(MessageFields.status, ActivityStatus.a.name());

    List<Message> m3List = messagePersister.findForMe(map);

    Message message3 = m3List.get(0);

    long deleted = messagePersister.delete(message3);

    Assertions.assertEquals(message2.getMessageId(), message3.getMessageId());
    Assertions.assertEquals(new String(message2.getPayload(), StandardCharsets.UTF_8), new String(message3.getPayload(), StandardCharsets.UTF_8));

    Assertions.assertEquals(1, deleted);

    Message message4 = messagePersister.lookup(message2.getMessageId());
    Assertions.assertNull(message4);
  }

  @Test
  void testInsertLookupDelete() throws SQLException {
    MessagePersister messagePersister = SchedulePersistanceFactory.makeMessagePersister();
    messagePersister.deleteAll();

    Message message1 = createMessage();

    Message message2 = persist(messagePersister, message1);

    Message message3 = messagePersister.lookup(message2.getMessageId());

    long deleted = messagePersister.delete(message3);

    Assertions.assertEquals(message2.getMessageId(), message3.getMessageId());
    Assertions.assertEquals(new String(message2.getPayload(), StandardCharsets.UTF_8), new String(message3.getPayload(), StandardCharsets.UTF_8));

    Assertions.assertEquals(1, deleted);

    Message message4 = messagePersister.lookup(message2.getMessageId());
    Assertions.assertNull(message4);
  }

  @Test
  void testInsertLookupDeleteLargePayload() throws SQLException {
    MessagePersister messagePersister = SchedulePersistanceFactory.makeMessagePersister();
    messagePersister.deleteAll();

    Message message1 = createMessage(true);

    Message message2 = persist(messagePersister, message1);

    Message message3 = messagePersister.lookup(message2.getMessageId());

    long deleted = messagePersister.delete(message3);

    Assertions.assertEquals(message2.getMessageId(), message3.getMessageId());
    Assertions.assertEquals(new String(message2.getPayload(), StandardCharsets.UTF_8), new String(message3.getPayload(), StandardCharsets.UTF_8));

    Assertions.assertEquals(1, deleted);

    Message message4 = messagePersister.lookup(message2.getMessageId());
    Assertions.assertNull(message4);
  }

  @Test
  void testInsertLookupDeleteLargePayload2() throws SQLException {
    MessagePersister messagePersister = SchedulePersistanceFactory.makeMessagePersister();
    messagePersister.deleteAll();

    Message message1 = createMessage(true);
    message1.setProperty("BigProp", new String(makeLargePayload(), StandardCharsets.UTF_8));

    Message message2 = persist(messagePersister, message1);

    Message message3 = messagePersister.lookup(message2.getMessageId());

    long deleted = messagePersister.delete(message3);

    Assertions.assertEquals(message2.getMessageId(), message3.getMessageId());
    Assertions.assertEquals(message1.getProperty("BigProp"), message3.getProperty("BigProp"));
    Assertions.assertEquals(new String(message2.getPayload(), StandardCharsets.UTF_8), new String(message3.getPayload(), StandardCharsets.UTF_8));

    Assertions.assertEquals(1, deleted);

    Message message4 = messagePersister.lookup(message2.getMessageId());
    Assertions.assertNull(message4);
  }

  @Test
  void testInsertLookupDeleteLoad() throws SQLException {
    MessagePersister messagePersister = SchedulePersistanceFactory.makeMessagePersister();
    messagePersister.deleteAll();

    double rounds = 10;

    for (int i = 0; i < rounds; i++) {
      Message message1 = createMessage();

      Message message2 = persist(messagePersister, message1);

      Message message3 = messagePersister.lookup(message2.getMessageId());

      long deleted = messagePersister.delete(message3);

      Assertions.assertEquals(message2.getMessageId(), message3.getMessageId());
      Assertions.assertEquals(new String(message2.getPayload(), StandardCharsets.UTF_8), new String(message3.getPayload(), StandardCharsets.UTF_8));

      Assertions.assertEquals(1, deleted);

      Message message4 = messagePersister.lookup(message2.getMessageId());
      Assertions.assertNull(message4);
    }
  }

  @Test
  void testInsertLookupUpdateAckDeleteStateA() throws SQLException {
    MessagePersister messagePersister = SchedulePersistanceFactory.makeMessagePersister();

    Message message1 = createMessage();

    Message message2 = persist(messagePersister, message1);
    Message message3 = messagePersister.lookup(message2.getMessageId());

    Assertions.assertEquals(message2.getMessageId(), message3.getMessageId());
    Assertions.assertEquals(message2.getPayload().length, message3.getPayload().length);

    messagePersister.updateForAck(message2, ActivityStatus.a);
    message3 = messagePersister.lookup(message2.getMessageId());

    long deleted = messagePersister.delete(message3);

    Assertions.assertEquals(message2.getMessageId(), message3.getMessageId());
    Assertions.assertFalse(message3.getProperties().toString().indexOf("ACK") < 0);
    Assertions.assertEquals("a", message3.getProperties().get(MessageFields.status.name()));

    Assertions.assertEquals(1, deleted);

    Message message4 = messagePersister.lookup(message2.getMessageId());
    Assertions.assertNull(message4);
  }

  @Test
  void testInsertLookupUpdateAckDeleteStateD() throws SQLException {
    MessagePersister messagePersister = SchedulePersistanceFactory.makeMessagePersister();

    Message message1 = createMessage();

    Message message2 = persist(messagePersister, message1);
    Message message3 = messagePersister.lookup(message2.getMessageId());

    Assertions.assertEquals(message2.getMessageId(), message3.getMessageId());
    Assertions.assertEquals(message2.getPayload().length, message3.getPayload().length);

    messagePersister.updateForAck(message2, ActivityStatus.d);
    message3 = messagePersister.lookup(message2.getMessageId());

    long deleted = messagePersister.delete(message3);

    Assertions.assertEquals(message2.getMessageId(), message3.getMessageId());
    Assertions.assertFalse(message3.getProperties().toString().indexOf("ACK") < 0);
    Assertions.assertNull(message3.getProperties().get(MessageFields.status.name()));
    Assertions.assertEquals("1", message3.getProperties().get(MetaData.SRP_DST_VERSION.name()));

    Assertions.assertEquals(1, deleted);

    Message message4 = messagePersister.lookup(message2.getMessageId());
    Assertions.assertNull(message4);
  }

  @Test
  void testInsertLookupUpdateForConnectionEstablished() throws Exception {
    MessagePersister messagePersister = SchedulePersistanceFactory.makeMessagePersister();
    messagePersister.deleteAll();

    Message message1 = createMessage();
    Message message2 = persist(messagePersister, message1);
    Message message3 = messagePersister.lookup(message2.getMessageId());

    // update nextChecktime to future
    long nextTs = System.currentTimeMillis() + (1000 * 3600 * 24 * 2);
    message2.setProperty(MessageFields.nextCheckTime.name(), Long.toString(nextTs));
    messagePersister.updateNextCheckTime(message2, nextTs, -1);

    TimeUnit.MILLISECONDS.sleep(10);

    // check update ok
    message3 = messagePersister.lookup(message2.getMessageId());
    Assertions.assertTrue(Long.parseLong(message3.getProperty(MessageFields.nextCheckTime.name())) > (System.currentTimeMillis() + (1000 * 3600 * 24)));

    TimeUnit.MILLISECONDS.sleep(10);

    // update for Conn Est shall set partitiondate and nextChecktime to now
    messagePersister.updateForConnectionEstablished(message2.getVehicleID(), 1);

    TimeUnit.MILLISECONDS.sleep(10);

    message3 = messagePersister.lookup(message2.getMessageId());

    // select based on partition date
    Long pdate = getPartitionDate(System.currentTimeMillis());
    Map<String, String> map = new HashMap<>();
    map.put(MessageFields.id.name(), message3.getMessageId());
    map.put(MessageFields.partitionDate.name(), pdate.toString());

    // check one is found
    List<Message> list = messagePersister.find(map);
    Assertions.assertEquals(1, list.size());
    Assertions.assertTrue(Long.parseLong(list.get(0).getProperty(MessageFields.nextCheckTime.name())) < System.currentTimeMillis());

    long deleted = messagePersister.delete(message3);

    Assertions.assertEquals(1, deleted);

    Message message4 = messagePersister.lookup(message2.getMessageId());

    Assertions.assertNull(message4);
  }

  /** Check that update for conn est work with selected message ids */
  @Test
  void testInsertLookupUpdateForConnectionEstablishedPerMsg() throws Exception {
    MessagePersister messagePersister = SchedulePersistanceFactory.makeMessagePersister();
    messagePersister.deleteAll();

    // create msg
    Message message1 = createMessage();
    Message message2 = persist(messagePersister, message1);
    Message message3 = messagePersister.lookup(message2.getMessageId());
    Message message4 = createMessage();
    Message message5 = persist(messagePersister, message4);
    Message message6 = messagePersister.lookup(message5.getMessageId());

    Message message7 = createMessage();
    Message message8 = persist(messagePersister, message7);
    Message message9 = messagePersister.lookup(message8.getMessageId());

    // update nextChecktime to future
    long nextTs = System.currentTimeMillis() + (1000 * 3600 * 24 * 2);
    message8.setProperty(MessageFields.nextCheckTime.name(), Long.toString(nextTs));
    messagePersister.updateNextCheckTime(message8, nextTs, -1);
    message2.setProperty(MessageFields.nextCheckTime.name(), Long.toString(nextTs));
    messagePersister.updateNextCheckTime(message2, nextTs, -1);
    message5.setProperty(MessageFields.nextCheckTime.name(), Long.toString(nextTs));
    messagePersister.updateNextCheckTime(message5, nextTs, -1);

    // check update ok
    message9 = messagePersister.lookup(message8.getMessageId());
    Assertions.assertTrue(Long.parseLong(message9.getProperty(MessageFields.nextCheckTime.name())) > (System.currentTimeMillis() + (1_000 * 3_600 * 24)));
    message3 = messagePersister.lookup(message2.getMessageId());
    Assertions.assertTrue(Long.parseLong(message3.getProperty(MessageFields.nextCheckTime.name())) > (System.currentTimeMillis() + (1_000 * 3_600 * 24)));
    message6 = messagePersister.lookup(message5.getMessageId());
    Assertions.assertTrue(Long.parseLong(message3.getProperty(MessageFields.nextCheckTime.name())) > (System.currentTimeMillis() + (1_000 * 3_600 * 24)));

    // update for Conn Est shall set partitiondate and nextChecktime to now
    List<Message> msgs = new ArrayList<>();
    msgs.add(message3);
    msgs.add(message6);

    messagePersister.updateForConnectionEstablished(msgs);

    // check two updated, one not
    message9 = messagePersister.lookup(message8.getMessageId());
    message3 = messagePersister.lookup(message2.getMessageId());
    message6 = messagePersister.lookup(message5.getMessageId());

    Assertions.assertTrue(Long.parseLong(message9.getProperty(MessageFields.nextCheckTime.name())) > System.currentTimeMillis());
    Assertions.assertTrue(Long.parseLong(message3.getProperty(MessageFields.nextCheckTime.name())) < System.currentTimeMillis());
    Assertions.assertTrue(Long.parseLong(message6.getProperty(MessageFields.nextCheckTime.name())) < System.currentTimeMillis());

    messagePersister.delete(message9);
    messagePersister.delete(message3);
    messagePersister.delete(message6);
  }

  @Test
  void testInsertLookupUpdateNextTimeDelete() throws SQLException {
    MessagePersister messagePersister = SchedulePersistanceFactory.makeMessagePersister();
    messagePersister.deleteAll();

    Message message1 = createMessage();

    Message message2 = persist(messagePersister, message1);
    Message message3 = messagePersister.lookup(message2.getMessageId());

    Assertions.assertEquals(message2.getMessageId(), message3.getMessageId());
    Assertions.assertEquals(new String(message2.getPayload(), StandardCharsets.UTF_8), new String(message3.getPayload(), StandardCharsets.UTF_8));

    messagePersister.updateNextCheckTime(message2, 99, 88);
    message3 = messagePersister.lookup(message2.getMessageId());

    Assertions.assertEquals(message2.getMessageId(), message3.getMessageId());
    Assertions.assertTrue(Long.parseLong(message3.getProperty(MessageFields.removeTime.name())) > 0);

    long deleted = messagePersister.delete(message3);

    message2.getProperties().remove(MessageFields.removeTime.name());
    message3.getProperties().remove(MessageFields.removeTime.name());
    Assertions.assertEquals(new String(message2.getPayload(), StandardCharsets.UTF_8), new String(message3.getPayload(), StandardCharsets.UTF_8));

    Assertions.assertEquals(1, deleted);

    Message message4 = messagePersister.lookup(message2.getMessageId());
    Assertions.assertNull(message4);
  }

  @Test
  void testInsertLookupUpdateNextTimeTomorrowDelete() throws SQLException {
    MessagePersister messagePersister = SchedulePersistanceFactory.makeMessagePersister();
    messagePersister.deleteAll();

    Message message1 = createMessage();

    Message message2 = persist(messagePersister, message1);
    Message message3 = messagePersister.lookup(message2.getMessageId());

    Assertions.assertEquals(message2.getMessageId(), message3.getMessageId());
    Assertions.assertEquals(message1.getProperty(MetaData.HANDLE), message2.getProperty(MetaData.HANDLE));
    Assertions.assertEquals(message1.getProperty(MetaData.HANDLE), message3.getProperty(MetaData.HANDLE));
    Assertions.assertEquals(new String(message2.getPayload(), StandardCharsets.UTF_8), new String(message3.getPayload(), StandardCharsets.UTF_8));

    Message message4 = messagePersister.updateNextCheckTime(message2, System.currentTimeMillis() + (3600 * 24), 88);
    message3 = messagePersister.lookup(message2.getMessageId());
    message4 = messagePersister.lookup(message4.getMessageId());

    Assertions.assertEquals(message2.getMessageId(), message3.getMessageId());
    Assertions.assertEquals(message3.getMessageId(), message4.getMessageId());
    Assertions.assertEquals(message1.getProperty(MetaData.HANDLE), message4.getProperty(MetaData.HANDLE));
    Assertions.assertEquals(message1.getProperty(MetaData.HANDLE), message3.getProperty(MetaData.HANDLE));
    Assertions.assertEquals(ActivityStatus.a.name(), message4.getProperty(MessageFields.status.name()));
    Assertions.assertTrue(Long.parseLong(message3.getProperty(MessageFields.removeTime.name())) > 0);

    long deleted = messagePersister.delete(message3);

    message2.getProperties().remove(MessageFields.removeTime.name());
    message3.getProperties().remove(MessageFields.removeTime.name());
    Assertions.assertEquals(new String(message2.getPayload(), StandardCharsets.UTF_8), new String(message3.getPayload(), StandardCharsets.UTF_8));

    Assertions.assertEquals(1, deleted);

    Message message5 = messagePersister.lookup(message2.getMessageId());
    Assertions.assertNull(message5);
  }

  @Test
  void testInsertLookupUpdateNextTimeTomorrowDelete2() throws SQLException {
    MessagePersister messagePersister = SchedulePersistanceFactory.makeMessagePersister();
    messagePersister.deleteAll();

    Message message1 = createMessage();

    Message message2 = persist(messagePersister, message1);
    Message message3 = messagePersister.lookup(message2.getMessageId());

    Assertions.assertEquals(message2.getMessageId(), message3.getMessageId());
    Assertions.assertEquals(new String(message2.getPayload(), StandardCharsets.UTF_8), new String(message3.getPayload(), StandardCharsets.UTF_8));

    Message message4 = messagePersister.updateNextCheckTime(message2, System.currentTimeMillis() + (3600 * 24), System.currentTimeMillis() + (3600 * 24));
    message3 = messagePersister.lookup(message2.getMessageId());
    message4 = messagePersister.lookup(message4.getMessageId());

    Assertions.assertEquals(message2.getMessageId(), message3.getMessageId());
    Assertions.assertEquals(message3.getMessageId(), message4.getMessageId());
    Assertions.assertEquals(ActivityStatus.a.name(), message4.getProperty(MessageFields.status.name()));
    Assertions.assertTrue(Long.parseLong(message3.getProperty(MessageFields.removeTime.name())) > 0);

    long deleted = messagePersister.delete(message3);

    message2.getProperties().remove(MessageFields.removeTime.name());
    message3.getProperties().remove(MessageFields.removeTime.name());
    Assertions.assertEquals(new String(message2.getPayload(), StandardCharsets.UTF_8), new String(message3.getPayload(), StandardCharsets.UTF_8));

    Assertions.assertEquals(1, deleted);

    Message message5 = messagePersister.lookup(message2.getMessageId());
    Assertions.assertNull(message5);
  }

  @Test
  void testInsertLookupUpdateNextTimeTomorrowDelete3() throws SQLException {
    MessagePersister messagePersister = SchedulePersistanceFactory.makeMessagePersister();
    messagePersister.deleteAll();

    Message message1 = createMessage();

    Message message2 = persist(messagePersister, message1);
    Message message3 = messagePersister.lookup(message2.getMessageId());

    Assertions.assertEquals(message2.getMessageId(), message3.getMessageId());
    Assertions.assertEquals(new String(message2.getPayload(), StandardCharsets.UTF_8), new String(message3.getPayload(), StandardCharsets.UTF_8));

    Message m4 = messagePersister.updateNextCheckTime(message2, 0, System.currentTimeMillis() + (3600 * 24));
    message3 = messagePersister.lookup(message2.getMessageId());
    m4 = messagePersister.lookup(m4.getMessageId());

    Assertions.assertEquals(message2.getMessageId(), message3.getMessageId());
    Assertions.assertEquals(message3.getMessageId(), m4.getMessageId());
    Assertions.assertEquals(ActivityStatus.a.name(), m4.getProperty(MessageFields.status.name()));
    Assertions.assertTrue(Long.parseLong(message3.getProperty(MessageFields.removeTime.name())) > 0);

    long deleted = messagePersister.delete(message3);

    message2.getProperties().remove(MessageFields.removeTime.name());
    message3.getProperties().remove(MessageFields.removeTime.name());
    Assertions.assertEquals(new String(message2.getPayload(), StandardCharsets.UTF_8), new String(message3.getPayload(), StandardCharsets.UTF_8));

    Assertions.assertEquals(1, deleted);

    Message message5 = messagePersister.lookup(message2.getMessageId());
    Assertions.assertNull(message5);
  }

  @Test
  void testInsertLookupUpdateNextTimeTomorrowDelete4() throws SQLException {
    MessagePersister messagePersister = SchedulePersistanceFactory.makeMessagePersister();
    messagePersister.deleteAll();

    Message message1 = createMessage();

    Message message2 = persist(messagePersister, message1);
    Message message3 = messagePersister.lookup(message2.getMessageId());

    Assertions.assertEquals(message2.getMessageId(), message3.getMessageId());
    Assertions.assertEquals(new String(message2.getPayload(), StandardCharsets.UTF_8), new String(message3.getPayload(), StandardCharsets.UTF_8));

    Message message4 = messagePersister.updateNextCheckTime(message2, System.currentTimeMillis() + (3600 * 24), 0);
    message3 = messagePersister.lookup(message2.getMessageId());
    message4 = messagePersister.lookup(message4.getMessageId());

    Assertions.assertEquals(message2.getMessageId(), message3.getMessageId());
    Assertions.assertEquals(message3.getMessageId(), message4.getMessageId());
    Assertions.assertEquals(ActivityStatus.a.name(), message4.getProperty(MessageFields.status.name()));
    Assertions.assertTrue(Long.parseLong(message3.getProperty(MessageFields.removeTime.name())) > 0);

    long deleted = messagePersister.delete(message3);

    message2.getProperties().remove(MessageFields.removeTime.name());
    message3.getProperties().remove(MessageFields.removeTime.name());
    Assertions.assertEquals(new String(message2.getPayload(), StandardCharsets.UTF_8), new String(message3.getPayload(), StandardCharsets.UTF_8));

    Assertions.assertEquals(1, deleted);

    Message message5 = messagePersister.lookup(message2.getMessageId());
    Assertions.assertNull(message5);
  }

  @Test
  void testInsertLookupUpdateStatusDelete() throws SQLException {
    MessagePersister messagePersister = SchedulePersistanceFactory.makeMessagePersister();
    messagePersister.deleteAll();

    Message message1 = createMessage();

    Message message2 = persist(messagePersister, message1);
    Message message3 = messagePersister.lookup(message2.getMessageId());

    Assertions.assertEquals(message2.getMessageId(), message3.getMessageId());
    Assertions.assertEquals(new String(message2.getPayload(), StandardCharsets.UTF_8), new String(message3.getPayload(), StandardCharsets.UTF_8));

    message2.setProperty(MessageFields.status.name(), ActivityStatus.w.name());
    messagePersister.updateStatusAndProps(message2);
    message3 = messagePersister.lookup(message2.getMessageId());

    long deleted = messagePersister.delete(message3);

    Assertions.assertEquals(message2.getMessageId(), message3.getMessageId());
    Assertions.assertEquals(ActivityStatus.w.name(), message3.getProperty(MessageFields.status.name()));
    Assertions.assertNotNull(message3.getProperty(MessageFields.nextCheckTime.name()));
    Assertions.assertEquals(new String(message2.getPayload(), StandardCharsets.UTF_8), new String(message3.getPayload(), StandardCharsets.UTF_8));

    Assertions.assertEquals(1, deleted);

    Message message4 = messagePersister.lookup(message2.getMessageId());
    Assertions.assertNull(message4);
  }

  @Test
  void testInsertLookupUpdateStatusDeleteWithProps() throws SQLException {
    MessagePersister messagePersister = SchedulePersistanceFactory.makeMessagePersister();
    messagePersister.deleteAll();

    Message message1 = createMessage();

    Message message2 = persist(messagePersister, message1);
    Message message3 = messagePersister.lookup(message2.getMessageId());

    Assertions.assertEquals(message1.getProperty(MetaData.HANDLE), message2.getProperty(MetaData.HANDLE));
    Assertions.assertEquals(message1.getProperty(MetaData.HANDLE), message3.getProperty(MetaData.HANDLE));
    Assertions.assertEquals(message2.getMessageId(), message3.getMessageId());
    Assertions.assertEquals(new String(message2.getPayload(), StandardCharsets.UTF_8), new String(message3.getPayload(), StandardCharsets.UTF_8));

    message2.setProperty(MessageFields.status.name(), ActivityStatus.w.name());
    messagePersister.updateStatusAndProps(message2);
    message3 = messagePersister.lookup(message2.getMessageId());

    long deleted = messagePersister.delete(message3);

    Assertions.assertEquals(message1.getProperty(MetaData.HANDLE), message3.getProperty(MetaData.HANDLE));
    Assertions.assertEquals(message2.getMessageId(), message3.getMessageId());
    Assertions.assertEquals(ActivityStatus.w.name(), message3.getProperty(MessageFields.status.name()));
    Assertions.assertNotNull(message3.getProperty(MessageFields.nextCheckTime.name()));
    Assertions.assertEquals(new String(message2.getPayload(), StandardCharsets.UTF_8), new String(message3.getPayload(), StandardCharsets.UTF_8));

    Assertions.assertEquals(1, deleted);

    Message message4 = messagePersister.lookup(message2.getMessageId());
    Assertions.assertNull(message4);
  }
}
