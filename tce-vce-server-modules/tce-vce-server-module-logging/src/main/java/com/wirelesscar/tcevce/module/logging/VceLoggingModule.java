package com.wirelesscar.tcevce.module.logging;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import com.wirelesscar.componentbase.logging.Logging;
import com.wirelesscar.tce.module.api.Message;
import com.wirelesscar.tce.module.api.MetaData;
import com.wirelesscar.tce.module.api.ModuleBase;
import com.wirelesscar.tce.module.api.ModuleTypeConstants;
import com.wirelesscar.tce.module.metrics.ModuleMetricReporter;
import com.wirelesscar.tce.utils.LoggingHelper;

@Component(ModuleTypeConstants.VCE_LOGGING)
@Scope("prototype")
public class VceLoggingModule extends ModuleBase {
  private static final Logger logger = LoggerFactory.getLogger(VceLoggingModule.class);

  public VceLoggingModule(ModuleMetricReporter moduleMetricReporter) {
    super(moduleMetricReporter);
  }

  @Override
  public void down(Message message) {
    sendDown(message);
  }

  @Override
  public void up(Message message) {
    String interfaceName = getInterfaceNameForIntegrationLogging(message);

    logger.debug("doing integration logging for: {}", interfaceName);

    LoggingHelper.integrationLogging(message, message.getPayload(), Logging.Status.SUCCESS, Logging.Direction.CLIENT_IN, interfaceName,
        LoggingHelper.createExtraMetaDataBuilder()
            .putIfNotNull(LoggingHelper.ExtraMetaData.KEYS.mobileDirection, MetaData.MO.name())
            .putIfNotNull(LoggingHelper.ExtraMetaData.KEYS.serviceId, message.getProperty(MetaData.VCE_SERVICE))
            .putIfNotNull(LoggingHelper.ExtraMetaData.KEYS.stackName, message.getProperty(MetaData.TRANSPORT_TYPE))
            .getAsOptional());

    sendUp(message);
  }

  String getInterfaceNameForIntegrationLogging(Message message) {
    StringBuilder interfaceName = new StringBuilder();
    if (message.isStatus()) {
      interfaceName.append("MTSTATUS");
      interfaceName.append('/');
      interfaceName.append(message.getProperty(MetaData.TRANSPORT_TYPE));
      interfaceName.append('/');
      interfaceName.append(message.getProperty(MetaData.STATUS));
    } else {
      interfaceName.append(MetaData.MO);
      interfaceName.append('/');
      interfaceName.append(message.getProperty(MetaData.TRANSPORT_TYPE));
      interfaceName.append('/');
      interfaceName.append(message.getProperty(MetaData.VCE_SERVICE));
    }
    return interfaceName.toString();
  }
}
