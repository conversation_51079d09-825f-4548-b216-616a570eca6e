<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
     xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>com.volvo.tisp</groupId>
    <artifactId>tce-vce-server</artifactId>
    <version>0-SNAPSHOT</version>
  </parent>

  <artifactId>tce-vce-server-modules</artifactId>
  <packaging>pom</packaging>

  <modules>
    <module>tce-vce-server-module-identify</module>
    <module>tce-vce-server-module-logging</module>
    <module>tce-vce-server-module-mo-throttling</module>
    <module>tce-vce-server-module-mt-doorkeeper</module>
    <module>tce-vce-server-module-persist</module>
    <module>tce-vce-server-module-router-subscription</module>
    <module>tce-vce-server-module-scheduler</module>
    <module>tce-vce-server-module-schedule-selector</module>
    <module>tce-vce-server-module-segmentation</module>
    <module>tce-vce-server-module-split-ack</module>
  </modules>
</project>
