package com.wirelesscar.tcevce.module.subscriptionrouter;

import java.nio.charset.StandardCharsets;
import java.util.function.Predicate;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.wirelesscar.config.mock.MockConfiguration;
import com.wirelesscar.tce.api.v2.MoMessage;
import com.wirelesscar.tce.api.v2.Property;
import com.wirelesscar.tce.module.api.Message;
import com.wirelesscar.tce.module.api.MetaData;

class MoMessageV2OutputConverterTest {
  private static final String HANDLE = "myHandle";
  private static final MockConfiguration mockConfiguration = MockConfiguration.getConfig();
  private static final String VPI = "myVpi";

  private static void activateFeatureUseVpiWithoutFallbackToHandleForExternalMoMessageVpiField() {
    mockConfiguration.setPropertySpecific(mockConfiguration.getComponentShortName(),
        Feature.FEATURE_USE_VPI_WITHOUT_FALLBACK_TO_HANDLE_FOR_EXTERNAL_MO_MESSAGE_VPI_FIELD.toString(), "true");
  }

  private static void testConvert(String vpi, String handle, boolean useVpiWithoutFallbackToHandleForExternalMoMessage, boolean useVehicleTimeStamp) {
    // arrange
    Message inMsg = new Message();

    if (vpi != null) {
      inMsg.setVehicleID(vpi);
      inMsg.setProperty(MetaData.VPI, vpi);
    } else {
      inMsg.setVehicleID(handle);
    }

    // The vehicle timestamp is in seconds
    if (useVehicleTimeStamp) {
      inMsg.setProperty(MetaData.SRP_TIMESTAMP, String.valueOf(System.currentTimeMillis() / 1000L));
    }

    inMsg.setProperty(MetaData.HANDLE, handle);
    inMsg.setPayload("myPayload".getBytes(StandardCharsets.UTF_8));

    inMsg.setProperty(MetaData.SRP_DST_SERVICE, "99");
    inMsg.setProperty(MetaData.SRP_DST_VERSION, "88");

    // act
    MoMessage outMsg = MoMessageV2OutputConverter.INSTANCE.apply(inMsg);

    // assert
    if (useVpiWithoutFallbackToHandleForExternalMoMessage) {
      Assertions.assertEquals(vpi, outMsg.getVehiclePlatformId());
    } else {
      if (vpi != null) {
        Assertions.assertEquals(vpi, outMsg.getVehiclePlatformId());
      } else {
        Assertions.assertEquals(handle, outMsg.getVehiclePlatformId());
      }
    }

    Assertions.assertEquals(handle, outMsg.getHandle());
    Assertions.assertArrayEquals("myPayload".getBytes(StandardCharsets.UTF_8), outMsg.getPayload());
    Assertions.assertEquals(99, outMsg.getSrpOption().getDstService());
    Assertions.assertEquals(88, outMsg.getSrpOption().getDstVersion());

    if (useVehicleTimeStamp) {
      final Predicate<Property> isVehicleTimestampPresent = property -> MoMessageV2OutputConverter.VEHICLE_TIMESTAMP.equals(property.getKey());

      Assertions.assertTrue(outMsg.getSolutionSpecificProperties().stream().anyMatch(isVehicleTimestampPresent));

      final long incomingTimestamp = Long.parseLong(inMsg.getProperty(MetaData.SRP_TIMESTAMP));
      final Property outgoingTimestampProp = outMsg.getSolutionSpecificProperties()
          .stream()
          .filter(isVehicleTimestampPresent)
          .findFirst()
          .orElse(null);

      // Verify the outgoing timestamp is the incoming timestamp times 1000
      Assertions.assertNotNull(outgoingTimestampProp);
      Assertions.assertEquals(incomingTimestamp, Long.parseLong(outgoingTimestampProp.getValue()));
    }
  }

  @BeforeEach
  void beforeEach() {
    mockConfiguration.deletePropertySpecific(mockConfiguration.getComponentShortName(),
        Feature.FEATURE_USE_VPI_WITHOUT_FALLBACK_TO_HANDLE_FOR_EXTERNAL_MO_MESSAGE_VPI_FIELD.toString());
  }

  @Test
  void testConvert() {
    testConvert(VPI, HANDLE, false, false);
  }

  @Test
  void testConvertFeatureActiveVpiWithoutFallbackToHandle() {
    activateFeatureUseVpiWithoutFallbackToHandleForExternalMoMessageVpiField();
    testConvert(VPI, HANDLE, true, false);
  }

  @Test
  void testConvertNoHandle() {
    testConvert(VPI, null, false, false);
  }

  @Test
  void testConvertNoVpi() {
    testConvert(null, HANDLE, false, false);
  }

  @Test
  void testConvertNoVpiAndFeatureActiveVpiWithoutFallbackToHandle() {
    activateFeatureUseVpiWithoutFallbackToHandleForExternalMoMessageVpiField();
    testConvert(null, HANDLE, true, false);
  }

  @Test
  void testConvertWithoutVehicleTimestamp() {
    testConvert(VPI, HANDLE, false, false);
  }

  @Test
  void testConvertWithVehicleTimestamp() {
    testConvert(VPI, HANDLE, false, true);
  }
}
