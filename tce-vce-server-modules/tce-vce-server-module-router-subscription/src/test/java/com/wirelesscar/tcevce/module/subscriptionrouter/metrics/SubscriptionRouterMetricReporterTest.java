package com.wirelesscar.tcevce.module.subscriptionrouter.metrics;

import java.time.Duration;

import org.junit.jupiter.api.Test;

import io.micrometer.core.instrument.Tags;

class SubscriptionRouterMetricReporterTest {
  private static final String STACK_TEST = "stack-test";

  @Test
  void logNamePublishTest() {
    MetricsReporterTestUtils.initReporterAndTest(SubscriptionRouterMetricReporter::new, (meterRegistry, subscriptionRouterMetricReporter) -> {
      subscriptionRouterMetricReporter.logNamePublish(Duration.ofMillis(2));
      MetricsReporterTestUtils.checkTimer(meterRegistry, Duration.ofMillis(2), 1, SubscriptionRouterMetricReporter.METRIC_NAME_PUBLISH, Tags.empty());
    });
  }

  @Test
  void logUpPublishedTest() {
    MetricsReporterTestUtils.initReporterAndTest(SubscriptionRouterMetricReporter::new, (meterRegistry, subscriptionRouterMetricReporter) -> {
      subscriptionRouterMetricReporter.logUpPublished(STACK_TEST, Duration.ofMillis(2));
      MetricsReporterTestUtils.checkTimer(meterRegistry, Duration.ofMillis(2), 1, SubscriptionRouterMetricReporter.METRIC_UP_PUBLISHED,
          Tags.of(SubscriptionRouterMetricReporter.STACK_VAR_NAME, STACK_TEST));
    });
  }

  @Test
  void logUpTotLatencyTest() {
    MetricsReporterTestUtils.initReporterAndTest(SubscriptionRouterMetricReporter::new, (meterRegistry, subscriptionRouterMetricReporter) -> {
      subscriptionRouterMetricReporter.logUpTotLatency(STACK_TEST, Duration.ofMillis(2));
      MetricsReporterTestUtils.checkTimer(meterRegistry, Duration.ofMillis(2), 1, SubscriptionRouterMetricReporter.METRIC_UP_TOT_LATENCY,
          Tags.of(SubscriptionRouterMetricReporter.STACK_VAR_NAME, STACK_TEST));
    });
  }

  @Test
  void onUpDroppedTest() {
    MetricsReporterTestUtils.initReporterAndTest(SubscriptionRouterMetricReporter::new, (meterRegistry, subscriptionRouterMetricReporter) -> {
      subscriptionRouterMetricReporter.onUpDropped(STACK_TEST);
      MetricsReporterTestUtils.checkCounter(meterRegistry, 1, SubscriptionRouterMetricReporter.METRIC_UP_DROPPED,
          Tags.of(SubscriptionRouterMetricReporter.STACK_VAR_NAME, STACK_TEST));
    });
  }

  @Test
  void onUpSubscriptionMissingTest() {
    MetricsReporterTestUtils.initReporterAndTest(SubscriptionRouterMetricReporter::new, (meterRegistry, subscriptionRouterMetricReporter) -> {
      subscriptionRouterMetricReporter.onUpSubscriptionMissing(STACK_TEST);
      MetricsReporterTestUtils.checkCounter(meterRegistry, 1, SubscriptionRouterMetricReporter.METRIC_UP_SUBSCRIPTION_MISSING,
          Tags.of(SubscriptionRouterMetricReporter.STACK_VAR_NAME, STACK_TEST));
    });
  }
}
