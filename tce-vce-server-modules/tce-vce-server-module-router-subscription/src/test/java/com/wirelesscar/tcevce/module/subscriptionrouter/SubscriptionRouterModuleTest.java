package com.wirelesscar.tcevce.module.subscriptionrouter;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionException;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import com.volvo.tisp.subscriptionrepository.client.MessagePublisher;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;
import com.wirelesscar.config.mock.MockConfiguration;
import com.wirelesscar.tce.module.api.Message;
import com.wirelesscar.tce.module.api.exception.EngineRuntimeException;
import com.wirelesscar.tce.module.api.exception.NoConsumerException;
import com.wirelesscar.tce.module.metrics.ModuleMetricReporter;
import com.wirelesscar.tce.utils.GenericConfigNames;
import com.wirelesscar.tcevce.module.subscriptionrouter.metrics.SubscriptionRouterMetricReporter;

class SubscriptionRouterModuleTest {
  public static final Vpi VPI = Vpi.ofString("1234567890ABCDEF1234567890ABCDEF");

  public static Message createMessage() {
    Message message = new Message("1234567", new byte[42]);
    message.setVehicleID(VPI.toString());
    return message;
  }

  @BeforeAll
  static void beforeAll() {
    MockConfiguration mockConfiguration = MockConfiguration.getConfig();
    mockConfiguration.setPropertySpecific(mockConfiguration.getComponentShortName(), GenericConfigNames.POST_JMS_UP, "true");
    mockConfiguration.setPlatformProperty("servicediscovery.subr", "http://localhost");
  }

  private static SubscriptionRouterModule createSubscriptionRouterModule() {
    return new SubscriptionRouterModule(Mockito.mock((MessagePublisher.class)), Mockito.mock(SubscriptionRouterMetricReporter.class),
        Mockito.mock(ModuleMetricReporter.class), Mockito.mock(MtStatusResponder.class));
  }

  private static SubscriptionRouterModule createSubscriptionRouterModule(MessagePublisher messagePublisher) {
    return new SubscriptionRouterModule(messagePublisher, Mockito.mock(SubscriptionRouterMetricReporter.class),
        Mockito.mock(ModuleMetricReporter.class), Mockito.mock(MtStatusResponder.class));
  }

  @Test
  void configBasicTest() {
    SubscriptionRouterModule subscriptionRouterModule = createSubscriptionRouterModule();

    Map<String, List<String>> optionsMap = new HashMap<>();
    String config = "default:OPTION_KEY1,OPTION_KEY2";

    subscriptionRouterModule.populateOptions(optionsMap, config);

    Assertions.assertEquals(1, optionsMap.size());
    Assertions.assertEquals(2, optionsMap.get("default").size());
  }

  @Test
  void configNoPostUpByRefTest() {
    SubscriptionRouterModule subscriptionRouterModule = createSubscriptionRouterModule();
    subscriptionRouterModule.deploy();
  }

  @Test
  void configNoPostUpTest() throws Exception {
    MockConfiguration mockConfiguration = MockConfiguration.getConfig();
    mockConfiguration.setPropertySpecific(mockConfiguration.getComponentShortName(), GenericConfigNames.POST_JMS_UP, "false");

    SubscriptionRouterModule subscriptionRouterModule = createSubscriptionRouterModule();
    subscriptionRouterModule.setOldConfigProperties();
    subscriptionRouterModule.deploy();
  }

  @Test
  void configToManyTest() {
    SubscriptionRouterModule subscriptionRouterModule = createSubscriptionRouterModule();

    Map<String, List<String>> optionsMap = new HashMap<>();
    String config = "default:OPTION_KEY1,OPTION_KEY2;SOLUTION2:OPTION_KEY1,OPTION_KEY2,OPTION_KEY3";

    AssertThrows.exception(() -> subscriptionRouterModule.populateOptions(optionsMap, config), "Only 1 option allowed at this time: default",
        EngineRuntimeException.class);
  }

  @Test
  void configWrongNameTest() {
    SubscriptionRouterModule subscriptionRouterModule = createSubscriptionRouterModule();

    Map<String, List<String>> optionsMap = new HashMap<>();
    String config = "SOLUTION2:OPTION_KEY1,OPTION_KEY2,OPTION_KEY3";

    AssertThrows.exception(() -> subscriptionRouterModule.populateOptions(optionsMap, config), "Only default option allowed at this time: default",
        EngineRuntimeException.class);
  }

  @Test
  void moduleTest() {
    SubscriptionRouterModule subscriptionRouterModule = createSubscriptionRouterModule();
    subscriptionRouterModule.deploy();
    subscriptionRouterModule.start();
  }

  @Test
  void noConfigTest() {
    SubscriptionRouterModule subscriptionRouterModule = createSubscriptionRouterModule();

    Map<String, List<String>> optionsMap = new HashMap<>();
    String config = "";

    subscriptionRouterModule.populateOptions(optionsMap, config);

    Assertions.assertEquals(0, optionsMap.size());
  }

  @Test
  void splitLogicTest() {
    String test = " tag1, tag2, tag3 ,tag4";
    List<String> processingTagList = List.of(test.trim().split("\\s*,\\s*"));
    int i = 1;

    for (String testTag : processingTagList) {
      Assertions.assertEquals("tag" + i, testTag);
      i++;
    }
  }

  @Test
  void upTest() {
    MessagePublisher<com.wirelesscar.tce.module.api.Message> messagePublisher = Mockito.mock(MessagePublisher.class);
    SubscriptionRouterModule subscriptionRouterModule = createSubscriptionRouterModule(messagePublisher);

    CompletableFuture<Integer> future = CompletableFuture.completedFuture(1);
    createMessagePublisher(messagePublisher, future);

    Message message = createMessage();
    subscriptionRouterModule.up(message);

    Mockito.verify(messagePublisher, Mockito.times(1)).newMessage();
    Mockito.verify(messagePublisher.newMessage(), Mockito.times(1)).publish(message);
  }

  @Test
  void upTestWithNoSubscribers() {
    MessagePublisher<com.wirelesscar.tce.module.api.Message> messagePublisher = Mockito.mock(MessagePublisher.class);
    SubscriptionRouterModule subscriptionRouterModule = createSubscriptionRouterModule(messagePublisher);

    CompletableFuture<Integer> future = CompletableFuture.completedFuture(0);
    createMessagePublisher(messagePublisher, future);

    Message message = createMessage();

    CompletionException thrownException = Assertions.assertThrows(CompletionException.class, () -> {
      subscriptionRouterModule.up(message);
    });

    Assertions.assertTrue(thrownException.getCause() instanceof NoConsumerException);
    Assertions.assertEquals("No subscribers for message with id " + message.getMessageId(), thrownException.getCause().getMessage());

    Mockito.verify(messagePublisher, Mockito.times(1)).newMessage();
    Mockito.verify(messagePublisher.newMessage(), Mockito.times(1)).publish(message);
  }

  private void createMessagePublisher(MessagePublisher<Message> messagePublisher, CompletableFuture<Integer> future) {
    MessagePublisher.Message<Message> publisherMessage = Mockito.mock(MessagePublisher.Message.class);
    Mockito.when(messagePublisher.newMessage()).thenReturn(publisherMessage);
    Mockito.when(publisherMessage.publish(Mockito.any(Message.class))).thenReturn(future);
  }
}
