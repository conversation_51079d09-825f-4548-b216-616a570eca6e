package com.wirelesscar.tcevce.module.subscriptionrouter;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.ArgumentMatchers;
import org.mockito.Mockito;
import org.springframework.jms.core.JmsTemplate;

import com.wirelesscar.tce.api.v2.MtStatusMessage;
import com.wirelesscar.tce.client.opus.MessageTypesJms;
import com.wirelesscar.tce.module.api.Message;
import com.wirelesscar.tce.module.api.MessageStatus;
import com.wirelesscar.tce.module.api.MetaData;
import com.wirelesscar.tcevce.module.subscriptionrouter.metrics.SubscriptionRouterMetricReporter;

class MtStatusResponderTest {
  private static final String CORRELATION_ID = "someCorrelationId";
  private static final String REPLY_TO_QUEUE = "REPLYTO.QUEUE";

  private final JmsTemplate jmsTemplate = Mockito.mock(JmsTemplate.class);
  private final Message message = createMessage();

  private static void checkMtStatusMessage(MtStatusMessage mtStatusMessage) {
    Assertions.assertEquals("DELIVERED", mtStatusMessage.getStatus());
    Assertions.assertEquals(CORRELATION_ID, mtStatusMessage.getCorrelationId());
    Assertions.assertNull(mtStatusMessage.getVehiclePlatformId());
  }

  private static Message createMessage() {
    Message message = Message.createStatusMessage(MessageStatus.DELIVERED);

    message.setMessageId("messageId-1");
    message.setVehicleID("vehicleID-1");
    message.setProperty(MetaData.JMS_CORRELATION_ID, CORRELATION_ID);
    message.setProperty(MetaData.JMS_REPLY_TO, REPLY_TO_QUEUE);
    message.setProperty(MetaData.EXTERNAL_MSG_VERSION, "1.0");

    return message;
  }

  @Test
  void publishMtStatusMessageTest() {
    message.setProperty(MetaData.EXTERNAL_MSG_VERSION, MessageTypesJms.VERSION_2_0);
    message.setProperty(MetaData.EXTERNAL_MSG_TYPE, MessageTypesJms.TCE_MT_MESSAGE_TYPE);

    new MtStatusResponder(Mockito.mock(SubscriptionRouterMetricReporter.class), jmsTemplate).publish(message);

    ArgumentCaptor<MtStatusMessage> argumentCaptor = ArgumentCaptor.forClass(MtStatusMessage.class);
    Mockito.verify(jmsTemplate).convertAndSend(ArgumentMatchers.eq(REPLY_TO_QUEUE), argumentCaptor.capture(), ArgumentMatchers.any());

    checkMtStatusMessage(argumentCaptor.getValue());
  }
}
