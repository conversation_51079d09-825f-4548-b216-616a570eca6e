package com.wirelesscar.tcevce.module.subscriptionrouter.metrics;

import java.time.Duration;

import org.springframework.stereotype.Component;

import com.volvo.tisp.vc.main.utils.lib.Validate;

import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;

@Component
public class SubscriptionRouterMetricReporter {
  static final String METRIC_NAME_PUBLISH = "module.subrouter.mtstatus.published";
  static final String METRIC_UP_DROPPED = "module.subrouter.up.dropped";
  static final String METRIC_UP_PUBLISHED = "module.subrouter.up.published";
  static final String METRIC_UP_SUBSCRIPTION_MISSING = "module.subrouter.up.subscription.missing";
  static final String METRIC_UP_TOT_LATENCY = "module.subrouter.up.totMsgLatency";
  static final String STACK_VAR_NAME = "stackName";
  private static final String DURATION_VAR_NAME = "duration";

  private final MeterRegistry meterRegistry;
  private final Timer namePublishCounter;

  public SubscriptionRouterMetricReporter(MeterRegistry meterRegistry) {
    this.meterRegistry = meterRegistry;

    namePublishCounter = meterRegistry.timer(METRIC_NAME_PUBLISH);
  }

  public void logNamePublish(Duration duration) {
    Validate.notNegative(duration, DURATION_VAR_NAME);

    namePublishCounter.record(duration);
  }

  public void logUpPublished(String stackName, Duration duration) {
    Validate.notEmpty(stackName, STACK_VAR_NAME);
    Validate.notNegative(duration, DURATION_VAR_NAME);

    meterRegistry.timer(METRIC_UP_PUBLISHED, STACK_VAR_NAME, stackName).record(duration);
  }

  public void logUpTotLatency(String stackName, Duration duration) {
    Validate.notEmpty(stackName, STACK_VAR_NAME);
    Validate.notNegative(duration, DURATION_VAR_NAME);

    meterRegistry.timer(METRIC_UP_TOT_LATENCY, STACK_VAR_NAME, stackName).record(duration);
  }

  public void onUpDropped(String stackName) {
    Validate.notEmpty(stackName, STACK_VAR_NAME);

    meterRegistry.counter(METRIC_UP_DROPPED, STACK_VAR_NAME, stackName).increment();
  }

  public void onUpSubscriptionMissing(String stackName) {
    Validate.notEmpty(stackName, STACK_VAR_NAME);

    meterRegistry.counter(METRIC_UP_SUBSCRIPTION_MISSING, STACK_VAR_NAME, stackName).increment();
  }
}
