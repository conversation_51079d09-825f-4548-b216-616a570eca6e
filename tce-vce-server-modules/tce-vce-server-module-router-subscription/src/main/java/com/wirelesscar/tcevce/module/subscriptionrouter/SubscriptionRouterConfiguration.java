package com.wirelesscar.tcevce.module.subscriptionrouter;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.volvo.tisp.subscriptionrepository.client.MessagePublisher;
import com.wirelesscar.tce.client.opus.MessageTypesJms;
import com.wirelesscar.tce.module.api.Message;

@Configuration
class SubscriptionRouterConfiguration {
  @Bean
  MessagePublisher<Message> createMessagePublisherMo(MessagePublisher.Builder builder) {
    return builder
        .messageType(MessageTypesJms.TCE_MO_MESSAGE_TYPE, Message.class)
        .version(MessageTypesJms.VERSION_2_0, MoMessageV2OutputConverter.INSTANCE)
        .build();
  }
}
