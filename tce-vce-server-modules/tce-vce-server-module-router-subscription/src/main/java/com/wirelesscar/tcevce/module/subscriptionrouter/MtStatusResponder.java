package com.wirelesscar.tcevce.module.subscriptionrouter;

import static com.wirelesscar.tce.module.api.MessageStatus.REJECTED;

import java.time.Duration;
import java.time.Instant;
import java.util.Optional;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.jms.core.JmsTemplate;
import org.springframework.stereotype.Component;

import com.volvo.tisp.framework.jms.TispJmsHeader;
import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.wirelesscar.tce.api.v2.MtStatusMessage;
import com.wirelesscar.tce.client.opus.MessageTypesJms;
import com.wirelesscar.tce.module.api.Message;
import com.wirelesscar.tce.module.api.MessageStatus;
import com.wirelesscar.tce.module.api.MetaData;
import com.wirelesscar.tcevce.module.subscriptionrouter.metrics.SubscriptionRouterMetricReporter;

/**
 * Send MT-status message on JMS to the REPLY_TO queue set on the original MT-message.
 *
 * <p>
 * Will use the same version for the MT-status message as the version of the original MT-message.
 *
 * <p>
 * Note: this interaction <b>does not use the subscription-repo</b>. MT-status is a request-response interaction.
 */
@Component
public class MtStatusResponder {
  private enum MessageVersion {
    V2;
  }

  private static final Logger logger = LoggerFactory.getLogger(MtStatusResponder.class);

  private final JmsTemplate jmsTemplate;
  private final SubscriptionRouterMetricReporter subscriptionRouterMetricReporter;

  public MtStatusResponder(SubscriptionRouterMetricReporter subscriptionRouterMetricReporter, JmsTemplate jmsTemplate) {
    this.subscriptionRouterMetricReporter = subscriptionRouterMetricReporter;
    this.jmsTemplate = jmsTemplate;
  }

  private static Object convertMessageToExternalObject(Message message, MessageVersion messageVersion) {
    switch (messageVersion) {
      case V2:
        return convertToMtStatusV2(message);
      default:
        throw new IllegalArgumentException("unknown messageVersion: " + messageVersion);
    }
  }

  private static MtStatusMessage convertToMtStatusV2(Message message) {
    MtStatusMessage mtStatusMessage = new MtStatusMessage();
    mtStatusMessage.setCorrelationId(message.getProperty(MetaData.JMS_CORRELATION_ID));
    if (message.getStatus() == REJECTED) {
      mtStatusMessage.setStatus(MessageStatus.TIMEOUT.name());
    } else {
      mtStatusMessage.setStatus(message.getStatus().name());
    }
    mtStatusMessage.setHandle(message.getProperty(MetaData.HANDLE));
    mtStatusMessage.setVehiclePlatformId(message.getProperty(MetaData.VPI));
    return mtStatusMessage;
  }

  private static Optional<MessageVersion> getMessageVersion(Message message) {
    String msgVersion = message.getProperty(MetaData.EXTERNAL_MSG_VERSION);
    String msgType = message.getProperty(MetaData.EXTERNAL_MSG_TYPE);

    if (msgType == null) {
      if (MessageTypesJms.VERSION_2_0.equals(msgVersion)) {
        return Optional.of(MessageVersion.V2);
      } else {
        return Optional.empty();
      }
    }

    if (MessageTypesJms.TCE_MT_MESSAGE_TYPE.equals(msgType)
        && MessageTypesJms.VERSION_2_0.equals(msgVersion)) {
      return Optional.of(MessageVersion.V2);
    } else {
      return Optional.empty();
    }
  }

  public void publish(Message message) {
    Validate.notNull(message, "message");

    Optional<MessageVersion> optional = getMessageVersion(message);

    if (optional.isEmpty()) {
      logger.warn("Unexpected message version: {}", message.getProperty(MetaData.EXTERNAL_MSG_VERSION));
      return;
    }

    MessageVersion messageVersion = optional.get();
    Object jaxbRootElem = convertMessageToExternalObject(message, messageVersion);

    Instant startTime = Instant.now();
    publishToJms(message, messageVersion, jaxbRootElem);
    Duration duration = Duration.between(startTime, Instant.now());

    subscriptionRouterMetricReporter.logNamePublish(duration);
  }

  private void publishToJms(Message message, MessageVersion messageVersion, Object jaxbRootElement) {
    String queueName = message.getProperty(MetaData.JMS_REPLY_TO);

    if (queueName == null || queueName.trim().isEmpty()) {
      logger.debug("not sending MtStatus, {} not set for msg: {}", MetaData.JMS_REPLY_TO, message);
      return;
    }

    logger.debug("preparing to send MtStatus to queue: {} for msg: {}", queueName, message);

    jmsTemplate.convertAndSend(queueName, jaxbRootElement, jmsMessage -> {
      String correlationId = message.getProperty(MetaData.JMS_CORRELATION_ID);
      if (correlationId != null && !correlationId.isEmpty()) {
        jmsMessage.setJMSCorrelationID(correlationId);
      }

      switch (messageVersion) {
        case V2:
          jmsMessage.setStringProperty(TispJmsHeader.MESSAGE_TYPE.value(), MessageTypesJms.TCE_MTSTATUS_MESSAGE_TYPE);
          jmsMessage.setStringProperty(TispJmsHeader.MESSAGE_TYPE_VERSION.value(), MessageTypesJms.VERSION_2_0);
          break;
        default:
          throw new IllegalArgumentException("unknown messageVersion: " + messageVersion);
      }

      return jmsMessage;
    });

    logger.debug("sent MtStatus to queue: {} for msg: {}", queueName, message);
  }
}
