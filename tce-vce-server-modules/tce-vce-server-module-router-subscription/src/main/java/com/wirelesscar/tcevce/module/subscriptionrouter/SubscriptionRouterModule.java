package com.wirelesscar.tcevce.module.subscriptionrouter;

import java.time.Duration;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import com.volvo.tisp.subscriptionrepository.client.MessagePublisher;
import com.wirelesscar.config.Config;
import com.wirelesscar.config.ConfigFactory;
import com.wirelesscar.tce.core.conf.TceConfig;
import com.wirelesscar.tce.module.api.Message;
import com.wirelesscar.tce.module.api.MetaData;
import com.wirelesscar.tce.module.api.ModuleBase;
import com.wirelesscar.tce.module.api.ModuleTypeConstants;
import com.wirelesscar.tce.module.api.exception.EngineRuntimeException;
import com.wirelesscar.tce.module.api.exception.NoConsumerException;
import com.wirelesscar.tce.module.metrics.ModuleMetricReporter;
import com.wirelesscar.tce.utils.GenericConfigNames;
import com.wirelesscar.tce.utils.MetadataUtils;
import com.wirelesscar.tcevce.module.subscriptionrouter.metrics.SubscriptionRouterMetricReporter;

/**
 * TODO, module need a make-over since subscription-repository was not finalized at the time of writing.
 * <p>
 * TODO, there subscription should always work only with MessageV1, etc.
 */
@Component(ModuleTypeConstants.SUBSCRIPTION_ROUTER)
@Scope("prototype")
public class SubscriptionRouterModule extends ModuleBase {
  private static final String METADATA = "metadata";
  private static final String OPTIONS = "options";
  private static final String POST_JMS_UP = "post-jms-up";
  private static final String PUBLISH_TIMEOUT = "publish-timeout";
  private static final String SYSTEM_HEADER = "SYSTEM";
  private static final String SYSTEM_PROPERTY_KEY = "system.id"; // global config for tce environment
  private static final Logger log = LoggerFactory.getLogger(SubscriptionRouterModule.class);

  private final MessagePublisher<Message> messagePublisher;
  private final MtStatusResponder mtStatusResponder;
  private final Map<String, List<String>> optionsMap = new HashMap<>();

  @TceConfig(configKey = OPTIONS, defaultValue = "")
  private String optionsMapStr = "";

  @TceConfig(configKey = PUBLISH_TIMEOUT, defaultValue = "5")
  private long publishTimeout;

  @TceConfig(configKey = POST_JMS_UP, defaultValue = "true")
  private boolean publishUp = true; // used to tmp stop posting to AMQ. Must me coordinated with not updating status in DB.

  private MetaData[] requiredMetadata = null;

  @TceConfig(configKey = METADATA, defaultValue = "")
  private String requiredMetadataStr = "";
  private final SubscriptionRouterMetricReporter subscriptionRouterMetricReporter;

  public SubscriptionRouterModule(MessagePublisher<Message> messagePublisher, SubscriptionRouterMetricReporter subscriptionRouterMetricReporter,
      ModuleMetricReporter moduleMetricReporter, MtStatusResponder mtStatusResponder) {
    super(moduleMetricReporter);
    this.messagePublisher = messagePublisher;
    this.mtStatusResponder = mtStatusResponder;
    this.subscriptionRouterMetricReporter = subscriptionRouterMetricReporter;
  }

  @Override
  public void deploy() {
    this.requiredMetadata = MetadataUtils.parseMetaData(this.requiredMetadataStr);
    populateOptions(this.optionsMap, this.optionsMapStr);

    log.info("publishTimeout: {}", this.publishTimeout);
    log.info("publishUp: {}", this.publishUp);
  }

  @Override
  public void down(final Message message) {
    MetadataUtils.validateRequiredMetaData(message, this.requiredMetadata);

    sendDown(message);
  }

  @Override
  public void up(final Message message) {
    try {
      MetadataUtils.validateRequiredMetaData(message, this.requiredMetadata);

      if (!this.publishUp) {
        log.info("Not publishing message as publishUp = false : {}", message.getMessageId());
        return;
      }

      if (message.isStatus() && message.getProperty(MetaData.JMS_REPLY_TO) == null) {
        log.trace("Dropping Status message with no JMSReplyTo, messageId: {}", message.getMessageId());

        subscriptionRouterMetricReporter.onUpDropped(getStackName());
        return; // drop status message that don't have reply-to
      }

      if (message.getProperty(MetaData.JMS_REPLY_TO) != null) {
        this.mtStatusResponder.publish(message);
      } else {
        final long ts00 = System.currentTimeMillis();
        final MessagePublisher.Message<Message> outMessage = this.messagePublisher.newMessage();

        final List<String> optionKeys = getOptions();
        for (final String optionKey : optionKeys) {
          final String optionValue = message.getProperty(optionKey);
          if (optionValue != null) {
            /*
             * Vehicle Services Core Team wants Softcars using ProcessingTags to have Transitive JMS Headers. See TECH-63243
             *
             * Sending Processing Tags as a List because, multiple teams may add Processing Tags to the same VPI, SubRepo will add the list as different
             * criteria
             *
             */
            if (optionKey.equals(MetaData.PROCESSING_TAG.name())) {
              final List<String> processingTagList = List.of(optionValue.trim().split("\\s*,\\s*"));
              outMessage.option(true, optionKey, processingTagList);
            } else {
              outMessage.option(optionKey, optionValue);
            }
          }
        }

        // set system option which should be used for subscription criteria
        final String system = ConfigFactory.getConfig().getString(SYSTEM_PROPERTY_KEY).orElse(null);
        if (system != null) {
          outMessage.option(true, SYSTEM_HEADER, system);
        }

        final String msStartStr = message.getProperty(MetaData.TS_RECEIVED.name());

        CompletableFuture<Integer> publishResult = outMessage.publish(message);

        publishResult.handle((noOfSubscribers, throwable) -> {
          if (throwable != null) {
            throw new EngineRuntimeException("Error publishing message", throwable);
          }

          if (noOfSubscribers == 0) {
            // No one subscribes to the posted message
            subscriptionRouterMetricReporter.onUpSubscriptionMissing(getStackName());
            throw new NoConsumerException("No subscribers for message with id " + message.getMessageId());
          }

          log.trace("Published {}", message);
          perfLog(msStartStr, System.currentTimeMillis() - ts00);
          return null;
        }).join();
      }
    } finally {
      // As this module is the last in chain, it is important to invoke sendUp() in order to get the
      // latency metrics
      sendUp(message);
    }
  }

  @Override
  protected void setOldModuleConfig(final Map<String, String> oldConfigProperties) {
    Config config = ConfigFactory.getConfig();
    oldConfigProperties.put(PUBLISH_TIMEOUT, config.getString("tce.subscriptionrouter." + PUBLISH_TIMEOUT).orElse("5"));
    oldConfigProperties.put(METADATA, config.getString(getStackName() + ".subscriptionrouter.module." + METADATA).orElse(""));
    oldConfigProperties.put(OPTIONS, config.getString(getStackName() + ".subscriptionrouter." + OPTIONS).orElse(""));
    oldConfigProperties.put(POST_JMS_UP, config.getString(GenericConfigNames.POST_JMS_UP).orElse("true"));
  }

  /**
   * Expected format of configString: SOLUTION1:OPTION_KEY1,OPTION_KEY2;SOLUTION2:OPTION_KEY1,OPTION_KEY2,OPTION_KEY3
   */
  void populateOptions(final Map<String, List<String>> optionsMap, final String configString) {
    final String[] solutionArr = configString.split(";");

    if (solutionArr.length > 1) {
      throw new EngineRuntimeException("Only 1 option allowed at this time: default");
    }

    for (final String element : solutionArr) {
      final String[] nameArr = element.split(":");
      if (nameArr.length == 2) {
        final String solutionName = nameArr[0];

        if (!solutionName.equals("default")) {
          throw new EngineRuntimeException("Only default option allowed at this time: default");
        }

        final List<String> list = List.of(nameArr[1].split(","));
        optionsMap.put(solutionName, list);
      }
    }
  }

  private List<String> getOptions() {
    final String serviceId = "default";

    List<String> options = this.optionsMap.get(serviceId);

    if (options == null) {
      options = Collections.emptyList();
    }

    return options;
  }

  private void perfLog(final String msStartStr, final long postTimeInMs) {
    if (msStartStr != null) {
      long timeInNanos = (System.currentTimeMillis() - Long.valueOf(msStartStr)) * 1_000_000;
      subscriptionRouterMetricReporter.logUpTotLatency(getStackName(), Duration.ofNanos(timeInNanos));
    }

    subscriptionRouterMetricReporter.logUpPublished(getStackName(), Duration.ofMillis(postTimeInMs));
  }
}
