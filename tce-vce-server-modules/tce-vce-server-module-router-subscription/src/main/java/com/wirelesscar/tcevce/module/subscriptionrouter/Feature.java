package com.wirelesscar.tcevce.module.subscriptionrouter;

import com.wirelesscar.config.Config;
import com.wirelesscar.config.ConfigFactory;

/**
 * Feature toogles for TCE.
 *
 * <p>
 * Usage: in env.properties
 *
 * <pre>
 * &lt;mycompshortname&gt;.FEATURE_TEST_CONFIGURED_TO_DISABLED=false
 * &lt;mycompshortname&gt;.FEATURE_TEST_CONFIGURED_TO_ENABLED=true
 * </pre>
 */
public enum Feature {
  /**
   * Propagate MSISDN in external MO message.
   *
   * <p>
   * Used for VCE / Caretrack in activation messages to propagate MSISDN to Caretrack for later use by a tool &quot;ct-admin tool&quot; that seems to rely on
   * MSISDN.
   *
   * <p>
   * Ref: https://jira1.srv.volvo.com:8443/browse/TECH-33213
   */
  FEATURE_PROPAGATE_MSISDN_IN_EXTERNAL_MO_MESSAGE,
  /**
   * Use the correct mapping of &quot;vehiclePlatformId&quot; to external MO message &quot;vehiclePlatformId&quot;, instead of using fallback to
   * &quot;handle&quot; if no &quot;vehiclePlatformId&quot; is present.
   *
   * <p>
   * The TCE implementation for populating the internal TCE message attribute vehicleID (<code>
   * com.wirelesscar.tce.module.api.Message.vehicleID</code>) set vehicleID to either &quot;handle&quot; or &quot;vehiclePlatformId&quot;. <br>
   * The default is to map vehicleID to outgoing external MO message as &quot;vehiclePlatformId&quot;, but this gets wrong when vehicleID contains handle. <br>
   * This feature flag, when set, makes the mapping to &quot;vehiclePlatformId&quot; correct, while the default maintains the old in-correct mapping.
   *
   * <p>
   * Ref: https://jira1.srv.volvo.com:8443/browse/TECH-32516
   */
  FEATURE_USE_VPI_WITHOUT_FALLBACK_TO_HANDLE_FOR_EXTERNAL_MO_MESSAGE_VPI_FIELD;

  private static final Config config = ConfigFactory.getConfig();

  public boolean isActive() {
    return config.getBoolean(this.name()).orElse(false);
  }
}
