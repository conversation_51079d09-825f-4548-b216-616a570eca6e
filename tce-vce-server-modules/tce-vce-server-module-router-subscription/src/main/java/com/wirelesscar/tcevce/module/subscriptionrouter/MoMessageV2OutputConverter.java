package com.wirelesscar.tcevce.module.subscriptionrouter;

import java.util.function.Function;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.wirelesscar.tce.api.v2.MoMessage;
import com.wirelesscar.tce.api.v2.Property;
import com.wirelesscar.tce.api.v2.SrpLevel;
import com.wirelesscar.tce.api.v2.SrpOption;
import com.wirelesscar.tce.module.api.Message;
import com.wirelesscar.tce.module.api.MetaData;

public final class MoMessageV2OutputConverter implements Function<Message, MoMessage> {
  public static final Function<Message, MoMessage> INSTANCE = new MoMessageV2OutputConverter();
  static final String VEHICLE_TIMESTAMP = "VehicleTimestamp";
  private static final Logger logger = LoggerFactory.getLogger(MoMessageV2OutputConverter.class);
  private static final String SOLUTION_SPECIFIC_PROPERTY_MSISDN = "MSISDN";

  private MoMessageV2OutputConverter() {
    // do nothing
  }

  @Override
  public MoMessage apply(final Message messageInt) {
    final MoMessage messageExt = new MoMessage();

    if (Feature.FEATURE_USE_VPI_WITHOUT_FALLBACK_TO_HANDLE_FOR_EXTERNAL_MO_MESSAGE_VPI_FIELD.isActive()) {
      messageExt.setVehiclePlatformId(messageInt.getProperty(MetaData.VPI));
    } else {
      // use default mode - which might contain either handle or VPI depending on what was available
      // during identify
      // Note: mapping handle to external field VPI is NOT correct, kept for temporarily backwards
      // compatibility - to be phased out. Receivers should explicitly
      // use handle or VPI from the message.
      // Ref: https://jira1.srv.volvo.com:8443/browse/TECH-32516
      messageExt.setVehiclePlatformId(messageInt.getVehicleID());
    }

    messageExt.setHandle(messageInt.getProperty(MetaData.HANDLE));
    messageExt.setPayload(messageInt.getPayload());

    if (messageInt.getProperty(MetaData.SRP_DST_SERVICE) != null) {
      SrpOption srpOption = new SrpOption();
      srpOption.setDstService(Integer.parseInt(messageInt.getProperty(MetaData.SRP_DST_SERVICE)));
      srpOption.setDstVersion(Integer.parseInt(messageInt.getProperty(MetaData.SRP_DST_VERSION)));
      if (messageInt.getProperty(MetaData.SRP_LEVEL) != null) {
        srpOption.setSrpLevel(SrpLevel.valueOf(messageInt.getProperty(MetaData.SRP_LEVEL)));
      }
      messageExt.setSrpOption(srpOption);
    }

    if (Feature.FEATURE_PROPAGATE_MSISDN_IN_EXTERNAL_MO_MESSAGE.isActive()) {
      Property property = new Property();
      property.setKey(SOLUTION_SPECIFIC_PROPERTY_MSISDN);
      property.setValue(messageInt.getProperty(MetaData.SMPP_SOURCE_ADDRESS));
      messageExt.getSolutionSpecificProperties().add(property);
    }

    // If SRP_TIMESTAMP is provided in the Message, copy it from Message => MoMessage
    final String messageTimeStamp = messageInt.getProperty(MetaData.SRP_TIMESTAMP.name());
    if (messageTimeStamp != null) {
      try {
        final long timeStamp = Long.parseLong(messageTimeStamp);
        final Property property = new Property();
        property.setKey(VEHICLE_TIMESTAMP);
        property.setValue(String.valueOf(timeStamp));
        messageExt.getSolutionSpecificProperties().add(property);
      } catch (final NumberFormatException e) {
        logger.warn("Failed parsing the vehicle message timestamp: {}", messageTimeStamp, e);
      }
    }

    return messageExt;
  }
}
