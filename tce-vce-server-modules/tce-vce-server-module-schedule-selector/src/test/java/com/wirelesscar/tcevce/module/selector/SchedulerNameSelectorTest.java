package com.wirelesscar.tcevce.module.selector;

import java.nio.charset.StandardCharsets;
import java.util.Optional;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.vc.test.utils.lib.AssertThrows;
import com.wirelesscar.tce.module.api.Message;
import com.wirelesscar.tce.module.api.MetaData;
import com.wirelesscar.tcevce.wecu.device.info.database.model.DeviceInfo;

class SchedulerNameSelectorTest {

  private static final String ACTIVATE_IMMOBILIZER_LEVEL_2_SCHEDULE_HINT = "activate-immobilizer-level2";
  private static final String ACTIVATE_IMMOBILIZER_LEVEL_3_SCHEDULE_HINT = "activate-immobilizer-level3";
  private static final String ACTIVATE_MACHINE_SCHEDULE_HINT = "activate-machine";
  private static final String UNKNOWN_SCHEDULE_HINT = "pelle";

  private static Message makeMessage(String hint) {
    Message message = makeMessage();
    message.setProperty(MetaData.SCHEDULE_HINT, hint);

    return message;
  }

  private static Message makeMessage() {
    Message message = new Message();
    message.setPayload("payload".getBytes(StandardCharsets.UTF_8));
    message.setVehicleID(String.valueOf(1_000_000_000));
    message.setProperty(MetaData.SERVICE_ID, "S1");
    message.setProperty(MetaData.MESSAGE_ID, "M1");
    message.setProperty(MetaData.HANDLE, TestUtils.HANDLE.toString());

    return message;
  }

  @Test
  void selectNameInvalidTest() {
    SchedulerNameSelector schedulerNameSelector = new SchedulerNameSelector();

    AssertThrows.illegalArgumentException(() -> schedulerNameSelector.selectName(null, TestUtils.createDeviceInfo()), "message must not be null");
    AssertThrows.illegalArgumentException(() -> schedulerNameSelector.selectName(new Message(), null), "deviceInfo must not be null");
  }

  @Test
  void selectNameMsisdnAndSatTest() {
    DeviceInfo deviceInfo = TestUtils.createDeviceInfoBuilder()
        .setSatelliteId(Optional.of(TestUtils.SATELLITE_ID))
        .build();

    SchedulerNameSelector schedulerNameSelector = new SchedulerNameSelector();

    Assertions.assertAll(
        () -> Assertions.assertEquals("SmsGprsSat_default_7d", schedulerNameSelector.selectName(makeMessage(UNKNOWN_SCHEDULE_HINT), deviceInfo)),
        () -> Assertions.assertEquals("SmsGprsSat_immobilizer_1d",
            schedulerNameSelector.selectName(makeMessage(ACTIVATE_IMMOBILIZER_LEVEL_2_SCHEDULE_HINT), deviceInfo)),
        () -> Assertions.assertEquals("SmsGprsSat_immobilizer_1d",
            schedulerNameSelector.selectName(makeMessage(ACTIVATE_IMMOBILIZER_LEVEL_3_SCHEDULE_HINT), deviceInfo)),
        () -> Assertions.assertEquals("SmsSat_activation_30d", schedulerNameSelector.selectName(makeMessage(ACTIVATE_MACHINE_SCHEDULE_HINT), deviceInfo)));
  }

  @Test
  void selectNameMsisdnTest() {
    DeviceInfo deviceInfo = TestUtils.createDeviceInfo();

    SchedulerNameSelector schedulerNameSelector = new SchedulerNameSelector();

    Assertions.assertAll(
        () -> Assertions.assertEquals("SmsGprs_default_7d", schedulerNameSelector.selectName(makeMessage(UNKNOWN_SCHEDULE_HINT), deviceInfo)),
        () -> Assertions.assertEquals("SmsGprs_immobilizer_1d",
            schedulerNameSelector.selectName(makeMessage(ACTIVATE_IMMOBILIZER_LEVEL_2_SCHEDULE_HINT), deviceInfo)),
        () -> Assertions.assertEquals("SmsGprs_immobilizer_1d",
            schedulerNameSelector.selectName(makeMessage(ACTIVATE_IMMOBILIZER_LEVEL_3_SCHEDULE_HINT), deviceInfo)),
        () -> Assertions.assertEquals("Sms_activation_30d", schedulerNameSelector.selectName(makeMessage(ACTIVATE_MACHINE_SCHEDULE_HINT), deviceInfo)));
  }

  @Test
  void selectNameNoScheduleHintTest() {
    DeviceInfo deviceInfo = TestUtils.createDeviceInfoBuilder()
        .setSatelliteId(Optional.of(TestUtils.SATELLITE_ID))
        .build();

    SchedulerNameSelector schedulerNameSelector = new SchedulerNameSelector();

    Assertions.assertEquals("SmsGprsSat_default_7d", schedulerNameSelector.selectName(makeMessage(), deviceInfo));
  }

  @Test
  void selectNameSatTest() {
    DeviceInfo deviceInfo = TestUtils.createDeviceInfoBuilder()
        .setSimInfo(Optional.empty())
        .setSatelliteId(Optional.of(TestUtils.SATELLITE_ID))
        .build();

    SchedulerNameSelector schedulerNameSelector = new SchedulerNameSelector();

    Assertions.assertAll(
        () -> Assertions.assertEquals("Sat_default_7d", schedulerNameSelector.selectName(makeMessage(UNKNOWN_SCHEDULE_HINT), deviceInfo)),
        () -> Assertions.assertEquals("Sat_immobilizer_1d",
            schedulerNameSelector.selectName(makeMessage(ACTIVATE_IMMOBILIZER_LEVEL_2_SCHEDULE_HINT), deviceInfo)),
        () -> Assertions.assertEquals("Sat_immobilizer_1d",
            schedulerNameSelector.selectName(makeMessage(ACTIVATE_IMMOBILIZER_LEVEL_3_SCHEDULE_HINT), deviceInfo)),
        () -> Assertions.assertEquals("Sat_activation_30d", schedulerNameSelector.selectName(makeMessage(ACTIVATE_MACHINE_SCHEDULE_HINT), deviceInfo)));
  }
}
