package com.wirelesscar.tcevce.module.selector;

import java.util.Optional;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.InOrder;
import org.mockito.Mockito;

import com.volvo.tisp.vc.test.utils.lib.AssertThrows;
import com.wirelesscar.config.mock.MockConfiguration;
import com.wirelesscar.tce.core.conf.TceConfigException;
import com.wirelesscar.tce.module.api.Message;
import com.wirelesscar.tce.module.api.MetaData;
import com.wirelesscar.tce.module.api.ModuleBase;
import com.wirelesscar.tce.module.api.exception.ValidationException;
import com.wirelesscar.tce.module.metrics.ModuleMetricReporter;
import com.wirelesscar.tcevce.wecu.device.info.cache.core.api.CacheDeviceInfoReader;
import com.wirelesscar.tcevce.wecu.device.info.database.model.PersistedDeviceInfo;

class VceScheduleSelectorModuleTest {
  private static Message createMessage() {
    Message message = new Message();
    message.setProperty(MetaData.VPI, TestUtils.VPI.toString());
    message.setProperty(MetaData.HANDLE, TestUtils.HANDLE.toString());
    return message;
  }

  private static MockConfiguration getEmptyMockConfiguration() {
    MockConfiguration mockConfiguration = MockConfiguration.getConfig();
    mockConfiguration.deleteAllProperties();
    return mockConfiguration;
  }

  private static void initModuleConfig(VceScheduleSelectorModule vceScheduleSelectorModule, ModuleBase moduleBase) {
    vceScheduleSelectorModule.setDown(moduleBase);
    vceScheduleSelectorModule.setUp(moduleBase);
  }

  @Test
  void downInvalidTest() {
    CacheDeviceInfoReader cacheDeviceInfoReader = Mockito.mock(CacheDeviceInfoReader.class);
    ModuleMetricReporter moduleMetricReporter = Mockito.mock(ModuleMetricReporter.class);
    ModuleBase moduleBase = Mockito.mock(ModuleBase.class);
    SchedulerNameSelector schedulerNameSelector = Mockito.mock(SchedulerNameSelector.class);

    VceScheduleSelectorModule vceScheduleSelectorModule = new VceScheduleSelectorModule(moduleMetricReporter, cacheDeviceInfoReader, schedulerNameSelector);
    initModuleConfig(vceScheduleSelectorModule, moduleBase);
    AssertThrows.illegalArgumentException(() -> vceScheduleSelectorModule.down(null), "message must not be null");

    Mockito.verifyNoInteractions(cacheDeviceInfoReader, moduleMetricReporter, schedulerNameSelector);
    Mockito.verify(moduleBase, Mockito.times(2)).getName();
    Mockito.verifyNoMoreInteractions(moduleBase);
  }

  @Test
  void downWhenDeviceFoundByHandleTest() {
    PersistedDeviceInfo persistedDeviceInfo = TestUtils.createPersistedDeviceInfo();
    String scheduleHint = "SmsGprs_default_7d";

    Message message = createMessage();

    CacheDeviceInfoReader cacheDeviceInfoReader = Mockito.mock(CacheDeviceInfoReader.class);
    ModuleBase moduleBase = Mockito.mock(ModuleBase.class);
    ModuleMetricReporter moduleMetricReporter = Mockito.mock(ModuleMetricReporter.class);
    SchedulerNameSelector schedulerNameSelector = Mockito.mock(SchedulerNameSelector.class);

    Mockito.when(cacheDeviceInfoReader.findDeviceInfoByVpi(TestUtils.VPI)).thenReturn(Optional.empty());
    Mockito.when(cacheDeviceInfoReader.findDeviceInfoByHandle(TestUtils.HANDLE)).thenReturn(Optional.of(persistedDeviceInfo));
    Mockito.when(schedulerNameSelector.selectName(message, persistedDeviceInfo.getDeviceInfo())).thenReturn(scheduleHint);

    VceScheduleSelectorModule vceScheduleSelectorModule = new VceScheduleSelectorModule(moduleMetricReporter, cacheDeviceInfoReader, schedulerNameSelector);
    initModuleConfig(vceScheduleSelectorModule, moduleBase);
    vceScheduleSelectorModule.down(message);

    ArgumentCaptor<Message> messageArgumentCaptor = ArgumentCaptor.forClass(Message.class);

    InOrder inOrder = Mockito.inOrder(cacheDeviceInfoReader, moduleMetricReporter, moduleBase, schedulerNameSelector);
    inOrder.verify(cacheDeviceInfoReader).findDeviceInfoByVpi(TestUtils.VPI);
    inOrder.verify(cacheDeviceInfoReader).findDeviceInfoByHandle(TestUtils.HANDLE);
    inOrder.verify(schedulerNameSelector).selectName(message, persistedDeviceInfo.getDeviceInfo());
    inOrder.verify(moduleBase).down(messageArgumentCaptor.capture());
    inOrder.verifyNoMoreInteractions();

    Assertions.assertEquals(1, messageArgumentCaptor.getAllValues().size());
    Message capturedMessage = messageArgumentCaptor.getValue();
    Assertions.assertEquals(scheduleHint, capturedMessage.getProperty(MetaData.SCHEDULE_NAME));
    Assertions.assertEquals(TestUtils.HANDLE.toString(), capturedMessage.getProperty(MetaData.HANDLE));
    Assertions.assertEquals(TestUtils.VPI.toString(), capturedMessage.getProperty(MetaData.VPI));
  }

  @Test
  void downWhenDeviceFoundByVpiTest() {
    PersistedDeviceInfo persistedDeviceInfo = TestUtils.createPersistedDeviceInfo();
    String scheduleHint = "SmsGprs_default_7d";

    Message message = createMessage();

    CacheDeviceInfoReader cacheDeviceInfoReader = Mockito.mock(CacheDeviceInfoReader.class);
    ModuleBase moduleBase = Mockito.mock(ModuleBase.class);
    ModuleMetricReporter moduleMetricReporter = Mockito.mock(ModuleMetricReporter.class);
    SchedulerNameSelector schedulerNameSelector = Mockito.mock(SchedulerNameSelector.class);

    Mockito.when(cacheDeviceInfoReader.findDeviceInfoByVpi(TestUtils.VPI)).thenReturn(Optional.of(persistedDeviceInfo));
    Mockito.when(schedulerNameSelector.selectName(message, persistedDeviceInfo.getDeviceInfo())).thenReturn(scheduleHint);

    VceScheduleSelectorModule vceScheduleSelectorModule = new VceScheduleSelectorModule(moduleMetricReporter, cacheDeviceInfoReader, schedulerNameSelector);
    initModuleConfig(vceScheduleSelectorModule, moduleBase);
    vceScheduleSelectorModule.down(message);

    ArgumentCaptor<Message> messageArgumentCaptor = ArgumentCaptor.forClass(Message.class);

    InOrder inOrder = Mockito.inOrder(cacheDeviceInfoReader, moduleMetricReporter, moduleBase, schedulerNameSelector);
    inOrder.verify(cacheDeviceInfoReader).findDeviceInfoByVpi(TestUtils.VPI);
    inOrder.verify(schedulerNameSelector).selectName(message, persistedDeviceInfo.getDeviceInfo());
    inOrder.verify(moduleBase).down(messageArgumentCaptor.capture());
    inOrder.verifyNoMoreInteractions();

    Assertions.assertEquals(1, messageArgumentCaptor.getAllValues().size());
    Message capturedMessage = messageArgumentCaptor.getValue();
    Assertions.assertEquals(scheduleHint, capturedMessage.getProperty(MetaData.SCHEDULE_NAME));
    Assertions.assertEquals(TestUtils.HANDLE.toString(), capturedMessage.getProperty(MetaData.HANDLE));
    Assertions.assertEquals(TestUtils.VPI.toString(), capturedMessage.getProperty(MetaData.VPI));
  }

  @Test
  void downWhenDeviceInfoNotFoundTest() {
    CacheDeviceInfoReader cacheDeviceInfoReader = Mockito.mock(CacheDeviceInfoReader.class);
    ModuleBase moduleBase = Mockito.mock(ModuleBase.class);
    ModuleMetricReporter moduleMetricReporter = Mockito.mock(ModuleMetricReporter.class);
    SchedulerNameSelector schedulerNameSelector = Mockito.mock(SchedulerNameSelector.class);

    Mockito.when(cacheDeviceInfoReader.findDeviceInfoByVpi(TestUtils.VPI)).thenReturn(Optional.empty());
    Mockito.when(cacheDeviceInfoReader.findDeviceInfoByHandle(TestUtils.HANDLE)).thenReturn(Optional.empty());

    VceScheduleSelectorModule vceScheduleSelectorModule = new VceScheduleSelectorModule(moduleMetricReporter, cacheDeviceInfoReader, schedulerNameSelector);
    initModuleConfig(vceScheduleSelectorModule, moduleBase);

    AssertThrows.illegalStateException(
        () -> vceScheduleSelectorModule.down(createMessage()),
        "Could not find deviceInfo for Message[messageId=null, vehicleID=null, Type=MESSAGE, payloadSize=0, Properties={HANDLE=123456, VPI=1234567890ABCDEF1234567890ABCDEF}]"
    );

    InOrder inOrder = Mockito.inOrder(cacheDeviceInfoReader, moduleMetricReporter, moduleBase, moduleBase, schedulerNameSelector);
    inOrder.verify(cacheDeviceInfoReader).findDeviceInfoByVpi(TestUtils.VPI);
    inOrder.verify(cacheDeviceInfoReader).findDeviceInfoByHandle(TestUtils.HANDLE);
    inOrder.verifyNoMoreInteractions();
  }

  @Test
  void downWhenOldConfigThenFailValidationTest() throws TceConfigException {
    MockConfiguration mockConfiguration = getEmptyMockConfiguration();
    mockConfiguration.setPropertySpecific(mockConfiguration.getComponentShortName(), "notset.scheduler.module.metadata", "SMPP_SOURCE_ADDRESS");

    CacheDeviceInfoReader cacheDeviceInfoReader = Mockito.mock(CacheDeviceInfoReader.class);
    ModuleMetricReporter moduleMetricReporter = Mockito.mock(ModuleMetricReporter.class);
    SchedulerNameSelector schedulerNameSelector = Mockito.mock(SchedulerNameSelector.class);

    VceScheduleSelectorModule vceScheduleSelectorModule = new VceScheduleSelectorModule(moduleMetricReporter, cacheDeviceInfoReader, schedulerNameSelector);
    vceScheduleSelectorModule.setOldConfigProperties();
    vceScheduleSelectorModule.deploy();

    Message message = createMessage();
    message.setProperty(MetaData.SERVICE_ID, "S1");
    message.setProperty(MetaData.MESSAGE_ID, "M1");

    AssertThrows.exception(() -> vceScheduleSelectorModule.down(message), "Required metadata missing: SMPP_SOURCE_ADDRESS", ValidationException.class);

    Mockito.verifyNoInteractions(cacheDeviceInfoReader, moduleMetricReporter, schedulerNameSelector);

    mockConfiguration.deleteAllProperties();
  }

  @Test
  void upTest() {
    CacheDeviceInfoReader cacheDeviceInfoReader = Mockito.mock(CacheDeviceInfoReader.class);
    ModuleBase moduleBase = Mockito.mock(ModuleBase.class);
    ModuleMetricReporter moduleMetricReporter = Mockito.mock(ModuleMetricReporter.class);
    SchedulerNameSelector schedulerNameSelector = Mockito.mock(SchedulerNameSelector.class);

    VceScheduleSelectorModule vceScheduleSelectorModule = new VceScheduleSelectorModule(moduleMetricReporter, cacheDeviceInfoReader, schedulerNameSelector);
    AssertThrows.exception(() -> vceScheduleSelectorModule.up(null), "Unsupported operation - UP", UnsupportedOperationException.class);

    Mockito.verifyNoInteractions(cacheDeviceInfoReader, moduleMetricReporter, schedulerNameSelector, moduleBase);
  }
}
