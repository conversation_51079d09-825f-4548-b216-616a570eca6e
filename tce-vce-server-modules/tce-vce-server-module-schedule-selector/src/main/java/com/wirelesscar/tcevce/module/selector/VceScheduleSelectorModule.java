package com.wirelesscar.tcevce.module.selector;

import java.util.HexFormat;
import java.util.Map;
import java.util.Optional;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.wirelesscar.config.ConfigFactory;
import com.wirelesscar.tce.core.conf.TceConfig;
import com.wirelesscar.tce.module.api.Message;
import com.wirelesscar.tce.module.api.MetaData;
import com.wirelesscar.tce.module.api.ModuleBase;
import com.wirelesscar.tce.module.api.ModuleTypeConstants;
import com.wirelesscar.tce.module.metrics.ModuleMetricReporter;
import com.wirelesscar.tce.utils.MetadataUtils;
import com.wirelesscar.tcevce.wecu.device.info.cache.core.api.CacheDeviceInfoReader;
import com.wirelesscar.tcevce.wecu.device.info.database.model.DeviceInfo;
import com.wirelesscar.tcevce.wecu.device.info.database.model.Handle;
import com.wirelesscar.tcevce.wecu.device.info.database.model.PersistedDeviceInfo;

/**
 * Module for selecting a schedule name that will be used by scheduler module. Selection based on input in message.
 */
@Component(ModuleTypeConstants.VCE_SCHEDULE_SELECTOR)
@Scope("prototype")
public class VceScheduleSelectorModule extends ModuleBase {
  private static final String METADATA = "metadata";
  private static final Logger logger = LoggerFactory.getLogger(VceScheduleSelectorModule.class);

  private final CacheDeviceInfoReader cacheDeviceInfoReader;

  private MetaData[] requiredMetadata = null;

  @TceConfig(configKey = METADATA, defaultValue = "")
  private String requiredMetadataStr = "";

  private final SchedulerNameSelector schedulerNameSelector;

  public VceScheduleSelectorModule(ModuleMetricReporter moduleMetricReporter, CacheDeviceInfoReader cacheDeviceInfoReader,
      SchedulerNameSelector schedulerNameSelector) {
    super(moduleMetricReporter);
    this.cacheDeviceInfoReader = cacheDeviceInfoReader;
    this.schedulerNameSelector = schedulerNameSelector;
  }

  private static Optional<Vpi> getVpi(Message message) {
    return Optional.ofNullable(message.getProperty(MetaData.VPI))
        .map(Vpi::ofString);
  }

  @Override
  public void deploy() {
    logger.debug("Started to deploy");
    requiredMetadata = MetadataUtils.parseMetaData(requiredMetadataStr);
  }

  @Override
  public void down(final Message message) {
    MetadataUtils.validateRequiredMetaData(message, requiredMetadata);

    logger.info("Started to DOWN {}", message);
    byte[] payload = Optional.ofNullable(message.getPayload()).orElse(new byte[0]);
    logger.debug("stackname={}, message={}, string_payload={}, hex_payload={}", getStackName(), message, new String(payload),
        HexFormat.of().withDelimiter(" ").withUpperCase().formatHex(payload));

    DeviceInfo deviceInfo = getVpi(message).flatMap(this::findDeviceInfoByVpi)
        .or(() -> findDeviceInfoByHandle(Handle.ofString(message.getProperty(MetaData.HANDLE))))
        .orElseThrow(() -> new IllegalStateException("Could not find deviceInfo for " + message));

    message.setProperty(MetaData.SCHEDULE_NAME, schedulerNameSelector.selectName(message, deviceInfo));

    logger.info("Sending DOWN {}", message);
    sendDown(message);
  }

  @Override
  public void up(final Message message) {
    throw new UnsupportedOperationException("Unsupported operation - UP");
  }

  @Override
  protected void setOldModuleConfig(Map<String, String> oldConfigProperties) {
    logger.debug("Started to set old module config");
    oldConfigProperties.put(METADATA, ConfigFactory.getConfig().getString(getStackName() + ".scheduler.module." + METADATA).orElse(""));
  }

  private Optional<DeviceInfo> findDeviceInfoByHandle(Handle handle) {
    return cacheDeviceInfoReader.findDeviceInfoByHandle(handle)
        .map(PersistedDeviceInfo::getDeviceInfo);
  }

  private Optional<DeviceInfo> findDeviceInfoByVpi(Vpi vpi) {
    return cacheDeviceInfoReader.findDeviceInfoByVpi(vpi)
        .map(PersistedDeviceInfo::getDeviceInfo);
  }
}
