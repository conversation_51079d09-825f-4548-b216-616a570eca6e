package com.wirelesscar.tcevce.module.selector;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.wirelesscar.tce.module.api.Message;
import com.wirelesscar.tce.module.api.MetaData;
import com.wirelesscar.tcevce.wecu.device.info.database.model.DeviceInfo;

@Component
public class SchedulerNameSelector {
  private static final Logger logger = LoggerFactory.getLogger(SchedulerNameSelector.class);
  private static final Map<String, String> sendSchemas = createMap();

  private static Map<String, String> createMap() {
    Map<String, String> map = new HashMap<>();

    // DUAL
    map.put("gsm+orbcomm", "SmsGprsSat_default_7d");
    map.put("gsm+orbcomm.activate-machine", "SmsSat_activation_30d");
    map.put("gsm+orbcomm.activate-immobilizer-level2", "SmsGprsSat_immobilizer_1d");
    map.put("gsm+orbcomm.activate-immobilizer-level3", "SmsGprsSat_immobilizer_1d");

    // GPRS
    map.put("gsm", "SmsGprs_default_7d");
    map.put("gsm.activate-machine", "Sms_activation_30d");
    map.put("gsm.activate-immobilizer-level2", "SmsGprs_immobilizer_1d");
    map.put("gsm.activate-immobilizer-level3", "SmsGprs_immobilizer_1d");

    // SAT
    map.put("orbcomm", "Sat_default_7d");
    map.put("orbcomm.activate-machine", "Sat_activation_30d");
    map.put("orbcomm.activate-immobilizer-level2", "Sat_immobilizer_1d");
    map.put("orbcomm.activate-immobilizer-level3", "Sat_immobilizer_1d");

    // Default
    map.put("default", "SmsGprsSat_default_7d");

    // Text SMS
    map.put("gsm.sms", "Sms_text_0");

    return Collections.unmodifiableMap(map);
  }

  private static Optional<String> getCarriers(DeviceInfo deviceInfo) {
    boolean isSatelliteIdPresent = deviceInfo.getSatelliteId().isPresent();
    boolean isSimInfoPresent = deviceInfo.getSimInfo().isPresent();

    if (isSimInfoPresent && isSatelliteIdPresent) {
      return Optional.of("gsm+orbcomm");
    }
    if (isSimInfoPresent) {
      return Optional.of("gsm");
    }
    if (isSatelliteIdPresent) {
      return Optional.of("orbcomm");
    }

    logger.error("Device {} lacks msisdn (gsm) and orbcomm address", deviceInfo);
    return Optional.empty();
  }

  public String selectName(Message message, DeviceInfo deviceInfo) {
    Validate.notNull(message, "message");
    Validate.notNull(deviceInfo, "deviceInfo");

    // In case of VCE SCHEDULE_HINT is messageType
    String messageType = message.getProperty(MetaData.SCHEDULE_HINT);

    StringBuilder schemaKey = new StringBuilder();
    String carriers = getCarriers(deviceInfo).orElse(null);
    String schemaLookupKey = schemaKey.append(carriers).append('.').append(messageType).toString();
    schemaLookupKey = sendSchemas.containsKey(schemaLookupKey) ? schemaLookupKey : carriers;

    String schema = sendSchemas.containsKey(schemaLookupKey)
        ? sendSchemas.get(schemaLookupKey)
        : sendSchemas.get("default");

    if (messageType != null && messageType.equals("sms")) {
      schema = sendSchemas.get("gsm.sms");
    }

    logger.debug("Selecting schema ScheduleHint/MessageType: {}, SchemaLookupKey: {}, Schema Selected: {}", messageType, schemaLookupKey, schema);

    return schema;
  }
}
