package com.wirelesscar.tcevce.module.persist;

import java.sql.SQLException;
import java.time.Duration;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.wirelesscar.config.Config;
import com.wirelesscar.config.ConfigFactory;
import com.wirelesscar.tce.core.conf.TceConfig;
import com.wirelesscar.tce.core.event.EventService;
import com.wirelesscar.tce.core.event.events.AckEvent;
import com.wirelesscar.tce.db.common.db.api.MessageFields;
import com.wirelesscar.tce.db.common.db.api.MessagePersister;
import com.wirelesscar.tce.db.common.metrics.ProcessorMetricsReporter;
import com.wirelesscar.tce.module.api.Message;
import com.wirelesscar.tce.module.api.MessageStatus;
import com.wirelesscar.tce.module.api.MetaData;
import com.wirelesscar.tce.module.api.ModuleBase;
import com.wirelesscar.tce.module.api.ModuleTypeConstants;
import com.wirelesscar.tce.module.api.exception.EngineRuntimeException;
import com.wirelesscar.tce.module.metrics.ModuleMetricReporter;
import com.wirelesscar.tce.utils.ClusterConfig;
import com.wirelesscar.tce.utils.MetadataUtils;
import com.wirelesscar.tcevce.module.persist.db.api.PersistPersistanceFactory;
import com.wirelesscar.tcevce.module.persist.metrics.PersistModuleMetricReporter;

@Component(ModuleTypeConstants.PERSIST)
@Scope("prototype")
public class PersistModule extends ModuleBase {
  private static final String BATCH_PERSIST = "batch.persist";
  private static final String HASH_KEY = "hash.key";
  private static final String HASH_KEY_SECONDARY = "hash.key-secondary";
  private static final String HASH_KEY_TERTIARY = "hash.key-tertiary";
  private static final String IN_PROCESS_UP = "inprocessup";
  private static final String IN_STACK_UP = "instackup";
  private static final Logger log = LoggerFactory.getLogger(PersistModule.class);
  private static final String METADATA = "metadata";
  private static final String MO_ACK = "mo.ack";
  private static final String MO_BATCH_CACHE_SECS = "mo.batch-cache-secs";
  private static final String MT_ACK = "mt.ack";
  private static final String PERSIST_FOR_UP = "persistforup";
  private static final String READ_PROCESS_START = "readprocess.start";

  @TceConfig(configKey = MO_BATCH_CACHE_SECS, defaultValue = "300")
  int batchCacheSecs;

  @TceConfig(configKey = HASH_KEY, defaultValue = "IP_SRC_ADDRESS")
  String hashKey;

  private Cache<String, List<Message>> batchCache = null;

  @TceConfig(configKey = BATCH_PERSIST, defaultValue = "true")
  private boolean batchPerist = true;

  private final EventService eventService;

  private MetaData hashKeyBase = null;

  @TceConfig(configKey = HASH_KEY_SECONDARY, defaultValue = "SMPP_SOURCE_ADDRESS")
  private MetaData hashKeyBaseSecondary = null;

  @TceConfig(configKey = HASH_KEY_TERTIARY, defaultValue = "SATELLITE_SOURCE_ADDRESS")
  private MetaData hashKeyBaseTertiary = null;

  private Cache<String, Integer> idCache = null;

  @TceConfig(configKey = IN_PROCESS_UP, defaultValue = "true")
  private boolean inProcessUp = true;

  @TceConfig(configKey = IN_STACK_UP, defaultValue = "true")
  private boolean inStackUp = true;

  private final AtomicLong logSizeCnt = new AtomicLong(0);

  private final Cache<Thread, MessagePersister> persistCache = CacheBuilder.newBuilder()
      .concurrencyLevel(10)
      .maximumSize(1000)
      .expireAfterAccess(30, TimeUnit.SECONDS)
      .build();

  @TceConfig(configKey = PERSIST_FOR_UP, defaultValue = "true")
  private boolean persistForUp = true;

  private final PersistModuleMetricReporter persistModuleMetricReporter;
  private PersistProcessor processor = null;
  private final ProcessorMetricsReporter processorMetricsReporter;
  private MetaData[] requiredMetadata = null;
  @TceConfig(configKey = METADATA, defaultValue = "MESSAGE_ID")
  private String requiredMetadataStr = null;
  @TceConfig(configKey = READ_PROCESS_START, defaultValue = "true")
  private boolean startReadProcess = true;
  @TceConfig(configKey = MO_ACK, defaultValue = "false")
  private boolean useMoAck = false;
  @TceConfig(configKey = MT_ACK, defaultValue = "false")
  private boolean useMtAck = false;

  public PersistModule(EventService eventService, ProcessorMetricsReporter processorMetricsReporter, PersistModuleMetricReporter persistModuleMetricReporter,
      ModuleMetricReporter moduleMetricReporter) {
    super(moduleMetricReporter);
    this.eventService = eventService;
    this.processorMetricsReporter = processorMetricsReporter;
    this.persistModuleMetricReporter = persistModuleMetricReporter;
  }

  @Override
  public void deploy() {
    requiredMetadata = MetadataUtils.parseMetaData(requiredMetadataStr);
    hashKeyBase = hashKey.equals("VEHICLE_ID") ? null : MetaData.valueOf(hashKey);
    batchCache = CacheBuilder.newBuilder()
        .concurrencyLevel(10)
        .maximumSize(batchCacheSecs * 1000L)
        .expireAfterWrite(batchCacheSecs, TimeUnit.SECONDS)
        .build();
    idCache = CacheBuilder.newBuilder()
        .concurrencyLevel(10)
        .maximumSize(500_000)
        .expireAfterWrite(5, TimeUnit.MINUTES)
        .build();

    final String stackName = getStackName();
    final String moduleName = getName();

    log.info("{}.{} config startReadProcess: {}", stackName, moduleName, startReadProcess);
    log.info("{}.{} config persistForUp: {}", stackName, moduleName, persistForUp);
    log.info("{}.{} config inProcessUp: {}", stackName, moduleName, inProcessUp);
    log.info("{}.{} config inStackUp: {}", stackName, moduleName, inStackUp);
    log.info("{}.{} config use MO ack: {}", stackName, moduleName, useMoAck);
    log.info("{}.{} config use MT ack: {}", stackName, moduleName, useMtAck);
    log.info("{}.{} config batch persist: {}", stackName, moduleName, batchPerist);
    log.info("{}.{} config HashKeyBase: {}", stackName, moduleName, hashKeyBase);
    log.info("{}.{} config HashKeyBaseSecondary: {}", stackName, moduleName, hashKeyBaseSecondary);
    log.info("{}.{} config hashKeyBaseTertiary: {}", stackName, moduleName, hashKeyBaseTertiary);
    log.info("{}.{} config batchCahceSecs: {}", stackName, moduleName, batchCacheSecs);
  }

  /** Arrival point for MT message */
  @Override
  public void down(Message message) {
    sendDown(message);
  }

  /** Enables module to send async message upwards */
  public void processUp(Message message) {
    log.trace("{} Persist sending UP {}", getStackName(), message);

    sendUp(message);
  }

  @Override
  public void start() {
    log.info("Starting {}", getClass().getName());

    if (startReadProcess && processor == null) {
      processor = new PersistProcessor(this, processorMetricsReporter, persistModuleMetricReporter);
      Thread thread = new Thread(processor);
      thread.start();
    }
  }

  @Override
  public void stop() {
    log.info("Stopping {}", getClass().getName());

    if (processor != null) {
      processor.stopMe();
    }
  }

  /** Used to send status message upwards in stack */
  @Override
  public void up(Message message) {
    validateBeforeUp(message);
    Instant startTime = Instant.now();
    try {
      log.debug("{} Persist received UP: {}", getStackName(), message);
      setHashKeyBase(message);

      List<Message> persistedMsgs = null;
      if (persistForUp) {
        if (message.isStatus()) {
          ackMessageMT(message);
        } else {
          persistedMsgs = handlePersist(message);
          if (!persistedMsgs.isEmpty()) {
            logLatencyBySize(startTime);
            ackMessageMO(message);
          }
        }
      }

      if (persistForUp
          && persistedMsgs != null
          && persistedMsgs.size() > 1) { // send on batched messages after they are persisted
        for (Message persistedMessage : persistedMsgs) {
          if (!persistedMessage
              .getMessageId()
              .equals(message.getMessageId())) { // current message will be sent up normal way
            sendUp(persistedMessage);
          }
        }
      }

      if (!message.isStatus() || !persistForUp) {
        if (inProcessUp) {
          log.trace("{} Persist in process up {}", getStackName(), message);
          processor.addToQueue(message);
        } else if (inStackUp
            && ClusterConfig.getInstance()
                .isMyMessage(message, hashKeyBase.name(), hashKeyBaseSecondary.name(), hashKeyBaseTertiary.name())) {
          if (persistedMsgs != null && !persistedMsgs.isEmpty()) {
            log.trace("{} Persist in stack up {}", getStackName(), message);
            sendUp(message);
          }
        } else if (inStackUp
            && !ClusterConfig.getInstance()
                .isMyMessage(message, hashKeyBase.name(), hashKeyBaseSecondary.name(), hashKeyBaseTertiary.name())) {
          log.info("{} Msg routed to wrong TCE? {}", getStackName(), message);
        }
      }
    } catch (Exception e) {
      throw new EngineRuntimeException("Error persisting message: " + message, e);
    }

    Duration duration = Duration.between(startTime, Instant.now());
    persistModuleMetricReporter.logModuleUp(getStackName(), duration);
  }

  @Override
  protected void setOldModuleConfig(Map<String, String> oldConfigProperties) {
    Config config = ConfigFactory.getConfig();
    final String keyPrefix = getStackName() + ".persist.module.";
    oldConfigProperties.put(METADATA, config.getString(keyPrefix + METADATA).orElse("MESSAGE_ID"));
    oldConfigProperties.put(READ_PROCESS_START, config.getString(keyPrefix + READ_PROCESS_START).orElse("true"));
    oldConfigProperties.put(PERSIST_FOR_UP, config.getString(keyPrefix + PERSIST_FOR_UP).orElse("true"));
    oldConfigProperties.put(IN_PROCESS_UP, config.getString(keyPrefix + IN_PROCESS_UP).orElse("true"));
    oldConfigProperties.put(IN_STACK_UP, config.getString(keyPrefix + IN_STACK_UP).orElse("true"));
    oldConfigProperties.put(MO_ACK, config.getString(keyPrefix + MO_ACK).orElse("false"));
    oldConfigProperties.put(MT_ACK, config.getString(keyPrefix + MT_ACK).orElse("false"));
    oldConfigProperties.put(BATCH_PERSIST, config.getString(keyPrefix + BATCH_PERSIST).orElse("true"));
    oldConfigProperties.put(MO_BATCH_CACHE_SECS, config.getString(keyPrefix + MO_BATCH_CACHE_SECS).orElse("300"));
    oldConfigProperties.put(HASH_KEY, config.getString(keyPrefix + HASH_KEY).orElse(MetaData.IP_SRC_ADDRESS.name()));
    oldConfigProperties.put(HASH_KEY_SECONDARY, config
        .getString(keyPrefix + HASH_KEY_SECONDARY)
        .orElse(MetaData.SMPP_SOURCE_ADDRESS.name()));
  }

  /** Acks message by replying on down with a Delivered status message */
  private void ackMessageMO(Message message) {
    if (useMoAck) {
      long ts0 = System.nanoTime();

      log.trace("{} Persist MO Ack {}", getStackName(), message);

      Message reply = Message.createStatusMessage(MessageStatus.DELIVERED);
      reply.setMessageId(message.getMessageId());
      reply.setPayload(message.getPayload());
      sendDown(reply);

      log.trace("{} Persist MO Ack finished {}", getStackName(), message);

      long timeInNanos = System.nanoTime() - ts0;
      persistModuleMetricReporter.logAckMo(getStackName(), Duration.ofMillis(timeInNanos));
    }
  }

  /** Acks message to Scheduler */
  private void ackMessageMT(Message message) {
    if (message.isStatus() && useMtAck) {
      long ts0 = System.nanoTime();

      log.trace("{} Persist MT Ack {}", getStackName(), message);

      AckEvent ackEvent = new AckEvent(message);
      eventService.fireSynchronousEvent(ackEvent);
      long timeInNanos = System.nanoTime() - ts0;
      persistModuleMetricReporter.logAckMt(getStackName(), Duration.ofMillis(timeInNanos));
    }
  }

  private MessagePersister getPersister() {
    MessagePersister messagePersister = persistCache.getIfPresent(Thread.currentThread());

    if (messagePersister == null) {
      messagePersister = PersistPersistanceFactory.makeMessagePersister();
      persistCache.put(Thread.currentThread(), messagePersister);
      log.debug("Made MessagePerister for {}", Thread.currentThread().getId());
    }

    return messagePersister;
  }

  /** Handle batch and single persist based on props in message */
  private List<Message> handlePersist(Message message) throws SQLException {
    List<Message> messages = new ArrayList<>();

    if (isDuplicate(message)) { // handle WTP timing issues creating duplicates
      messages.add(message);
      return messages;
    }

    String sizeStr = message.getProperty(MetaData.GROUP_SIZE);
    String ixStr = message.getProperty(MetaData.GROUP_IX);

    if (batchPerist && sizeStr != null && sizeStr.length() > 0) {
      String batchid = message.getProperty(MetaData.GROUP_ID);
      int size = Integer.parseInt(sizeStr);
      int ix = Integer.parseInt(ixStr);

      if (size == 1) { // only one, persist now
        message = getPersister().persist(message, inProcessUp);
        messages.add(message);
      } else if (size == ix) { // reached last in batch, ix starts at 1, persist all
        messages = batchCache.getIfPresent(batchid);
        if (messages == null) {
          log.warn("No batch found for msg: {}", message);
          message = getPersister().persist(message, inProcessUp);
          messages = new ArrayList<>();
          messages.add(message);
        } else {
          messages.add(message);
          getPersister().persistBatch(messages, inProcessUp);
        }
        batchCache.invalidate(batchid);
      } else { // cache in mem until last in batch arrive
        List<Message> inMemMsgs = batchCache.getIfPresent(batchid);
        if (inMemMsgs == null) {
          inMemMsgs = new ArrayList<>();
          batchCache.put(batchid, inMemMsgs);
        }
        inMemMsgs.add(message);
      }
    } else {
      message = getPersister().persist(message, inProcessUp);
      messages.add(message);
    }

    return messages;
  }

  /** Checks duplicate based on messageID and hashcode for payload */
  private boolean isDuplicate(Message message) {
    Integer checksum = idCache.getIfPresent(message.getMessageId());
    boolean same = false;

    if (checksum != null) {
      same = checksum.equals(Integer.valueOf(Arrays.hashCode(message.getPayload())));

      if (same) {
        persistModuleMetricReporter.logPersistOneDuplicate(getStackName());
      }
    } else {
      idCache.put(message.getMessageId(), Integer.valueOf(Arrays.hashCode(message.getPayload())));
    }

    return same;
  }

  private void logLatencyBySize(Instant startTime) {
    if (logSizeCnt.incrementAndGet() % 5 == 0) {
      persistModuleMetricReporter.logPersistOne(getStackName(), Duration.between(startTime, Instant.now()));
    }
  }

  private void setHashKeyBase(Message message) {
    if (hashKeyBase == null) {
      message.setProperty(MessageFields.hashKey.name(), message.getVehicleID());
    } else {
      if (message.getProperty(hashKeyBase.name()) != null) {
        message.setProperty(MessageFields.hashKey.name(), message.getProperty(hashKeyBase.name()));
      } else {
        message.setProperty(MessageFields.hashKey.name(), message.getProperty(hashKeyBaseSecondary.name()));
      }
    }
  }

  private void validateBeforeUp(Message message) {
    if (getPersister() == null) {
      throw new IllegalStateException("No persister in place, can not handle message: " + message);
    }

    MetadataUtils.validateRequiredMetaData(message, requiredMetadata);
  }
}
