package com.wirelesscar.tcevce.module.persist;

import java.sql.SQLException;
import java.time.Duration;
import java.time.Instant;
import java.util.concurrent.TimeUnit;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.wirelesscar.config.ConfigFactory;
import com.wirelesscar.tce.db.common.AbstractProcessorThread;
import com.wirelesscar.tce.db.common.api.ActivityStatus;
import com.wirelesscar.tce.db.common.db.api.MessageFields;
import com.wirelesscar.tce.db.common.db.api.MessagePersister;
import com.wirelesscar.tce.module.api.Message;
import com.wirelesscar.tce.utils.GenericConfigNames;
import com.wirelesscar.tcevce.module.persist.db.api.PersistPersistanceFactory;
import com.wirelesscar.tcevce.module.persist.metrics.PersistModuleMetricReporter;

public class PersistProcessorThread extends AbstractProcessorThread {
  private static final Logger log = LoggerFactory.getLogger(PersistProcessorThread.class);

  private boolean ackInDb; // used to tmp stop acking message in DB. Must me coordinated with not posting to
  // AMQ.
  private final PersistModuleMetricReporter persistModuleMetricReporter;
  private final int pollTimeOutMs;

  public PersistProcessorThread(PersistProcessor persistProcessor, int number, PersistModuleMetricReporter persistModuleMetricReporter) {
    super(persistProcessor, number);
    Validate.notNull(persistModuleMetricReporter, "persistModuleMetricReporter");

    this.persistModuleMetricReporter = persistModuleMetricReporter;

    ackInDb = ConfigFactory.getConfig().getBoolean(GenericConfigNames.POST_JMS_UP).orElse(true);

    pollTimeOutMs = 200;
    log.info("ackInDb : {}", ackInDb);
  }

  @Override
  protected MessagePersister getNewMessagePersister() {
    return PersistPersistanceFactory.makeMessagePersister();
  }

  /** Polls one message from Q and tries to execute all Actions for the next Transition in line. */
  @Override
  protected void processOneMessage() throws SQLException, InterruptedException {
    Message message = queue.poll(pollTimeOutMs, TimeUnit.MILLISECONDS);

    if (message != null) {
      runningEmpty = false;
      log.debug("Starting on msg: {}, Qsize: {}", message, queue.size());
      handleActiveMsg(message);
    } else {
      runningEmpty = true;
    }
  }

  /** send on then tag for delete */
  private void handleActiveMsg(Message message) throws SQLException {
    Instant startTime = Instant.now();

    ((PersistModule) processor.getModule()).processUp(message);
    Duration duration = Duration.between(startTime, Instant.now());
    persistModuleMetricReporter.logSendUp(duration);

    if (ackInDb) {
      startTime = Instant.now();
      message.setProperty(MessageFields.status.name(), ActivityStatus.d.name());
      getPersister().updateStatusAndProps(message);
      Duration updateDuration = Duration.between(startTime, Instant.now());
      persistModuleMetricReporter.logUpdate(updateDuration);
    } else {
      log.info("Not updating status to sent in DB according to ackInDb property for msg: {}", message.getMessageId());
    }
  }
}
