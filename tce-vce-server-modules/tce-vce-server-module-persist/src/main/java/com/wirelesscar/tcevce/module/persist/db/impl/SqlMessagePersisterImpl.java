package com.wirelesscar.tcevce.module.persist.db.impl;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.wirelesscar.tce.db.common.db.api.TableName;
import com.wirelesscar.tce.db.standard.sql.impl.AbstractSqlMessagePersister;

public class SqlMessagePersisterImpl extends AbstractSqlMessagePersister {
  private static final Logger log = LoggerFactory.getLogger(SqlMessagePersisterImpl.class);

  public SqlMessagePersisterImpl() {
    super(TableName.PRST_PERSIST_MESSAGE.name());
  }

  @Override
  protected String getHintIndxedName() {
    return "IX_PRSST_MSSG_STTS_LOCAL";
  }

  @Override
  protected Logger getLogger() {
    return log;
  }
}
