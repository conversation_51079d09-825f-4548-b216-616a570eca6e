package com.wirelesscar.tcevce.module.persist;

import java.util.concurrent.TimeUnit;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.wirelesscar.config.Config;
import com.wirelesscar.config.ConfigFactory;
import com.wirelesscar.tce.db.common.AbstractProcessor;
import com.wirelesscar.tce.db.common.db.api.MessagePersister;
import com.wirelesscar.tce.db.common.metrics.ProcessorMetricsReporter;
import com.wirelesscar.tce.module.api.Module;
import com.wirelesscar.tcevce.module.persist.db.api.PersistPersistanceFactory;
import com.wirelesscar.tcevce.module.persist.metrics.PersistModuleMetricReporter;

public class PersistProcessor extends AbstractProcessor<PersistProcessorThread> {
  private static final Config config = ConfigFactory.getConfig();
  private static final Logger log = LoggerFactory.getLogger(PersistProcessor.class);

  private final Cache<Thread, MessagePersister> persistCache = CacheBuilder.newBuilder().maximumSize(1_000).expireAfterAccess(60, TimeUnit.SECONDS).build();
  private final PersistModuleMetricReporter persistModuleMetricReporter;
  private int threads = 0;

  public PersistProcessor(Module module, ProcessorMetricsReporter processorMetricsReporter, PersistModuleMetricReporter persistModuleMetricReporter) {
    super(module, "PersistProcessor", processorMetricsReporter, false);

    this.persistModuleMetricReporter = persistModuleMetricReporter;
    numberOfThreads = config
        .getInt(getModule().getStackName() + ".persist.processor.number_of_threads")
        .orElse(50);
    maxQSize = config
        .getInt(getModule().getStackName() + ".persist.processor.queuesize.max")
        .orElse(5_000);
    maxAge = config
        .getInt(getModule().getStackName() + ".persist.processor.key.maxage_ms")
        .orElse(1_000 * 60 * 10);
    minFetchSize = config.getInt(getModule().getStackName() + ".persist.processor.fetchsize").orElse(100);
    nextChecktimeFails = config
        .getLong(getModule().getStackName() + ".persist.nextchecktime.add_ms")
        .orElse(nextChecktimeFails);

    log.info("Config Number of threads {}", numberOfThreads);
    log.info("Config nextChecktimeFails: {}", nextChecktimeFails);
    createUsedIdsCache();
  }

  /** returns the designated messageProcessorThread for that device */
  public PersistProcessorThread getProcessorThreadForDevice(String deviceID) {
    return getProcessor(deviceID);
  }

  @Override
  protected MessagePersister getMessagePersister() {
    MessagePersister messagePersister = persistCache.getIfPresent(Thread.currentThread());

    if (messagePersister == null) {
      messagePersister = PersistPersistanceFactory.makeMessagePersister();
      persistCache.put(Thread.currentThread(), messagePersister);
      log.debug("Made MessagePerister for {}", Thread.currentThread().getId());
    }

    return messagePersister;
  }

  @Override
  protected PersistProcessorThread makeNewProcessorThread() {
    return new PersistProcessorThread(this, threads++, persistModuleMetricReporter);
  }
}
