package com.wirelesscar.tcevce.module.persist.metrics;

import java.time.Duration;

import org.springframework.stereotype.Component;

import com.volvo.tisp.vc.main.utils.lib.Validate;

import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;

@Component
public class PersistModuleMetricReporter {
  static final String METRIC_ACK_MO = "module.persist.ackMO";
  static final String METRIC_ACK_MT = "module.persist.ackMT";
  static final String METRIC_PERSIST_ONE = "module.persist.insert-db";
  static final String METRIC_PERSIST_ONE_DUPLICATE = "module.persist.insert-db.duplicate";
  static final String METRIC_SEND_UP = "module.persist.sendUp";
  static final String METRIC_UP = "module.persist.up";
  static final String METRIC_UPDATE = "module.persist.upIncDbUpdate";
  static final String STACK_VAR_NAME = "stackName";
  private static final String DURATION_VAR_NAME = "duration";

  private final MeterRegistry meterRegistry;
  private final Timer sendUpTimer;
  private final Timer updateTimer;

  public PersistModuleMetricReporter(MeterRegistry meterRegistry) {
    this.meterRegistry = meterRegistry;

    updateTimer = meterRegistry.timer(METRIC_UPDATE);
    sendUpTimer = meterRegistry.timer(METRIC_SEND_UP);
  }

  public void logAckMo(String stackName, Duration duration) {
    Validate.notEmpty(stackName, STACK_VAR_NAME);
    Validate.notNegative(duration, DURATION_VAR_NAME);

    meterRegistry.timer(METRIC_ACK_MO, STACK_VAR_NAME, stackName).record(duration);
  }

  public void logAckMt(String stackName, Duration duration) {
    Validate.notEmpty(stackName, STACK_VAR_NAME);
    Validate.notNegative(duration, DURATION_VAR_NAME);

    meterRegistry.timer(METRIC_ACK_MT, STACK_VAR_NAME, stackName).record(duration);
  }

  public void logModuleUp(String stackName, Duration duration) {
    Validate.notEmpty(stackName, STACK_VAR_NAME);
    Validate.notNegative(duration, DURATION_VAR_NAME);

    meterRegistry.timer(METRIC_UP, STACK_VAR_NAME, stackName).record(duration);
  }

  public void logPersistOne(String stackName, Duration duration) {
    Validate.notEmpty(stackName, STACK_VAR_NAME);
    Validate.notNegative(duration, DURATION_VAR_NAME);

    meterRegistry.timer(METRIC_PERSIST_ONE, STACK_VAR_NAME, stackName).record(duration);
  }

  public void logPersistOneDuplicate(String stackName) {
    Validate.notEmpty(stackName, STACK_VAR_NAME);

    meterRegistry.counter(METRIC_PERSIST_ONE_DUPLICATE, STACK_VAR_NAME, stackName).increment();
  }

  public void logSendUp(Duration duration) {
    Validate.notNegative(duration, DURATION_VAR_NAME);

    sendUpTimer.record(duration);
  }

  public void logUpdate(Duration duration) {
    Validate.notNegative(duration, DURATION_VAR_NAME);

    updateTimer.record(duration);
  }
}
