package com.wirelesscar.tcevce.module.persist.db;

import java.nio.charset.StandardCharsets;
import java.sql.SQLException;
import java.util.concurrent.TimeUnit;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import com.volvo.tisp.framework.context.TispContext;
import com.wirelesscar.config.mock.MockConfiguration;
import com.wirelesscar.tce.core.event.EventService;
import com.wirelesscar.tce.db.common.api.ActivityStatus;
import com.wirelesscar.tce.db.common.db.api.MessageFields;
import com.wirelesscar.tce.db.common.db.api.MessagePersister;
import com.wirelesscar.tce.db.common.metrics.ProcessorMetricsReporter;
import com.wirelesscar.tce.db.source.TceDataSource;
import com.wirelesscar.tce.db.standard.sql.impl.SqlPersistanceFactory;
import com.wirelesscar.tce.module.api.Message;
import com.wirelesscar.tce.module.api.Module;
import com.wirelesscar.tce.module.metrics.ModuleMetricReporter;
import com.wirelesscar.tce.utils.GenericConfigNames;
import com.wirelesscar.tcevce.module.persist.PersistModule;
import com.wirelesscar.tcevce.module.persist.PersistProcessor;
import com.wirelesscar.tcevce.module.persist.PersistProcessorThread;
import com.wirelesscar.tcevce.module.persist.db.api.PersistPersistanceFactory;
import com.wirelesscar.tcevce.module.persist.metrics.PersistModuleMetricReporter;

class MessageProcessorThreadTest {
  private static final String PAYLOAD = "Hepp payload hopp";

  private PersistProcessor persistProcessor;

  @BeforeAll
  static void beforeAll() {
    MockConfiguration mockConfiguration = MockConfiguration.getConfig();
    mockConfiguration.setPropertySpecific(mockConfiguration.getComponentShortName(), TceDataSource.TEST_DB_PROP, "true");
  }

  private static Message createMessage() {
    Message message = new Message();

    message.setPayload(PAYLOAD.getBytes(StandardCharsets.UTF_8));
    message.setVehicleID("" + Math.random() * 1_000_000_000);
    message.setProperty("1", "a");
    message.setProperty("2", "b");
    message.setProperty("3", "c");
    message.setProperty(MessageFields.status.name(), ActivityStatus.a.name());

    return message;
  }

  private static Module createModule() {
    PersistModule persistModule = new PersistModule(Mockito.mock(EventService.class), Mockito.mock(ProcessorMetricsReporter.class),
        Mockito.mock(PersistModuleMetricReporter.class), Mockito.mock(ModuleMetricReporter.class));
    return persistModule;
  }

  private static void persist(MessagePersister messagePersister, Message message) {
    TispContext.runInContext(() -> {
      try {
        messagePersister.persist(message);
      } catch (SQLException e) {
        throw new IllegalStateException(e);
      }
    });
  }

  @AfterEach
  void afterEach() {
    MockConfiguration mockConfiguration = MockConfiguration.getConfig();
    mockConfiguration.setPropertySpecific(mockConfiguration.getComponentShortName(), GenericConfigNames.POST_JMS_UP, "true");

    persistProcessor.stopMe();
  }

  @BeforeEach
  void beforeEach() throws Exception {
    persistProcessor = new PersistProcessor(createModule(), Mockito.mock(ProcessorMetricsReporter.class), Mockito.mock(PersistModuleMetricReporter.class));
    SqlPersistanceFactory.cleanDB();

    MockConfiguration mockConfiguration = MockConfiguration.getConfig();
    mockConfiguration.setPropertySpecific(mockConfiguration.getComponentShortName(), "notset.scheduler.workerthread.min_future_ms_for_connection_estbl", "1");
  }

  @Test
  void testAddOne() {
    PersistProcessorThread procThread = new PersistProcessorThread(persistProcessor, 0, Mockito.mock(PersistModuleMetricReporter.class));

    Message message = createMessage();
    boolean ok = procThread.addToQueue(message);

    Assertions.assertEquals(1, procThread.getQSize());
    Assertions.assertTrue(ok);
  }

  @Test
  void testDeliverOne() throws InterruptedException, SQLException {
    PersistProcessorThread procThread = new PersistProcessorThread(persistProcessor, 0, Mockito.mock(PersistModuleMetricReporter.class));
    Thread thread = new Thread(procThread);
    MessagePersister messagePersister = PersistPersistanceFactory.makeMessagePersister();

    Message message = createMessage();
    message.setMessageId("1" + Math.random());
    message.setVehicleID("10" + Math.random());
    persist(messagePersister, message);
    boolean ok = procThread.addToQueue(message);

    Assertions.assertEquals(1, procThread.getQSize());
    Assertions.assertTrue(ok);

    thread.start();
    int rounds = 0;
    while ((procThread.getQSize() > 0) && rounds++ < 10) {
      TimeUnit.MILLISECONDS.sleep(200);
    }

    Assertions.assertEquals(0, procThread.getQSize());

    Message m2 = messagePersister.lookup(message.getMessageId());
    Assertions.assertNull(m2.getProperty(MessageFields.status.name()));

    procThread.stopMe();
  }

  @Test
  void testOnHoldOne() {
    PersistProcessorThread procThread = new PersistProcessorThread(persistProcessor, 0, Mockito.mock(PersistModuleMetricReporter.class));

    Message message = createMessage();
    procThread.setOnHold(message.getVehicleID());
    boolean ok = procThread.addToQueue(message);

    Assertions.assertEquals(0, procThread.getQSize());
    Assertions.assertFalse(ok);
  }

  @Test
  void testProcessOne() throws InterruptedException {
    PersistProcessorThread procThread = new PersistProcessorThread(persistProcessor, 0, Mockito.mock(PersistModuleMetricReporter.class));
    Thread thread = new Thread(procThread);

    Message message = createMessage();
    boolean ok = procThread.addToQueue(message);

    Assertions.assertEquals(1, procThread.getQSize());
    Assertions.assertTrue(ok);

    thread.start();
    int i = 0;
    while (i++ < 10 && procThread.getQSize() > 0) {
      TimeUnit.MILLISECONDS.sleep(100);
    }

    Assertions.assertEquals(0, procThread.getQSize());
  }

  @Test
  void testUnOnHold() {
    PersistProcessorThread procThread = new PersistProcessorThread(persistProcessor, 0, Mockito.mock(PersistModuleMetricReporter.class));

    Message message = createMessage();
    procThread.setOnHold(message.getVehicleID());
    boolean ok = procThread.addToQueue(message);

    Assertions.assertEquals(0, procThread.getQSize());
    Assertions.assertFalse(ok);

    procThread.unHoldAll();
    ok = procThread.addToQueue(message);

    Assertions.assertEquals(1, procThread.getQSize());
    Assertions.assertTrue(ok);
  }
}
