package com.wirelesscar.tcevce.module.persist.metrics;

import java.time.Duration;

import org.junit.jupiter.api.Test;

import io.micrometer.core.instrument.Tags;

class PersistModuleMetricReporterTest {
  private static final String STACK_TEST = "stack-test";

  @Test
  void logAckMoTest() {
    MetricsReporterTestUtils.initReporterAndTest(PersistModuleMetricReporter::new, (meterRegistry, persistModuleMetricReporter) -> {
      persistModuleMetricReporter.logAckMo(STACK_TEST, Duration.ofMillis(2));
      MetricsReporterTestUtils.checkTimer(meterRegistry, Duration.ofMillis(2), 1, PersistModuleMetricReporter.METRIC_ACK_MO,
          Tags.of(PersistModuleMetricReporter.STACK_VAR_NAME, STACK_TEST));
    });
  }

  @Test
  void logAckMtTest() {
    MetricsReporterTestUtils.initReporterAndTest(PersistModuleMetricReporter::new, (meterRegistry, persistModuleMetricReporter) -> {
      persistModuleMetricReporter.logAckMt(STACK_TEST, Duration.ofMillis(2));
      MetricsReporterTestUtils.checkTimer(meterRegistry, Duration.ofMillis(2), 1, PersistModuleMetricReporter.METRIC_ACK_MT,
          Tags.of(PersistModuleMetricReporter.STACK_VAR_NAME, STACK_TEST));
    });
  }

  @Test
  void logModuleUpTest() {
    MetricsReporterTestUtils.initReporterAndTest(PersistModuleMetricReporter::new, (meterRegistry, persistModuleMetricReporter) -> {
      persistModuleMetricReporter.logModuleUp(STACK_TEST, Duration.ofMillis(2));
      MetricsReporterTestUtils.checkTimer(meterRegistry, Duration.ofMillis(2), 1, PersistModuleMetricReporter.METRIC_UP,
          Tags.of(PersistModuleMetricReporter.STACK_VAR_NAME, STACK_TEST));
    });
  }

  @Test
  void logPersistOneDuplicateTest() {
    MetricsReporterTestUtils.initReporterAndTest(PersistModuleMetricReporter::new, (meterRegistry, persistModuleMetricReporter) -> {
      persistModuleMetricReporter.logPersistOneDuplicate(STACK_TEST);
      MetricsReporterTestUtils.checkCounter(meterRegistry, 1, PersistModuleMetricReporter.METRIC_PERSIST_ONE_DUPLICATE,
          Tags.of(PersistModuleMetricReporter.STACK_VAR_NAME, STACK_TEST));
    });
  }

  @Test
  void logPersistOneTest() {
    MetricsReporterTestUtils.initReporterAndTest(PersistModuleMetricReporter::new, (meterRegistry, persistModuleMetricReporter) -> {
      persistModuleMetricReporter.logPersistOne(STACK_TEST, Duration.ofMillis(2));
      MetricsReporterTestUtils.checkTimer(meterRegistry, Duration.ofMillis(2), 1, PersistModuleMetricReporter.METRIC_PERSIST_ONE,
          Tags.of(PersistModuleMetricReporter.STACK_VAR_NAME, STACK_TEST));
    });
  }

  @Test
  void logSendUpTest() {
    MetricsReporterTestUtils.initReporterAndTest(PersistModuleMetricReporter::new, (meterRegistry, persistModuleMetricReporter) -> {
      persistModuleMetricReporter.logSendUp(Duration.ofMillis(3));
      MetricsReporterTestUtils.checkTimer(meterRegistry, Duration.ofMillis(3), 1, PersistModuleMetricReporter.METRIC_SEND_UP, Tags.empty());
    });
  }

  @Test
  void logUpdateTest() {
    MetricsReporterTestUtils.initReporterAndTest(PersistModuleMetricReporter::new, (meterRegistry, persistModuleMetricReporter) -> {
      persistModuleMetricReporter.logUpdate(Duration.ofMillis(4));
      MetricsReporterTestUtils.checkTimer(meterRegistry, Duration.ofMillis(4), 1, PersistModuleMetricReporter.METRIC_UPDATE, Tags.empty());
    });
  }
}
