package com.wirelesscar.tcevce.module.persist.db;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.EnumMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicReference;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.framework.context.TispContext;
import com.wirelesscar.config.mock.MockConfiguration;
import com.wirelesscar.tce.db.common.api.ActivityStatus;
import com.wirelesscar.tce.db.common.db.api.MessageFields;
import com.wirelesscar.tce.db.common.db.api.MessagePersister;
import com.wirelesscar.tce.db.common.db.api.TableName;
import com.wirelesscar.tce.db.source.TceDataSource;
import com.wirelesscar.tce.module.api.Message;
import com.wirelesscar.tce.module.api.MessagePriority;
import com.wirelesscar.tce.module.api.MetaData;
import com.wirelesscar.tcevce.module.persist.db.api.PersistPersistanceFactory;

class PersistPersisterImplTest {
  private static final byte[] payloadArr = new byte[] {
                                                       0,
                                                       1,
                                                       2,
                                                       3,
                                                       4,
                                                       5,
                                                       6,
                                                       7,
                                                       8,
                                                       9,
                                                       10,
                                                       11,
                                                       12,
                                                       13,
                                                       14,
                                                       15,
                                                       16,
                                                       17,
                                                       19,
                                                       20,
                                                       21,
                                                       22,
                                                       23,
                                                       24,
                                                       25
  };
  private static final String REMOVE_TIME = Long.toString(Long.MAX_VALUE);
  private static final String TRANSITION_A = "Transition A";

  @BeforeAll
  static void beforeAll() {
    MockConfiguration mockConfiguration = MockConfiguration.getConfig();
    mockConfiguration.setPropertySpecific(mockConfiguration.getComponentShortName(), TceDataSource.TEST_DB_PROP, "true");
  }

  private static Message createMessage() {
    Message message = new Message();

    message.setPayload(payloadArr);
    message.setVehicleID("" + Math.random() * 1000000000);
    message.setProperty("1", "a");
    message.setProperty("2", "b");
    message.setProperty("3", "c");
    message.setProperty(MessageFields.status.name(), ActivityStatus.a.name());
    message.setProperty(MessageFields.lastTransitionName.name(), TRANSITION_A);
    message.setProperty(MessageFields.removeTime.name(), REMOVE_TIME);
    message.setProperty(MessageFields.nextCheckTime.name(), "0");
    message.setProperty(MetaData.SERVICE_ID.name(), "S1");
    message.setProperty(MetaData.MESSAGE_ID.name(), "M1" + Math.random() * 1000000000);

    return message;
  }

  private static Message persist(MessagePersister messagePersister, Message message) {
    AtomicReference<Message> atomicReference = new AtomicReference<>();
    TispContext.runInContext(() -> {
      try {
        atomicReference.set(messagePersister.persist(message));
      } catch (SQLException e) {
        throw new IllegalStateException(e);
      }
    });
    return atomicReference.get();
  }

  private static void persistBatch(MessagePersister messagePersister, List<Message> messages) {
    TispContext.runInContext(() -> {
      try {
        messagePersister.persistBatch(messages, true);
      } catch (SQLException e) {
        throw new IllegalStateException(e);
      }
    });
  }

  @BeforeEach
  void beforeEach() {
    System.getProperties().put(TceDataSource.TEST_DB_PROP, "true");
  }

  @Test
  void testInsertFindAllDelete() throws SQLException {
    MessagePersister messagePersister = PersistPersistanceFactory.makeMessagePersister();

    List<Message> messages = new ArrayList<>();
    for (int i = 0; i < 10; i++) {
      Message message = createMessage();

      Message message2 = persist(messagePersister, message);

      messages.add(message2);
    }

    List<Message> m3List = messagePersister.findForMe(Collections.emptyMap());

    for (Message message3 : m3List) {
      long deleted = messagePersister.delete(message3);
      Assertions.assertEquals(1, deleted);

      Message message4 = messagePersister.lookup(message3.getMessageId());

      Assertions.assertNull(message4);
    }

    Assertions.assertTrue(m3List.size() >= 10);
  }

  @Test
  void testInsertFindByPriority() throws SQLException {
    MessagePersister messagePersister = PersistPersistanceFactory.makeMessagePersister();

    Message message = createMessage();
    Message m2 = persist(messagePersister, message);

    Message msgPL = createMessage();
    msgPL.setProperty(MetaData.PRIORITY, MessagePriority.Low.name());
    Message mPL = persist(messagePersister, msgPL);

    Message msgPH = createMessage();
    msgPH.setProperty(MetaData.PRIORITY, MessagePriority.Urgent.name());
    Message mPH = persist(messagePersister, msgPH);

    List<Message> m3List = messagePersister.findAllActiveAndReady(true);

    messagePersister.delete(m2);
    messagePersister.delete(mPL);
    messagePersister.delete(mPH);

    Message m3H = m3List.get(0);
    Message m3L = m3List.get(2);
    Assertions.assertEquals(3, m3List.size());

    Assertions.assertEquals(mPH.getMessageId(), m3H.getMessageId());
    Assertions.assertEquals(mPL.getMessageId(), m3L.getMessageId());
  }

  @Test
  void testInsertFindDelete() throws SQLException {
    MessagePersister messagePersister = PersistPersistanceFactory.makeMessagePersister();

    Message message = createMessage();

    Message m2 = persist(messagePersister, message);

    Map<MessageFields, String> map = new EnumMap<>(MessageFields.class);
    map.put(MessageFields.vehicleID, m2.getVehicleID());

    List<Message> m3List = messagePersister.findForMe(map);

    Message m3 = m3List.get(0);

    long deleted = messagePersister.delete(m3);

    Assertions.assertEquals(m2.getMessageId(), m3.getMessageId());
    Assertions.assertEquals(m2.getPayload().length, m3.getPayload().length);

    Assertions.assertEquals(1, deleted);

    Message m4 = messagePersister.lookup(m2.getMessageId());

    Assertions.assertNull(m4);
  }

  @Test
  void testInsertLookupDelete() throws SQLException {
    MessagePersister messagePersister = PersistPersistanceFactory.makeMessagePersister();

    Message message = createMessage();

    Message m2 = persist(messagePersister, message);

    Message m3 = messagePersister.lookup(m2.getMessageId());

    long deleted = messagePersister.delete(m3);

    Assertions.assertEquals(m2.getMessageId(), m3.getMessageId());
    Assertions.assertEquals(m2.getPayload().length, m3.getPayload().length);

    Assertions.assertEquals(1, deleted);

    Message m4 = messagePersister.lookup(m2.getMessageId());

    Assertions.assertNull(m4);
  }

  @Test
  void testInsertLookupDeleteBatchOf1() throws SQLException {
    MessagePersister messagePersister = PersistPersistanceFactory.makeMessagePersister();
    List<Message> messages = new ArrayList<>();
    int num = 1;
    String pbid = "PBATCH" + Math.random();

    for (int i = 0; i < num; i++) {
      Message message = createMessage();
      message.setMessageId(message.getProperty(MetaData.MESSAGE_ID));
      message.setProperty(MetaData.GROUP_ID, pbid);
      message.setProperty(MetaData.GROUP_IX, "" + (i + 1));
      message.setProperty(MetaData.GROUP_SIZE, "" + num);
      messages.add(message);
    }

    persistBatch(messagePersister, messages);

    for (Message message : messages) {
      Message m2 = messagePersister.lookup(message.getMessageId());
      Assertions.assertEquals(message.getProperty("1"), m2.getProperty("1"));
      messagePersister.delete(m2);
    }
  }

  @Test
  void testInsertLookupDeleteBatchOf10() throws SQLException {
    MessagePersister messagePersister = PersistPersistanceFactory.makeMessagePersister();
    List<Message> messages = new ArrayList<>();
    int num = 10;
    String pbid = "PBATCH" + Math.random();

    for (int i = 0; i < num; i++) {
      Message message = createMessage();
      message.setMessageId(message.getProperty(MetaData.MESSAGE_ID));
      message.setProperty(MetaData.GROUP_ID, pbid);
      message.setProperty(MetaData.GROUP_IX, "" + (i + 1));
      message.setProperty(MetaData.GROUP_SIZE, "" + num);
      messages.add(message);
    }

    persistBatch(messagePersister, messages);

    for (Message message : messages) {
      Message m2 = messagePersister.lookup(message.getMessageId());
      Assertions.assertEquals(message.getProperty("1"), m2.getProperty("1"));
      messagePersister.delete(m2);
    }
  }

  @Test
  void testInsertLookupDeleteLoad() throws SQLException {
    MessagePersister messagePersister = PersistPersistanceFactory.makeMessagePersister();
    double rounds = 10;

    for (int i = 0; i < rounds; i++) {
      Message message = createMessage();

      Message m2 = persist(messagePersister, message);

      Message m3 = messagePersister.lookup(m2.getMessageId());

      long deleted = messagePersister.delete(m3);

      Assertions.assertEquals(m2.getMessageId(), m3.getMessageId());
      Assertions.assertEquals(m2.getPayload().length, m3.getPayload().length);

      Assertions.assertEquals(1, deleted);

      Message m4 = messagePersister.lookup(m2.getMessageId());

      Assertions.assertNull(m4);
    }
  }

  @Test
  void testInsertLookupUpdateAckDelete() throws SQLException {
    MessagePersister messagePersister = PersistPersistanceFactory.makeMessagePersister();

    Message message = createMessage();

    Message m2 = persist(messagePersister, message);
    Message m3 = messagePersister.lookup(m2.getMessageId());

    Assertions.assertEquals(m2.getMessageId(), m3.getMessageId());
    Assertions.assertEquals(m2.getPayload().length, m3.getPayload().length);

    messagePersister.updateForAck(m2, null);
    m3 = messagePersister.lookup(m2.getMessageId());

    long deleted = messagePersister.delete(m3);

    Assertions.assertEquals(m2.getMessageId(), m3.getMessageId());
    Assertions.assertFalse(m3.getProperties().toString().indexOf("ACK") < 0);

    Assertions.assertEquals(1, deleted);

    Message m4 = messagePersister.lookup(m2.getMessageId());

    Assertions.assertNull(m4);
  }

  @Test
  void testInsertLookupUpdateNextTimeDelete() throws SQLException {
    MessagePersister messagePersister = PersistPersistanceFactory.makeMessagePersister();

    Message message = createMessage();

    Message m2 = persist(messagePersister, message);
    Message m3 = messagePersister.lookup(m2.getMessageId());

    Assertions.assertEquals(m2.getMessageId(), m3.getMessageId());
    Assertions.assertEquals(m2.getPayload().length, m3.getPayload().length);

    messagePersister.updateNextCheckTime(m2, 99, 88);
    m3 = messagePersister.lookup(m2.getMessageId());

    Assertions.assertEquals(m2.getMessageId(), m3.getMessageId());
    Assertions.assertTrue(Long.valueOf(m3.getProperty(MessageFields.removeTime.name())) > 0);

    long deleted = messagePersister.delete(m3);

    m2.getProperties().remove(MessageFields.removeTime.name());
    m3.getProperties().remove(MessageFields.removeTime.name());
    Assertions.assertEquals(m2.getPayload().length, m3.getPayload().length);

    Assertions.assertEquals(1, deleted);

    Message m4 = messagePersister.lookup(m2.getMessageId());

    Assertions.assertNull(m4);
  }

  @Test
  void testInsertLookupUpdateNextTimeDeleteWithPartitionDate() throws SQLException {
    MockConfiguration mockConfiguration = MockConfiguration.getConfig();
    mockConfiguration.setPropertySpecific(mockConfiguration.getComponentShortName(), "message.update_with_date." + TableName.PRST_PERSIST_MESSAGE.name(),
        "true");

    MessagePersister messagePersister = PersistPersistanceFactory.makeMessagePersister();

    Message message = createMessage();

    Message m2 = persist(messagePersister, message);
    Message m3 = messagePersister.lookup(m2.getMessageId());

    Assertions.assertEquals(m2.getMessageId(), m3.getMessageId());
    Assertions.assertEquals(m2.getPayload().length, m3.getPayload().length);

    messagePersister.updateNextCheckTime(m2, 99, 88);
    m3 = messagePersister.lookup(m2.getMessageId());

    Assertions.assertEquals(m2.getMessageId(), m3.getMessageId());
    Assertions.assertTrue(Long.valueOf(m3.getProperty(MessageFields.removeTime.name())) > 0);

    long deleted = messagePersister.delete(m3);

    m2.getProperties().remove(MessageFields.removeTime.name());
    m3.getProperties().remove(MessageFields.removeTime.name());
    Assertions.assertEquals(m2.getPayload().length, m3.getPayload().length);

    Assertions.assertEquals(1, deleted);

    Message m4 = messagePersister.lookup(m2.getMessageId());

    Assertions.assertNull(m4);
  }

  @Test
  void testInsertLookupUpdateStatusDelete() throws SQLException {
    MessagePersister messagePersister = PersistPersistanceFactory.makeMessagePersister();

    Message message = createMessage();

    Message m2 = persist(messagePersister, message);
    Message m3 = messagePersister.lookup(m2.getMessageId());

    Assertions.assertEquals(m2.getMessageId(), m3.getMessageId());
    Assertions.assertEquals(m2.getPayload().length, m3.getPayload().length);

    m2.setProperty(MessageFields.status.name(), ActivityStatus.w.name());
    messagePersister.updateStatusAndProps(m2);
    m3 = messagePersister.lookup(m2.getMessageId());

    long deleted = messagePersister.delete(m3);

    Assertions.assertEquals(m2.getMessageId(), m3.getMessageId());
    Assertions.assertEquals(ActivityStatus.w.name(), m3.getProperty(MessageFields.status.name()));
    Assertions.assertNotNull(m3.getProperty(MessageFields.nextCheckTime.name()));
    Assertions.assertEquals(m2.getPayload().length, m3.getPayload().length);

    Assertions.assertEquals(1, deleted);

    Message m4 = messagePersister.lookup(m2.getMessageId());

    Assertions.assertNull(m4);
  }
}
