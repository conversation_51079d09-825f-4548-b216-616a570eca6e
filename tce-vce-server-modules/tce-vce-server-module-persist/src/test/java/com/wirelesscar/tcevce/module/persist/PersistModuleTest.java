package com.wirelesscar.tcevce.module.persist;

import java.nio.charset.StandardCharsets;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import com.volvo.tisp.framework.context.TispContext;
import com.wirelesscar.config.exception.ConfigurationException;
import com.wirelesscar.config.mock.MockConfiguration;
import com.wirelesscar.tce.core.conf.TceConfigException;
import com.wirelesscar.tce.core.event.Event;
import com.wirelesscar.tce.core.event.EventListener;
import com.wirelesscar.tce.core.event.EventService;
import com.wirelesscar.tce.core.event.SynchronousEventListener;
import com.wirelesscar.tce.core.event.Type;
import com.wirelesscar.tce.db.common.api.ActivityStatus;
import com.wirelesscar.tce.db.common.db.api.MessageFields;
import com.wirelesscar.tce.db.common.db.api.MessagePersister;
import com.wirelesscar.tce.db.common.metrics.ProcessorMetricsReporter;
import com.wirelesscar.tce.db.source.TceDataSource;
import com.wirelesscar.tce.module.api.Message;
import com.wirelesscar.tce.module.api.MessageStatus;
import com.wirelesscar.tce.module.api.MetaData;
import com.wirelesscar.tce.module.api.ModuleBase;
import com.wirelesscar.tce.module.metrics.ModuleMetricReporter;
import com.wirelesscar.tcevce.module.persist.db.api.PersistPersistanceFactory;
import com.wirelesscar.tcevce.module.persist.metrics.PersistModuleMetricReporter;

class PersistModuleTest extends ModuleBase {
  private static final String STACKNAME = "notset";

  private int downCalls = 0;
  private int eventsCalls = 0;
  private int upCalls = 0;

  PersistModuleTest() {
    super(Mockito.mock(ModuleMetricReporter.class));
  }

  @BeforeAll
  static void beforeAll() {
    MockConfiguration mockConfiguration = MockConfiguration.getConfig();
    mockConfiguration.setPropertySpecific(mockConfiguration.getComponentShortName(), TceDataSource.TEST_DB_PROP, "true");
  }

  private static Message createMessage(String payload, boolean status) {
    Message message = status ? Message.createStatusMessage(MessageStatus.DELIVERED) : new Message();
    message.setMessageId("ID-" + Math.random());
    message.setPayload(payload.getBytes(StandardCharsets.UTF_8));
    message.setVehicleID("" + Math.random() * 1000000000);
    message.setProperty("1", "a");
    message.setProperty("2", "b");
    message.setProperty("3", "c");
    message.setProperty(MessageFields.status.name(), ActivityStatus.a.name());
    message.setProperty(MetaData.SERVICE_ID.name(), "S1");
    message.setProperty(MetaData.MESSAGE_ID.name(), "M1" + Math.random());
    message.setProperty(MetaData.IP_SRC_ADDRESS.name(), "*******");

    return message;
  }

  private static PersistModule createPersistModule(EventService eventService) {
    return new PersistModule(eventService, Mockito.mock(ProcessorMetricsReporter.class), Mockito.mock(PersistModuleMetricReporter.class),
        Mockito.mock(ModuleMetricReporter.class));
  }

  private static EventService makeEventService() {
    return new EventService() {
      SynchronousEventListener sListener = null;

      @Override
      public void fireEvent(Event event) {}

      @Override
      public void fireSynchronousEvent(Event event) {
        if (event.getType().equals(Type.ACK)) {
          sListener.handleEvent(event);
        }
      }

      @Override
      public int getEventQueueSize(EventListener listener) {
        return 0;
      }

      @Override
      public void registerAsListener(EventListener listener, Type... eventTypes) {}

      @Override
      public void registerAsListener(SynchronousEventListener listener, Type... eventTypes) {
        sListener = listener;
      }

      @Override
      public void unregister(EventListener listener) {}

      @Override
      public void unregister(SynchronousEventListener listener) {}
    };
  }

  @Override
  public void down(Message message) {
    downCalls++;
  }

  @Override
  public void up(Message message) {
    upCalls++;
  }

  @Test
  void testAckMo() throws Exception {
    MockConfiguration mockConfiguration = MockConfiguration.getConfig();
    mockConfiguration.setPropertySpecific(mockConfiguration.getComponentShortName(), TceDataSource.TEST_DB_PROP, "true");
    mockConfiguration.setPropertySpecific(mockConfiguration.getComponentShortName(), STACKNAME + ".persist.module.readprocess.start", "false");
    mockConfiguration.setPropertySpecific(mockConfiguration.getComponentShortName(), STACKNAME + ".persist.module.persistforup", "true");
    mockConfiguration.setPropertySpecific(mockConfiguration.getComponentShortName(), STACKNAME + ".persist.module.inprocessup", "false");
    mockConfiguration.setPropertySpecific(mockConfiguration.getComponentShortName(), STACKNAME + ".persist.module.mo.ack", "true");
    mockConfiguration.setPropertySpecific(mockConfiguration.getComponentShortName(), STACKNAME + ".persist.module.mt.ack", "true");

    SynchronousEventListener listener = makeListener();
    EventService eventService = makeEventService();
    eventService.registerAsListener(listener, Type.ACK);

    PersistModule persistModule = createPersistModule(eventService);
    persistModule.setDown(this);
    persistModule.setUp(this);
    persistModule.setOldConfigProperties();
    persistModule.deploy();
    persistModule.start();

    int events = eventsCalls;
    int downs = downCalls;
    int upps = upCalls;
    Message message = createMessage("payload", false);

    MessagePersister persister = PersistPersistanceFactory.makeMessagePersister();
    List<Message> msgBefore = persister.findForMe(Collections.emptyMap());

    TispContext.runInContext(() -> persistModule.up(message));
    List<Message> msgAfter = persister.findForMe(Collections.emptyMap());

    Assertions.assertNotNull(message.getMessageId());
    Assertions.assertEquals(1, (msgAfter.size() - msgBefore.size()));
    Assertions.assertEquals(downs + 1, downCalls); // should be acked down
    Assertions.assertEquals(upps + 1, upCalls); // should be sent up
    Assertions.assertEquals(events, eventsCalls); // should not be sent to Scheduler via listener
  }

  @Test
  void testAckMt() throws TceConfigException, ConfigurationException, SQLException {
    MockConfiguration mockConfiguration = MockConfiguration.getConfig();
    mockConfiguration.setPropertySpecific(mockConfiguration.getComponentShortName(), TceDataSource.TEST_DB_PROP, "true");
    mockConfiguration.setPropertySpecific(mockConfiguration.getComponentShortName(), STACKNAME + ".persist.module.readprocess.start", "false");
    mockConfiguration.setPropertySpecific(mockConfiguration.getComponentShortName(), STACKNAME + ".persist.module.persistforup", "true");
    mockConfiguration.setPropertySpecific(mockConfiguration.getComponentShortName(), STACKNAME + ".persist.module.inprocessup", "false");
    mockConfiguration.setPropertySpecific(mockConfiguration.getComponentShortName(), STACKNAME + ".persist.module.mo.ack", "true");
    mockConfiguration.setPropertySpecific(mockConfiguration.getComponentShortName(), STACKNAME + ".persist.module.mt.ack", "true");

    SynchronousEventListener listener = makeListener();
    EventService eventService = makeEventService();
    eventService.registerAsListener(listener, Type.ACK);

    PersistModule persistModule = createPersistModule(eventService);
    persistModule.setDown(this);
    persistModule.setUp(this);
    persistModule.setOldConfigProperties();
    persistModule.deploy();
    persistModule.start();

    int events = eventsCalls;
    int upps = upCalls;
    int downs = downCalls;
    Message message = createMessage("payload", true);
    message.setMessageId(null);

    MessagePersister messagePersister = PersistPersistanceFactory.makeMessagePersister();
    List<Message> msgBefore = messagePersister.findForMe(Collections.emptyMap());

    persistModule.up(message);
    List<Message> msgAfter = messagePersister.findForMe(Collections.emptyMap());

    Assertions.assertNull(message.getMessageId());
    Assertions.assertEquals(0, (msgAfter.size() - msgBefore.size()));
    Assertions.assertEquals(downs, downCalls); // should not be acked down
    Assertions.assertEquals(upps, upCalls); // should not be sent up
    Assertions.assertEquals(events + 1, eventsCalls); // should be sent to Scheduler via listener
  }

  @Test
  void testMoBatch() throws Exception {
    MockConfiguration mockConfiguration = MockConfiguration.getConfig();
    mockConfiguration.setPropertySpecific(mockConfiguration.getComponentShortName(), TceDataSource.TEST_DB_PROP, "true");
    mockConfiguration.setPropertySpecific(mockConfiguration.getComponentShortName(), STACKNAME + ".persist.module.readprocess.start", "false");
    mockConfiguration.setPropertySpecific(mockConfiguration.getComponentShortName(), STACKNAME + ".persist.module.persistforup", "true");
    mockConfiguration.setPropertySpecific(mockConfiguration.getComponentShortName(), STACKNAME + ".persist.module.inprocessup", "false");
    mockConfiguration.setPropertySpecific(mockConfiguration.getComponentShortName(), STACKNAME + ".persist.module.mo.ack", "false");
    mockConfiguration.setPropertySpecific(mockConfiguration.getComponentShortName(), STACKNAME + ".persist.module.mt.ack", "false");

    PersistModule persistModule = createPersistModule(makeEventService());

    persistModule.setDown(this);
    persistModule.setUp(this);
    persistModule.setOldConfigProperties();
    persistModule.deploy();
    persistModule.start();

    int num = 5;
    String pbid = "PBATCH" + Math.random();
    List<Message> messages = new ArrayList<>();

    for (int i = 0; i < num; i++) {
      Message message = createMessage("payload", false);
      message.setMessageId(message.getProperty(MetaData.MESSAGE_ID));
      message.setProperty(MetaData.GROUP_ID, pbid);
      message.setProperty(MetaData.GROUP_IX, "" + (i + 1));
      message.setProperty(MetaData.GROUP_SIZE, "" + num);
      messages.add(message);
    }

    int events = eventsCalls;
    int downs = downCalls;
    int upps = upCalls;

    MessagePersister persister = PersistPersistanceFactory.makeMessagePersister();
    List<Message> msgBefore = persister.findForMe(Collections.emptyMap());

    for (Message message : messages) {
      TispContext.runInContext(() -> persistModule.up(message));
    }

    TimeUnit.MILLISECONDS.sleep(10);
    List<Message> msgAfter = persister.findForMe(Collections.emptyMap());

    Assertions.assertEquals(num, (msgAfter.size() - msgBefore.size()));
    Assertions.assertEquals(downs, downCalls); // should not be acked down
    Assertions.assertEquals(upps + num, upCalls); // should be sent up
    Assertions.assertEquals(events, eventsCalls); // should not be sent to Scheduler via listener
  }

  /** Duplicate check shall suppress second one */
  @Test
  void testModuleDuplicateMsg() throws Exception {
    MockConfiguration mockConfiguration = MockConfiguration.getConfig();
    mockConfiguration.setPropertySpecific(mockConfiguration.getComponentShortName(), TceDataSource.TEST_DB_PROP, "true");
    mockConfiguration.setPropertySpecific(mockConfiguration.getComponentShortName(), STACKNAME + ".persist.module.readprocess.start", "false");
    mockConfiguration.setPropertySpecific(mockConfiguration.getComponentShortName(), STACKNAME + ".persist.module.persistforup", "true");
    mockConfiguration.setPropertySpecific(mockConfiguration.getComponentShortName(), STACKNAME + ".persist.module.inprocessup", "false");

    PersistModule persistModule = createPersistModule(makeEventService());
    persistModule.setOldConfigProperties();
    persistModule.deploy();
    persistModule.start();

    Message message = createMessage("payload", false);

    MessagePersister persister = PersistPersistanceFactory.makeMessagePersister();
    List<Message> msgBefore = persister.findForMe(Collections.emptyMap());

    TispContext.runInContext(() -> persistModule.up(message));
    List<Message> msgAfter = persister.findForMe(Collections.emptyMap());

    persistModule.up(message);
    List<Message> msgAfter2 = persister.findForMe(Collections.emptyMap());

    Assertions.assertNotNull(message.getMessageId());
    Assertions.assertEquals(1, (msgAfter.size() - msgBefore.size()));
    Assertions.assertEquals(1, (msgAfter2.size() - msgBefore.size()));
  }

  /** Duplicate check shall send it through as different payload */
  @Test
  void testModuleNotDuplicateMsg() throws Exception {
    MockConfiguration mockConfiguration = MockConfiguration.getConfig();
    mockConfiguration.setPropertySpecific(mockConfiguration.getComponentShortName(), TceDataSource.TEST_DB_PROP, "true");
    mockConfiguration.setPropertySpecific(mockConfiguration.getComponentShortName(), STACKNAME + ".persist.module.readprocess.start", "false");
    mockConfiguration.setPropertySpecific(mockConfiguration.getComponentShortName(), STACKNAME + ".persist.module.persistforup", "true");
    mockConfiguration.setPropertySpecific(mockConfiguration.getComponentShortName(), STACKNAME + ".persist.module.inprocessup", "false");

    PersistModule persistModule = createPersistModule(makeEventService());
    persistModule.setOldConfigProperties();
    persistModule.deploy();
    persistModule.start();

    Message message = createMessage("payload", false);

    MessagePersister persister = PersistPersistanceFactory.makeMessagePersister();
    List<Message> msgBefore = persister.findForMe(Collections.emptyMap());

    TispContext.runInContext(() -> persistModule.up(message));
    List<Message> msgAfter = persister.findForMe(Collections.emptyMap());

    message.setPayload("1111111111111111111111111111111111".getBytes(StandardCharsets.UTF_8));
    TispContext.runInContext(() -> persistModule.up(message));
    List<Message> msgAfter2 = persister.findForMe(Collections.emptyMap());

    Assertions.assertNotNull(message.getMessageId());
    Assertions.assertEquals(1, (msgAfter.size() - msgBefore.size()));
    Assertions.assertEquals(2, (msgAfter2.size() - msgBefore.size()));
  }

  @Test
  void testModuleReadOnly() throws TceConfigException, ConfigurationException, SQLException {
    MockConfiguration mockConfiguration = MockConfiguration.getConfig();
    mockConfiguration.setPropertySpecific(mockConfiguration.getComponentShortName(), TceDataSource.TEST_DB_PROP, "true");
    mockConfiguration.setPropertySpecific(mockConfiguration.getComponentShortName(), STACKNAME + ".persist.module.readprocess.start", "false");
    mockConfiguration.setPropertySpecific(mockConfiguration.getComponentShortName(), STACKNAME + ".persist.module.persistforup", "false");
    mockConfiguration.setPropertySpecific(mockConfiguration.getComponentShortName(), STACKNAME + ".persist.module.inprocessup", "false");

    PersistModule persistModule = createPersistModule(makeEventService());
    persistModule.setOldConfigProperties();
    persistModule.deploy();

    Message message = createMessage("payload", false);
    message.setMessageId(null);

    MessagePersister persister = PersistPersistanceFactory.makeMessagePersister();
    List<Message> msgBefore = persister.findForMe(Collections.emptyMap());

    persistModule.up(message);
    List<Message> msgAfter = persister.findForMe(Collections.emptyMap());

    Assertions.assertNull(message.getMessageId());
    Assertions.assertEquals(0, (msgAfter.size() - msgBefore.size()));
  }

  @Test
  void testModuleWriteOnly() throws TceConfigException, ConfigurationException, SQLException {
    MockConfiguration mockConfiguration = MockConfiguration.getConfig();
    mockConfiguration.setPropertySpecific(mockConfiguration.getComponentShortName(), TceDataSource.TEST_DB_PROP, "true");
    mockConfiguration.setPropertySpecific(mockConfiguration.getComponentShortName(), STACKNAME + ".persist.module.readprocess.start", "false");
    mockConfiguration.setPropertySpecific(mockConfiguration.getComponentShortName(), STACKNAME + ".persist.module.persistforup", "true");
    mockConfiguration.setPropertySpecific(mockConfiguration.getComponentShortName(), STACKNAME + ".persist.module.inprocessup", "false");

    PersistModule persistModule = createPersistModule(makeEventService());
    persistModule.setOldConfigProperties();
    persistModule.deploy();
    persistModule.start();

    Message message = createMessage("payload", false);

    MessagePersister persister = PersistPersistanceFactory.makeMessagePersister();
    List<Message> msgBefore = persister.findForMe(Collections.emptyMap());

    TispContext.runInContext(() -> persistModule.up(message));
    List<Message> msgAfter = persister.findForMe(Collections.emptyMap());

    Assertions.assertNotNull(message.getMessageId());
    Assertions.assertEquals(1, (msgAfter.size() - msgBefore.size()));
  }

  @Test
  void testMoSingleWithNewProps() throws Exception {
    MockConfiguration mockConfiguration = MockConfiguration.getConfig();
    mockConfiguration.setPropertySpecific(mockConfiguration.getComponentShortName(), TceDataSource.TEST_DB_PROP, "true");
    mockConfiguration.setPropertySpecific(mockConfiguration.getComponentShortName(), STACKNAME + ".persist.module.readprocess.start", "false");
    mockConfiguration.setPropertySpecific(mockConfiguration.getComponentShortName(), STACKNAME + ".persist.module.persistforup", "true");
    mockConfiguration.setPropertySpecific(mockConfiguration.getComponentShortName(), STACKNAME + ".persist.module.inprocessup", "false");
    mockConfiguration.setPropertySpecific(mockConfiguration.getComponentShortName(), STACKNAME + ".persist.module.mo.ack", "false");
    mockConfiguration.setPropertySpecific(mockConfiguration.getComponentShortName(), STACKNAME + ".persist.module.mt.ack", "false");

    PersistModule persistModule = createPersistModule(makeEventService());

    persistModule.setDown(this);
    persistModule.setUp(this);
    persistModule.setOldConfigProperties();
    persistModule.deploy();
    persistModule.start();

    int events = eventsCalls;
    int downs = downCalls;
    int upps = upCalls;
    String pbid = "PBATCH" + Math.random();
    Message message = createMessage("payload", false);
    message.setMessageId(message.getProperty(MetaData.MESSAGE_ID));
    message.setProperty(MetaData.GROUP_ID, pbid);
    message.setProperty(MetaData.GROUP_IX, "" + 1);
    message.setProperty(MetaData.GROUP_SIZE, "" + 1);

    MessagePersister persister = PersistPersistanceFactory.makeMessagePersister();
    List<Message> msgBefore = persister.findForMe(Collections.emptyMap());

    TispContext.runInContext(() -> persistModule.up(message));
    List<Message> msgAfter = persister.findForMe(Collections.emptyMap());

    Assertions.assertNotNull(message.getMessageId());
    Assertions.assertEquals(1, (msgAfter.size() - msgBefore.size()));
    Assertions.assertEquals(downs, downCalls); // should not be acked down
    Assertions.assertEquals(upps + 1, upCalls); // should be sent up
    Assertions.assertEquals(events, eventsCalls); // should not be sent to Scheduler via listener
  }

  @Test
  void testMoSingleWithoutNewProps() throws Exception {
    MockConfiguration mockConfiguration = MockConfiguration.getConfig();
    mockConfiguration.setPropertySpecific(mockConfiguration.getComponentShortName(), TceDataSource.TEST_DB_PROP, "true");
    mockConfiguration.setPropertySpecific(mockConfiguration.getComponentShortName(), STACKNAME + ".persist.module.readprocess.start", "false");
    mockConfiguration.setPropertySpecific(mockConfiguration.getComponentShortName(), STACKNAME + ".persist.module.persistforup", "true");
    mockConfiguration.setPropertySpecific(mockConfiguration.getComponentShortName(), STACKNAME + ".persist.module.inprocessup", "false");
    mockConfiguration.setPropertySpecific(mockConfiguration.getComponentShortName(), STACKNAME + ".persist.module.mo.ack", "false");
    mockConfiguration.setPropertySpecific(mockConfiguration.getComponentShortName(), STACKNAME + ".persist.module.mt.ack", "false");

    PersistModule persistModule = createPersistModule(makeEventService());

    persistModule.setDown(this);
    persistModule.setUp(this);
    persistModule.setOldConfigProperties();
    persistModule.deploy();
    persistModule.start();

    int events = eventsCalls;
    int downs = downCalls;
    int upps = upCalls;
    Message message = createMessage("payload", false);
    message.setMessageId(message.getProperty(MetaData.MESSAGE_ID));

    MessagePersister persister = PersistPersistanceFactory.makeMessagePersister();
    List<Message> msgBefore = persister.findForMe(Collections.emptyMap());

    TispContext.runInContext(() -> persistModule.up(message));
    List<Message> msgAfter = persister.findForMe(Collections.emptyMap());

    Assertions.assertNotNull(message.getMessageId());
    Assertions.assertEquals(1, (msgAfter.size() - msgBefore.size()));
    Assertions.assertEquals(downs, downCalls); // should not be acked down
    Assertions.assertEquals(upps + 1, upCalls); // should be sent up
    Assertions.assertEquals(events, eventsCalls); // should not be sent to Scheduler via listener
  }

  private SynchronousEventListener makeListener() {
    return event -> eventsCalls++;
  }
}
