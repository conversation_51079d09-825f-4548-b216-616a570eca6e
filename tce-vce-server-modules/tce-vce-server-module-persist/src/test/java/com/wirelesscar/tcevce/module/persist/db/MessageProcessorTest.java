package com.wirelesscar.tcevce.module.persist.db;

import java.nio.charset.StandardCharsets;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import com.volvo.tisp.framework.context.TispContext;
import com.wirelesscar.config.mock.MockConfiguration;
import com.wirelesscar.tce.core.event.EventService;
import com.wirelesscar.tce.db.common.api.ActivityStatus;
import com.wirelesscar.tce.db.common.db.api.MessageFields;
import com.wirelesscar.tce.db.common.db.api.MessagePersister;
import com.wirelesscar.tce.db.common.metrics.ProcessorMetricsReporter;
import com.wirelesscar.tce.db.source.TceDataSource;
import com.wirelesscar.tce.module.api.Message;
import com.wirelesscar.tce.module.api.Module;
import com.wirelesscar.tce.module.metrics.ModuleMetricReporter;
import com.wirelesscar.tcevce.module.persist.PersistModule;
import com.wirelesscar.tcevce.module.persist.PersistProcessor;
import com.wirelesscar.tcevce.module.persist.db.api.PersistPersistanceFactory;
import com.wirelesscar.tcevce.module.persist.metrics.PersistModuleMetricReporter;

class MessageProcessorTest {
  private static final String PAYLOAD = "Hepp payload hopp";
  private static final String STACKNAME = "notset";

  private PersistProcessor persistProcessor;

  @BeforeAll
  static void beforeAll() {
    MockConfiguration mockConfiguration = MockConfiguration.getConfig();
    mockConfiguration.setPropertySpecific(mockConfiguration.getComponentShortName(), TceDataSource.TEST_DB_PROP, "true");
    mockConfiguration.setPropertySpecific(mockConfiguration.getComponentShortName(), STACKNAME + ".persist.module.readprocess.start", "true");
    mockConfiguration.setPropertySpecific(mockConfiguration.getComponentShortName(), STACKNAME + ".persist.module.persistforup", "true");
    mockConfiguration.setPropertySpecific(mockConfiguration.getComponentShortName(), STACKNAME + ".persist.module.inprocessup", "false");
  }

  private static Message createMessage() {
    Message message = new Message();

    message.setPayload(PAYLOAD.getBytes(StandardCharsets.UTF_8));
    message.setVehicleID("" + (Math.random() * 100000000000.0));
    message.setProperty("1", "a");
    message.setProperty("2", "b");
    message.setProperty("3", "c");
    message.setProperty(MessageFields.status.name(), ActivityStatus.a.name());
    message.setProperty(MessageFields.nextCheckTime.name(), "" + (System.currentTimeMillis() - 10000));

    return message;
  }

  private static Module createModule() {
    return new PersistModule(Mockito.mock(EventService.class), Mockito.mock(ProcessorMetricsReporter.class), Mockito.mock(PersistModuleMetricReporter.class),
        Mockito.mock(ModuleMetricReporter.class));
  }

  private static Message persist(MessagePersister messagePersister, Message message) {
    AtomicReference<Message> atomicReference = new AtomicReference<>();
    TispContext.runInContext(() -> {
      try {
        atomicReference.set(messagePersister.persist(message));
      } catch (SQLException e) {
        throw new IllegalStateException(e);
      }
    });
    return atomicReference.get();
  }

  @AfterEach
  void afterEach() {
    persistProcessor.stopMe();
    persistProcessor = null;
  }

  @BeforeEach
  void beforeEach() {
    persistProcessor = new PersistProcessor(createModule(), Mockito.mock(ProcessorMetricsReporter.class), Mockito.mock(PersistModuleMetricReporter.class));
  }

  @Test
  void testAddOne() throws SQLException, InterruptedException {
    Thread thread = new Thread(persistProcessor);
    thread.start();

    MessagePersister messagePersister = PersistPersistanceFactory.makeMessagePersister();

    List<Message> messages = new ArrayList<>();
    for (int i = 0; i < 10; i++) {
      Message message = createMessage();
      message.setVehicleID(message.getVehicleID() + 1);
      message.setMessageId(null);
      message = persist(messagePersister, message);
      messages.add(message);
    }

    int i = 0;
    while (persistProcessor.processedMsgSize() < 10) {
      TimeUnit.MILLISECONDS.sleep(500);
      if (i++ > 10) {
        break;
      }
    }

    Assertions.assertEquals(0, persistProcessor.onHoldMsgSize());
    Assertions.assertEquals(0, persistProcessor.getQSize());

    for (Message message : messages) {
      messagePersister.delete(message);
    }
  }

  @Test
  void testAddSame() {
    PersistProcessor persistProcessor = new PersistProcessor(createModule(), Mockito.mock(ProcessorMetricsReporter.class),
        Mockito.mock(PersistModuleMetricReporter.class));
    Message message = createMessage();
    message.setVehicleID(message.getVehicleID() + 1);
    message.setMessageId("id" + Math.random());
    for (int i = 0; i < 10; i++) {
      Assertions.assertTrue(persistProcessor.addToQueue(message));
    }

    Assertions.assertEquals(1, persistProcessor.getQSize());
  }
}
