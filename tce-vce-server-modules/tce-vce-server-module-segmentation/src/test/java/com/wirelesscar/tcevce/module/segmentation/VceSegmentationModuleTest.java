package com.wirelesscar.tcevce.module.segmentation;

import java.nio.charset.StandardCharsets;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.TimeZone;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import com.wirelesscar.caretrack.dh.caretrack.protocol.ASNException;
import com.wirelesscar.caretrack.dh.caretrack.protocol.PERStream;
import com.wirelesscar.caretrack.dh.caretrack.protocol.TransportHeader;
import com.wirelesscar.config.exception.ConfigurationException;
import com.wirelesscar.tce.core.conf.TceConfigException;
import com.wirelesscar.tce.core.conf.TceConfigParser;
import com.wirelesscar.tce.module.api.Message;
import com.wirelesscar.tce.module.api.MetaData;
import com.wirelesscar.tce.module.api.ModuleBase;
import com.wirelesscar.tce.module.api.TransportType;
import com.wirelesscar.tce.module.api.exception.EngineRuntimeException;
import com.wirelesscar.tce.module.metrics.ModuleMetricReporter;
import com.wirelesscar.tcevce.module.segmentation.encryption.VceEncryptionHandler;
import com.wirelesscar.tcevce.module.segmentation.metrics.VceSegmentationModuleMetricReporter;
import com.wirelesscar.tcevce.wecu.device.info.database.api.WecuKeyWriterFactory;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoMoreInteractions;
import static org.mockito.Mockito.when;

class VceSegmentationModuleTest {
  private VceEncryptionHandler vceEncryptionHandler;
  private VceSegmentationModule vceSegmentationModule;
  private VceSegmentationModuleMetricReporter vceSegmentationModuleMetricReporterMock;
  private WecuKeyWriterFactory wecuKeyWriterFactory;

  /**
   * Generate a list of messages without using MessageSplitter.split()
   */
  private static List<Message> generateSegmentedMessages(int count, int seqNr) throws ASNException {
    List<Message> messages = new ArrayList<>(count);
    Message message = null;

    for (int i = 0; i < count; i++) {
      TransportHeader transportHeader = new TransportHeader();
      transportHeader.getMessageCount().setStandard();
      transportHeader.getMessageCount().getStandard().setIndexOfCurrentPacket(i);
      transportHeader.getMessageCount().getStandard().setTotalNumberOfPackets(count);
      transportHeader.setMobileOriginated(true);
      transportHeader.setSequenceNumber(seqNr);

      PERStream perStream = new PERStream();
      transportHeader.encode(perStream);
      perStream.alignOnByte();

      // Prepare each message
      message = new Message();
      byte[] headerBytes = perStream.getBuffer();
      byte[] bodyBytes = ("Message index: " + i + " of total count: " + count + " ")
          .getBytes(StandardCharsets.UTF_8);
      byte[] messageBody = new byte[headerBytes.length + bodyBytes.length];
      System.arraycopy(headerBytes, 0, messageBody, 0, headerBytes.length);
      System.arraycopy(bodyBytes, 0, messageBody, headerBytes.length, bodyBytes.length);
      message.setMessageId("MessageId: " + i);
      message.setPayload(messageBody);
      messages.add(i, message);
    }
    return messages;
  }

  /**
   * Removes the TransportHeader from the payload
   *
   * @return byte[] without header
   */
  private static byte[] removeTransportHeader(byte[] payload) throws ASNException {
    PERStream perStream = new PERStream(payload);
    TransportHeader transportHeader = new TransportHeader();
    transportHeader.decode(perStream);
    perStream.alignOnByte();

    // This is the original payload without any headers
    return perStream.getUnusedBufferPart();
  }

  @BeforeEach
  void setup() {
    vceSegmentationModuleMetricReporterMock = Mockito.mock(VceSegmentationModuleMetricReporter.class);
    vceEncryptionHandler = Mockito.mock(VceEncryptionHandler.class);
    wecuKeyWriterFactory = Mockito.mock(WecuKeyWriterFactory.class);
    when(vceEncryptionHandler.handleDecryption(any())).thenReturn(true);
    vceSegmentationModule = new VceSegmentationModule(Mockito.mock(ModuleMetricReporter.class), vceSegmentationModuleMetricReporterMock, vceEncryptionHandler,
        wecuKeyWriterFactory);
  }

  /**
   * Test message split when more then 16 segments are generated
   */
  @Test
  void testExtendedSegments() throws ASNException {
    StringBuilder stringBuilder = new StringBuilder();
    int value = 123_456;

    // append two bytes for the header (short header is 2 bytes)
    stringBuilder.append("00");

    // 22 x 6 = 132 bytes will not fit in 16 segments of 8 bytes = 128 bytes => extended
    // segmentation
    for (int i = 0; i < 22; i++) {
      stringBuilder.append(value - i);
    }
    Message bigMessage = new Message("Id", stringBuilder.toString().getBytes(StandardCharsets.UTF_8));
    int i = 0;

    // extended segmentation will be used: 22 segments with 4 bytes header + 6 byte data per segment
    // of 10 bytes
    for (Message message : MessageSplitter.split(bigMessage, 10)) {
      byte[] payload = removeTransportHeader(message.getPayload());
      Assertions.assertEquals(Integer.toString(value - i++), new String(payload, StandardCharsets.UTF_8));
    }
  }

  /**
   * Test to split and reassemble a message that splits in less then 16 segments
   */
  @Test
  void testMessagesWithOutVehicleID()
      throws ASNException, TceConfigException, ConfigurationException {
    StringBuilder stringBuilder = new StringBuilder("00"); // 2 bytes header data
    String testData = "012345678901234567890123456789012345678901234567890123456789";
    Message message = new Message("Id", stringBuilder.append(testData).toString().getBytes(StandardCharsets.UTF_8));
    message.setProperty(MetaData.VEHICLE_NOT_FOUND, "true");

    TceConfigParser.setConfig(vceSegmentationModule, Collections.emptyMap());
    vceSegmentationModule.deploy();

    // Split the message into segments
    List<Message> messages = MessageSplitter.split(message, 10);

    Message result = null;
    // Reassemble a message
    for (Message segment : messages) {
      segment.setProperty(MetaData.TRANSPORT_TYPE, TransportType.UDP.name());
      result = vceSegmentationModule.receive(segment);
    }
    Assertions.assertNotNull(result);
    Assertions.assertEquals("true", result.getProperty(MetaData.VEHICLE_NOT_FOUND));
    Assertions.assertEquals(testData, new String(result.getPayload(), StandardCharsets.UTF_8));

    verify(vceSegmentationModuleMetricReporterMock).onSegmentNewMessage(anyString());
    verify(vceSegmentationModuleMetricReporterMock, times(8)).onSegmentAdded(anyString());
    verify(vceSegmentationModuleMetricReporterMock).onSegmentCompleteMessage(anyString());
    verify(vceSegmentationModuleMetricReporterMock).onCacheRemoveEntrySize(anyString(), anyInt());
    verify(vceSegmentationModuleMetricReporterMock).onCacheRemoveEntryComplete(anyString());

    verifyNoMoreInteractions(vceSegmentationModuleMetricReporterMock);
  }

  /**
   * Test to check reassembling of messages
   */
  @Test
  void testReassemble() throws Exception {
    int count = 10;
    int seqNr = 1;

    TceConfigParser.setConfig(vceSegmentationModule, Collections.emptyMap());
    vceSegmentationModule.deploy();
    List<Message> messages = generateSegmentedMessages(count, seqNr);
    Message result = null;

    for (Message message : messages) {
      message.setProperty(MetaData.TRANSPORT_TYPE, TransportType.UDP.name());
      result = vceSegmentationModule.receive(message);
    }
    Assertions.assertNotNull(result);

    // TODO add some reasonable Assertions.asserts

    verify(vceSegmentationModuleMetricReporterMock).onSegmentNewMessage(anyString());
    verify(vceSegmentationModuleMetricReporterMock, times(10)).onSegmentAdded(anyString());
    verify(vceSegmentationModuleMetricReporterMock).onSegmentCompleteMessage(anyString());
    verify(vceSegmentationModuleMetricReporterMock).onCacheRemoveEntrySize(anyString(), anyInt());
    verify(vceSegmentationModuleMetricReporterMock).onCacheRemoveEntryComplete(anyString());

    verifyNoMoreInteractions(vceSegmentationModuleMetricReporterMock);
  }

  @Test
  void testScheduleDeliveryTimeOnDown() throws Exception {
    String smsDelaySeconds = "33";

    ModuleBase receiver = new ModuleBase(Mockito.mock(ModuleMetricReporter.class)) {
      private Date lastOne;

      @Override
      public void down(Message message) {
        String deliveryTime = message.getProperty(MetaData.SMS_SCHEDULE_DELIVERY_TIME);
        SimpleDateFormat formatter = new SimpleDateFormat("yyMMddHHmmss", Locale.ROOT);
        formatter.setTimeZone(TimeZone.getTimeZone("GMT"));

        if (deliveryTime != null) {
          Date thisOne;
          try {
            thisOne = formatter.parse(deliveryTime);
          } catch (ParseException e) {
            throw new EngineRuntimeException("Parse date failed", e);
          }

          if (lastOne != null) {
            // Do the check, diff in time should be SMS_DELAY_SECONDS
            long diff = thisOne.getTime() - lastOne.getTime();
            Assertions.assertTrue(diff / 1_000 - Long.parseLong(smsDelaySeconds) <= 1);
          }
          lastOne = thisOne;
        }
      }

      @Override
      public void up(Message message) {
      }
    };

    String testData = "0123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789";
    StringBuilder stringBuilder = new StringBuilder("00"); // 2 bytes header data
    Message message = new Message("Id", stringBuilder
        .append(testData)
        .append(testData)
        .toString()
        .getBytes(StandardCharsets.UTF_8)); // 200 bytes
    message.setProperty(MetaData.TRANSPORT_TYPE, TransportType.SMS.name());

    // Run with default configuration
    Map<String, String> configProperties = new HashMap<>();
    configProperties.put("carrier-max-size-sms", "10");
    configProperties.put("use-splitter", "true");
    configProperties.put("sms-segments-delay-in-seconds", smsDelaySeconds);
    vceSegmentationModule.setConfigProperties(configProperties);

    vceSegmentationModule.deploy();
    vceSegmentationModule.setDown(receiver);
    vceSegmentationModule.down(message);

    verifyNoMoreInteractions(vceSegmentationModuleMetricReporterMock);
  }

  @Test
  void testSetSmsScheduleDeliveryTime() throws ParseException {
    int oneHourInSec = 3600;

    Message message = new Message();

    long future = (System.currentTimeMillis() + oneHourInSec * 1_000) / 1_000 * 1_000;
    message = VceSegmentationModule.setSmsScheduleDeliveryTime(oneHourInSec, message);

    SimpleDateFormat formatter = new SimpleDateFormat("yyMMddHHmmss");
    formatter.setTimeZone(TimeZone.getTimeZone("GMT"));
    long deliveryTime = formatter.parse(message.getProperty(MetaData.SMS_SCHEDULE_DELIVERY_TIME)).getTime();

    // Should not differ more then 1 second
    Assertions.assertTrue(Math.abs(deliveryTime - future) <= 1_000);
  }

  /**
   * Test that exactly 16 segments are generated and fitted in 160 bytes (16*2 bytes as headers + 8*16 payload bytes = 160 bytes)
   */
  @Test
  void testShortExact16Segments() throws ASNException {
    StringBuilder stringBuilder = new StringBuilder();
    int value = 12_345_678;

    // append two bytes for the header (short header is 2 bytes)
    stringBuilder.append("00");
    for (int i = 0; i < 16; i++) {
      stringBuilder.append(value - i);
    }
    Message bigMessage = new Message("Id", stringBuilder.toString().getBytes(StandardCharsets.UTF_8));
    int i = 0;

    for (Message message : MessageSplitter.split(bigMessage, 10)) {
      // Remove the header
      PERStream perStream = new PERStream(message.getPayload());
      ProtocolUtil.getTransportHeader(perStream);

      byte[] payload = perStream.getUnusedBufferPart();
      Assertions.assertEquals(Integer.toString(value - i++), new String(payload, StandardCharsets.UTF_8));
    }
  }

  /**
   * Test to split and reassemble a message that splits in more then 16 segments
   */
  @Test
  void testSplitAndReassembleExtendedSegments()
      throws ASNException, TceConfigException, ConfigurationException {
    StringBuilder stringBuilder = new StringBuilder("00"); // 2 bytes header data
    String testData = "0123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789";
    Message message = new Message("Id", stringBuilder
        .append(testData)
        .append(testData)
        .toString()
        .getBytes(StandardCharsets.UTF_8)); // 200 bytes

    // Split the message into segments
    List<Message> messages = MessageSplitter.split(message, 10);

    TceConfigParser.setConfig(vceSegmentationModule, Collections.emptyMap());
    vceSegmentationModule.deploy();

    Message result = null;

    // Reassemble a message
    for (Message segment : messages) {
      segment.setProperty(MetaData.TRANSPORT_TYPE, TransportType.UDP.name());
      result = vceSegmentationModule.receive(segment);
    }
    Assertions.assertNotNull(result);
    Assertions.assertEquals(testData + testData, new String(result.getPayload(), StandardCharsets.UTF_8));

    verify(vceSegmentationModuleMetricReporterMock).onSegmentNewMessage(anyString());
    verify(vceSegmentationModuleMetricReporterMock, times(34)).onSegmentAdded(anyString());
    verify(vceSegmentationModuleMetricReporterMock).onSegmentCompleteMessage(anyString());
    verify(vceSegmentationModuleMetricReporterMock).onCacheRemoveEntrySize(anyString(), anyInt());
    verify(vceSegmentationModuleMetricReporterMock).onCacheRemoveEntryComplete(anyString());

    verifyNoMoreInteractions(vceSegmentationModuleMetricReporterMock);
  }

  /**
   * Test to split and reassemble a message that splits in less then 16 segments
   */
  @Test
  void testSplitAndReassembleShortSegments()
      throws ASNException, TceConfigException, ConfigurationException {
    StringBuilder stringBuilder = new StringBuilder("00"); // 2 bytes header data
    String testData = "012345678901234567890123456789012345678901234567890123456789";
    Message message = new Message("Id", stringBuilder.append(testData).toString().getBytes(StandardCharsets.UTF_8));

    TceConfigParser.setConfig(vceSegmentationModule, Collections.emptyMap());
    vceSegmentationModule.deploy();

    // Split the message into segments
    List<Message> messages = MessageSplitter.split(message, 10);

    Message result = null;
    // Reassemble a message
    for (Message segment : messages) {
      segment.setProperty(MetaData.TRANSPORT_TYPE, TransportType.UDP.name());
      result = vceSegmentationModule.receive(segment);
    }
    Assertions.assertNotNull(result);
    Assertions.assertEquals(testData, new String(result.getPayload(), StandardCharsets.UTF_8));

    verify(vceSegmentationModuleMetricReporterMock).onSegmentNewMessage(anyString());
    verify(vceSegmentationModuleMetricReporterMock, times(8)).onSegmentAdded(anyString());
    verify(vceSegmentationModuleMetricReporterMock).onSegmentCompleteMessage(anyString());
    verify(vceSegmentationModuleMetricReporterMock).onCacheRemoveEntrySize(anyString(), anyInt());
    verify(vceSegmentationModuleMetricReporterMock).onCacheRemoveEntryComplete(anyString());

    verifyNoMoreInteractions(vceSegmentationModuleMetricReporterMock);
  }

  @Test
  void validateBeforeUpTest() throws TceConfigException, ConfigurationException {
    Message message = new Message();
    message.setProperty(MetaData.TRANSPORT_TYPE, "SMS");
    message.setProperty(MetaData.SMPP_SOURCE_ADDRESS, "foo");
    message.setPayload(new byte[1]);

    TceConfigParser.setConfig(vceSegmentationModule, Collections.emptyMap());

    try {
      vceSegmentationModule.up(message);
      Assertions.fail();
    } catch (IllegalStateException e) {
      Assertions.assertEquals("initDone == false", e.getMessage());
    }
    verifyNoMoreInteractions(vceSegmentationModuleMetricReporterMock);
  }
}
