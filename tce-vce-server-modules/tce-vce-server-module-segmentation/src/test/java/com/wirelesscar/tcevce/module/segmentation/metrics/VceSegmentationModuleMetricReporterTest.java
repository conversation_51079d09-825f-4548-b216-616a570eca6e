package com.wirelesscar.tcevce.module.segmentation.metrics;

import static com.wirelesscar.tcevce.module.segmentation.metrics.VceSegmentationModuleMetricReporter.CACHE_REMOVE_ENTRY_COMPLETE;
import static com.wirelesscar.tcevce.module.segmentation.metrics.VceSegmentationModuleMetricReporter.CACHE_REMOVE_ENTRY_NOT_COMPLETED;
import static com.wirelesscar.tcevce.module.segmentation.metrics.VceSegmentationModuleMetricReporter.CACHE_REMOVE_ENTRY_SIZE;
import static com.wirelesscar.tcevce.module.segmentation.metrics.VceSegmentationModuleMetricReporter.COMPLETE_MESSAGE;
import static com.wirelesscar.tcevce.module.segmentation.metrics.VceSegmentationModuleMetricReporter.ONE_SEGMENT_MESSAGE;
import static com.wirelesscar.tcevce.module.segmentation.metrics.VceSegmentationModuleMetricReporter.SEGMENT_ADDED;
import static com.wirelesscar.tcevce.module.segmentation.metrics.VceSegmentationModuleMetricReporter.SEGMENT_NEW_MESSAGE;
import static com.wirelesscar.tcevce.module.segmentation.metrics.VceSegmentationModuleMetricReporter.STACK_VAR_NAME;
import static org.junit.jupiter.api.Assertions.assertEquals;

import java.util.function.BiConsumer;
import java.util.function.Function;

import org.junit.jupiter.api.Test;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tags;
import io.micrometer.core.instrument.simple.SimpleMeterRegistry;

class VceSegmentationModuleMetricReporterTest {
  private static final String STACK_TEST = "stack";

  private static void checkCounter(MeterRegistry meterRegistry, int expectedCount, String name, Tags tags) {
    final Counter counter = meterRegistry.find(name).tags(tags).counter();

    assertEquals(expectedCount, counter.count());
  }

  private static <T> void initReporterAndTest(Function<MeterRegistry, T> initMetricsReporter, BiConsumer<MeterRegistry, T> test) {
    MeterRegistry meterRegistry = new SimpleMeterRegistry();
    T metricsReporter = initMetricsReporter.apply(meterRegistry);

    test.accept(meterRegistry, metricsReporter);
  }

  @Test
  void onCacheRemoveEntryCompleteTest() {
    initReporterAndTest(VceSegmentationModuleMetricReporter::new, (meterRegistry, vceSegmentationModuleMetricReporter) -> {
      vceSegmentationModuleMetricReporter.onCacheRemoveEntryComplete(STACK_TEST);
      checkCounter(meterRegistry, 1, CACHE_REMOVE_ENTRY_COMPLETE, Tags.of(STACK_VAR_NAME, STACK_TEST));
    });
  }

  @Test
  void onCacheRemoveEntryNotCompletedTest() {
    initReporterAndTest(VceSegmentationModuleMetricReporter::new, (meterRegistry, vceSegmentationModuleMetricReporter) -> {
      vceSegmentationModuleMetricReporter.onCacheRemoveEntryNotCompleted(STACK_TEST);
      checkCounter(meterRegistry, 1, CACHE_REMOVE_ENTRY_NOT_COMPLETED, Tags.of(STACK_VAR_NAME, STACK_TEST));
    });
  }

  @Test
  void onCacheRemoveEntrySizeTest() {
    initReporterAndTest(VceSegmentationModuleMetricReporter::new, (meterRegistry, vceSegmentationModuleMetricReporter) -> {
      vceSegmentationModuleMetricReporter.onCacheRemoveEntrySize(STACK_TEST, 42);
      checkCounter(meterRegistry, 42, CACHE_REMOVE_ENTRY_SIZE, Tags.of(STACK_VAR_NAME, STACK_TEST));
    });
  }

  @Test
  void onOneSegmentMessageTest() {
    initReporterAndTest(VceSegmentationModuleMetricReporter::new, (meterRegistry, vceSegmentationModuleMetricReporter) -> {
      vceSegmentationModuleMetricReporter.onOneSegmentMessage(STACK_TEST);
      checkCounter(meterRegistry, 1, ONE_SEGMENT_MESSAGE, Tags.of(STACK_VAR_NAME, STACK_TEST));
    });
  }

  @Test
  void onSegmentAddedTest() {
    initReporterAndTest(VceSegmentationModuleMetricReporter::new, (meterRegistry, vceSegmentationModuleMetricReporter) -> {
      vceSegmentationModuleMetricReporter.onSegmentAdded(STACK_TEST);
      checkCounter(meterRegistry, 1, SEGMENT_ADDED, Tags.of(STACK_VAR_NAME, STACK_TEST));
    });
  }

  @Test
  void onSegmentCompleteMessageTest() {
    initReporterAndTest(VceSegmentationModuleMetricReporter::new, (meterRegistry, vceSegmentationModuleMetricReporter) -> {
      vceSegmentationModuleMetricReporter.onSegmentCompleteMessage(STACK_TEST);
      checkCounter(meterRegistry, 1, COMPLETE_MESSAGE, Tags.of(STACK_VAR_NAME, STACK_TEST));
    });
  }

  @Test
  void onSegmentNewMessageTest() {
    initReporterAndTest(VceSegmentationModuleMetricReporter::new, (meterRegistry, vceSegmentationModuleMetricReporter) -> {
      vceSegmentationModuleMetricReporter.onSegmentNewMessage(STACK_TEST);
      checkCounter(meterRegistry, 1, SEGMENT_NEW_MESSAGE, Tags.of(STACK_VAR_NAME, STACK_TEST));
    });
  }
}
