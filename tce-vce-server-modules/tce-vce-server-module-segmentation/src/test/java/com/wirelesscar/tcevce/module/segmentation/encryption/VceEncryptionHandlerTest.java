package com.wirelesscar.tcevce.module.segmentation.encryption;

import java.nio.charset.StandardCharsets;
import java.util.Optional;
import java.util.UUID;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import com.volvo.tisp.vc.common.dto.lib.vehicle.Imsi;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Msisdn;
import com.volvo.tisp.vc.main.utils.lib.type.Either;
import com.volvo.tisp.vc.main.utils.lib.type.ImmutableByteArray;
import com.volvo.vc.crypto.common.entity.PlainTextPayload;
import com.volvo.vc.crypto.symmetric.encryption.gcm.AesGcmEncryptionResult;
import com.volvo.vc.crypto.symmetric.encryption.gcm.SymmetricAesGcmEncryptionService;
import com.volvo.vc.crypto.symmetric.encryption.gcm.SymmetricAesGcmEncryptionServiceImpl;
import com.volvo.vc.crypto.symmetric.key.SymmetricKey;
import com.wirelesscar.tce.module.api.Message;
import com.wirelesscar.tce.module.api.MetaData;
import com.wirelesscar.tcevce.module.segmentation.identify.ConrepoIdentifier;
import com.wirelesscar.tcevce.wecu.device.info.cache.core.api.VehicleKeyCache;
import com.wirelesscar.tcevce.wecu.device.info.database.api.DeviceInfoReader;
import com.wirelesscar.tcevce.wecu.device.info.database.api.WecuKeyWriter;
import com.wirelesscar.tcevce.wecu.device.info.database.api.WecuKeyWriterFactory;
import com.wirelesscar.tcevce.wecu.device.info.database.model.DeviceInfo;
import com.wirelesscar.tcevce.wecu.device.info.database.model.Handle;
import com.wirelesscar.tcevce.wecu.device.info.database.model.PersistedDeviceInfo;
import com.wirelesscar.tcevce.wecu.device.info.database.model.PersistedWecuKey;
import com.wirelesscar.tcevce.wecu.device.info.database.model.PersistedWecuKeyBuilder;
import com.wirelesscar.tcevce.wecu.device.info.database.model.SimInfo;
import com.wirelesscar.tcevce.wecu.device.info.database.model.WecuKey;
import com.wirelesscar.tcevce.wecu.device.info.database.model.WecuKeyBuilder;

class VceEncryptionHandlerTest {

  private static final Optional<Imsi> IMSI = Optional.of(Imsi.ofLong(240080014556780L));
  private static final Msisdn MSISDN = Msisdn.ofString("+123456789012345");
  private ConrepoIdentifier conrepoIdentifier;
  private DeviceInfoReader deviceInfoReader;
  private VehicleKeyCache keyCache;
  private final SymmetricAesGcmEncryptionService symmetricAesGcmEncryptionService = SymmetricAesGcmEncryptionServiceImpl.INSTANCE;
  private VceEncryptionHandler vceEncryptionHandler;
  private WecuKeyWriter wecuKeyWriter;

  private static byte[] assembleBody(AesGcmEncryptionResult aesGcmEncryptionResult) {
    ImmutableByteArray messageAuthenticationCode = aesGcmEncryptionResult.getMessageAuthenticationCode().getImmutableByteArray();
    ImmutableByteArray initializationVector = aesGcmEncryptionResult.getInitializationVector().getImmutableByteArray();
    ImmutableByteArray encryptedPayloadWithoutMac = aesGcmEncryptionResult.getEncryptedPayloadWithoutMac().getImmutableByteArray();

    byte[] encryptedBody = new byte[initializationVector.getLength() + messageAuthenticationCode.getLength() + encryptedPayloadWithoutMac.getLength()];

    System.arraycopy(initializationVector.toByteArray(), 0, encryptedBody, 0, initializationVector.getLength());
    System.arraycopy(messageAuthenticationCode.toByteArray(), 0, encryptedBody, initializationVector.getLength(),
        messageAuthenticationCode.getLength());
    System.arraycopy(encryptedPayloadWithoutMac.toByteArray(), 0, encryptedBody, initializationVector.getLength() + messageAuthenticationCode.getLength(),
        encryptedPayloadWithoutMac.getLength());

    return encryptedBody;
  }

  @Test
  void decryptPayload() {
    String messageBody = "012345678901234567890123456789012345678901234567890123456789";
    Message message = createMessage(messageBody);

    Handle handle = Handle.ofString(message.getVehicleID());

    SimInfo simInfo = Mockito.mock(SimInfo.class);
    Mockito.when(simInfo.getMsisdn()).thenReturn(MSISDN);
    Mockito.when(simInfo.getImsi()).thenReturn(IMSI);

    DeviceInfo deviceInfo = Mockito.mock(DeviceInfo.class);
    Mockito.when(deviceInfo.getHandle()).thenReturn(handle);
    Mockito.when(deviceInfo.getSimInfo()).thenReturn(Optional.of(simInfo));
    PersistedDeviceInfo persistedDeviceInfo = Mockito.mock(PersistedDeviceInfo.class);
    Mockito.when(persistedDeviceInfo.getDeviceInfo()).thenReturn(deviceInfo);

    Mockito.when(deviceInfoReader.findDeviceInfoByVpi(Mockito.any())).thenReturn(Optional.of(persistedDeviceInfo));
    WecuKey wecuKey = new WecuKeyBuilder().setHandle(handle).setMsisdn(MSISDN).build();
    PersistedWecuKey persistedWecuKey = new PersistedWecuKeyBuilder().setWecuKey(wecuKey).build();
    Mockito.when(wecuKeyWriter.findWecuKeyByHandle(Mockito.any())).thenReturn(Optional.of(persistedWecuKey));

    vceEncryptionHandler.handleDecryption(message);
    Assertions.assertArrayEquals(messageBody.getBytes(), message.getPayload());
  }

  @BeforeEach
  void setUp() {
    conrepoIdentifier = Mockito.mock(ConrepoIdentifier.class);
    deviceInfoReader = Mockito.mock(DeviceInfoReader.class);
    keyCache = Mockito.mock(VehicleKeyCache.class);
    wecuKeyWriter = Mockito.mock(WecuKeyWriter.class);
    WecuKeyWriterFactory wecuKeyWriterFactory = Mockito.mock(WecuKeyWriterFactory.class);
    Mockito.when(wecuKeyWriterFactory.createReadCommitted()).thenReturn(wecuKeyWriter);

    vceEncryptionHandler = new VceEncryptionHandler(conrepoIdentifier, symmetricAesGcmEncryptionService, keyCache, wecuKeyWriterFactory, deviceInfoReader);
  }

  private Message createMessage(String messageBody) {
    String vehicleId = "0123456789ABCDEF0123456789ABCDEF";
    String messageId = UUID.randomUUID().toString();
    byte[] encryptedBody = encryptMessageBody(messageBody);

    Message message = new Message(messageId, encryptedBody);
    message.setProperty(MetaData.VPI, vehicleId);
    message.setProperty(MetaData.HANDLE, vehicleId);

    message.setVehicleID(vehicleId);

    return message;
  }

  private byte[] encryptMessageBody(String messageBody) {
    byte[] hash = Hkdf.usingDefaults().defaultHash(IMSI.get().toString().getBytes());
    SymmetricKey symmetricKey = SymmetricKey.create(ImmutableByteArray.of(hash));

    byte[] bodyBytes = messageBody.getBytes(StandardCharsets.UTF_8);

    Either<RuntimeException, AesGcmEncryptionResult> result = symmetricAesGcmEncryptionService.encryptWithRandomIv(symmetricKey,
        PlainTextPayload.create(ImmutableByteArray.of(bodyBytes)));

    return assembleBody(result.getRight());
  }
}
