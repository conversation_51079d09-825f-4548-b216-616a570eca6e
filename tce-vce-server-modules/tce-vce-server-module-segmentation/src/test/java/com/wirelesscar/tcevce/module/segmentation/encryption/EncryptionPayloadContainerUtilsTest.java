package com.wirelesscar.tcevce.module.segmentation.encryption;

import java.util.Base64;
import java.util.HexFormat;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.vc.main.utils.lib.type.Either;
import com.volvo.tisp.vc.main.utils.lib.type.ImmutableByteArray;
import com.volvo.vc.crypto.common.entity.InitializationVector;
import com.volvo.vc.crypto.common.entity.PlainTextPayload;
import com.volvo.vc.crypto.symmetric.encryption.gcm.AesGcmEncryptionResult;
import com.volvo.vc.crypto.symmetric.encryption.gcm.EncryptedPayloadWithoutMac;
import com.volvo.vc.crypto.symmetric.encryption.gcm.MessageAuthenticationCode;
import com.volvo.vc.crypto.symmetric.encryption.gcm.SymmetricAesGcmEncryptionService;
import com.volvo.vc.crypto.symmetric.encryption.gcm.SymmetricAesGcmEncryptionServiceImpl;
import com.volvo.vc.crypto.symmetric.key.SymmetricKey;

class EncryptionPayloadContainerUtilsTest {

  private static final String ENCODED_IV = "Q5GONjp9o8yDuVMy";
  private static final String ENCODED_MAC = "cd4seOVBNXVwZU2CRfQKxQ==";
  private static final String ENCRYPTED_ENCODED_PAYLOAD = "hpmDx+9Rk5VqCZpwdU4s";
  private static final String ENCRYPTED_MESSAGE = "Q5GONjp9o8yDuVMycd4seOVBNXVwZU2CRfQKxYaZg8fvUZOVagmacHVOLA==";
  private static final String PAYLOAD = "MESSAGE_PAYLOAD";
  private static final SymmetricKey SYMMETRIC_KEY = SymmetricKey.create(ImmutableByteArray.of("1234567890123456".getBytes()));
  private final SymmetricAesGcmEncryptionService symmetricAesGcmEncryptionService = SymmetricAesGcmEncryptionServiceImpl.INSTANCE;

  private static String formatHex(ImmutableByteArray bytes) {
    return HexFormat.of().withDelimiter(" ").withUpperCase().formatHex(bytes.toByteArray());
  }

  private static ImmutableByteArray parseHex(String hex) {
    return ImmutableByteArray.of(HexFormat.of().withDelimiter(" ").parseHex(hex));
  }

  @Test
  void test() {
    String hexPayload = "00 00 EB 55 D7 21 E3 0D 4F 59 FB E5 B4 26 78 0A 1F 1F CA 9A 06 46 70 3A 1A DF A4 56 B5 92 01 21 3F E2 41 AC 5A 37 45 8F 5B 54 9E E3 9B D6 A8 C5 76 A5 A5 4E 2E D2 3F DE 9C 78 17 D2 90 C5 D7";
    String imsi = "240080014556780";
    decryptPayload(hexPayload, imsi);
  }

  @Test
  void testFromBytes() {
    byte[] ENCRYPTED_MESSAGE_BYTES = Base64.getDecoder().decode(ENCRYPTED_MESSAGE);

    EncryptionPayloadContainer container = EncryptionPayloadContainerUtils.fromBytes(ImmutableByteArray.of(ENCRYPTED_MESSAGE_BYTES));

    Assertions.assertEquals(ENCODED_IV, container.getInitializationVector().getImmutableByteArray().asBase64String());
    Assertions.assertEquals(ENCODED_MAC, container.getMessageAuthenticationCode().getImmutableByteArray().asBase64String());
    Assertions.assertEquals(ENCRYPTED_ENCODED_PAYLOAD, container.getEncryptedPayloadWithoutMac().getImmutableByteArray().asBase64String());

    AesGcmEncryptionResult aesGcmEncryptionResult = AesGcmEncryptionResult.create(container.getEncryptedPayloadWithoutMac(),
        container.getInitializationVector(), container.getMessageAuthenticationCode());

    Either<RuntimeException, PlainTextPayload> resultDecryption = symmetricAesGcmEncryptionService.decrypt(SYMMETRIC_KEY, aesGcmEncryptionResult);
    Assertions.assertArrayEquals(PAYLOAD.getBytes(), resultDecryption.getRight().getImmutableByteArray().toByteArray());
  }

  @Test
  void testToBytes() {
    InitializationVector initializationVector = InitializationVector.create(ImmutableByteArray.of(Base64.getDecoder().decode(ENCODED_IV)));
    MessageAuthenticationCode messageAuthenticationCode = MessageAuthenticationCode.create(ImmutableByteArray.of(Base64.getDecoder().decode(ENCODED_MAC)));
    EncryptedPayloadWithoutMac encryptedPayloadWithoutMac = EncryptedPayloadWithoutMac.create(
        ImmutableByteArray.of(Base64.getDecoder().decode(ENCRYPTED_ENCODED_PAYLOAD)));
    AesGcmEncryptionResult aesGcmEncryptionResult = AesGcmEncryptionResult.create(encryptedPayloadWithoutMac, initializationVector, messageAuthenticationCode);

    byte[] encrypted = EncryptionPayloadContainerUtils.toBytes(aesGcmEncryptionResult);

    byte[] decoded = Base64.getDecoder().decode(ENCRYPTED_MESSAGE);
    Assertions.assertArrayEquals(decoded, encrypted);
  }

  @Test
  void testWithExternalProvidedData() {
    String IV = "00 00 2D CF 46 29 04 B4 78 D8 6C 8D";
    String MAC = "9B 47 F0 19 C0 80 C3 70 5E 16 72 CD 1E DE 87 21";
    String encryptedPayload = "29 A2 0A 1F 46 DE BB 8F 80 24 65 51 86 C4 1F 88 9D 28 A6 6A 70 84 35 A0 20 68 5A 11 78 E4 DC 99";
    String fullMessage = "00 00 2D CF 46 29 04 B4 78 D8 6C 8D 9B 47 F0 19 C0 80 C3 70 5E 16 72 CD 1E DE 87 21 29 A2 0A 1F 46 DE BB 8F 80 24 65 51 86 C4 1F 88 9D 28 A6 6A 70 84 35 A0 20 68 5A 11 78 E4 DC 99";
    String expectedPayload = "Hello, AES128-GCM!!";

    InitializationVector initializationVector = InitializationVector.create(parseHex(IV));
    MessageAuthenticationCode messageAuthenticationCode = MessageAuthenticationCode.create(parseHex(MAC));
    EncryptedPayloadWithoutMac encryptedPayloadWithoutMac = EncryptedPayloadWithoutMac.create(parseHex(encryptedPayload));

    EncryptionPayloadContainer encryptionPayloadContainer = EncryptionPayloadContainerUtils.fromBytes(parseHex(fullMessage));
    Assertions.assertEquals(initializationVector, encryptionPayloadContainer.getInitializationVector());
    Assertions.assertEquals(messageAuthenticationCode, encryptionPayloadContainer.getMessageAuthenticationCode());
    Assertions.assertEquals(encryptedPayloadWithoutMac, encryptionPayloadContainer.getEncryptedPayloadWithoutMac());

    String aesKey = "CD 8B AD F7 FC 20 22 8D 68 05 0B 61 E3 62 7F CC";
    SymmetricKey symmetricKey = SymmetricKey.create(ImmutableByteArray.of(HexFormat.of().parseHex(aesKey.replace(" ", ""))));

    AesGcmEncryptionResult aesGcmEncryptionResult = AesGcmEncryptionResult.create(encryptionPayloadContainer.getEncryptedPayloadWithoutMac(),
        encryptionPayloadContainer.getInitializationVector(), encryptionPayloadContainer.getMessageAuthenticationCode());

    Either<RuntimeException, PlainTextPayload> resultDecryption = symmetricAesGcmEncryptionService.decrypt(symmetricKey, aesGcmEncryptionResult);
    Assertions.assertTrue(resultDecryption.isRight());
    PlainTextPayload plainTextPayload = resultDecryption.getRight();
    Assertions.assertArrayEquals(expectedPayload.getBytes(), plainTextPayload.toString().trim().getBytes());
  }

  private byte[] decryptPayload(String hexPayload, String imsi) {
    EncryptionPayloadContainer encryptionPayloadContainer = EncryptionPayloadContainerUtils.fromBytes(parseHex(hexPayload));

    byte[] imsiHash = Hkdf.usingDefaults().defaultHash(imsi.getBytes());

    SymmetricKey symmetricKey = SymmetricKey.create(ImmutableByteArray.of(imsiHash));

    AesGcmEncryptionResult aesGcmEncryptionResult = AesGcmEncryptionResult.create(encryptionPayloadContainer.getEncryptedPayloadWithoutMac(),
        encryptionPayloadContainer.getInitializationVector(), encryptionPayloadContainer.getMessageAuthenticationCode());

    Either<RuntimeException, PlainTextPayload> resultDecryption = symmetricAesGcmEncryptionService.decrypt(symmetricKey, aesGcmEncryptionResult);
    if (resultDecryption.isLeft()) {
      System.err.println(resultDecryption.getLeft().getMessage());
      return null;
    }

    Assertions.assertTrue(resultDecryption.isRight());
    PlainTextPayload plainTextPayload = resultDecryption.getRight();
    return plainTextPayload.getImmutableByteArray().toByteArray();
  }

}