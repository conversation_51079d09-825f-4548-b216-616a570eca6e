package com.wirelesscar.tcevce.module.segmentation.encryption;

import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import com.github.benmanes.caffeine.cache.Cache;
import com.volvo.tisp.vc.main.utils.lib.type.ImmutableByteArray;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;
import com.volvo.vc.crypto.symmetric.key.SymmetricKey;
import com.wirelesscar.tcevce.wecu.device.info.cache.core.api.VehicleKeyCache;
import com.wirelesscar.tcevce.wecu.device.info.cache.core.impl.VehicleKeyCacheImpl;

class VehicleKeyCacheImplTest {

  private static final String VEHICLE_ID = "vehicleId";

  @Test
  void putNullTest() {
    Cache<String, SymmetricKey> cache = Mockito.mock(Cache.class);
    VehicleKeyCache vehicleKeyCache = new VehicleKeyCacheImpl(cache);
    AssertThrows.illegalArgumentException(() -> vehicleKeyCache.putSymmetricKey(null, null),
        "vehicleId must not be null");
    AssertThrows.illegalArgumentException(() -> vehicleKeyCache.putSymmetricKey(VEHICLE_ID, null),
        "symmetricKey must not be null");

    Mockito.verifyNoInteractions(cache);
  }

  @Test
  void putTest() {
    Cache<String, SymmetricKey> cache = Mockito.mock(Cache.class);
    VehicleKeyCache vehicleKeyCache = new VehicleKeyCacheImpl(cache);
    SymmetricKey symmetricKey = SymmetricKey.create(ImmutableByteArray.of("key".getBytes()));
    vehicleKeyCache.putSymmetricKey(VEHICLE_ID, symmetricKey);

    Mockito.verify(cache).put(VEHICLE_ID, symmetricKey);
    Mockito.verifyNoMoreInteractions(cache);
  }

  @Test
  void removeNullTest() {
    Cache<String, SymmetricKey> cache = Mockito.mock(Cache.class);
    VehicleKeyCache vehicleKeyCache = new VehicleKeyCacheImpl(cache);
    AssertThrows.illegalArgumentException(() -> vehicleKeyCache.removeSymmetricKey(null),
        "vehicleId must not be null");

    Mockito.verifyNoInteractions(cache);
  }

  @Test
  void removeTest() {
    Cache<String, SymmetricKey> cache = Mockito.mock(Cache.class);
    VehicleKeyCache vehicleKeyCache = new VehicleKeyCacheImpl(cache);

    SymmetricKey symmetricKey = SymmetricKey.create(ImmutableByteArray.of("key".getBytes()));
    Mockito.when(cache.getIfPresent(VEHICLE_ID)).thenReturn(symmetricKey);

    vehicleKeyCache.removeSymmetricKey(VEHICLE_ID);

    Mockito.verify(cache).getIfPresent(VEHICLE_ID);
    Mockito.verify(cache).invalidate(VEHICLE_ID);
    Mockito.verifyNoMoreInteractions(cache);
  }
}
