package com.wirelesscar.tcevce.module.segmentation;

import com.wirelesscar.caretrack.dh.caretrack.protocol.ASNException;
import com.wirelesscar.caretrack.dh.caretrack.protocol.MessageCount;
import com.wirelesscar.caretrack.dh.caretrack.protocol.PERStream;
import com.wirelesscar.caretrack.dh.caretrack.protocol.TransportHeader;

public final class ProtocolUtil {
  private ProtocolUtil() {
    throw new IllegalStateException();
  }

  public static int getIndexOfPacket(TransportHeader transportHeader) {
    try {
      if (transportHeader.getMessageCount().getChoice() == MessageCount.E_STANDARD) {
        return (int) transportHeader.getMessageCount().getStandard().getIndexOfCurrentPacket();
      }
      return (int) transportHeader.getMessageCount().getExtended().getIndexOfCurrentPacket();
    } catch (ASNException e) {
      throw new IllegalArgumentException(e);
    }
  }

  public static int getTotalPackets(TransportHeader transportHeader) {
    try {
      if (transportHeader.getMessageCount().getChoice() == MessageCount.E_STANDARD) {
        return (int) transportHeader.getMessageCount().getStandard().getTotalNumberOfPackets();
      }
      return (int) transportHeader.getMessageCount().getExtended().getTotalNumberOfPackets();
    } catch (ASNException e) {
      throw new IllegalArgumentException(e);
    }
  }

  public static TransportHeader getTransportHeader(PERStream perStream) {
    try {
      TransportHeader transportHeader = new TransportHeader();
      transportHeader.decode(perStream);
      perStream.alignOnByte();
      return transportHeader;
    } catch (ASNException e) {
      throw new IllegalArgumentException(e);
    }
  }
}
