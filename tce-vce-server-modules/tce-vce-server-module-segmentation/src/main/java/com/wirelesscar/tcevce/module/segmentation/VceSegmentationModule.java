package com.wirelesscar.tcevce.module.segmentation;

import java.util.Date;
import java.util.HashMap;
import java.util.HexFormat;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import com.volvo.tisp.framework.context.TispContext;
import com.volvo.tisp.identifier.TrackingIdentifier;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.wirelesscar.caretrack.dh.caretrack.protocol.ASNException;
import com.wirelesscar.caretrack.dh.caretrack.protocol.PERStream;
import com.wirelesscar.caretrack.dh.caretrack.protocol.TransportHeader;
import com.wirelesscar.tce.core.conf.TceConfig;
import com.wirelesscar.tce.module.api.Message;
import com.wirelesscar.tce.module.api.MetaData;
import com.wirelesscar.tce.module.api.ModuleBase;
import com.wirelesscar.tce.module.api.ModuleTypeConstants;
import com.wirelesscar.tce.module.api.TransportType;
import com.wirelesscar.tce.module.api.exception.EngineRuntimeException;
import com.wirelesscar.tce.module.metrics.ModuleMetricReporter;
import com.wirelesscar.tce.utils.MetadataUtils;
import com.wirelesscar.tcevce.module.segmentation.encryption.VceEncryptionHandler;
import com.wirelesscar.tcevce.module.segmentation.metrics.VceSegmentationModuleMetricReporter;
import com.wirelesscar.tcevce.wecu.device.info.database.api.WecuKeyWriter;
import com.wirelesscar.tcevce.wecu.device.info.database.api.WecuKeyWriterFactory;
import com.wirelesscar.tcevce.wecu.device.info.database.model.Handle;
import com.wirelesscar.tcevce.wecu.device.info.database.model.PersistedWecuKey;

@Component(ModuleTypeConstants.VCE_SEGMENTATION)
@Scope("prototype")
public class VceSegmentationModule extends ModuleBase {
  private static final Map<String, String> identifiers = new HashMap<>();
  private static final Logger logger = LoggerFactory.getLogger(VceSegmentationModule.class);

  @TceConfig(configKey = "cache-ttl-in-minutes", defaultValue = "1440")
  private int cacheTtlInMinutes;

  @TceConfig(configKey = "carrier-max-size-sat", defaultValue = "828")
  private int carrierMaxSizeSat;

  @TceConfig(configKey = "carrier-max-size-sms", defaultValue = "140")
  private int carrierMaxSizeSms;

  @TceConfig(configKey = "carrier-max-size-udp", defaultValue = "828")
  private int carrierMaxSizeUdp;
  private boolean initDone = false;
  @TceConfig(configKey = "maximum-cache-size", defaultValue = "50000")
  private int maximumCacheSize;
  private MultiFrameCache multiFrameCache = null;
  @TceConfig(configKey = "sat-identifier", defaultValue = "SATELLITE_SOURCE_ADDRESS")
  private MetaData satIdentifier;
  @TceConfig(configKey = "sms-segments-delay-in-seconds", defaultValue = "65")
  private int smsDelayInSec;
  @TceConfig(configKey = "sms-identifier", defaultValue = "SMPP_SOURCE_ADDRESS")
  private MetaData smsIdentifier;
  @TceConfig(configKey = "udp-identifier", defaultValue = "IP_SRC_ADDRESS")
  private MetaData udpIdentifier;
  @TceConfig(configKey = "use-receiver", defaultValue = "true")
  private boolean useReceiver;
  @TceConfig(configKey = "use-splitter", defaultValue = "true")
  private boolean useSplitter;
  private final VceEncryptionHandler vceEncryptionHandler;
  private final VceSegmentationModuleMetricReporter vceSegmentationModuleMetricReporter;
  private final WecuKeyWriterFactory wecuKeyWriterFactory;

  public VceSegmentationModule(ModuleMetricReporter moduleMetricReporter, VceSegmentationModuleMetricReporter vceSegmentationModuleMetricReporter,
      VceEncryptionHandler vceEncryptionHandler, WecuKeyWriterFactory wecuKeyWriterFactory) {
    super(moduleMetricReporter);
    this.vceSegmentationModuleMetricReporter = vceSegmentationModuleMetricReporter;
    this.vceEncryptionHandler = vceEncryptionHandler;
    this.wecuKeyWriterFactory = wecuKeyWriterFactory;
  }

  static Message setSmsScheduleDeliveryTime(int timeFromNowInSeconds, Message message) {
    long deliveryTime = System.currentTimeMillis() + timeFromNowInSeconds * 1_000;
    Date date = new Date(deliveryTime);
    return MetadataUtils.setSmsScheduleDeliveryTime(date, message);
  }

  private static String getVcehicleIdentifier(String transportType) {
    switch (transportType) {
      case "UDP":
        return identifiers.get(TransportType.UDP.name());
      case "SMS":
        return identifiers.get(TransportType.SMS.name());
      case "SATELLITE":
        return identifiers.get(TransportType.SATELLITE.name());
      default:
        return TransportType.UDP.name();
    }
  }

  /**
   * Handle Life Cycle State transition from BOOTED -> HALTED
   */
  @Override
  public void deploy() {
    initConfig();
  }

  @Override
  public void down(Message message) {
    validateBeforeDown(message);

    logger.debug("Use splitter: {}", useSplitter);

    vceEncryptionHandler.handleEncryption(message);

    if (useSplitter) {
      try {
        List<Message> subMessages = MessageSplitter.split(message, getCarrierMaxSize(message));
        logger.debug("Splited message ID: {} in {} sub messages", message.getMessageId(), subMessages.size());
        TransportType transportType = MetadataUtils.getTransportType(message);
        int msgNr = 0;
        for (Message subMessage : subMessages) {
          if (msgNr != 0 && transportType == TransportType.SMS && smsDelayInSec > 0) {
            setSmsScheduleDeliveryTime(msgNr * smsDelayInSec, subMessage);
          }
          msgNr++;

          logger.trace("Original message ID: {}, message number {} of {}, new sub message ID: {}, sub message payload:{}", message.getMessageId(), msgNr,
              subMessages.size(), subMessage.getMessageId(), HexFormat.of().withUpperCase().withDelimiter(" ").formatHex(message.getPayload()));
          sendDown(subMessage);
        }
        // no error return message
        return;
      } catch (ASNException e) {
        throw new EngineRuntimeException("failed when try to splitt message: " + message, e);
      }
    }

    sendDown(message);
  }

  /**
   * Handle Life Cycle State transition from HALTED -> BOOTED
   */
  @Override
  public void undeploy() {
    initDone = false;
  }

  @Override
  public void up(Message message) {
    validateBeforeUp(message);

    if (useReceiver) {
      Message fullMessage = receive(message);
      if (fullMessage != null) {
        try (final TispContext.Scope ignored = TispContext.current().newScope().tid(TrackingIdentifier.create()).activate()) {
          fullMessage.setMessageId(TispContext.current().tid().toString());
          logger.debug("Send complete assembled message UP: {}", fullMessage);
          sendUp(fullMessage);
        }
      }
    } else {
      sendUp(message);
    }
  }

  /**
   * Handle segmented and not segmented messages. Keep track of messages in flight and reassembles the segments into complete messages. When a complet
   */
  Message receive(Message message) {
    logger.trace("Received mo message: {}", message);

    String transportType = message.getProperty(MetaData.TRANSPORT_TYPE);
    String identifier = message.getProperty(getVcehicleIdentifier(transportType));

    PERStream perStream = new PERStream(message.getPayload());
    TransportHeader transportHeader = new TransportHeader();

    try {
      transportHeader.decode(perStream);
    } catch (ASNException e) {
      throw new IllegalArgumentException(e);
    }
    perStream.alignOnByte();

    int totalPackets = ProtocolUtil.getTotalPackets(transportHeader);
    int indexOfPacket = ProtocolUtil.getIndexOfPacket(transportHeader);
    int sequenceNumber = (int) transportHeader.getSequenceNumber();
    boolean mobileOriginated = transportHeader.getMobileOriginated();
    boolean encrypted = transportHeader.getEncrypted();
    boolean skipAck = transportHeader.getSkipAck();

    logger.trace(
        "Transport header for MessageId: {}, totalPackets: {}, indexOfPacket: {}, sequenceNumber: {}, mobileOriginated: {}, skipAck: {}, encrypted: {}",
        message.getMessageId(), totalPackets, indexOfPacket, sequenceNumber, mobileOriginated, skipAck, encrypted);
    Message fullMessage = null;

    try {
      if (totalPackets == 1) {
        logger.debug("No segmentation needed for message: {}", message);
        // This message is only one segment so this is it!
        fullMessage = new Message();
        // Strip the transport header
        fullMessage.setPayload(perStream.getUnusedBufferPart());
        vceSegmentationModuleMetricReporter.onOneSegmentMessage(getStackName());
      } else if (totalPackets > 1) {
        logger.debug("Handle multiple packets for handle: {}, sequenceNumber: {}, Message (Multiframe {}/{}) : {}", identifier, sequenceNumber, indexOfPacket,
            totalPackets, message);

        // Discard messages with sequence number larger than 15 since it means WECU sends incorrect
        // data
        if (transportHeader.getSequenceNumber() > 15) {
          logger.error("Discarded message for handle: {} with sequence number {}  Message (Multiframe {}/{}): {}", identifier, sequenceNumber, indexOfPacket,
              totalPackets, message);
          return null;
        }

        // Also, it should not be possible for indexOfPacket to be larger than totalPackets
        if (indexOfPacket > totalPackets) {
          logger.error("Discarded message for handle: {} with sequence number {}  Message (Multiframe {}/{}): {}", identifier, sequenceNumber, indexOfPacket,
              totalPackets, message);
          logger.error("IndexOfMessage is greater than totalNumberOfPackets! ({} > {})", indexOfPacket, totalPackets);
          return null;
        }

        // SequenceNumber can be max 15, ok to cast down to int
        String messageKey = identifier + "-" + sequenceNumber + "-" + (mobileOriginated ? "mobileOriginated" : "");
        MultiFrameMessage multiFrameMessage = multiFrameCache.get(messageKey);

        logger.debug("Segmentation Key: {}, existing segments (without this): {} Current Index {}/{}", messageKey, multiFrameMessage, indexOfPacket,
            totalPackets);

        if (multiFrameMessage == null) {
          multiFrameMessage = new MultiFrameMessage(totalPackets);
          multiFrameCache.put(messageKey, multiFrameMessage);
          vceSegmentationModuleMetricReporter.onSegmentNewMessage(getStackName());
        }

        multiFrameMessage.add(indexOfPacket, perStream.getUnusedBufferPart());
        vceSegmentationModuleMetricReporter.onSegmentAdded(getStackName());

        if (multiFrameMessage.isComplete()) {
          fullMessage = multiFrameMessage.getComplete();
          vceSegmentationModuleMetricReporter.onSegmentCompleteMessage(getStackName());

          multiFrameCache.remove(messageKey);
          logger.debug("Complete multiframe message received: {}", fullMessage);
        }

        // If we are not finished with multiple packets just return and do nothing.
        if (fullMessage == null) {
          logger.debug("Waiting for segments   : {} with sequence number {} Message (Multiframe {} / {}): {}", identifier, sequenceNumber, indexOfPacket,
              totalPackets, message);
        }
      } else {
        logger.warn("Strange number of segments: {} silently drop message", totalPackets);
      }
    } catch (Exception e) {
      throw new IllegalStateException(e);
    }

    if (fullMessage != null) {
      // Add existing properties
      fullMessage.addProperties(message.getProperties());
      // Add new
      fullMessage.setProperty(MetaData.SEQUENCE_NUMBER, Integer.toString(sequenceNumber));
      fullMessage.setProperty(MetaData.MOBILE_ORIGINATED, Boolean.toString(mobileOriginated));
      fullMessage.setProperty(MetaData.SKIP_ACK, Boolean.toString(skipAck));

      fullMessage.setMessageId(message.getMessageId());
      fullMessage.setVehicleID(message.getVehicleID());
      fullMessage.setProperty(MetaData.MO, MetaData.MO.name());

      if (encrypted) {
        if (!vceEncryptionHandler.handleDecryption(fullMessage)) {
          vceSegmentationModuleMetricReporter.onDecryptionFailed(getStackName());
          return null;
        }
      } else {
        if (StringUtils.hasText(message.getVehicleID()) && findWecuKey(fullMessage).isPresent()) {
          logger.warn("UP: Received unencrypted message={} from vehicleId={}", fullMessage, fullMessage.getVehicleID());
          vceSegmentationModuleMetricReporter.onDroppedMessage(getStackName());
          return null;
        }
      }
    }

    return fullMessage;
  }

  private Optional<PersistedWecuKey> findWecuKey(Message message) {
    Optional<Vpi> vpi = Optional.ofNullable(message.getProperty(MetaData.VPI)).map(Vpi::ofString); //Optional field
    Optional<Handle> handle = Optional.ofNullable(message.getProperty(MetaData.HANDLE)).map(Handle::ofString);
    try (WecuKeyWriter wecuKeyWriter = wecuKeyWriterFactory.createReadCommitted()) {
      return vpi.flatMap(wecuKeyWriter::findWecuKeyByVpi).or(() -> handle.flatMap(wecuKeyWriter::findWecuKeyByHandle));
    }
  }

  private int getCarrierMaxSize(Message msg) {
    TransportType transportType = MetadataUtils.getTransportType(msg);
    int carrierMaxSize;

    switch (transportType) {
      case UDP:
        carrierMaxSize = carrierMaxSizeUdp;
        break;
      case SMS:
        carrierMaxSize = carrierMaxSizeSms;
        break;
      case SATELLITE:
        carrierMaxSize = carrierMaxSizeSat;
        break;
      default:
        carrierMaxSize = carrierMaxSizeSms;
        break;
    }

    logger.debug("Using carrier [{}] for with carrier max size [{}] for MT-message with id [{}]", transportType.name(), carrierMaxSize, msg.getMessageId());
    return carrierMaxSize;
  }

  /**
   * Initialization of configuration
   */
  private void initConfig() {
    // Create once but after deploy so name of the stack is set
    if (null == multiFrameCache) {
      multiFrameCache = new MultiFrameCache(getStackName(), vceSegmentationModuleMetricReporter, cacheTtlInMinutes, maximumCacheSize);
    }
    identifiers.put(TransportType.UDP.name(), udpIdentifier.name());
    identifiers.put(TransportType.SMS.name(), smsIdentifier.name());
    identifiers.put(TransportType.SATELLITE.name(), satIdentifier.name());
    initDone = true;
  }

  private void validateBeforeDown(Message message) {
    if (message.getPayload() == null || message.getPayload().length == 0) {
      // For some reason the message is empty, this should never happen but we have seen it in
      // production log files.
      throw new IllegalArgumentException("Payload for MT message was null or empty!");
    }

    if (!initDone) {
      throw new IllegalStateException("initDone == false");
    }
  }

  private void validateBeforeUp(Message message) {
    String transportType = message.getProperty(MetaData.TRANSPORT_TYPE);
    String vcehicleIdentifier = getVcehicleIdentifier(transportType);
    String identifier = message.getProperty(vcehicleIdentifier);

    if (null == identifier) {
      throw new IllegalArgumentException("Missing vehicle identifier "
          + vcehicleIdentifier
          + " for transport type "
          + transportType);
    }

    if (message.getPayload() == null || message.getPayload().length == 0) {
      throw new IllegalArgumentException("Payload for MO message was null or empty!");
    }

    if (!initDone) {
      throw new IllegalStateException("initDone == false");
    }
  }
}
