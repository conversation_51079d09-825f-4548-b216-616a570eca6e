package com.wirelesscar.tcevce.module.segmentation.metrics;

import org.springframework.stereotype.Component;

import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.wirelesscar.tce.module.api.ModuleTypeConstants;

import io.micrometer.core.instrument.MeterRegistry;

@Component
public class VceSegmentationModuleMetricReporter {
  static final String CACHE_NOTIFY_PUBLISH = "module." + ModuleTypeConstants.VCE_SEGMENTATION + ".cacheNotify.publish";
  static final String CACHE_NOTIFY_RECEIVE = "module." + ModuleTypeConstants.VCE_SEGMENTATION + ".cacheNotify.receive";
  static final String CACHE_REMOVE_ENTRY_COMPLETE = "module." + ModuleTypeConstants.VCE_SEGMENTATION + ".cacheRemoveEntry.complete";
  static final String CACHE_REMOVE_ENTRY_NOT_COMPLETED = "module." + ModuleTypeConstants.VCE_SEGMENTATION + ".cacheRemoveEntry.notComplete";
  static final String CACHE_REMOVE_ENTRY_SIZE = "module." + ModuleTypeConstants.VCE_SEGMENTATION + ".cacheRemoveEntry.size";
  static final String COMPLETE_MESSAGE = "module." + ModuleTypeConstants.VCE_SEGMENTATION + ".segment.completeMessage";
  static final String ONE_SEGMENT_MESSAGE = "module." + ModuleTypeConstants.VCE_SEGMENTATION + ".oneSegmentMessage";
  static final String SEGMENT_ADDED = "module." + ModuleTypeConstants.VCE_SEGMENTATION + ".segment.added";
  static final String SEGMENT_NEW_MESSAGE = "module." + ModuleTypeConstants.VCE_SEGMENTATION + ".segment.newMessage";

  static final String STACK_VAR_NAME = "stackName";

  private final MeterRegistry meterRegistry;

  public VceSegmentationModuleMetricReporter(MeterRegistry meterRegistry) {
    this.meterRegistry = meterRegistry;
  }

  public void onCacheNotifyPublish() {
    meterRegistry.counter(CACHE_NOTIFY_PUBLISH).increment();
  }

  public void onCacheNotifyReceive() {
    meterRegistry.counter(CACHE_NOTIFY_RECEIVE).increment();
  }

  public void onCacheRemoveEntryComplete(String stackName) {
    Validate.notEmpty(stackName, STACK_VAR_NAME);

    meterRegistry.counter(CACHE_REMOVE_ENTRY_COMPLETE, STACK_VAR_NAME, stackName).increment();
  }

  public void onCacheRemoveEntryNotCompleted(String stackName) {
    Validate.notEmpty(stackName, STACK_VAR_NAME);

    meterRegistry.counter(CACHE_REMOVE_ENTRY_NOT_COMPLETED, STACK_VAR_NAME, stackName).increment();
  }

  public void onCacheRemoveEntrySize(String stackName, int size) {
    Validate.notEmpty(stackName, STACK_VAR_NAME);

    meterRegistry.counter(CACHE_REMOVE_ENTRY_SIZE, STACK_VAR_NAME, stackName).increment(size);
  }

  public void onDecryptionFailed(String stackName) {
    Validate.notEmpty(stackName, STACK_VAR_NAME);

    meterRegistry.counter("module." + ModuleTypeConstants.VCE_SEGMENTATION + ".decryptionFailed", STACK_VAR_NAME, stackName).increment();
  }

  public void onDroppedMessage(String stackName) {
    Validate.notEmpty(stackName, STACK_VAR_NAME);

    meterRegistry.counter("module." + ModuleTypeConstants.VCE_SEGMENTATION + ".droppedMessage", STACK_VAR_NAME, stackName).increment();
  }

  public void onOneSegmentMessage(String stackName) {
    Validate.notEmpty(stackName, STACK_VAR_NAME);

    meterRegistry.counter(ONE_SEGMENT_MESSAGE, STACK_VAR_NAME, stackName).increment();
  }

  public void onSegmentAdded(String stackName) {
    Validate.notEmpty(stackName, STACK_VAR_NAME);

    meterRegistry.counter(SEGMENT_ADDED, STACK_VAR_NAME, stackName).increment();
  }

  public void onSegmentCompleteMessage(String stackName) {
    Validate.notEmpty(stackName, STACK_VAR_NAME);

    meterRegistry.counter(COMPLETE_MESSAGE, STACK_VAR_NAME, stackName).increment();
  }

  public void onSegmentNewMessage(String stackName) {
    Validate.notEmpty(stackName, STACK_VAR_NAME);

    meterRegistry.counter(SEGMENT_NEW_MESSAGE, STACK_VAR_NAME, stackName).increment();
  }
}
