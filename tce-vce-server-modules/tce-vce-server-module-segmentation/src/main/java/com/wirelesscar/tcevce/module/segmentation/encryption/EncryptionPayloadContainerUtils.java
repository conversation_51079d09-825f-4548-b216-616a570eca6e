package com.wirelesscar.tcevce.module.segmentation.encryption;

import com.volvo.tisp.vc.main.utils.lib.type.ImmutableByteArray;
import com.volvo.vc.crypto.common.entity.InitializationVector;
import com.volvo.vc.crypto.symmetric.encryption.gcm.AesGcmEncryptionResult;
import com.volvo.vc.crypto.symmetric.encryption.gcm.EncryptedPayloadWithoutMac;
import com.volvo.vc.crypto.symmetric.encryption.gcm.MessageAuthenticationCode;

public class EncryptionPayloadContainerUtils {
  public static EncryptionPayloadContainer fromBytes(ImmutableByteArray bytes) {
    byte[] payload = bytes.toByteArray();
    byte[] initializationVector = new byte[InitializationVector.IV_LENGTH_IN_BYTES];
    byte[] messageAuthenticationCode = new byte[MessageAuthenticationCode.MAC_LENGTH_IN_BYTES];
    byte[] encryptedPayloadWithoutMac = new byte[payload.length - InitializationVector.IV_LENGTH_IN_BYTES - MessageAuthenticationCode.MAC_LENGTH_IN_BYTES];
    int PAYLOAD_START_INDEX = InitializationVector.IV_LENGTH_IN_BYTES + MessageAuthenticationCode.MAC_LENGTH_IN_BYTES;

    System.arraycopy(payload, 0, initializationVector, 0, InitializationVector.IV_LENGTH_IN_BYTES);
    System.arraycopy(payload, InitializationVector.IV_LENGTH_IN_BYTES, messageAuthenticationCode, 0, MessageAuthenticationCode.MAC_LENGTH_IN_BYTES);
    System.arraycopy(payload, PAYLOAD_START_INDEX, encryptedPayloadWithoutMac, 0, payload.length - PAYLOAD_START_INDEX);

    return EncryptionPayloadContainer.create(
        InitializationVector.create(ImmutableByteArray.of(initializationVector)),
        MessageAuthenticationCode.create(ImmutableByteArray.of(messageAuthenticationCode)),
        EncryptedPayloadWithoutMac.create(ImmutableByteArray.of(encryptedPayloadWithoutMac)));
  }

  public static byte[] toBytes(AesGcmEncryptionResult encryptionResult) {
    byte[] initializationVector = encryptionResult.getInitializationVector().getImmutableByteArray().toByteArray();
    byte[] messageAuthenticationCode = encryptionResult.getMessageAuthenticationCode().getImmutableByteArray().toByteArray();
    byte[] encryptedPayloadWithoutMac = encryptionResult.getEncryptedPayloadWithoutMac().getImmutableByteArray().toByteArray();

    byte[] payload = new byte[initializationVector.length + messageAuthenticationCode.length + encryptedPayloadWithoutMac.length];

    System.arraycopy(initializationVector, 0, payload, 0, initializationVector.length);
    System.arraycopy(messageAuthenticationCode, 0, payload, initializationVector.length, messageAuthenticationCode.length);
    System.arraycopy(encryptedPayloadWithoutMac, 0, payload, initializationVector.length + messageAuthenticationCode.length,
        encryptedPayloadWithoutMac.length);

    return payload;
  }
}
