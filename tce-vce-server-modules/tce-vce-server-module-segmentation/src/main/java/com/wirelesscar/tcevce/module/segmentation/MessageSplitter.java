package com.wirelesscar.tcevce.module.segmentation;

import java.util.ArrayList;
import java.util.List;

import com.wirelesscar.caretrack.dh.caretrack.protocol.ASNException;
import com.wirelesscar.caretrack.dh.caretrack.protocol.PERStream;
import com.wirelesscar.caretrack.dh.caretrack.protocol.TransportHeader;
import com.wirelesscar.tce.module.api.Message;

public final class MessageSplitter {
  private MessageSplitter() {
  }

  public static List<Message> split(Message originalMessage, int carrierMaxSize)
      throws ASNException {
    List<Message> messages = new ArrayList<>();
    byte[] originalMessagePayload = originalMessage.getPayload();

    if (originalMessagePayload.length <= carrierMaxSize) {
      // Message will fit in one frame we are done!
      messages.add(originalMessage);
      return messages;
    }

    TransportHeader originalTransportHeader = new TransportHeader();
    originalTransportHeader.getMessageCount().setStandard();

    // Encoded size is in bits, add 7 bits before integer division by 8 to ensure correct number of
    // bytes
    int headerSize = (int) ((originalTransportHeader.encodedSize() + 7) / 8);
    int payloadCarrierMaxSize = carrierMaxSize - headerSize;

    PERStream originalStream = new PERStream(originalMessagePayload);
    originalTransportHeader.decode(originalStream);
    originalStream.alignOnByte();

    // This is the original payload without any headers
    byte[] originalPayload = originalStream.getUnusedBufferPart();

    int msgCount = originalPayload.length / payloadCarrierMaxSize;
    if (originalPayload.length % payloadCarrierMaxSize != 0) {
      msgCount++; // add an extra message for the not full segment
    }
    boolean extended = msgCount > 16;

    if (extended) {
      // Recalculate number of segments needed
      originalTransportHeader.getMessageCount().setExtended();
      headerSize = (int) ((originalTransportHeader.encodedSize() + 7) / 8);
      payloadCarrierMaxSize = carrierMaxSize - headerSize;
      msgCount = originalPayload.length / payloadCarrierMaxSize;
      if (originalPayload.length % payloadCarrierMaxSize != 0) {
        msgCount++; // add an extra message for the not full segment
      }
    }
    PERStream headerStream = new PERStream(10);

    for (int i = 0; i < msgCount; i++) {
      TransportHeader tp = new TransportHeader();
      tp.setSkipAck(false);
      tp.setEncrypted(originalTransportHeader.getEncrypted());
      tp.setSequenceNumber(originalTransportHeader.getSequenceNumber());
      tp.setMobileOriginated(originalTransportHeader.getMobileOriginated());

      if (extended) {
        tp.getMessageCount().setExtended();
        tp.getMessageCount().getExtended().setIndexOfCurrentPacket(i);
        tp.getMessageCount().getExtended().setTotalNumberOfPackets(msgCount);
      } else {
        tp.getMessageCount().setStandard();
        tp.getMessageCount().getStandard().setIndexOfCurrentPacket(i);
        tp.getMessageCount().getStandard().setTotalNumberOfPackets(msgCount);
      }
      headerStream.startEncoding();
      tp.encode(headerStream);
      headerStream.alignOnByte();
      byte[] headerBuffer = headerStream.getBuffer();

      // Set this segment payload size
      int payloadSize = payloadCarrierMaxSize;
      if (i == msgCount - 1 && originalPayload.length % payloadCarrierMaxSize != 0) {
        // Last segment is less then payloadCarrierMaxSize!
        payloadSize = originalPayload.length % payloadCarrierMaxSize;
      }
      byte[] currentSegment = new byte[headerSize + payloadSize];

      System.arraycopy(headerBuffer, 0, currentSegment, 0, headerBuffer.length);
      System.arraycopy(originalPayload, i * payloadCarrierMaxSize, currentSegment, headerSize, payloadSize);

      // Create and add the message
      Message message = new Message();

      message.addProperties(originalMessage.getProperties());
      message.setVehicleID(originalMessage.getVehicleID());
      message.setMessageId(originalMessage.getMessageId());
      message.setPayload(currentSegment);
      messages.add(message);
    }
    return messages;
  }
}
