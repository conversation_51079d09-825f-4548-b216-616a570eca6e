package com.wirelesscar.tcevce.module.segmentation.identify;

import java.util.Optional;
import java.util.Set;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.volvo.tisp.vc.common.dto.lib.vehicle.Msisdn;
import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.volvo.tisp.vc.main.utils.lib.type.Either;
import com.wirelesscar.conrepo.api.v1.DeviceDetailedEntry;
import com.wirelesscar.tce.module.api.exception.EngineRuntimeException;
import com.wirelesscar.tcevce.connectivityrepo.converter.DeviceDetailedEntryInputConverterFunction;
import com.wirelesscar.tcevce.module.identify.ConrepoLookupClient;
import com.wirelesscar.tcevce.wecu.device.info.cache.core.api.CacheInvalidator;
import com.wirelesscar.tcevce.wecu.device.info.database.api.DeviceInfoWriter;
import com.wirelesscar.tcevce.wecu.device.info.database.api.DeviceInfoWriterFactory;
import com.wirelesscar.tcevce.wecu.device.info.database.model.DeviceInfo;

@Component("segmentationConrepoIdentifier")
public class ConrepoIdentifier {
  private static final Logger logger = LoggerFactory.getLogger(ConrepoIdentifier.class);
  private final CacheInvalidator cacheInvalidator;
  private final ConrepoLookupClient conrepoLookupClient;
  private final DeviceInfoWriterFactory deviceInfoWriterFactory;

  public ConrepoIdentifier(ConrepoLookupClient conrepoLookupClient, DeviceInfoWriterFactory deviceInfoWriterFactory, CacheInvalidator cacheInvalidator) {
    this.conrepoLookupClient = conrepoLookupClient;
    this.deviceInfoWriterFactory = deviceInfoWriterFactory;
    this.cacheInvalidator = cacheInvalidator;
  }

  public Optional<DeviceInfo> identifyByMsisdn(Msisdn msisdn) {
    Validate.notNull(msisdn, "msisdn");

    String srcMsisdnString = msisdn.toString();
    logger.debug("Trying to identify device in connectivity repository by msisdn [{}]", srcMsisdnString);
    return conrepoLookupClient.lookupByMsisdn(srcMsisdnString).flatMap(this::updateDeviceInfo);
  }

  private Optional<DeviceInfo> updateDeviceInfo(DeviceDetailedEntry deviceDetailedEntry) {
    Either<IllegalArgumentException, DeviceInfo> deviceInfoEither = DeviceDetailedEntryInputConverterFunction.INSTANCE
        .apply(deviceDetailedEntry);

    if (deviceInfoEither.isLeft()) {
      throw new EngineRuntimeException("Error converting deviceDetailedEntry to deviceinfo locally", deviceInfoEither.getLeft());
    }

    return updateDeviceInfo(deviceInfoEither.getRight());
  }

  private Optional<DeviceInfo> updateDeviceInfo(DeviceInfo deviceInfo) {
    try (DeviceInfoWriter deviceInfoWriter = deviceInfoWriterFactory.createReadCommitted()) {
      int rows = deviceInfoWriter.updateDeviceInfoByHandle(deviceInfo);

      if (rows == 0) {
        return Optional.empty();
      }

      cacheInvalidator.invalidateCache(Set.of(deviceInfo.getHandle()));
      return Optional.of(deviceInfo);
    }
  }

}
