package com.wirelesscar.tcevce.module.segmentation.encryption;

import java.util.Base64;
import java.util.HexFormat;
import java.util.Objects;
import java.util.Optional;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.volvo.tisp.vc.common.dto.lib.vehicle.Imsi;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Msisdn;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.tisp.vc.main.utils.lib.type.Either;
import com.volvo.tisp.vc.main.utils.lib.type.ImmutableByteArray;
import com.volvo.vc.crypto.common.entity.PlainTextPayload;
import com.volvo.vc.crypto.symmetric.encryption.gcm.AesGcmEncryptionResult;
import com.volvo.vc.crypto.symmetric.encryption.gcm.SymmetricAesGcmEncryptionService;
import com.volvo.vc.crypto.symmetric.key.SymmetricKey;
import com.wirelesscar.caretrack.dh.caretrack.protocol.ASNException;
import com.wirelesscar.caretrack.dh.caretrack.protocol.PERStream;
import com.wirelesscar.caretrack.dh.caretrack.protocol.TransportHeader;
import com.wirelesscar.tce.db.common.db.api.MessageFields;
import com.wirelesscar.tce.module.api.Message;
import com.wirelesscar.tce.module.api.MetaData;
import com.wirelesscar.tce.module.api.exception.EngineRuntimeException;
import com.wirelesscar.tcevce.module.segmentation.identify.ConrepoIdentifier;
import com.wirelesscar.tcevce.wecu.device.info.cache.core.api.VehicleKeyCache;
import com.wirelesscar.tcevce.wecu.device.info.database.api.DeviceInfoReader;
import com.wirelesscar.tcevce.wecu.device.info.database.api.WecuKeyWriter;
import com.wirelesscar.tcevce.wecu.device.info.database.api.WecuKeyWriterFactory;
import com.wirelesscar.tcevce.wecu.device.info.database.model.DeviceInfo;
import com.wirelesscar.tcevce.wecu.device.info.database.model.Handle;
import com.wirelesscar.tcevce.wecu.device.info.database.model.InsertionFailure;
import com.wirelesscar.tcevce.wecu.device.info.database.model.PersistedDeviceInfo;
import com.wirelesscar.tcevce.wecu.device.info.database.model.PersistedWecuKey;
import com.wirelesscar.tcevce.wecu.device.info.database.model.SimInfo;
import com.wirelesscar.tcevce.wecu.device.info.database.model.WecuKey;
import com.wirelesscar.tcevce.wecu.device.info.database.model.WecuKeyBuilder;
import com.wirelesscar.tcevce.wecu.device.info.database.model.WecuKeyId;

@Component
public class VceEncryptionHandler {
  private static final HexFormat HEX_FORMAT = HexFormat.of().withUpperCase().withDelimiter(" ");
  private static final Hkdf HKDF = Hkdf.usingDefaults();
  private final static Logger log = LoggerFactory.getLogger(VceEncryptionHandler.class);
  private final ConrepoIdentifier conrepoIdentifier;
  private final DeviceInfoReader deviceInfoReader;
  private final VehicleKeyCache keyCache;
  private final SymmetricAesGcmEncryptionService symmetricAesGcmEncryptionService;
  private final WecuKeyWriterFactory wecuKeyWriterFactory;

  public VceEncryptionHandler(ConrepoIdentifier conrepoIdentifier, SymmetricAesGcmEncryptionService symmetricAesGcmEncryptionService,
      VehicleKeyCache keyCache,
      WecuKeyWriterFactory wecuKeyWriterFactory,
      DeviceInfoReader deviceInfoReader) {
    this.conrepoIdentifier = conrepoIdentifier;
    this.symmetricAesGcmEncryptionService = symmetricAesGcmEncryptionService;
    this.keyCache = keyCache;
    this.wecuKeyWriterFactory = wecuKeyWriterFactory;
    this.deviceInfoReader = deviceInfoReader;
  }

  private static Optional<Msisdn> getMsisdn(Message message) {
    return Optional.ofNullable(message.getProperty(MessageFields.msisdn.name()))
        .or(() -> Optional.ofNullable(message.getProperties().get(MetaData.SMPP_SOURCE_ADDRESS.name())))
        .map(Msisdn::ofString);
  }

  public SymmetricKey createAndStoreSymmetricKey(String identifier, Imsi imsi) {
    log.debug("Creating key for identifier={} and imsi={}", identifier, imsi.toString());
    byte[] hash = HKDF.defaultHash(imsi.toString().getBytes());
    SymmetricKey symmetricKey = SymmetricKey.create(ImmutableByteArray.of(hash));

    keyCache.putSymmetricKey(identifier, symmetricKey);
    return symmetricKey;
  }

  /**
   * Decrypts the payload of the message and sets it as the payload of the message again.
   * At this point, the transport header should have already been removed from the payload.
   *
   * @param message The message to decrypt
   */
  public boolean handleDecryption(Message message) {
    boolean vehicleNotFound = Optional.ofNullable(message.getProperty(MetaData.VEHICLE_NOT_FOUND)).map(Boolean::parseBoolean).orElse(false);
    if (vehicleNotFound) {
      log.warn("UP: Received encrypted message from unidentified vehicle. message={}", message);
      return false;
    }

    String hexPayload = HEX_FORMAT.formatHex(message.getPayload());
    log.debug("UP: Decrypting payload={} for message={}", hexPayload, message);
    Optional<Vpi> vpi = Optional.ofNullable(message.getProperty(MetaData.VPI)).map(Vpi::ofString); //Optional field
    Optional<Handle> handle = Optional.ofNullable(message.getProperty(MetaData.HANDLE)).map(Handle::ofString);
    Optional<Msisdn> msisdn = getMsisdn(message); //Optional field

    log.trace("UP: Received message with vehicleID={}, vpi={}, handle={} and msisdn={}", message.getVehicleID(), vpi.orElse(null), handle.orElse(null),
        msisdn.orElse(null));

    Optional<PersistedDeviceInfo> persistedDeviceInfo = vpi.flatMap(deviceInfoReader::findDeviceInfoByVpi)
        .or(() -> handle.flatMap(deviceInfoReader::findDeviceInfoByHandle))
        .or(() -> msisdn.flatMap(deviceInfoReader::findDeviceInfoByMsisdn));

    if (persistedDeviceInfo.isEmpty()) {
      log.error("UP: Unable to find device info for message={}", message);
      return false;
    }

    DeviceInfo deviceInfo = persistedDeviceInfo.get().getDeviceInfo();

    if (deviceInfo.getSimInfo().isEmpty()) {
      log.warn("UP: No sim info found for deviceInfo={}", deviceInfo);
      return false;
    }

    SimInfo simInfo = deviceInfo.getSimInfo().get();
    Msisdn deviceMsisdn = simInfo.getMsisdn();
    Optional<Imsi> optionalImsi = simInfo.getImsi()
        .or(() -> conrepoIdentifier.identifyByMsisdn(deviceMsisdn).flatMap(DeviceInfo::getSimInfo).flatMap(SimInfo::getImsi));

    if (optionalImsi.isEmpty()) {
      log.warn("UP: Imsi not present for deviceInfo={}", deviceInfo);
      return false;
    }

    Handle deviceHandle = deviceInfo.getHandle();
    Optional<Vpi> deviceVpi = deviceInfo.getVpi();

    handleWecuKey(deviceVpi, deviceHandle, deviceMsisdn);

    String vehicleId = deviceVpi.map(Vpi::toString)
        .orElse(deviceHandle.toString());

    SymmetricKey symmetricKey = createAndStoreSymmetricKey(vehicleId, optionalImsi.get());
    final ImmutableByteArray decryptedPayload;
    try {
      decryptedPayload = decryptPayload(symmetricKey, message);
    } catch (RuntimeException e) {
      log.error("UP: Unable to decrypt payload for message={}", message, e);
      return false;
    }

    message.setPayload(decryptedPayload.toByteArray());
    return true;
  }

  /**
   * Encrypts the payload of the message and sets it as the payload of the message again.
   * It will also set the transport header as encrypted.
   *
   * @param message The message to encrypt
   * @see TransportHeader#setEncrypted(boolean)
   */
  public void handleEncryption(Message message) {
    handleEncryption(message, false);
  }

  public void handleEncryption(Message message, boolean isTextMessage) {
    Optional<Vpi> vpi = Optional.ofNullable(message.getProperty(MetaData.VPI)).map(Vpi::ofString); //Optional field
    Optional<Handle> handle = Optional.ofNullable(message.getProperty(MetaData.HANDLE)).map(Handle::ofString);
    Optional<String> vehicleID = Optional.ofNullable(message.getVehicleID());

    Optional<String> identifier = vpi.map(Vpi::toString)
        .or(() -> handle.map(Handle::toString))
        .or(() -> vehicleID.map(String::toString));

    if (identifier.isEmpty()) {
      log.warn("DOWN: Message without any identifier. message={}", message);
      return;
    }

    identifier.ifPresentOrElse(
        id -> keyCache.getSymmetricKey(id)
            .or(() -> createKeyFromDatabase(message))
            .ifPresentOrElse(
                symmetricKey -> encryptPayload(symmetricKey, message, isTextMessage),
                () -> log.debug("DOWN: No secretKey created for message={}", message)
            ),
        () -> log.warn("DOWN: Message without any identifier. message={}", message)
    );
  }

  private Optional<SymmetricKey> createKeyFromDatabase(Message message) {
    try (WecuKeyWriter wecuKeyWriter = wecuKeyWriterFactory.createReadCommitted()) {
      Optional<Vpi> vpi = Optional.ofNullable(message.getProperty(MetaData.VPI)).map(Vpi::ofString);
      Optional<Handle> handle = Optional.ofNullable(message.getProperty(MetaData.HANDLE)).map(Handle::ofString);

      return vpi.flatMap(wecuKeyWriter::findWecuKeyByVpi)
          .or(() -> handle.flatMap(wecuKeyWriter::findWecuKeyByHandle))
          .map(PersistedWecuKey::getWecuKey)
          .map(WecuKey::getMsisdn)
          .flatMap(deviceInfoReader::findDeviceInfoByMsisdn)
          .map(PersistedDeviceInfo::getDeviceInfo)
          .flatMap(DeviceInfo::getSimInfo)
          .flatMap(SimInfo::getImsi)
          .map(imsi -> createAndStoreSymmetricKey(message.getVehicleID(), imsi));
    }
  }

  private ImmutableByteArray decryptPayload(SymmetricKey key, Message message) {
    EncryptionPayloadContainer encryptionPayloadContainer = EncryptionPayloadContainerUtils.fromBytes(ImmutableByteArray.of(message.getPayload()));

    AesGcmEncryptionResult aesGcmEncryptionResult = AesGcmEncryptionResult.create(encryptionPayloadContainer.getEncryptedPayloadWithoutMac(),
        encryptionPayloadContainer.getInitializationVector(), encryptionPayloadContainer.getMessageAuthenticationCode());

    Either<RuntimeException, PlainTextPayload> result = symmetricAesGcmEncryptionService.decrypt(key, aesGcmEncryptionResult);
    if (result.isLeft()) {
      throw new EngineRuntimeException("Decryption failed", result.getLeft());
    }
    PlainTextPayload decryptedPayload = result.getRight();
    return decryptedPayload.getImmutableByteArray();
  }

  private byte[] doEncrypt(SymmetricKey key, byte[] payload) {
    Either<RuntimeException, AesGcmEncryptionResult> encryptionResult = symmetricAesGcmEncryptionService.encryptWithRandomIv(key,
        PlainTextPayload.create(ImmutableByteArray.of(payload)));
    if (encryptionResult.isLeft()) {
      throw new EngineRuntimeException("Encryption failed", encryptionResult.getLeft());
    }
    AesGcmEncryptionResult result = encryptionResult.getRight();
    return EncryptionPayloadContainerUtils.toBytes(result);
  }

  private byte[] encryptAsn1Payload(SymmetricKey key, Message message) {
    String hexOriginalPayload = HEX_FORMAT.formatHex(message.getPayload());
    PERStream perStream = new PERStream(message.getPayload());
    TransportHeader transportHeader = new TransportHeader();
    try {
      transportHeader.decode(perStream);
    } catch (ASNException e) {
      throw new EngineRuntimeException("Error decoding TransportHeader for message=%s".formatted(message), e);
    }
    perStream.alignOnByte();
    byte[] rawPayload;
    try {
      rawPayload = perStream.getUnusedBufferPart();
    } catch (ASNException e) {
      throw new EngineRuntimeException("Error retrieving payload from message=%s".formatted(message), e);
    }

    String hexRawPayload = HEX_FORMAT.formatHex(rawPayload);
    log.debug("Encrypting rawPayload={}, message={}, originalPayload={}", hexRawPayload, message, hexOriginalPayload);

    byte[] encryptedPayload = doEncrypt(key, rawPayload);

    transportHeader.setEncrypted(true);
    perStream.startEncoding();
    try {
      transportHeader.encode(perStream);
    } catch (ASNException e) {
      throw new EngineRuntimeException("Error encoding TransportHeader for message=%s and transportHeader=%s".formatted(message, transportHeader), e);
    }
    perStream.alignOnByte();
    byte[] headerBuffer = perStream.getBuffer();

    byte[] newPayload = new byte[headerBuffer.length + encryptedPayload.length];
    System.arraycopy(headerBuffer, 0, newPayload, 0, headerBuffer.length);
    System.arraycopy(encryptedPayload, 0, newPayload, headerBuffer.length, encryptedPayload.length);

    return newPayload;
  }

  private void encryptPayload(SymmetricKey key, Message message, boolean isTextMessage) {
    byte[] newPayload;
    if (isTextMessage) {
      newPayload = encryptTextPayload(key, message);
    } else {
      newPayload = encryptAsn1Payload(key, message);
    }

    String hexPayload = HEX_FORMAT.formatHex(newPayload);
    log.debug("DOWN: Encrypted payload={} for message={}", hexPayload, message);
    message.setPayload(newPayload);
  }

  private byte[] encryptTextPayload(SymmetricKey key, Message message) {
    byte[] encryptedPayload = doEncrypt(key, message.getPayload());
    return Base64.getEncoder().encode(encryptedPayload);
  }

  /**
   * Checks if WecuKey exists for given VPI and Handle. If it does not exist, insert it into the database,
   * If the record exists, check if the msisdn is different from the one in the database. If it is different, update the record.
   *
   * @param vpi    VPI of the device
   * @param handle Handle of the device
   * @param msisdn msisdn of the device
   */
  private void handleWecuKey(Optional<Vpi> vpi, Handle handle, Msisdn msisdn) {
    try (WecuKeyWriter wecuKeyWriter = wecuKeyWriterFactory.createReadCommitted()) {
      Optional<PersistedWecuKey> optionalPersistedWecuKey = vpi.flatMap(wecuKeyWriter::findWecuKeyByVpi)
          .or(() -> wecuKeyWriter.findWecuKeyByHandle(handle))
          .or(() -> wecuKeyWriter.findWecuKeyByMsisdn(msisdn));

      WecuKeyBuilder wecuKeyBuilder = new WecuKeyBuilder()
          .setHandle(handle)
          .setMsisdn(msisdn);
      vpi.ifPresent(wecuKeyBuilder::setVpi);

      Optional<WecuKey> optionalWecuKey = optionalPersistedWecuKey.map(PersistedWecuKey::getWecuKey);

      optionalWecuKey.ifPresentOrElse(wecuKey -> {
        boolean isVpiEquals = Objects.equals(vpi.orElse(null), wecuKey.getVpi().orElse(null));
        if (!isVpiEquals || !wecuKey.getHandle().equals(handle) || !wecuKey.getMsisdn().equals(msisdn)) {
          WecuKeyId wecuKeyId = optionalPersistedWecuKey.get().getWecuKeyId();
          int updateWecuKey = wecuKeyWriter.updateWecuKey(wecuKeyBuilder.build(), wecuKeyId);
          log.info("Update {} rows for wecuKeyId={}", updateWecuKey, wecuKeyId);
        }
      }, () -> {
        Either<InsertionFailure, WecuKeyId> wecuKeyIdEither = wecuKeyWriter.insertWecuKey(wecuKeyBuilder.build());
        if (wecuKeyIdEither.isLeft()) {
          throw new EngineRuntimeException("Unable to insert wecu key", wecuKeyIdEither.getLeft().getRuntimeException());
        }
      });
    }
  }

}
