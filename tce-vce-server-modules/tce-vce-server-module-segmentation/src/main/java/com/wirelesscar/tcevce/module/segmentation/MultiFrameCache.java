package com.wirelesscar.tcevce.module.segmentation;

import java.util.concurrent.TimeUnit;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.RemovalListener;
import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.wirelesscar.tcevce.module.segmentation.metrics.VceSegmentationModuleMetricReporter;

public final class MultiFrameCache {
  private static final Logger logger = LoggerFactory.getLogger(MultiFrameCache.class);

  private final Cache<String, MultiFrameMessage> cache;
  private int count = 0;
  private final String stackName;
  private final VceSegmentationModuleMetricReporter vceSegmentationModuleMetricReporter;

  public MultiFrameCache(String stackName, VceSegmentationModuleMetricReporter vceSegmentationModuleMetricReporter, int ttlInMinutes, int maximumSize) {
    Validate.notNull(stackName, "stackName");
    Validate.notNull(vceSegmentationModuleMetricReporter, "vceSegmentationModuleMetricReporter");

    // Very first time check null synchronized
    cache = CacheBuilder.newBuilder()
        .maximumSize(maximumSize)
        .removalListener(getRemovalListener())
        .expireAfterAccess(ttlInMinutes, TimeUnit.MINUTES)
        .build();

    this.stackName = stackName;
    this.vceSegmentationModuleMetricReporter = vceSegmentationModuleMetricReporter;
  }

  public MultiFrameMessage get(String key) {
    return cache.getIfPresent(key);
  }

  public boolean hasEntry(String key) {
    return null != cache.getIfPresent(key);
  }

  public void put(String key, MultiFrameMessage value) {
    cache.put(key, value);
  }

  public void remove(String key) {
    cache.invalidate(key);
  }

  /**
   * Create new removal listener that handles cache timeout
   */
  private RemovalListener<String, MultiFrameMessage> getRemovalListener() {
    return notification -> {
      MultiFrameMessage message = notification.getValue();
      logger.debug("Discarded not complete segmented message from stack: {}, Key: {} Containes: {} of Total: {} Cause is: {}", stackName,
          notification.getKey(),
          message.countParts(), message.totalCount(), notification.getCause());

      // Metrics
      if (message.countParts() != message.totalCount()) {
        vceSegmentationModuleMetricReporter.onCacheRemoveEntryNotCompleted(stackName);
      } else {
        vceSegmentationModuleMetricReporter.onCacheRemoveEntryComplete(stackName);
      }
      if (count++ % 100 == 0) {
        // dont call size every time
        vceSegmentationModuleMetricReporter.onCacheRemoveEntrySize(stackName, (int) cache.size());
      }
    };
  }
}
