package com.wirelesscar.tcevce.module.segmentation;

import com.wirelesscar.tce.module.api.Message;

public class MultiFrameMessage {
  private final MultiFramePart[] messageParts;

  public MultiFrameMessage(int totalFrames) {
    messageParts = new MultiFramePart[totalFrames];
  }

  public void add(int indexOfPacket, byte[] payload) {
    MultiFramePart newMessagePart = new MultiFramePart(payload);
    messageParts[indexOfPacket] = newMessagePart;
  }

  public long countParts() {
    Long totalCount = 0L;
    for (MultiFramePart messagePart : messageParts) {
      if (messagePart != null) {
        totalCount++;
      }
    }
    return totalCount;
  }

  public MultiFramePart findMessagePart(int indexOfPacket) {
    return messageParts[indexOfPacket];
  }

  public boolean isComplete() {
    for (MultiFramePart messagePart : messageParts) {
      if (messagePart == null) {
        return false;
      }
    }
    return true;
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append(MultiFrameMessage.class.getSimpleName())
        .append(" Segments total count: ")
        .append(totalCount());

    sb.append(" Segments found index:size ");
    for (int i = 0; i < totalCount(); i++) {
      if (messageParts[i] != null) {
        sb.append(i).append(":").append(messageParts[i].getMessagePart().length).append(", ");
      }
    }

    return sb.toString();
  }

  public int totalCount() {
    return messageParts.length;
  }

  Message getComplete() {
    if (!isComplete()) {
      return null;
    }

    int fullArrayLength = 0;

    for (MultiFramePart multiFramePart : messageParts) {
      fullArrayLength += multiFramePart.getMessagePart().length;
    }

    byte[] fullLengthMessage = new byte[fullArrayLength];
    int currentStartIndex = 0;

    for (MultiFramePart multiFramePart : messageParts) {
      System.arraycopy(multiFramePart.getMessagePart(), 0, fullLengthMessage, currentStartIndex, multiFramePart.getMessagePart().length);
      currentStartIndex += multiFramePart.getMessagePart().length;
    }

    Message newFullMessage = new Message();
    newFullMessage.setPayload(fullLengthMessage);

    return newFullMessage;
  }
}
