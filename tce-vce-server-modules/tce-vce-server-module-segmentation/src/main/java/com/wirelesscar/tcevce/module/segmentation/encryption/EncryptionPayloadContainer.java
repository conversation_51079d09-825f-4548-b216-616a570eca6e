package com.wirelesscar.tcevce.module.segmentation.encryption;

import com.volvo.vc.crypto.common.entity.InitializationVector;
import com.volvo.vc.crypto.symmetric.encryption.gcm.EncryptedPayloadWithoutMac;
import com.volvo.vc.crypto.symmetric.encryption.gcm.MessageAuthenticationCode;

public class EncryptionPayloadContainer {
  private final EncryptedPayloadWithoutMac encryptedPayloadWithoutMac;
  private final InitializationVector initializationVector;
  private final MessageAuthenticationCode messageAuthenticationCode;

  private EncryptionPayloadContainer(InitializationVector initializationVector, MessageAuthenticationCode messageAuthenticationCode,
      EncryptedPayloadWithoutMac encryptedPayloadWithoutMac) {
    this.initializationVector = initializationVector;
    this.messageAuthenticationCode = messageAuthenticationCode;
    this.encryptedPayloadWithoutMac = encryptedPayloadWithoutMac;
  }

  public static EncryptionPayloadContainer create(InitializationVector initializationVector, MessageAuthenticationCode messageAuthenticationCode,
      EncryptedPayloadWithoutMac encryptedPayloadWithoutMac) {
    return new EncryptionPayloadContainer(initializationVector, messageAuthenticationCode, encryptedPayloadWithoutMac);
  }

  public EncryptedPayloadWithoutMac getEncryptedPayloadWithoutMac() {
    return encryptedPayloadWithoutMac;
  }

  public InitializationVector getInitializationVector() {
    return initializationVector;
  }

  public MessageAuthenticationCode getMessageAuthenticationCode() {
    return messageAuthenticationCode;
  }
}
