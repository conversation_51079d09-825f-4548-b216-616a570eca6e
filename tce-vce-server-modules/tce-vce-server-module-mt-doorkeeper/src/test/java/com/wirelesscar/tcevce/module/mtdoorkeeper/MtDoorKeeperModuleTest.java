package com.wirelesscar.tcevce.module.mtdoorkeeper;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import com.volvo.tisp.framework.context.TispContext;
import com.wirelesscar.config.exception.ConfigurationException;
import com.wirelesscar.tce.core.conf.TceConfigException;
import com.wirelesscar.tce.module.api.Message;
import com.wirelesscar.tce.module.api.ModuleBase;
import com.wirelesscar.tce.module.metrics.ModuleMetricReporter;
import com.wirelesscar.tcevce.module.mtdoorkeeper.metrics.DoorKeeperMetricReporter;

class MtDoorKeeperModuleTest {
  private static final MtMessageRepository mtMessageRepository = Mockito.mock(MtMessageRepository.class);

  private final DoorKeeperMetricReporter doorKeeperMetricReporter = Mockito.mock(DoorKeeperMetricReporter.class);
  private final ModuleBase dummyModule = Mockito.mock(ModuleBase.class);
  private final MtDoorKeeperModule mtDoorKeeperModule = new MtDoorKeeperModule(doorKeeperMetricReporter, mtMessageRepository,
      Mockito.mock(ModuleMetricReporter.class));

  private static Message createMessage() {
    Message message = new Message();
    message.setVehicleID("vehicleID" + Math.random());
    return message;
  }

  @BeforeEach
  void beforeEach() throws TceConfigException, ConfigurationException {
    mtDoorKeeperModule.setDown(dummyModule);
    mtDoorKeeperModule.setUp(dummyModule);
    mtDoorKeeperModule.setOldConfigProperties();
    mtDoorKeeperModule.start();
  }

  @Test
  void downMtmessagesAboveThresholdTest() {
    Message message = createMessage();

    Mockito.when(mtMessageRepository.queryCountOfActiveMtMessagesByVehicleId(message.getVehicleID())).thenReturn(101L);

    TispContext.runInContext(() -> mtDoorKeeperModule.down(message));
    Mockito.verify(mtMessageRepository).queryCountOfActiveMtMessagesByVehicleId(message.getVehicleID());
    Mockito.verify(dummyModule, Mockito.never()).down(message);
  }

  @Test
  void downMtmessagesBelowThresholdTest() {
    Message message = createMessage();
    Mockito.when(mtMessageRepository.queryCountOfActiveMtMessagesByVehicleId(message.getVehicleID())).thenReturn(99L);

    mtDoorKeeperModule.down(message);

    Assertions.assertEquals(message.getVehicleID(), message.getVehicleID());
    Mockito.verify(mtMessageRepository).queryCountOfActiveMtMessagesByVehicleId(message.getVehicleID());
    Mockito.verify(dummyModule).down(message);
  }

  @Test
  void upTest() {
    Message message = createMessage();

    mtDoorKeeperModule.up(message);
    Mockito.verify(dummyModule).up(message);
  }
}
