package com.wirelesscar.tcevce.module.mtdoorkeeper.metrics;

import org.junit.jupiter.api.Test;

import io.micrometer.core.instrument.Tags;

class DoorKeeperMetricReporterTest {
  @Test
  void onModuleDownTest() {
    MetricsReporterTestUtils.initReporterAndTest(DoorKeeperMetricReporter::new,
        (meterRegistry, doorKeeperMetricReporter) -> {
          final String stackName = "stack";
          doorKeeperMetricReporter.onModuleDown(stackName);
          MetricsReporterTestUtils.checkCounter(meterRegistry, 1, DoorKeeperMetricReporter.METRICS_DOWN,
              Tags.of(DoorKeeperMetricReporter.STACK_VAR_NAME, stackName));
        });
  }
}
