package com.wirelesscar.tcevce.module.mtdoorkeeper;

import javax.sql.DataSource;

import org.jdbi.v3.core.Jdbi;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.wirelesscar.tce.db.common.api.ActivityStatus;
import com.wirelesscar.tce.db.common.db.api.MessageFields;
import com.wirelesscar.tce.db.common.db.api.TableName;
import com.wirelesscar.tce.module.api.Message;
import com.zaxxer.hikari.HikariDataSource;

class MtMessageRepositoryTest {
  private static HikariDataSource createDataSource() {
    HikariDataSource dataSource = new HikariDataSource();
    dataSource.setJdbcUrl("jdbc:h2:mem:db1;MODE=Oracle");
    dataSource.setUsername("sa");
    dataSource.setPassword("");
    return dataSource;
  }

  private static Message createMessage(ActivityStatus activityStatus, int msgId) {
    Message message = Message.createMessage();
    message.setMessageId(Integer.toString(msgId));
    message.setProperty(MessageFields.status.name(), activityStatus.name());
    message.setVehicleID("123456789");
    return message;
  }

  private static Jdbi createTable(DataSource dataSource) {
    Jdbi jdbi = Jdbi.create(dataSource);

    jdbi.useHandle(datahandle -> {
      datahandle.execute(
          "CREATE TABLE SCHD_SCHEDULED_MESSAGE (id varchar(200) NOT NULL PRIMARY KEY, hashID number(18), nextCheckTime number(38), createTime number(38), removeTime number(38), priority varchar(20), status varchar(1), payload2000 raw(2000), payload blob, vehicleID varchar(200), properties varchar(4000), partitionDate int not null)");
    });

    return jdbi;
  }

  private static String getInsertSql(Message message) {
    return new StringBuilder()
        .append("INSERT INTO ")
        .append(TableName.SCHD_SCHEDULED_MESSAGE)
        .append(" (")
        .append(MessageFields.id.name())
        .append(", ")
        .append(MessageFields.hashID.name())
        .append(", ")
        .append(MessageFields.nextCheckTime.name())
        .append(", ")
        .append(MessageFields.createTime.name())
        .append(", ")
        .append(MessageFields.removeTime.name())
        .append(", ")
        .append(MessageFields.priority.name())
        .append(", ")
        .append(MessageFields.status.name())
        .append(", ")
        .append(MessageFields.vehicleID.name())
        .append(", ")
        .append(MessageFields.partitionDate.name())
        .append(") VALUES (")
        .append(message.getMessageId())
        .append(", 2345")
        .append(", 334567890")
        .append(", 334567890")
        .append(", 334567890")
        .append(", 4")
        .append(", " + "'" + message.getProperty(MessageFields.status.name()) + "'")
        .append(", " + message.getVehicleID())
        .append(", 20180202")
        .append(")")
        .toString();
  }

  @Test
  void vehicleWithActiveAndDeactiveMtMessages() {
    try (HikariDataSource hikariDataSource = createDataSource()) {
      Jdbi jdbi = createTable(hikariDataSource);

      Message message = createMessage(ActivityStatus.a, 1);
      jdbi.useHandle(
          datahandle -> {
            datahandle.execute(getInsertSql(message));
            datahandle.execute(getInsertSql(createMessage(ActivityStatus.d, 2)));
          });

      long result = new MtMessageRepository(jdbi).queryCountOfActiveMtMessagesByVehicleId(message.getVehicleID());
      Assertions.assertEquals(1, result);
    }
  }

  @Test
  void vehicleWithActiveMtMessageTest() {
    try (HikariDataSource hikariDataSource = createDataSource()) {
      Jdbi jdbi = createTable(hikariDataSource);

      Message message = createMessage(ActivityStatus.a, 1);
      jdbi.useHandle(
          datahandle -> {
            datahandle.execute(getInsertSql(message));
          });

      long result = new MtMessageRepository(jdbi).queryCountOfActiveMtMessagesByVehicleId(message.getVehicleID());
      Assertions.assertEquals(1, result);
    }
  }

  @Test
  void vehicleWithNoMtMessagesTest() {
    try (HikariDataSource hikariDataSource = createDataSource()) {
      Jdbi jdbi = createTable(hikariDataSource);

      long result = new MtMessageRepository(jdbi).queryCountOfActiveMtMessagesByVehicleId("1");
      Assertions.assertEquals(0, result);
    }
  }
}
