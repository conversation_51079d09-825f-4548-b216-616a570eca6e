package com.wirelesscar.tcevce.module.mtdoorkeeper.metrics;

import java.util.function.BiConsumer;
import java.util.function.Function;

import org.junit.jupiter.api.Assertions;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tags;
import io.micrometer.core.instrument.simple.SimpleMeterRegistry;

final class MetricsReporterTestUtils {
  private MetricsReporterTestUtils() {
    throw new IllegalStateException();
  }

  static void checkCounter(MeterRegistry meterRegistry, double expectedCount, String name, Tags tags) {
    final Counter counter = meterRegistry.find(name).tags(tags).counter();

    Assertions.assertEquals(expectedCount, counter.count());
  }

  static <T> void initReporterAndTest(Function<MeterRegistry, T> initMetricsReporter, BiConsumer<MeterRegistry, T> test) {
    MeterRegistry meterRegistry = new SimpleMeterRegistry();
    T metricsReporter = initMetricsReporter.apply(meterRegistry);

    test.accept(meterRegistry, metricsReporter);
  }
}
