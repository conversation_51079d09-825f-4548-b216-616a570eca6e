package com.wirelesscar.tcevce.module.mtdoorkeeper;

import org.jdbi.v3.core.Handle;
import org.jdbi.v3.core.Jdbi;
import org.jdbi.v3.core.statement.Query;
import org.springframework.stereotype.Component;

import com.volvo.tisp.vc.main.utils.lib.Validate;

@Component
public class MtMessageRepository {
  private final Jdbi jdbi;

  public MtMessageRepository(Jdbi jdbi) {
    this.jdbi = jdbi;
  }

  public long queryCountOfActiveMtMessagesByVehicleId(String vehicleId) {
    Validate.notEmpty(vehicleId, "vehicleId");

    try (Handle jdbiHandle = jdbi.open();
        Query query = jdbiHandle.createQuery("SELECT count(*) FROM SCHD_SCHEDULED_MESSAGE WHERE status ='a' AND vehicleId = :vehicleId")) {
      return query.bind("vehicleId", vehicleId).mapTo(Long.class).one().longValue();
    }
  }
}
