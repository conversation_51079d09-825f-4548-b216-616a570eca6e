package com.wirelesscar.tcevce.module.mtdoorkeeper.metrics;

import org.springframework.stereotype.Component;

import com.volvo.tisp.vc.main.utils.lib.Validate;

import io.micrometer.core.instrument.MeterRegistry;

@Component
public class DoorKeeperMetricReporter {
  static final String METRICS_DOWN = "module.mt_doorkeeper.down.dropped";
  static final String STACK_VAR_NAME = "stackName";

  private final MeterRegistry meterRegistry;

  public DoorKeeperMetricReporter(MeterRegistry meterRegistry) {
    this.meterRegistry = meterRegistry;
  }

  public void onModuleDown(String stackName) {
    Validate.notNull(stackName, STACK_VAR_NAME);

    meterRegistry.counter(METRICS_DOWN, STACK_VAR_NAME, stackName).increment();
  }
}
