package com.wirelesscar.tcevce.module.mtdoorkeeper;

import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.wirelesscar.componentbase.logging.Logging;
import com.wirelesscar.config.ConfigFactory;
import com.wirelesscar.tce.core.conf.TceConfig;
import com.wirelesscar.tce.module.api.Message;
import com.wirelesscar.tce.module.api.MetaData;
import com.wirelesscar.tce.module.api.ModuleBase;
import com.wirelesscar.tce.module.api.ModuleTypeConstants;
import com.wirelesscar.tce.module.metrics.ModuleMetricReporter;
import com.wirelesscar.tce.utils.LoggingHelper;
import com.wirelesscar.tcevce.module.mtdoorkeeper.metrics.DoorKeeperMetricReporter;

@Component(ModuleTypeConstants.MT_DOORKEEPER)
@Scope("prototype")
public class MtDoorKeeperModule extends ModuleBase {
  private static final String CONFIG_PREFIX = "." + ModuleTypeConstants.MT_DOORKEEPER + ".module.";
  private static final Logger log = LoggerFactory.getLogger(MtDoorKeeperModule.class);
  private static final String MAXIMUM_MESSAGES_COUNTS_PER_VEHICLE = "max.mt.messages.per.vehicle";

  private final DoorKeeperMetricReporter doorKeeperMetricReporter;

  @TceConfig(configKey = MAXIMUM_MESSAGES_COUNTS_PER_VEHICLE, defaultValue = "100")
  private int maxMtMessagesPerVehicle;
  private final MtMessageRepository mtMessageRepository;

  public MtDoorKeeperModule(DoorKeeperMetricReporter doorKeeperMetricReporter, MtMessageRepository mtMessageRepository,
      ModuleMetricReporter moduleMetricReporter) {
    super(moduleMetricReporter);
    this.doorKeeperMetricReporter = doorKeeperMetricReporter;
    this.mtMessageRepository = mtMessageRepository;
  }

  @Override
  public void down(Message message) {
    Validate.notNull(message, "message");

    long count = mtMessageRepository.queryCountOfActiveMtMessagesByVehicleId(message.getVehicleID());

    if (count >= maxMtMessagesPerVehicle) {
      String interfaceName;
      String dstService = message.getProperty(MetaData.SRP_DST_VERSION);
      if (dstService != null) {
        interfaceName = "MT/" + message.getProperty(MetaData.SRP_DST_VERSION) + "/discardMessageDueToTooManyMessagesPerVehicle";
      } else {
        interfaceName = "MT/discardMessageDueToTooManyMessagesPerVehicle";
      }
      LoggingHelper.integrationLogging(message, null, Logging.Status.FAILED, Logging.Direction.CLIENT_IN, interfaceName,
          LoggingHelper.createExtraMetaDataBuilder()
              .putIfNotNull(LoggingHelper.ExtraMetaData.KEYS.mobileDirection, "MT")
              .putIfNotNull(LoggingHelper.ExtraMetaData.KEYS.serviceId, message.getProperty(MetaData.SRP_DST_SERVICE))
              .putIfNotNull(LoggingHelper.ExtraMetaData.KEYS.serviceVersion, message.getProperty(MetaData.SRP_DST_VERSION))
              .putIfNotNull(LoggingHelper.ExtraMetaData.KEYS.vehicleTimestamp, message.getProperty(MetaData.SRP_TIMESTAMP))
              .getAsOptional());
      doorKeeperMetricReporter.onModuleDown(getStackName());
      log.warn("discarded messages because the number of mt messages received is above the threshold {} :{}", maxMtMessagesPerVehicle, message);
    } else {
      sendDown(message);
    }
  }

  @Override
  public void up(Message message) {
    sendUp(message);
  }

  @Override
  protected void setOldModuleConfig(Map<String, String> oldConfigProperties) {
    oldConfigProperties.put(MAXIMUM_MESSAGES_COUNTS_PER_VEHICLE,
        ConfigFactory.getConfig().getString(getStackName() + CONFIG_PREFIX + MAXIMUM_MESSAGES_COUNTS_PER_VEHICLE).orElse("100"));
  }
}
