package com.wirelesscar.tcevce.wecu.device.info.cache.notify.metrics;

import com.volvo.tisp.vc.main.utils.lib.Validate;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;

public class NotifyMetricReporter {
  public static String CACHE = "cache";
  public static String CACHE_DEVICE_INFO = "device-info";
  public static String CACHE_VEHICLE_KEY = "vehicle-key";
  public static String METRIC_NAME_NOTIFY = "notify";
  public static String OPERATION = "operation";
  public static String OPERATION_PUBLISH = "publish";
  public static String OPERATION_RECEIVE = "receive";
  private final Counter deviceInfoPublishNotificationCounter;
  private final Counter deviceInfoReceiveNotificationCounter;
  private final Counter vehicleKeyPublishNotificationCounter;
  private final Counter vehicleKeyReceiveNotificationCounter;

  public NotifyMetricReporter(MeterRegistry meterRegistry) {
    Validate.notNull(meterRegistry, "meterRegistry");

    deviceInfoReceiveNotificationCounter = meterRegistry.counter(METRIC_NAME_NOTIFY, OPERATION, OPERATION_RECEIVE, CACHE, CACHE_DEVICE_INFO);
    deviceInfoPublishNotificationCounter = meterRegistry.counter(METRIC_NAME_NOTIFY, OPERATION, OPERATION_PUBLISH, CACHE, CACHE_DEVICE_INFO);
    vehicleKeyReceiveNotificationCounter = meterRegistry.counter(METRIC_NAME_NOTIFY, OPERATION, OPERATION_RECEIVE, CACHE, CACHE_VEHICLE_KEY);
    vehicleKeyPublishNotificationCounter = meterRegistry.counter(METRIC_NAME_NOTIFY, OPERATION, OPERATION_PUBLISH, CACHE, CACHE_VEHICLE_KEY);
  }

  public void onPublishDeviceInfoNotification() {
    deviceInfoPublishNotificationCounter.increment();
  }

  public void onPublishVehicleKeyNotification() {
    vehicleKeyPublishNotificationCounter.increment();
  }

  public void onReceiveDeviceInfoNotification() {
    deviceInfoReceiveNotificationCounter.increment();
  }

  public void onReceiveVehicleKeyNotification() {
    vehicleKeyReceiveNotificationCounter.increment();
  }
}
