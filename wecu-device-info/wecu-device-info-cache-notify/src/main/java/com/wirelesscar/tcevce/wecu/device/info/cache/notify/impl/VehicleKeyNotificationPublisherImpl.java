package com.wirelesscar.tcevce.wecu.device.info.cache.notify.impl;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.hazelcast.core.HazelcastInstance;
import com.hazelcast.topic.ITopic;
import com.hazelcast.topic.MessageListener;
import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.wirelesscar.tcevce.wecu.device.info.cache.notify.api.VehicleKeyNotificationPublisher;
import com.wirelesscar.tcevce.wecu.device.info.cache.notify.metrics.NotifyMetricReporter;

public final class VehicleKeyNotificationPublisherImpl implements VehicleKeyNotificationPublisher {
  static final String TCE_NOTIFY_TOPIC_NAME = "tce-vehicle-key-notify";
  private static final Logger logger = LoggerFactory.getLogger(VehicleKeyNotificationPublisherImpl.class);

  private final ITopic<VehicleKeyNotification> iTopic;
  private final NotifyMetricReporter notifyMetricReporter;

  private VehicleKeyNotificationPublisherImpl(ITopic<VehicleKeyNotification> iTopic, NotifyMetricReporter notifyMetricReporter) {
    this.iTopic = iTopic;
    this.notifyMetricReporter = notifyMetricReporter;
  }

  public static VehicleKeyNotificationPublisher create(MessageListener<VehicleKeyNotification> vehicleKeyNotificationMessageListener,
      HazelcastInstance hazelcastInstance,
      NotifyMetricReporter notifyMetricReporter) {
    Validate.notNull(vehicleKeyNotificationMessageListener, "vehicleKeyNotificationMessageListener");
    Validate.notNull(hazelcastInstance, "hazelcastInstance");
    Validate.notNull(notifyMetricReporter, "notifyMetricReporter");

    ITopic<VehicleKeyNotification> iTopic = hazelcastInstance.getReliableTopic(TCE_NOTIFY_TOPIC_NAME);
    iTopic.addMessageListener(vehicleKeyNotificationMessageListener);
    return new VehicleKeyNotificationPublisherImpl(iTopic, notifyMetricReporter);
  }

  @Override
  public void publishNotification(VehicleKeyNotification vehicleKeyNotification) {
    Validate.notNull(vehicleKeyNotification, "vehicleKeyNotification");

    iTopic.publish(vehicleKeyNotification);

    notifyMetricReporter.onPublishVehicleKeyNotification();
    logger.debug("published message, iTopic: {}, vehicleKeyNotification: {}", iTopic.getName(), vehicleKeyNotification);
  }
}
