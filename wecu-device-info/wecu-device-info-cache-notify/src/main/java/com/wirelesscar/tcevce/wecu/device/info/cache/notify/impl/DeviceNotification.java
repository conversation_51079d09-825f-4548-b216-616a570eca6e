package com.wirelesscar.tcevce.wecu.device.info.cache.notify.impl;

import java.io.IOException;
import java.util.Arrays;
import java.util.Set;
import java.util.stream.Collectors;

import com.hazelcast.nio.ObjectDataInput;
import com.hazelcast.nio.ObjectDataOutput;
import com.hazelcast.nio.serialization.DataSerializable;
import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.wirelesscar.tcevce.wecu.device.info.database.model.Handle;

public final class DeviceNotification implements DataSerializable {
  private Set<Handle> handles;

  DeviceNotification() {
    // Needed by HazelCast serialization do not remove!
  }

  private DeviceNotification(Set<Handle> handles) {
    this.handles = handles;
  }

  public static DeviceNotification ofHandles(Set<Handle> handles) {
    Validate.notEmpty(handles, "handles");

    return new DeviceNotification(handles);
  }

  public Set<Handle> getHandles() {
    return handles;
  }

  @Override
  public void readData(ObjectDataInput objectDataInput) throws IOException {
    Validate.notNull(objectDataInput, "objectDataInput");

    handles = Arrays.stream(objectDataInput.readStringArray()).map(Handle::ofString).collect(Collectors.toUnmodifiableSet());
  }

  @Override
  public String toString() {
    return new StringBuilder(50).append("handles=").append(handles).toString();
  }

  @Override
  public void writeData(ObjectDataOutput objectDataOutput) throws IOException {
    Validate.notNull(objectDataOutput, "objectDataOutput");

    objectDataOutput.writeStringArray(toStringArray());
  }

  private String[] toStringArray() {
    String[] stringArray = new String[handles.size()];
    handles.stream().map(Handle::toString).collect(Collectors.toSet()).toArray(stringArray);
    return stringArray;
  }
}
