package com.wirelesscar.tcevce.wecu.device.info.cache.notify.impl;

import java.util.Set;

import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.wirelesscar.tcevce.wecu.device.info.cache.core.api.VehicleKeyCacheInvalidator;
import com.wirelesscar.tcevce.wecu.device.info.cache.notify.api.VehicleKeyNotificationPublisher;

public final class DistributedVehicleKeyCacheInvalidator implements VehicleKeyCacheInvalidator {
  private final VehicleKeyNotificationPublisher vehicleKeyNotificationPublisher;

  private DistributedVehicleKeyCacheInvalidator(VehicleKeyNotificationPublisher vehicleKeyNotificationPublisher) {
    this.vehicleKeyNotificationPublisher = vehicleKeyNotificationPublisher;
  }

  public static VehicleKeyCacheInvalidator create(VehicleKeyNotificationPublisher vehicleKeyNotificationPublisher) {
    Validate.notNull(vehicleKeyNotificationPublisher, "vehicleKeyNotificationPublisher");

    return new DistributedVehicleKeyCacheInvalidator(vehicleKeyNotificationPublisher);
  }

  @Override
  public void invalidateCache(Set<String> vehicleIds) {
    Validate.notEmpty(vehicleIds, "vehicleIds");

    vehicleKeyNotificationPublisher.publishNotification(VehicleKeyNotification.ofVehicleIds(vehicleIds));
  }
}
