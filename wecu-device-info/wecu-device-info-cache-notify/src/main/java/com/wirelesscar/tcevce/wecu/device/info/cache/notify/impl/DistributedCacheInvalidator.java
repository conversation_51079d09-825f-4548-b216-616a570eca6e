package com.wirelesscar.tcevce.wecu.device.info.cache.notify.impl;

import java.util.Set;

import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.wirelesscar.tcevce.wecu.device.info.cache.core.api.CacheInvalidator;
import com.wirelesscar.tcevce.wecu.device.info.cache.notify.api.DeviceNotificationPublisher;
import com.wirelesscar.tcevce.wecu.device.info.database.model.Handle;

public final class DistributedCacheInvalidator implements CacheInvalidator {
  private final DeviceNotificationPublisher deviceNotificationPublisher;

  private DistributedCacheInvalidator(DeviceNotificationPublisher deviceNotificationPublisher) {
    this.deviceNotificationPublisher = deviceNotificationPublisher;
  }

  public static CacheInvalidator create(DeviceNotificationPublisher deviceNotificationPublisher) {
    Validate.notNull(deviceNotificationPublisher, "deviceNotificationPublisher");

    return new DistributedCacheInvalidator(deviceNotificationPublisher);
  }

  @Override
  public void invalidateCache(Set<Handle> handles) {
    Validate.notEmpty(handles, "handles");

    deviceNotificationPublisher.publishNotification(DeviceNotification.ofHandles(handles));
  }
}
