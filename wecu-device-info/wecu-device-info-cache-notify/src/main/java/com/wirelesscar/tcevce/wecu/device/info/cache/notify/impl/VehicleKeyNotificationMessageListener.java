package com.wirelesscar.tcevce.wecu.device.info.cache.notify.impl;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.hazelcast.topic.Message;
import com.hazelcast.topic.MessageListener;
import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.wirelesscar.tcevce.wecu.device.info.cache.core.impl.VehicleKeyServiceCacheManager;
import com.wirelesscar.tcevce.wecu.device.info.cache.notify.metrics.NotifyMetricReporter;

public final class VehicleKeyNotificationMessageListener implements MessageListener<VehicleKeyNotification> {
  private static final Logger logger = LoggerFactory.getLogger(VehicleKeyNotificationMessageListener.class);
  private final NotifyMetricReporter notifyMetricReporter;
  private final VehicleKeyServiceCacheManager vehicleKeyServiceCacheManager;

  private VehicleKeyNotificationMessageListener(VehicleKeyServiceCacheManager vehicleKeyServiceCacheManager,
      NotifyMetricReporter metricReporter) {
    this.vehicleKeyServiceCacheManager = vehicleKeyServiceCacheManager;
    this.notifyMetricReporter = metricReporter;
  }

  public static MessageListener<VehicleKeyNotification> create(VehicleKeyServiceCacheManager vehicleKeyServiceCacheManager,
      NotifyMetricReporter notifyMetricReporter) {
    Validate.notNull(vehicleKeyServiceCacheManager, "vehicleKeyServiceCacheManager");
    Validate.notNull(notifyMetricReporter, "notifyMetricReporter");

    return new VehicleKeyNotificationMessageListener(vehicleKeyServiceCacheManager, notifyMetricReporter);
  }

  @Override
  public void onMessage(Message<VehicleKeyNotification> message) {
    Validate.notNull(message, "message");

    VehicleKeyNotification vehicleKeyNotification = message.getMessageObject();

    notifyMetricReporter.onReceiveVehicleKeyNotification();
    logger.debug("received message, vehicleKeyNotification: {}", vehicleKeyNotification);

    vehicleKeyServiceCacheManager.invalidateCache(vehicleKeyNotification.getVehicleIds());
  }
}
