package com.wirelesscar.tcevce.wecu.device.info.cache.notify.impl;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.hazelcast.core.HazelcastInstance;
import com.hazelcast.topic.ITopic;
import com.hazelcast.topic.MessageListener;
import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.wirelesscar.tcevce.wecu.device.info.cache.notify.api.DeviceNotificationPublisher;
import com.wirelesscar.tcevce.wecu.device.info.cache.notify.metrics.NotifyMetricReporter;

public final class DeviceNotificationPublisherImpl implements DeviceNotificationPublisher {
  static final String TCE_NOTIFY_TOPIC_NAME = "tce-device-notify";
  private static final Logger logger = LoggerFactory.getLogger(DeviceNotificationPublisherImpl.class);

  private final ITopic<DeviceNotification> iTopic;
  private final NotifyMetricReporter notifyMetricReporter;

  private DeviceNotificationPublisherImpl(ITopic<DeviceNotification> iTopic, NotifyMetricReporter notifyMetricReporter) {
    this.iTopic = iTopic;
    this.notifyMetricReporter = notifyMetricReporter;
  }

  public static DeviceNotificationPublisher create(MessageListener<DeviceNotification> deviceNotificationMessageListener, HazelcastInstance hazelcastInstance,
      NotifyMetricReporter notifyMetricReporter) {
    Validate.notNull(deviceNotificationMessageListener, "deviceNotificationMessageListener");
    Validate.notNull(hazelcastInstance, "hazelcastInstance");
    Validate.notNull(notifyMetricReporter, "notifyMetricReporter");

    ITopic<DeviceNotification> iTopic = hazelcastInstance.getReliableTopic(TCE_NOTIFY_TOPIC_NAME);
    iTopic.addMessageListener(deviceNotificationMessageListener);
    return new DeviceNotificationPublisherImpl(iTopic, notifyMetricReporter);
  }

  @Override
  public void publishNotification(DeviceNotification deviceNotification) {
    Validate.notNull(deviceNotification, "deviceNotification");

    iTopic.publish(deviceNotification);

    notifyMetricReporter.onPublishDeviceInfoNotification();
    logger.debug("published message, iTopic: {}, deviceNotification: {}", iTopic.getName(), deviceNotification);
  }
}
