package com.wirelesscar.tcevce.wecu.device.info.cache.notify.impl;

import java.io.IOException;
import java.util.Arrays;
import java.util.Set;
import java.util.stream.Collectors;

import com.hazelcast.nio.ObjectDataInput;
import com.hazelcast.nio.ObjectDataOutput;
import com.hazelcast.nio.serialization.DataSerializable;
import com.volvo.tisp.vc.main.utils.lib.Validate;

public final class VehicleKeyNotification implements DataSerializable {
  private Set<String> vehicleIds;

  VehicleKeyNotification() {
    // Needed by HazelCast serialization do not remove!
  }

  private VehicleKeyNotification(Set<String> vehicleIds) {
    this.vehicleIds = vehicleIds;
  }

  public static VehicleKeyNotification ofVehicleIds(Set<String> vehicleIds) {
    Validate.notEmpty(vehicleIds, "vehicleIds");

    return new VehicleKeyNotification(vehicleIds);
  }

  public Set<String> getVehicleIds() {
    return vehicleIds;
  }

  @Override
  public void readData(ObjectDataInput objectDataInput) throws IOException {
    Validate.notNull(objectDataInput, "objectDataInput");

    vehicleIds = Arrays.stream(objectDataInput.readStringArray()).collect(Collectors.toUnmodifiableSet());
  }

  @Override
  public String toString() {
    return "vehicleIds=" + vehicleIds;
  }

  @Override
  public void writeData(ObjectDataOutput objectDataOutput) throws IOException {
    Validate.notNull(objectDataOutput, "objectDataOutput");

    objectDataOutput.writeStringArray(getVehicleIds().toArray(new String[0]));
  }
}
