package com.wirelesscar.tcevce.wecu.device.info.cache.notify.impl;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.hazelcast.topic.Message;
import com.hazelcast.topic.MessageListener;
import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.wirelesscar.tcevce.wecu.device.info.cache.core.impl.DeviceServiceCacheManager;
import com.wirelesscar.tcevce.wecu.device.info.cache.notify.metrics.NotifyMetricReporter;

public final class DeviceNotificationMessageListener implements MessageListener<DeviceNotification> {
  private static final Logger logger = LoggerFactory.getLogger(DeviceNotificationMessageListener.class);

  private final DeviceServiceCacheManager deviceServiceCacheManager;
  private final NotifyMetricReporter notifyMetricReporter;

  private DeviceNotificationMessageListener(DeviceServiceCacheManager deviceServiceCacheManager, NotifyMetricReporter notifyMetricReporter) {
    this.deviceServiceCacheManager = deviceServiceCacheManager;
    this.notifyMetricReporter = notifyMetricReporter;
  }

  public static MessageListener<DeviceNotification> create(DeviceServiceCacheManager deviceServiceCacheManager, NotifyMetricReporter notifyMetricReporter) {
    Validate.notNull(deviceServiceCacheManager, "deviceServiceCacheManager");
    Validate.notNull(notifyMetricReporter, "notifyMetricReporter");

    return new DeviceNotificationMessageListener(deviceServiceCacheManager, notifyMetricReporter);
  }

  @Override
  public void onMessage(Message<DeviceNotification> message) {
    Validate.notNull(message, "message");

    DeviceNotification deviceNotification = message.getMessageObject();

    notifyMetricReporter.onReceiveDeviceInfoNotification();
    logger.debug("received message, deviceNotification: {}", deviceNotification);

    deviceServiceCacheManager.invalidateCache(deviceNotification.getHandles());
  }
}
