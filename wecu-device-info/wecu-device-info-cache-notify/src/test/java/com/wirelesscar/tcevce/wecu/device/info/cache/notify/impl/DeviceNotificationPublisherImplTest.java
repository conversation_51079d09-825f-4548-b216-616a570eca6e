package com.wirelesscar.tcevce.wecu.device.info.cache.notify.impl;

import java.util.Set;

import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import com.hazelcast.core.HazelcastInstance;
import com.hazelcast.topic.ITopic;
import com.hazelcast.topic.MessageListener;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;
import com.wirelesscar.tcevce.wecu.device.info.cache.notify.TestUtils;
import com.wirelesscar.tcevce.wecu.device.info.cache.notify.api.DeviceNotificationPublisher;
import com.wirelesscar.tcevce.wecu.device.info.cache.notify.metrics.NotifyMetricReporter;

class DeviceNotificationPublisherImplTest {
  private static final DeviceNotification DEVICE_NOTIFICATION = DeviceNotification.ofHandles(Set.of(TestUtils.HANDLE_1));

  private static HazelcastInstance mockHazelcastInstance(ITopic<DeviceNotification> iTopic) {
    HazelcastInstance hazelcastInstance = Mockito.mock(HazelcastInstance.class);
    Mockito.when(hazelcastInstance.<DeviceNotification>getReliableTopic(DeviceNotificationPublisherImpl.TCE_NOTIFY_TOPIC_NAME)).thenReturn(iTopic);
    return hazelcastInstance;
  }

  private static ITopic<DeviceNotification> mockITopic() {
    return Mockito.mock(ITopic.class);
  }

  private static MessageListener<DeviceNotification> mockMessageListener() {
    return Mockito.mock(MessageListener.class);
  }

  @Test
  void createInvalidTest() {
    MessageListener<DeviceNotification> messageListener = mockMessageListener();
    HazelcastInstance hazelcastInstance = Mockito.mock(HazelcastInstance.class);
    NotifyMetricReporter notifyMetricReporter = Mockito.mock(NotifyMetricReporter.class);

    AssertThrows.illegalArgumentException(() -> DeviceNotificationPublisherImpl.create(null, hazelcastInstance, notifyMetricReporter),
        "deviceNotificationMessageListener must not be null");
    AssertThrows.illegalArgumentException(() -> DeviceNotificationPublisherImpl.create(messageListener, null, notifyMetricReporter),
        "hazelcastInstance must not be null");
    AssertThrows.illegalArgumentException(() -> DeviceNotificationPublisherImpl.create(messageListener, hazelcastInstance, null),
        "notifyMetricReporter must not be null");
  }

  @Test
  void sendNotificationTest() {
    MessageListener<DeviceNotification> messageListener = mockMessageListener();
    ITopic<DeviceNotification> iTopic = mockITopic();
    HazelcastInstance hazelcastInstance = mockHazelcastInstance(iTopic);
    NotifyMetricReporter notifyMetricReporter = Mockito.mock(NotifyMetricReporter.class);

    DeviceNotificationPublisher deviceNotificationPublisher = DeviceNotificationPublisherImpl.create(messageListener, hazelcastInstance, notifyMetricReporter);
    deviceNotificationPublisher.publishNotification(DEVICE_NOTIFICATION);

    Mockito.verify(hazelcastInstance).getReliableTopic(DeviceNotificationPublisherImpl.TCE_NOTIFY_TOPIC_NAME);
    Mockito.verify(iTopic).addMessageListener(messageListener);
    Mockito.verify(iTopic).publish(DEVICE_NOTIFICATION);
    Mockito.verify(iTopic).getName();
    Mockito.verify(notifyMetricReporter).onPublishDeviceInfoNotification();
    Mockito.verifyNoMoreInteractions(messageListener, hazelcastInstance, iTopic, notifyMetricReporter);
  }

  @Test
  void sendNullTest() {
    MessageListener<DeviceNotification> messageListener = mockMessageListener();
    ITopic<DeviceNotification> iTopic = mockITopic();
    HazelcastInstance hazelcastInstance = mockHazelcastInstance(iTopic);
    NotifyMetricReporter notifyMetricReporter = Mockito.mock(NotifyMetricReporter.class);

    DeviceNotificationPublisher deviceNotificationPublisher = DeviceNotificationPublisherImpl.create(messageListener, hazelcastInstance, notifyMetricReporter);

    AssertThrows.illegalArgumentException(() -> deviceNotificationPublisher.publishNotification(null), "deviceNotification must not be null");

    Mockito.verify(hazelcastInstance).getReliableTopic(DeviceNotificationPublisherImpl.TCE_NOTIFY_TOPIC_NAME);
    Mockito.verify(iTopic).addMessageListener(messageListener);
    Mockito.verifyNoMoreInteractions(hazelcastInstance, iTopic);
    Mockito.verifyNoInteractions(notifyMetricReporter, messageListener);
  }
}
