package com.wirelesscar.tcevce.wecu.device.info.cache.notify.impl;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Collections;
import java.util.Set;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.hazelcast.internal.serialization.InternalSerializationService;
import com.hazelcast.internal.serialization.impl.DefaultSerializationServiceBuilder;
import com.hazelcast.internal.serialization.impl.ObjectDataInputStream;
import com.hazelcast.internal.serialization.impl.ObjectDataOutputStream;
import com.hazelcast.nio.ObjectDataInput;
import com.hazelcast.nio.ObjectDataOutput;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;
import com.wirelesscar.tcevce.wecu.device.info.cache.notify.TestUtils;
import com.wirelesscar.tcevce.wecu.device.info.database.model.Handle;

class DeviceNotificationTest {
  private static final Set<Handle> HANDLES = Set.of(TestUtils.HANDLE_1, TestUtils.HANDLE_2);

  private static DeviceNotification readData(byte[] bytes, InternalSerializationService internalSerializationService) throws IOException {
    try (ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(bytes)) {
      ObjectDataInput objectDataInput = new ObjectDataInputStream(byteArrayInputStream, internalSerializationService);
      DeviceNotification deviceNotification = new DeviceNotification();
      deviceNotification.readData(objectDataInput);
      return deviceNotification;
    }
  }

  private static void verifyReadWrite(DeviceNotification deviceNotification1) throws IOException {
    InternalSerializationService internalSerializationService = new DefaultSerializationServiceBuilder().build();
    byte[] bytes = writeData(deviceNotification1, internalSerializationService);

    DeviceNotification deviceNotification2 = readData(bytes, internalSerializationService);
    Assertions.assertEquals(deviceNotification1.getHandles(), deviceNotification2.getHandles());
  }

  private static byte[] writeData(DeviceNotification deviceNotification, InternalSerializationService internalSerializationService) throws IOException {
    try (ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream()) {
      ObjectDataOutput objectDataOutput = new ObjectDataOutputStream(byteArrayOutputStream, internalSerializationService);
      deviceNotification.writeData(objectDataOutput);

      return byteArrayOutputStream.toByteArray();
    }
  }

  @Test
  void createInvalidTest() {
    AssertThrows.illegalArgumentException(() -> DeviceNotification.ofHandles(null), "handles must not be null");
    AssertThrows.illegalArgumentException(() -> DeviceNotification.ofHandles(Collections.emptySet()), "handles must not be empty");
  }

  @Test
  void ofHandlesTest() {
    Assertions.assertSame(HANDLES, DeviceNotification.ofHandles(HANDLES).getHandles());
  }

  @Test
  void readAndWriteDataFullTest() throws IOException {
    verifyReadWrite(DeviceNotification.ofHandles(HANDLES));
  }

  @Test
  void toStringTest() {
    String expectedString = "handles=[1]";
    Assertions.assertEquals(expectedString, DeviceNotification.ofHandles(Set.of(TestUtils.HANDLE_1)).toString());
  }
}
