package com.wirelesscar.tcevce.wecu.device.info.cache.notify.impl;

import java.util.Collections;
import java.util.Set;

import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import com.hazelcast.cluster.Member;
import com.hazelcast.topic.Message;
import com.hazelcast.topic.MessageListener;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;
import com.wirelesscar.tcevce.wecu.device.info.cache.core.impl.DeviceServiceCacheManager;
import com.wirelesscar.tcevce.wecu.device.info.cache.notify.TestUtils;
import com.wirelesscar.tcevce.wecu.device.info.cache.notify.metrics.NotifyMetricReporter;

class DeviceNotificationMessageListenerTest {
  @Test
  void createInvalidTest() {
    DeviceServiceCacheManager deviceServiceCacheManager = Mockito.mock(DeviceServiceCacheManager.class);
    NotifyMetricReporter notifyMetricReporter = Mockito.mock(NotifyMetricReporter.class);

    AssertThrows.illegalArgumentException(() -> DeviceNotificationMessageListener.create(null, notifyMetricReporter),
        "deviceServiceCacheManager must not be null");
    AssertThrows.illegalArgumentException(() -> DeviceNotificationMessageListener.create(deviceServiceCacheManager, null),
        "notifyMetricReporter must not be null");
  }

  @Test
  void onMessageTest() {
    DeviceServiceCacheManager deviceServiceCacheManager = Mockito.mock(DeviceServiceCacheManager.class);
    NotifyMetricReporter notifyMetricReporter = Mockito.mock(NotifyMetricReporter.class);
    DeviceNotification deviceNotification = DeviceNotification.ofHandles(Set.of(TestUtils.HANDLE_1));
    Message<DeviceNotification> message = new Message<>(DeviceNotificationPublisherImpl.TCE_NOTIFY_TOPIC_NAME, deviceNotification, 0L,
        Mockito.mock(Member.class));

    MessageListener<DeviceNotification> messageListener = DeviceNotificationMessageListener.create(deviceServiceCacheManager, notifyMetricReporter);

    messageListener.onMessage(message);
    Mockito.verify(deviceServiceCacheManager).invalidateCache(Collections.singleton(TestUtils.HANDLE_1));
    Mockito.verify(notifyMetricReporter).onReceiveDeviceInfoNotification();
    Mockito.verifyNoMoreInteractions(deviceServiceCacheManager, notifyMetricReporter);
  }

  @Test
  void onMessageWithInvalidInputTest() {
    DeviceServiceCacheManager deviceServiceCacheManager = Mockito.mock(DeviceServiceCacheManager.class);
    NotifyMetricReporter notifyMetricReporter = Mockito.mock(NotifyMetricReporter.class);

    MessageListener<DeviceNotification> messageListener = DeviceNotificationMessageListener.create(deviceServiceCacheManager, notifyMetricReporter);
    AssertThrows.illegalArgumentException(() -> messageListener.onMessage(null), "message must not be null");

    Mockito.verifyNoInteractions(deviceServiceCacheManager, notifyMetricReporter);
  }
}
