package com.wirelesscar.tcevce.wecu.device.info.cache.notify.impl;

import java.util.Collections;
import java.util.Set;

import org.junit.jupiter.api.Test;
import org.mockito.ArgumentMatchers;
import org.mockito.Mockito;

import com.volvo.tisp.vc.test.utils.lib.AssertThrows;
import com.wirelesscar.tcevce.wecu.device.info.cache.notify.TestUtils;
import com.wirelesscar.tcevce.wecu.device.info.cache.notify.api.DeviceNotificationPublisher;
import com.wirelesscar.tcevce.wecu.device.info.database.model.Handle;

class DistributedCacheInvalidatorTest {
  private static final Set<Handle> HANDLES = Set.of(TestUtils.HANDLE_1, TestUtils.HANDLE_2);

  @Test
  void createInvalidTest() {
    AssertThrows.illegalArgumentException(() -> DistributedCacheInvalidator.create(null), "deviceNotificationPublisher must not be null");
  }

  @Test
  void emptySetTest() {
    DeviceNotificationPublisher deviceNotificationPublisher = Mockito.mock(DeviceNotificationPublisher.class);

    AssertThrows.illegalArgumentException(() -> DistributedCacheInvalidator.create(deviceNotificationPublisher).invalidateCache(Collections.emptySet()),
        "handles must not be empty");

    Mockito.verifyNoMoreInteractions(deviceNotificationPublisher);
  }

  @Test
  void invalidateCacheTest() {
    DeviceNotificationPublisher deviceNotificationPublisher = Mockito.mock(DeviceNotificationPublisher.class);

    DistributedCacheInvalidator.create(deviceNotificationPublisher).invalidateCache(HANDLES);

    Mockito.verify(deviceNotificationPublisher).publishNotification(ArgumentMatchers.any(DeviceNotification.class));
    Mockito.verifyNoMoreInteractions(deviceNotificationPublisher);
  }
}
