package com.wirelesscar.tcevce.wecu.device.info.cache.notify.impl;

import java.io.Closeable;

import com.hazelcast.config.Config;
import com.hazelcast.config.NetworkConfig;
import com.hazelcast.core.Hazelcast;
import com.hazelcast.core.HazelcastInstance;

final class HazelcastInstanceWrapper implements Closeable {
  private final HazelcastInstance hazelcastInstance;

  private HazelcastInstanceWrapper(HazelcastInstance hazelcastInstance) {
    this.hazelcastInstance = hazelcastInstance;
  }

  static HazelcastInstanceWrapper createAndStart() {
    return new HazelcastInstanceWrapper(Hazelcast.newHazelcastInstance(createConfig()));
  }

  private static Config createConfig() {
    return new Config().setNetworkConfig(new NetworkConfig().setPort(0)).setProperty("hazelcast.logging.type", "slf4j");
  }

  @Override
  public void close() {
    hazelcastInstance.shutdown();
  }

  HazelcastInstance getHazelcastInstance() {
    return hazelcastInstance;
  }
}
