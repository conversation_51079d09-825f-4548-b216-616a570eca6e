package com.wirelesscar.tcevce.wecu.device.info.cache.notify.metrics;

import java.util.function.Consumer;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tag;
import io.micrometer.core.instrument.Tags;
import io.micrometer.core.instrument.simple.SimpleMeterRegistry;

import static com.wirelesscar.tcevce.wecu.device.info.cache.notify.metrics.NotifyMetricReporter.CACHE;
import static com.wirelesscar.tcevce.wecu.device.info.cache.notify.metrics.NotifyMetricReporter.CACHE_DEVICE_INFO;
import static com.wirelesscar.tcevce.wecu.device.info.cache.notify.metrics.NotifyMetricReporter.CACHE_VEHICLE_KEY;
import static com.wirelesscar.tcevce.wecu.device.info.cache.notify.metrics.NotifyMetricReporter.METRIC_NAME_NOTIFY;
import static com.wirelesscar.tcevce.wecu.device.info.cache.notify.metrics.NotifyMetricReporter.OPERATION;
import static com.wirelesscar.tcevce.wecu.device.info.cache.notify.metrics.NotifyMetricReporter.OPERATION_PUBLISH;
import static com.wirelesscar.tcevce.wecu.device.info.cache.notify.metrics.NotifyMetricReporter.OPERATION_RECEIVE;

class NotifyMetricReporterTest {
  private static void checkMetricCountOne(Consumer<NotifyMetricReporter> consumer, String metricName, Iterable<Tag> tags) {
    MeterRegistry meterRegistry = new SimpleMeterRegistry();
    NotifyMetricReporter notifyMetricReporter = new NotifyMetricReporter(meterRegistry);

    consumer.accept(notifyMetricReporter);
    Counter counter = meterRegistry.find(metricName).tags(tags).counter();
    Assertions.assertNotNull(counter);
    Assertions.assertEquals(1, counter.count());
  }

  @Test
  void constructorTest() {
    AssertThrows.illegalArgumentException(() -> new NotifyMetricReporter(null), "meterRegistry must not be null");
  }

  @Test
  void onPublishDeviceInfoNotificationTest() {
    Tags tags = Tags.of(OPERATION, OPERATION_PUBLISH).and(CACHE, CACHE_DEVICE_INFO);
    checkMetricCountOne(NotifyMetricReporter::onPublishDeviceInfoNotification, METRIC_NAME_NOTIFY, tags);
  }

  @Test
  void onPublishVehicleKeyNotificationTest() {
    Tags tags = Tags.of(OPERATION, OPERATION_PUBLISH).and(CACHE, CACHE_VEHICLE_KEY);
    checkMetricCountOne(NotifyMetricReporter::onPublishVehicleKeyNotification, METRIC_NAME_NOTIFY, tags);
  }

  @Test
  void onReceiveDeviceInfoNotificationTest() {
    Tags tags = Tags.of(OPERATION, OPERATION_RECEIVE).and(CACHE, CACHE_DEVICE_INFO);
    checkMetricCountOne(NotifyMetricReporter::onReceiveDeviceInfoNotification, METRIC_NAME_NOTIFY, tags);
  }

  @Test
  void onReceiveVehicleKeyNotificationTest() {
    Tags tags = Tags.of(OPERATION, OPERATION_RECEIVE).and(CACHE, CACHE_VEHICLE_KEY);
    checkMetricCountOne(NotifyMetricReporter::onReceiveVehicleKeyNotification, METRIC_NAME_NOTIFY, tags);
  }
}
