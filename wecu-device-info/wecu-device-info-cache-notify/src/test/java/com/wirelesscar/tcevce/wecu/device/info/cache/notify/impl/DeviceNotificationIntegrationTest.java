package com.wirelesscar.tcevce.wecu.device.info.cache.notify.impl;

import java.util.Set;

import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import com.wirelesscar.tcevce.wecu.device.info.cache.core.impl.DeviceServiceCacheManager;
import com.wirelesscar.tcevce.wecu.device.info.cache.notify.TestUtils;
import com.wirelesscar.tcevce.wecu.device.info.cache.notify.api.DeviceNotificationPublisher;
import com.wirelesscar.tcevce.wecu.device.info.cache.notify.metrics.NotifyMetricReporter;

class DeviceNotificationIntegrationTest {
  private static final DeviceNotification DEVICE_NOTIFICATION = DeviceNotification.ofHandles(Set.of(TestUtils.HANDLE_1, TestUtils.HANDLE_2));

  private static DeviceNotificationPublisher createDeviceNotificationPublisher(NotifyMetricReporter notifyMetricReporter,
      DeviceServiceCacheManager deviceService<PERSON><PERSON><PERSON><PERSON>ger, HazelcastInstanceWrapper hazelcastInstanceWrapper) {
    return DeviceNotificationPublisherImpl.create(DeviceNotificationMessageListener.create(deviceServiceCacheManager, notifyMetricReporter),
        hazelcastInstanceWrapper.getHazelcastInstance(), notifyMetricReporter);
  }

  @Test
  void sendNotificationTest() {
    final int numberOfMessages = 10;
    NotifyMetricReporter notifyMetricReporter = Mockito.mock(NotifyMetricReporter.class);
    DeviceServiceCacheManager deviceServiceCacheManager = Mockito.mock(DeviceServiceCacheManager.class);

    try (HazelcastInstanceWrapper hazelcastInstanceWrapper = HazelcastInstanceWrapper.createAndStart()) {
      DeviceNotificationPublisher deviceNotificationPublisher = createDeviceNotificationPublisher(notifyMetricReporter, deviceServiceCacheManager,
          hazelcastInstanceWrapper);

      for (int i = 0; i < numberOfMessages; ++i) {
        deviceNotificationPublisher.publishNotification(DEVICE_NOTIFICATION);
      }

      Mockito.verify(notifyMetricReporter, Mockito.timeout(500).times(numberOfMessages)).onPublishDeviceInfoNotification();
      Mockito.verify(notifyMetricReporter, Mockito.timeout(500).times(numberOfMessages)).onReceiveDeviceInfoNotification();
      Mockito.verify(deviceServiceCacheManager, Mockito.timeout(500).times(numberOfMessages)).invalidateCache(DEVICE_NOTIFICATION.getHandles());
    }

    Mockito.verifyNoMoreInteractions(notifyMetricReporter, deviceServiceCacheManager);
  }
}
