package com.wirelesscar.tcevce.wecu.device.info.database.model;

public final class Wecu<PERSON>eyId extends PrimaryKey {
  private WecuKeyId(long id) {
    super(id);
  }

  public static WecuKeyId ofLong(long id) {
    return new WecuKeyId(id);
  }

  @Override
  public boolean equals(Object object) {
    if (this == object) {
      return true;
    } else if (object == null) {
      return false;
    } else if (getClass() != object.getClass()) {
      return false;
    }
    WecuKeyId other = (WecuKeyId) object;
    return id == other.id;
  }

  @Override
  public int hashCode() {
    return Long.hashCode(id);
  }
}
