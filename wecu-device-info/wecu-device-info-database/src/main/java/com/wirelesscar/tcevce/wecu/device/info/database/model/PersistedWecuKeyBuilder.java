package com.wirelesscar.tcevce.wecu.device.info.database.model;

import java.time.Instant;

import com.volvo.tisp.vc.main.utils.lib.Validate;

public class PersistedWecuKeyBuilder {
  private Instant created;
  private WecuKey wecuKey;
  private WecuKeyId wecuKeyId;
  private Instant lastUpdated;

  public PersistedWecuKey build() {
    return new PersistedWecuKey(this);
  }

  public WecuKey getWecuKey() {
    return wecuKey;
  }

  public WecuKeyId getWecuKeyId() {
    return wecuKeyId;
  }

  public PersistedWecuKeyBuilder setCreated(Instant created) {
    Validate.notNull(created, "created");
    this.created = created;
    return this;
  }

  public PersistedWecuKeyBuilder setWecuKey(WecuKey wecuKey) {
    Validate.notNull(wecuKey, "wecuKey");
    this.wecuKey = wecuKey;
    return this;
  }

  public PersistedWecuKeyBuilder setWecuKeyId(WecuKeyId wecuKeyId) {
    Validate.notNull(wecuKeyId, "wecuKeyId");
    this.wecuKeyId = wecuKeyId;
    return this;
  }

  public PersistedWecuKeyBuilder setLastUpdated(Instant lastUpdated) {
    Validate.notNull(lastUpdated, "lastUpdated");
    this.lastUpdated = lastUpdated;
    return this;
  }

  Instant getCreated() {
    return created;
  }

  Instant getLastUpdated() {
    return lastUpdated;
  }
}
