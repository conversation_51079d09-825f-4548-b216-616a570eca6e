package com.wirelesscar.tcevce.wecu.device.info.database.model;

import java.util.Objects;
import java.util.Optional;

import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.tisp.vc.main.utils.lib.Validate;

public final class DeviceInfo implements Sizable {
  private final Handle handle;

  private final Optional<SatelliteId> satelliteId;
  private final Optional<SimInfo> simInfo;
  private final Optional<Vpi> vpi;

  DeviceInfo(DeviceInfoBuilder deviceInfoBuilder) {
    Validate.notNull(deviceInfoBuilder, "deviceInfoBuilder");

    handle = deviceInfoBuilder.getHandle();
    satelliteId = deviceInfoBuilder.getSatelliteId();
    simInfo = deviceInfoBuilder.getSimInfo();
    vpi = deviceInfoBuilder.getVpi();
  }

  @Override
  public int calculateSize() {
    return SizeUtil.SIZE_OF_OBJECT
        + SizeUtil.SIZE_OF_REFERENCE
        + handle.calculateSize()
        + SizeUtil.SIZE_OF_REFERENCE
        + SizeUtil.estimateSizeOfOptional(satelliteId)
        + SizeUtil.SIZE_OF_REFERENCE
        + SizeUtil.estimateSizeOfOptional(simInfo)
        + SizeUtil.SIZE_OF_REFERENCE
        + SizeUtil.estimateSizeOfOptionalVpi(vpi);
  }

  @Override
  public boolean equals(Object object) {
    if (this == object) {
      return true;
    } else if (object == null) {
      return false;
    } else if (getClass() != object.getClass()) {
      return false;
    }

    DeviceInfo other = (DeviceInfo) object;
    if (!Objects.equals(handle, (other.handle))) {
      return false;
    } else if (!Objects.equals(satelliteId, (other.satelliteId))) {
      return false;
    } else if (!Objects.equals(simInfo, (other.simInfo))) {
      return false;
    } else if (!Objects.equals(vpi, (other.vpi))) {
      return false;
    }

    return true;
  }

  public Handle getHandle() {
    return handle;
  }

  public Optional<SatelliteId> getSatelliteId() {
    return satelliteId;
  }

  public Optional<SimInfo> getSimInfo() {
    return simInfo;
  }

  public Optional<Vpi> getVpi() {
    return vpi;
  }

  @Override
  public int hashCode() {
    final int prime = 31;
    int result = 1;
    result = prime * result + handle.hashCode();
    result = prime * result + satelliteId.hashCode();
    result = prime * result + simInfo.hashCode();
    result = prime * result + vpi.hashCode();
    return result;
  }

  @Override
  public String toString() {
    return new StringBuilder(200)
        .append("handle=")
        .append(handle)
        .append(", satelliteId=")
        .append(satelliteId)
        .append(", simInfo={")
        .append(simInfo)
        .append("}, vpi=")
        .append(vpi)
        .toString();
  }
}
