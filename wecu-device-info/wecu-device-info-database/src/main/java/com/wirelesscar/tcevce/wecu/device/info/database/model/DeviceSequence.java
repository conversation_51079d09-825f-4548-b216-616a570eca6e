package com.wirelesscar.tcevce.wecu.device.info.database.model;

import com.volvo.tisp.vc.main.utils.lib.Validate;

public final class DeviceSequence {
  private final DeviceInfoId deviceInfoId;
  private final SequenceNumber sequenceNumber;

  DeviceSequence(DeviceSequenceBuilder deviceSequenceBuilder) {
    Validate.notNull(deviceSequenceBuilder, "deviceSequenceBuilder");

    deviceInfoId = deviceSequenceBuilder.getDeviceInfoId();
    sequenceNumber = deviceSequenceBuilder.getSequenceNumber();
  }

  @Override
  public boolean equals(Object object) {
    if (this == object) {
      return true;
    } else if (object == null) {
      return false;
    } else if (getClass() != object.getClass()) {
      return false;
    }
    DeviceSequence other = (DeviceSequence) object;
    if (!deviceInfoId.equals(other.deviceInfoId)) {
      return false;
    } else if (!sequenceNumber.equals(other.sequenceNumber)) {
      return false;
    }
    return true;
  }

  public DeviceInfoId getDeviceInfoId() {
    return deviceInfoId;
  }

  public SequenceNumber getSequenceNumber() {
    return sequenceNumber;
  }

  @Override
  public int hashCode() {
    final int prime = 31;
    int result = 1;
    result = prime * result + deviceInfoId.hashCode();
    result = prime * result + sequenceNumber.hashCode();
    return result;
  }

  @Override
  public String toString() {
    return new StringBuilder(50)
        .append("deviceInfoId=")
        .append(deviceInfoId)
        .append(", sequenceNumber=")
        .append(sequenceNumber)
        .toString();
  }
}
