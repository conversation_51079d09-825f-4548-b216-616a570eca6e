package com.wirelesscar.tcevce.wecu.device.info.database.api;

import java.io.Closeable;
import java.util.Optional;

import com.volvo.tisp.vc.common.dto.lib.vehicle.Msisdn;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.wirelesscar.tcevce.wecu.device.info.database.model.Handle;
import com.wirelesscar.tcevce.wecu.device.info.database.model.WecuKeyId;
import com.wirelesscar.tcevce.wecu.device.info.database.model.PersistedWecuKey;

public interface WecuKeyReader extends Closeable {
  @Override
  void close();

  /**
   * @return the total number of devices (= rows in the {@code device_info} table).
   */
  int countAllDevices();

  /**
   * @return {@link Optional#empty()} if no matching {@link PersistedWecuKey} was found. This method never returns {@code null}.
   */
  Optional<PersistedWecuKey> findWecuKeyByHandle(Handle handle);

  /**
   * @return {@link Optional#empty()} if no matching {@link PersistedWecuKey} was found. This method never returns {@code null}.
   */
  Optional<PersistedWecuKey> findWecuKeyByMsisdn(Msisdn msisdn);

  /**
   * @return {@link Optional#empty()} if no matching {@link PersistedWecuKey} was found. This method never returns {@code null}.
   */
  Optional<PersistedWecuKey> findWecuKeyByVpi(Vpi vpi);

  /**
   * @return {@link Optional#empty()} if no matching {@link PersistedWecuKey} was found. This method never returns {@code null}.
   */
  Optional<PersistedWecuKey> findWecuKeyByWecuKeyId(WecuKeyId wecuKeyId);
}
