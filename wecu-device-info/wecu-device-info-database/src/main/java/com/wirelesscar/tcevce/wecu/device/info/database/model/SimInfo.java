package com.wirelesscar.tcevce.wecu.device.info.database.model;

import java.util.Optional;

import com.volvo.tisp.vc.common.dto.lib.vehicle.Imsi;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Ipv4Address;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Msisdn;
import com.volvo.tisp.vc.main.utils.lib.Validate;

public final class SimInfo implements Sizable {
  private final Optional<Imsi> imsi;
  private final Ipv4Address ipv4Address;
  private final Ipv4Port ipv4Port;
  private final MobileNetworkOperator mobileNetworkOperator;
  private final Msisdn msisdn;

  SimInfo(SimInfoBuilder simInfoBuilder) {
    Validate.notNull(simInfoBuilder, "simInfoBuilder");

    imsi = Optional.ofNullable(simInfoBuilder.getImsi());
    ipv4Address = simInfoBuilder.getIpv4Address();
    ipv4Port = simInfoBuilder.getIpv4Port();
    mobileNetworkOperator = simInfoBuilder.getMobileNetworkOperator();
    msisdn = simInfoBuilder.getMsisdn();
  }

  @Override
  public int calculateSize() {
    return SizeUtil.SIZE_OF_OBJECT
        + imsi.map(SizeUtil::estimateSizeOfImsi).orElse(0)
        + SizeUtil.SIZE_OF_REFERENCE
        + SizeUtil.estimateSizeOfIpv4Address(ipv4Address)
        + SizeUtil.SIZE_OF_REFERENCE
        + ipv4Port.calculateSize()
        + SizeUtil.SIZE_OF_REFERENCE
        + mobileNetworkOperator.calculateSize()
        + SizeUtil.SIZE_OF_REFERENCE
        + SizeUtil.estimateSizeOfMsisdn(msisdn);
  }

  @Override
  public boolean equals(Object object) {
    if (this == object) {
      return true;
    } else if (object == null) {
      return false;
    } else if (getClass() != object.getClass()) {
      return false;
    }
    SimInfo other = (SimInfo) object;
    if (!imsi.equals(other.imsi)) {
      return false;
    } else if (!ipv4Address.equals(other.ipv4Address)) {
      return false;
    } else if (!ipv4Port.equals(other.ipv4Port)) {
      return false;
    } else if (!mobileNetworkOperator.equals(other.mobileNetworkOperator)) {
      return false;
    } else if (!msisdn.equals(other.msisdn)) {
      return false;
    }

    return true;
  }

  public Optional<Imsi> getImsi() {
    return imsi;
  }

  public Ipv4Address getIpv4Address() {
    return ipv4Address;
  }

  public Ipv4Port getIpv4Port() {
    return ipv4Port;
  }

  public MobileNetworkOperator getMobileNetworkOperator() {
    return mobileNetworkOperator;
  }

  public Msisdn getMsisdn() {
    return msisdn;
  }

  @Override
  public int hashCode() {
    final int prime = 31;
    int result = 1;
    result = prime * result + imsi.hashCode();
    result = prime * result + ipv4Address.hashCode();
    result = prime * result + ipv4Port.hashCode();
    result = prime * result + mobileNetworkOperator.hashCode();
    result = prime * result + msisdn.hashCode();

    return result;
  }

  @Override
  public String toString() {
    return new StringBuilder(100)
        .append("imsi=")
        .append(imsi)
        .append(", ipv4Address=")
        .append(ipv4Address)
        .append(", ipv4Port=")
        .append(ipv4Port)
        .append(", mobileNetworkOperator=")
        .append(mobileNetworkOperator)
        .append(", msisdn=")
        .append(msisdn)
        .toString();
  }
}
