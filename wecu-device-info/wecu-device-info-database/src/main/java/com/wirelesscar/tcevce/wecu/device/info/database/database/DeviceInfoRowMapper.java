package com.wirelesscar.tcevce.wecu.device.info.database.database;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.Instant;
import java.util.Optional;

import org.jdbi.v3.core.mapper.RowMapper;
import org.jdbi.v3.core.statement.StatementContext;

import com.volvo.tisp.vc.common.dto.lib.vehicle.Imsi;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Ipv4Address;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Msisdn;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.wirelesscar.tcevce.wecu.device.info.database.model.DeviceInfo;
import com.wirelesscar.tcevce.wecu.device.info.database.model.DeviceInfoBuilder;
import com.wirelesscar.tcevce.wecu.device.info.database.model.DeviceInfoId;
import com.wirelesscar.tcevce.wecu.device.info.database.model.Handle;
import com.wirelesscar.tcevce.wecu.device.info.database.model.Ipv4Port;
import com.wirelesscar.tcevce.wecu.device.info.database.model.MobileNetworkOperator;
import com.wirelesscar.tcevce.wecu.device.info.database.model.PersistedDeviceInfo;
import com.wirelesscar.tcevce.wecu.device.info.database.model.PersistedDeviceInfoBuilder;
import com.wirelesscar.tcevce.wecu.device.info.database.model.SatelliteId;
import com.wirelesscar.tcevce.wecu.device.info.database.model.SimInfo;
import com.wirelesscar.tcevce.wecu.device.info.database.model.SimInfoBuilder;

public final class DeviceInfoRowMapper implements RowMapper<PersistedDeviceInfo> {
  private DeviceInfoRowMapper() {
  }

  public static RowMapper<PersistedDeviceInfo> create() {
    return new DeviceInfoRowMapper();
  }

  private static Instant getCreated(ResultSet resultSet) throws SQLException {
    return getInstant(resultSet, "device_info_created");
  }

  private static DeviceInfoId getDeviceInfoId(ResultSet resultSet) throws SQLException {
    return DeviceInfoId.ofLong(resultSet.getLong("device_info_id"));
  }

  private static Handle getHandle(ResultSet resultSet) throws SQLException {
    return Handle.ofString(resultSet.getString("handle"));
  }

  private static Optional<Imsi> getImsi(ResultSet resultSet) throws SQLException {
    long resultSetLong = resultSet.getLong("imsi");

    return resultSetLong == 0 ? Optional.empty() : Optional.of(Imsi.ofLong(resultSetLong));
  }

  private static Instant getInstant(ResultSet resultSet, String columnName) throws SQLException {
    return resultSet.getTimestamp(columnName).toInstant();
  }

  private static Optional<Ipv4Address> getIpv4Address(ResultSet resultSet) throws SQLException {
    return Optional.ofNullable(resultSet.getString("ipv4_address")).map(Ipv4Address::ofString);
  }

  private static Optional<Ipv4Port> getIpv4Port(ResultSet resultSet) throws SQLException {
    int ipv4Port = resultSet.getInt("ipv4_port");

    if (resultSet.wasNull()) {
      return Optional.empty();
    }
    return Optional.of(Ipv4Port.ofInt(ipv4Port));
  }

  private static Instant getLastUpdated(ResultSet resultSet) throws SQLException {
    return getInstant(resultSet, "device_info_last_updated");
  }

  private static Optional<MobileNetworkOperator> getMobileNetworkOperator(ResultSet resultSet) throws SQLException {
    return Optional.ofNullable(resultSet.getString("mobile_network_operator")).map(MobileNetworkOperator::ofString);
  }

  private static Optional<Msisdn> getMsisdn(ResultSet resultSet) throws SQLException {
    return Optional.ofNullable(resultSet.getString("msisdn")).map(Msisdn::ofString);
  }

  private static Optional<SatelliteId> getSatelliteId(ResultSet resultSet) throws SQLException {
    return Optional.ofNullable(resultSet.getString("satellite_id")).map(SatelliteId::ofString);
  }

  private static Optional<SimInfo> getSimInfo(ResultSet resultSet) throws SQLException {
    Imsi imsi = getImsi(resultSet).orElse(null);
    Optional<Ipv4Address> optionalIpv4Address = getIpv4Address(resultSet);
    Optional<Ipv4Port> optionalIpv4Port = getIpv4Port(resultSet);
    Optional<MobileNetworkOperator> optionalMobileNetworkOperator = getMobileNetworkOperator(resultSet);
    Optional<Msisdn> optionalMsisdn = getMsisdn(resultSet);

    if (optionalIpv4Address.isEmpty() || optionalIpv4Port.isEmpty() || optionalMobileNetworkOperator.isEmpty()
        || optionalMsisdn.isEmpty()) {
      return Optional.empty();
    }

    return Optional.of(
        new SimInfoBuilder()
            .setImsi(imsi)
            .setIpv4Address(optionalIpv4Address.get())
            .setIpv4Port(optionalIpv4Port.get())
            .setMobileNetworkOperator(optionalMobileNetworkOperator.get())
            .setMsisdn(optionalMsisdn.get())
            .build()
    );
  }

  private static Optional<Vpi> getVpi(ResultSet resultSet) throws SQLException {
    return Optional.ofNullable(resultSet.getString("vpi")).map(Vpi::ofString);
  }

  private static DeviceInfo mapDeviceInfo(ResultSet resultSet) throws SQLException {
    return new DeviceInfoBuilder()
        .setHandle(getHandle(resultSet))
        .setSatelliteId(getSatelliteId(resultSet))
        .setSimInfo(getSimInfo(resultSet))
        .setVpi(getVpi(resultSet))
        .build();
  }

  @Override
  public PersistedDeviceInfo map(ResultSet resultSet, StatementContext statementContext) throws SQLException {
    Validate.notNull(resultSet, "resultSet");

    return new PersistedDeviceInfoBuilder()
        .setCreated(getCreated(resultSet))
        .setDeviceInfo(mapDeviceInfo(resultSet))
        .setDeviceInfoId(getDeviceInfoId(resultSet))
        .setLastUpdated(getLastUpdated(resultSet))
        .build();
  }
}
