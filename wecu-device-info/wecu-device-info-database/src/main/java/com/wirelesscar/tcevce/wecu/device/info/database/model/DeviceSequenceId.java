package com.wirelesscar.tcevce.wecu.device.info.database.model;

public final class DeviceSequenceId extends PrimaryKey {
  private DeviceSequenceId(long id) {
    super(id);
  }

  public static DeviceSequenceId ofLong(long id) {
    return new DeviceSequenceId(id);
  }

  @Override
  public boolean equals(Object object) {
    if (this == object) {
      return true;
    } else if (object == null) {
      return false;
    } else if (getClass() != object.getClass()) {
      return false;
    }
    DeviceSequenceId other = (DeviceSequenceId) object;
    return id == other.id;
  }

  @Override
  public int hashCode() {
    return Long.hashCode(id);
  }
}
