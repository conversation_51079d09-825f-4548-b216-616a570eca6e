package com.wirelesscar.tcevce.wecu.device.info.database.database;

import java.time.Clock;

import org.jdbi.v3.core.Jdbi;
import org.jdbi.v3.core.mapper.RowMapper;

import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.wirelesscar.tcevce.wecu.device.info.database.api.WecuKeyReader;
import com.wirelesscar.tcevce.wecu.device.info.database.api.WecuKeyReaderFactory;
import com.wirelesscar.tcevce.wecu.device.info.database.model.PersistedWecuKey;

public final class WecuKeyReaderFactoryImpl implements WecuKeyReaderFactory {
  private final Clock clock;
  private final Jdbi jdbi;
  private final RowMapper<PersistedWecuKey> rowMapper;

  private WecuKeyReaderFactoryImpl(Clock clock, Jdbi jdbi, RowMapper<PersistedWecuKey> rowMapper) {
    this.clock = clock;
    this.jdbi = jdbi;
    this.rowMapper = rowMapper;
  }

  public static WecuKeyReaderFactory create(Clock clock, Jdbi jdbi, RowMapper<PersistedWecuKey> rowMapper) {
    Validate.notNull(clock, "clock");
    Validate.notNull(jdbi, "jdbi");
    Validate.notNull(rowMapper, "rowMapper");

    return new WecuKeyReaderFactoryImpl(clock, jdbi, rowMapper);
  }

  @Override
  public WecuKeyReader create() {
    return WecuKeyWriterImpl.create(clock, jdbi.open(), rowMapper);
  }
}
