package com.wirelesscar.tcevce.wecu.device.info.database.model;

import com.volvo.tisp.vc.common.dto.lib.vehicle.Imsi;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Ipv4Address;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Msisdn;
import com.volvo.tisp.vc.main.utils.lib.Validate;

public class SimInfoBuilder {
  private Imsi imsi;
  private Ipv4Address ipv4Address;
  private Ipv4Port ipv4Port;
  private MobileNetworkOperator mobileNetworkOperator;
  private Msisdn msisdn;

  public SimInfo build() {
    Validate.notNull(ipv4Address, "ipv4Address");
    Validate.notNull(ipv4Port, "ipv4Port");
    Validate.notNull(mobileNetworkOperator, "mobileNetworkOperator");
    Validate.notNull(msisdn, "msisdn");

    return new SimInfo(this);
  }

  public Imsi getImsi() {
    return imsi;
  }

  public Ipv4Address getIpv4Address() {
    return ipv4Address;
  }

  public Ipv4Port getIpv4Port() {
    return ipv4Port;
  }

  public MobileNetworkOperator getMobileNetworkOperator() {
    return mobileNetworkOperator;
  }

  public Msisdn getMsisdn() {
    return msisdn;
  }

  public SimInfoBuilder setImsi(Imsi imsi) {
    this.imsi = imsi;
    return this;
  }

  public SimInfoBuilder setIpv4Address(Ipv4Address ipv4Address) {
    Validate.notNull(ipv4Address, "ipv4Address");

    this.ipv4Address = ipv4Address;
    return this;
  }

  public SimInfoBuilder setIpv4Port(Ipv4Port ipv4Port) {
    Validate.notNull(ipv4Port, "ipv4Port");

    this.ipv4Port = ipv4Port;
    return this;
  }

  public SimInfoBuilder setMobileNetworkOperator(MobileNetworkOperator mobileNetworkOperator) {
    Validate.notNull(mobileNetworkOperator, "mobileNetworkOperator");

    this.mobileNetworkOperator = mobileNetworkOperator;
    return this;
  }

  public SimInfoBuilder setMsisdn(Msisdn msisdn) {
    Validate.notNull(msisdn, "msisdn");

    this.msisdn = msisdn;
    return this;
  }
}