package com.wirelesscar.tcevce.wecu.device.info.database.database;

import javax.sql.DataSource;

import org.flywaydb.core.Flyway;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.volvo.tisp.vc.main.utils.lib.Validate;

public final class DeviceServiceFlywayExecutor {
  private static final String FLYWAY_SCHEMA_NAME = "device_service";
  private static final Logger logger = LoggerFactory.getLogger(DeviceServiceFlywayExecutor.class);

  private DeviceServiceFlywayExecutor() {
    throw new IllegalStateException();
  }

  /**
   * Perform DDL schema migration against configured data source.
   *
   * <p>
   * This method is thread safe and Flyway state that they also are safe. https://flywaydb.org/documentation/faq#parallel
   *
   * @return the number of successfully applied migrations.
   */
  public static int performDatabaseMigration(DataSource dataSource) {
    Validate.notNull(dataSource, "dataSource");

    return performDatabaseMigration(dataSource, "classpath:db/migration/" + FLYWAY_SCHEMA_NAME);
  }

  static int performDatabaseMigration(DataSource dataSource, String location) {
    Validate.notNull(dataSource, "dataSource");
    Validate.notNull(location, "location");

    logger.info("starting flyway migration for: {}", FLYWAY_SCHEMA_NAME);

    Flyway flyway = Flyway.configure()
        .dataSource(dataSource)
        .baselineOnMigrate(true)
        .table("DEVICE_SERVICE_SCHEMA_VERSION")
        .locations(location)
        .load();

    flyway.baseline();
    int numberOfSuccessfullyAppliedMigrations = flyway.migrate().migrationsExecuted;
    logger.info("Flyway migrated {} files.", numberOfSuccessfullyAppliedMigrations);
    return numberOfSuccessfullyAppliedMigrations;
  }
}
