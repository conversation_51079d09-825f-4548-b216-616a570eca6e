package com.wirelesscar.tcevce.wecu.device.info.database.api;

import java.util.List;
import java.util.Optional;

import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.tisp.vc.main.utils.lib.type.Either;
import com.wirelesscar.tcevce.wecu.device.info.database.model.DeviceInfo;
import com.wirelesscar.tcevce.wecu.device.info.database.model.DeviceInfoId;
import com.wirelesscar.tcevce.wecu.device.info.database.model.DeviceSequence;
import com.wirelesscar.tcevce.wecu.device.info.database.model.DeviceSequenceId;
import com.wirelesscar.tcevce.wecu.device.info.database.model.Handle;
import com.wirelesscar.tcevce.wecu.device.info.database.model.InsertionFailure;
import com.wirelesscar.tcevce.wecu.device.info.database.model.PersistedDeviceInfo;
import com.wirelesscar.tcevce.wecu.device.info.database.model.UniqueVehicleIdentifier;

public interface DeviceInfoWriter extends DeviceInfoReader, Transaction {
  /**
   * @return the number of deleted rows.
   */
  int deleteDeviceInfoByHandle(Handle handle);

  /**
   * @return the number of deleted rows.
   */
  int deleteDeviceInfoById(DeviceInfoId deviceInfoId);

  /**
   * @return the number of deleted rows.
   */
  int deleteDeviceInfoByVpi(Vpi vpi);

  /**
   * @return the number of deleted rows.
   */
  int deleteDeviceSequenceByDeviceInfoId(DeviceInfoId deviceInfoId);

  /**
   * @return the number of deleted rows.
   */
  int deleteDeviceSequenceById(DeviceSequenceId deviceSequenceId);

  /**
   * If the row to be locked is currently locked by another database transaction, this method blocks until the row is released by the other transaction and the
   * exclusive lock was successfully acquired by this database transaction.
   *
   * <p>
   * The exclusive lock on the row is held until the current database transaction is either {@link Transaction#commitTransaction() committed} or
   * {@link Transaction#rollbackTransaction() rolled back}.
   *
   * @return {@link Optional#empty()} if no matching {@link PersistedDeviceInfo} was found. This method never returns {@code null}.
   */
  Optional<PersistedDeviceInfo> findAndLockByDeviceInfoId(DeviceInfoId deviceInfoId);

  /**
   * If the row(s) to be locked is currently locked by another database transaction, this method blocks until the row is released by the other transaction and
   * the exclusive lock was successfully acquired by this database transaction.
   *
   * <p>
   * The exclusive lock on the rows is held until the current database transaction is either {@link Transaction#commitTransaction() committed} or
   * {@link Transaction#rollbackTransaction() rolled back}.
   *
   * @return list of all rows with matching unique keys (or clause). This method never returns {@code null}.
   */
  List<PersistedDeviceInfo> findAndLockByDeviceInfoUniqueKeys(UniqueVehicleIdentifier uniqueVehicleIdentifier);

  /**
   * @return the generated key for the inserted {@link DeviceInfo}.
   */
  Either<InsertionFailure, DeviceInfoId> insertDeviceInfo(DeviceInfo deviceInfo);

  /**
   * @return the generated key for the inserted {@link DeviceSequence}.
   */
  Either<InsertionFailure, DeviceSequenceId> insertDeviceSequence(DeviceSequence deviceSequence);

  /**
   * @return the number of updated rows.
   */
  int updateDeviceInfoByHandle(DeviceInfo deviceInfo);

  /**
   * @return the number of updated rows.
   */
  int updateDeviceSequence(DeviceSequence deviceSequence);
}
