package com.wirelesscar.tcevce.wecu.device.info.database.model;

import java.util.Set;

import com.volvo.tisp.vc.common.dto.lib.vehicle.Ipv4Address;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Msisdn;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.tisp.vc.main.utils.lib.Validate;

public final class UniqueVehicleIdentifier {
  private final Set<Handle> handles;
  private final Set<Ipv4Address> ipv4Addresses;
  private final Set<Msisdn> msisdns;
  private final Set<SatelliteId> satelliteIds;
  private final Set<Vpi> vpis;

  UniqueVehicleIdentifier(UniqueVehicleIdentifierBuilder uniqueVehicleIdentifierBuilder) {
    Validate.notNull(uniqueVehicleIdentifierBuilder, "uniqueVehicleIdentifierBuilder");

    handles = Set.copyOf(uniqueVehicleIdentifierBuilder.getHandles());
    ipv4Addresses = Set.copyOf(uniqueVehicleIdentifierBuilder.getIpv4Addresses());
    msisdns = Set.copyOf(uniqueVehicleIdentifierBuilder.getMsisdns());
    satelliteIds = Set.copyOf(uniqueVehicleIdentifierBuilder.getSatelliteIds());
    vpis = Set.copyOf(uniqueVehicleIdentifierBuilder.getVpis());
  }

  public Set<Handle> getHandles() {
    return handles;
  }

  public Set<Ipv4Address> getIpv4Addresses() {
    return ipv4Addresses;
  }

  public Set<Msisdn> getMsisdns() {
    return msisdns;
  }

  public Set<SatelliteId> getSatelliteIds() {
    return satelliteIds;
  }

  public Set<Vpi> getVpis() {
    return vpis;
  }
}
