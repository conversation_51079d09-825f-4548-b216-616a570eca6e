package com.wirelesscar.tcevce.wecu.device.info.database.api;

public interface Transaction {
  /**
   * @throws IllegalStateException if no transaction has been started.
   */
  void commitTransaction();

  /**
   * @throws IllegalStateException if no transaction has been started.
   */
  void rollbackTransaction();

  /**
   * @throws IllegalStateException if a transaction has already been started.
   */
  void startTransaction();
}
