package com.wirelesscar.tcevce.wecu.device.info.database.model;

import java.util.HashSet;
import java.util.Set;

import com.volvo.tisp.vc.common.dto.lib.vehicle.Ipv4Address;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Msisdn;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.tisp.vc.main.utils.lib.Validate;

/**
 * This class collects data for unique keys in device_info table.
 */
public class UniqueVehicleIdentifierBuilder {
  private final Set<Handle> handles = new HashSet<>();
  private final Set<Ipv4Address> ipv4Addresses = new HashSet<>();
  private final Set<Msisdn> msisdns = new HashSet<>();
  private final Set<SatelliteId> satelliteIds = new HashSet<>();
  private final Set<Vpi> vpis = new HashSet<>();

  public UniqueVehicleIdentifierBuilder addHandle(Handle handle) {
    Validate.notNull(handle, "handle");

    handles.add(handle);
    return this;
  }

  public UniqueVehicleIdentifierBuilder addIpv4Address(Ipv4Address ipv4Address) {
    Validate.notNull(ipv4Address, "ipv4Address");

    ipv4Addresses.add(ipv4Address);
    return this;
  }

  public UniqueVehicleIdentifierBuilder addMsisdn(Msisdn msisdn) {
    Validate.notNull(msisdn, "msisdn");

    msisdns.add(msisdn);
    return this;
  }

  public UniqueVehicleIdentifierBuilder addSatelliteId(SatelliteId satelliteId) {
    Validate.notNull(satelliteId, "satelliteId");

    satelliteIds.add(satelliteId);
    return this;
  }

  public UniqueVehicleIdentifierBuilder addVpi(Vpi vpi) {
    Validate.notNull(vpi, "vpi");

    vpis.add(vpi);
    return this;
  }

  public UniqueVehicleIdentifier build() {
    Validate.notEmpty(vpis, "vpiSet");

    return new UniqueVehicleIdentifier(this);
  }

  public Set<Handle> getHandles() {
    return handles;
  }

  public Set<Ipv4Address> getIpv4Addresses() {
    return ipv4Addresses;
  }

  public Set<Msisdn> getMsisdns() {
    return msisdns;
  }

  public Set<SatelliteId> getSatelliteIds() {
    return satelliteIds;
  }

  public Set<Vpi> getVpis() {
    return vpis;
  }
}
