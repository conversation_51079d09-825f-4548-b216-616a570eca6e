package com.wirelesscar.tcevce.wecu.device.info.database.database;

import java.time.Clock;

import org.jdbi.v3.core.Handle;
import org.jdbi.v3.core.Jdbi;
import org.jdbi.v3.core.mapper.RowMapper;
import org.jdbi.v3.core.transaction.TransactionIsolationLevel;

import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.wirelesscar.tcevce.wecu.device.info.database.api.DeviceInfoWriter;
import com.wirelesscar.tcevce.wecu.device.info.database.api.DeviceInfoWriterFactory;
import com.wirelesscar.tcevce.wecu.device.info.database.model.PersistedDeviceInfo;

public final class DeviceInfoWriterFactoryImpl implements DeviceInfoWriterFactory {
  private final Clock clock;
  private final Jdbi jdbi;
  private final RowMapper<PersistedDeviceInfo> rowMapper;

  private DeviceInfoWriterFactoryImpl(Clock clock, Jdbi jdbi, RowMapper<PersistedDeviceInfo> rowMapper) {
    this.clock = clock;
    this.jdbi = jdbi;
    this.rowMapper = rowMapper;
  }

  public static DeviceInfoWriterFactory create(Clock clock, Jdbi jdbi, RowMapper<PersistedDeviceInfo> rowMapper) {
    Validate.notNull(clock, "clock");
    Validate.notNull(jdbi, "jdbi");
    Validate.notNull(rowMapper, "rowMapper");

    return new DeviceInfoWriterFactoryImpl(clock, jdbi, rowMapper);
  }

  @Override
  public DeviceInfoWriter createReadCommitted() {
    return create(TransactionIsolationLevel.READ_COMMITTED);
  }

  @Override
  public DeviceInfoWriter createSerializable() {
    return create(TransactionIsolationLevel.SERIALIZABLE);
  }

  private DeviceInfoWriter create(TransactionIsolationLevel transactionIsolationLevel) {
    Handle handle = jdbi.open();
    handle.setTransactionIsolation(transactionIsolationLevel);

    return DeviceInfoWriterImpl.create(clock, handle, rowMapper);
  }
}
