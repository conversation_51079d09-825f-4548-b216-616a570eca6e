package com.wirelesscar.tcevce.wecu.device.info.database.model;

import java.util.Optional;

import com.volvo.tisp.vc.common.dto.lib.vehicle.Msisdn;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.tisp.vc.main.utils.lib.Validate;

public class WecuKeyBuilder {
  private Handle handle;
  private Msisdn msisdn;
  private Optional<Vpi> vpi = Optional.empty();

  public WecuKey build() {
    Validate.notNull(handle, "handle");
    Validate.notNull(msisdn, "msisdn");

    return new WecuKey(this);
  }

  public Handle getHandle() {
    return handle;
  }

  public WecuKeyBuilder setHandle(Handle handle) {
    this.handle = handle;

    return this;
  }

  public WecuKeyBuilder setMsisdn(Msisdn msisdn) {
    Validate.notNull(msisdn, "msisdn");

    this.msisdn = msisdn;
    return this;
  }

  public WecuKeyBuilder setVpi(Vpi vpi) {
    this.vpi = Optional.ofNullable(vpi);

    return this;
  }

  public void setVpi(Optional<Vpi> vpi) {
    this.vpi = vpi;
  }

  Msisdn getMsisdn() {
    return msisdn;
  }

  Optional<Vpi> getVpi() {
    return vpi;
  }
}
