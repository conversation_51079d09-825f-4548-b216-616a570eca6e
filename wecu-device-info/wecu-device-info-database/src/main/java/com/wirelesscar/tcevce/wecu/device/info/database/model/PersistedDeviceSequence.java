package com.wirelesscar.tcevce.wecu.device.info.database.model;

import java.time.Instant;

import com.volvo.tisp.vc.main.utils.lib.Validate;

public final class PersistedDeviceSequence {
  private final Instant created;
  private final DeviceSequence deviceSequence;
  private final DeviceSequenceId deviceSequenceId;
  private final Instant lastUpdated;

  PersistedDeviceSequence(PersistedDeviceSequenceBuilder persistedDeviceSequenceBuilder) {
    Validate.notNull(persistedDeviceSequenceBuilder, "persistedDeviceSequenceBuilder");

    created = persistedDeviceSequenceBuilder.getCreated();
    deviceSequence = persistedDeviceSequenceBuilder.getDeviceSequence();
    deviceSequenceId = persistedDeviceSequenceBuilder.getDeviceSequenceId();
    lastUpdated = persistedDeviceSequenceBuilder.getLastUpdated();
  }

  @Override
  public boolean equals(Object object) {
    if (this == object) {
      return true;
    } else if (object == null) {
      return false;
    } else if (getClass() != object.getClass()) {
      return false;
    }
    PersistedDeviceSequence other = (PersistedDeviceSequence) object;
    if (!created.equals(other.created)) {
      return false;
    } else if (!deviceSequence.equals(other.deviceSequence)) {
      return false;
    } else if (!deviceSequenceId.equals(other.deviceSequenceId)) {
      return false;
    } else if (!lastUpdated.equals(other.lastUpdated)) {
      return false;
    }
    return true;
  }

  public Instant getCreated() {
    return created;
  }

  public DeviceSequence getDeviceSequence() {
    return deviceSequence;
  }

  public DeviceSequenceId getDeviceSequenceId() {
    return deviceSequenceId;
  }

  public Instant getLastUpdated() {
    return lastUpdated;
  }

  @Override
  public int hashCode() {
    final int prime = 31;
    int result = 1;
    result = prime * result + created.hashCode();
    result = prime * result + deviceSequence.hashCode();
    result = prime * result + deviceSequenceId.hashCode();
    result = prime * result + lastUpdated.hashCode();
    return result;
  }

  @Override
  public String toString() {
    return new StringBuilder(200)
        .append("created=")
        .append(created)
        .append(", deviceSequence={")
        .append(deviceSequence)
        .append("}, deviceSequenceId=")
        .append(deviceSequenceId)
        .append(", lastUpdated=")
        .append(lastUpdated)
        .toString();
  }
}
