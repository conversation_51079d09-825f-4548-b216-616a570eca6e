package com.wirelesscar.tcevce.wecu.device.info.database.api;

import java.io.Closeable;
import java.util.List;
import java.util.Optional;

import com.volvo.tisp.vc.common.dto.lib.vehicle.Ipv4Address;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Msisdn;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.wirelesscar.tcevce.wecu.device.info.database.model.DeviceInfoId;
import com.wirelesscar.tcevce.wecu.device.info.database.model.Handle;
import com.wirelesscar.tcevce.wecu.device.info.database.model.PersistedDeviceInfo;
import com.wirelesscar.tcevce.wecu.device.info.database.model.PersistedDeviceSequence;
import com.wirelesscar.tcevce.wecu.device.info.database.model.SatelliteId;

public interface DeviceInfoReader extends Closeable {
  @Override
  void close();

  /**
   * @return the total number of devices (= rows in the {@code device_info} table).
   */
  int countAllDevices();

  /**
   * @return {@link Optional#empty()} if no matching {@link PersistedDeviceInfo} was found. This method never returns {@code null}.
   */
  Optional<PersistedDeviceInfo> findDeviceInfoByDeviceInfoId(DeviceInfoId deviceInfoId);

  /**
   * @return {@link Optional#empty()} if no matching {@link PersistedDeviceInfo} was found. This method never returns {@code null}.
   */
  Optional<PersistedDeviceInfo> findDeviceInfoByHandle(Handle handle);

  /**
   * @return {@link Optional#empty()} if no matching {@link PersistedDeviceInfo} was found. This method never returns {@code null}.
   */
  Optional<PersistedDeviceInfo> findDeviceInfoByIpv4Address(Ipv4Address ipv4Address);

  /**
   * @return {@link Optional#empty()} if no matching {@link PersistedDeviceInfo} was found. This method never returns {@code null}.
   */
  Optional<PersistedDeviceInfo> findDeviceInfoByMsisdn(Msisdn msisdn);

  /**
   * @return {@link Optional#empty()} if no matching {@link PersistedDeviceInfo} was found. This method never returns {@code null}.
   */
  Optional<PersistedDeviceInfo> findDeviceInfoBySatelliteId(SatelliteId satelliteId);

  /**
   * @return {@link Optional#empty()} if no matching {@link PersistedDeviceInfo} was found. This method never returns {@code null}.
   */
  Optional<PersistedDeviceInfo> findDeviceInfoByVpi(Vpi vpi);

  /**
   * @return {@link Optional#empty()} if no matching {@link PersistedDeviceSequence} was found. This method never returns {@code null}.
   */
  Optional<PersistedDeviceSequence> findDeviceSequenceByDeviceInfoId(DeviceInfoId deviceInfoId);

  /**
   * @return {@link List}. This method never returns {@code null}.
   */
  List<PersistedDeviceInfo> getDeviceInfoWithWecuKeys(int page, int limit);
}
