package com.wirelesscar.tcevce.wecu.device.info.database.model;

import java.util.regex.Pattern;

import com.volvo.tisp.vc.main.utils.lib.Validate;

public final class SatelliteId implements Sizable {
  private static final Pattern SATELLITE_ID_PATTERN = Pattern.compile("^[Hh][Qq][0-9]{10,12}[Xx][0-9]{1,2}$");

  private final String satelliteIdString;

  private SatelliteId(String satelliteIdString) {
    this.satelliteIdString = satelliteIdString;
  }

  /**
   * @throws IllegalArgumentException if {@code satelliteIdString} is null or empty.
   */
  public static SatelliteId ofString(String satelliteIdString) {
    isSatelliteId(satelliteIdString, "satelliteIdString");

    return new SatelliteId(satelliteIdString);
  }

  private static void isSatelliteId(String string, String variableName) {
    Validate.notNull(string, variableName);

    if (!SATELLITE_ID_PATTERN.matcher(string).matches()) {
      throw new IllegalArgumentException(variableName + " is not a valid satellite id: " + string);
    }
  }

  @Override
  public int calculateSize() {
    return SizeUtil.SIZE_OF_OBJECT
        + SizeUtil.SIZE_OF_REFERENCE
        + SizeUtil.estimateSizeOfString(satelliteIdString);
  }

  @Override
  public boolean equals(Object object) {
    if (this == object) {
      return true;
    } else if (object == null) {
      return false;
    } else if (getClass() != object.getClass()) {
      return false;
    }

    SatelliteId other = (SatelliteId) object;
    return satelliteIdString.equals(other.satelliteIdString);
  }

  @Override
  public int hashCode() {
    return satelliteIdString.hashCode();
  }

  @Override
  public String toString() {
    return satelliteIdString;
  }
}
