package com.wirelesscar.tcevce.wecu.device.info.database.model;

import java.time.Instant;
import java.util.Objects;

import com.volvo.tisp.vc.main.utils.lib.Validate;

public class PersistedWecuKey implements Sizable {
  private final Instant created;
  private final WecuKey wecuKey;
  private final WecuKeyId wecuKeyId;
  private final Instant lastUpdated;

  PersistedWecuKey(PersistedWecuKeyBuilder persistedWecuKeyBuilder) {
    Validate.notNull(persistedWecuKeyBuilder, "persistedWecuKeyBuilder");

    this.created = persistedWecuKeyBuilder.getCreated();
    this.wecuKeyId = persistedWecuKeyBuilder.getWecuKeyId();
    this.lastUpdated = persistedWecuKeyBuilder.getLastUpdated();
    this.wecuKey = persistedWecuKeyBuilder.getWecuKey();
  }

  @Override
  public int calculateSize() {
    return SizeUtil.SIZE_OF_OBJECT
        + SizeUtil.SIZE_OF_REFERENCE
        + wecuKey.calculateSize()
        + SizeUtil.SIZE_OF_REFERENCE
        + wecuKeyId.calculateSize()
        + SizeUtil.SIZE_OF_REFERENCE
        + SizeUtil.estimateSizeOfInstant()
        + SizeUtil.SIZE_OF_REFERENCE
        + SizeUtil.estimateSizeOfInstant();
  }

  @Override
  public boolean equals(Object object) {
    if (this == object) {
      return true;
    }
    if (object == null || getClass() != object.getClass()) {
      return false;
    }
    PersistedWecuKey that = (PersistedWecuKey) object;
    return Objects.equals(wecuKeyId, that.wecuKeyId) &&
        Objects.equals(wecuKey, that.wecuKey) &&
        Objects.equals(created, that.created) &&
        Objects.equals(lastUpdated, that.lastUpdated);
  }

  public Instant getCreated() {
    return created;
  }

  public WecuKey getWecuKey() {
    return wecuKey;
  }

  public WecuKeyId getWecuKeyId() {
    return wecuKeyId;
  }

  public Instant getLastUpdated() {
    return lastUpdated;
  }

  @Override
  public int hashCode() {
    return Objects.hash(wecuKeyId, wecuKey, created, lastUpdated);
  }
}
