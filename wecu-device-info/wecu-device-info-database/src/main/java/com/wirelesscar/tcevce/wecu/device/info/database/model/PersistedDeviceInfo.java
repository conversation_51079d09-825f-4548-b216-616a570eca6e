package com.wirelesscar.tcevce.wecu.device.info.database.model;

import java.time.Instant;
import java.util.Objects;

import com.volvo.tisp.vc.main.utils.lib.Validate;

public final class PersistedDeviceInfo implements Sizable {
  private final Instant created;
  private final DeviceInfo deviceInfo;
  private final DeviceInfoId deviceInfoId;
  private final Instant lastUpdated;

  PersistedDeviceInfo(PersistedDeviceInfoBuilder persistedDeviceInfoBuilder) {
    Validate.notNull(persistedDeviceInfoBuilder, "persistedDeviceInfoBuilder");

    created = persistedDeviceInfoBuilder.getCreated();
    deviceInfo = persistedDeviceInfoBuilder.getDeviceInfo();
    deviceInfoId = persistedDeviceInfoBuilder.getDeviceInfoId();
    lastUpdated = persistedDeviceInfoBuilder.getLastUpdated();
  }

  @Override
  public int calculateSize() {
    return SizeUtil.SIZE_OF_OBJECT
        + SizeUtil.SIZE_OF_REFERENCE
        + SizeUtil.estimateSizeOfInstant()
        + SizeUtil.SIZE_OF_REFERENCE
        + deviceInfo.calculateSize()
        + SizeUtil.SIZE_OF_REFERENCE
        + deviceInfoId.calculateSize()
        + SizeUtil.SIZE_OF_REFERENCE
        + SizeUtil.estimateSizeOfInstant();
  }

  @Override
  public boolean equals(Object object) {
    if (this == object) {
      return true;
    }
    if (object == null) {
      return false;
    }
    if (getClass() != object.getClass()) {
      return false;
    }
    PersistedDeviceInfo other = (PersistedDeviceInfo) object;
    if (!Objects.equals(created, (other.created))) {
      return false;
    } else if (!Objects.equals(deviceInfo, (other.deviceInfo))) {
      return false;
    } else if (!Objects.equals(deviceInfoId, (other.deviceInfoId))) {
      return false;
    } else if (!Objects.equals(lastUpdated, (other.lastUpdated))) {
      return false;
    }
    return true;
  }

  public Instant getCreated() {
    return created;
  }

  public DeviceInfo getDeviceInfo() {
    return deviceInfo;
  }

  public DeviceInfoId getDeviceInfoId() {
    return deviceInfoId;
  }

  public Instant getLastUpdated() {
    return lastUpdated;
  }

  @Override
  public int hashCode() {
    final int prime = 31;
    int result = 1;
    result = prime * result + created.hashCode();
    result = prime * result + deviceInfo.hashCode();
    result = prime * result + deviceInfoId.hashCode();
    result = prime * result + lastUpdated.hashCode();
    return result;
  }

  @Override
  public String toString() {
    return new StringBuilder(100)
        .append("created=")
        .append(created)
        .append(", deviceInfo={")
        .append(deviceInfo)
        .append("}, deviceInfoId=")
        .append(deviceInfoId)
        .append(", lastUpdated=")
        .append(lastUpdated)
        .toString();
  }
}
