package com.wirelesscar.tcevce.wecu.device.info.database.database;

import java.sql.SQLException;
import java.time.Clock;
import java.time.Instant;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import org.jdbi.v3.core.Handle;
import org.jdbi.v3.core.mapper.RowMapper;
import org.jdbi.v3.core.result.ResultBearing;
import org.jdbi.v3.core.statement.Query;
import org.jdbi.v3.core.statement.Update;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.volvo.tisp.vc.common.dto.lib.vehicle.Imsi;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Ipv4Address;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Msisdn;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.volvo.tisp.vc.main.utils.lib.type.Either;
import com.wirelesscar.tcevce.wecu.device.info.database.api.DeviceInfoWriter;
import com.wirelesscar.tcevce.wecu.device.info.database.model.DeviceInfo;
import com.wirelesscar.tcevce.wecu.device.info.database.model.DeviceInfoId;
import com.wirelesscar.tcevce.wecu.device.info.database.model.DeviceSequence;
import com.wirelesscar.tcevce.wecu.device.info.database.model.DeviceSequenceId;
import com.wirelesscar.tcevce.wecu.device.info.database.model.InsertionFailure;
import com.wirelesscar.tcevce.wecu.device.info.database.model.InsertionFailureReason;
import com.wirelesscar.tcevce.wecu.device.info.database.model.Ipv4Port;
import com.wirelesscar.tcevce.wecu.device.info.database.model.MobileNetworkOperator;
import com.wirelesscar.tcevce.wecu.device.info.database.model.PersistedDeviceInfo;
import com.wirelesscar.tcevce.wecu.device.info.database.model.PersistedDeviceSequence;
import com.wirelesscar.tcevce.wecu.device.info.database.model.SatelliteId;
import com.wirelesscar.tcevce.wecu.device.info.database.model.SimInfo;
import com.wirelesscar.tcevce.wecu.device.info.database.model.UniqueVehicleIdentifier;

public final class DeviceInfoWriterImpl implements DeviceInfoWriter {
  private static final Logger logger = LoggerFactory.getLogger(DeviceInfoWriterImpl.class);

  private final Clock clock;
  private final Handle jdbiHandle;
  private final RowMapper<PersistedDeviceInfo> rowMapper;

  private DeviceInfoWriterImpl(Clock clock, Handle jdbiHandle, RowMapper<PersistedDeviceInfo> rowMapper) {
    this.clock = clock;
    this.jdbiHandle = jdbiHandle;
    this.rowMapper = rowMapper;
  }

  public static DeviceInfoWriter create(Clock clock, Handle jdbiHandle, RowMapper<PersistedDeviceInfo> rowMapper) {
    Validate.notNull(clock, "clock");
    Validate.notNull(jdbiHandle, "jdbiHandle");
    Validate.notNull(rowMapper, "rowMapper");

    return new DeviceInfoWriterImpl(clock, jdbiHandle, rowMapper);
  }

  private static <T> Either<InsertionFailure, T> createInsertionFailure(RuntimeException e) {
    if (e.getCause() instanceof SQLException) {
      SQLException sqlException = (SQLException) e.getCause();

      switch (sqlException.getSQLState()) {
        case "23505":
          return Either.left(new InsertionFailure(InsertionFailureReason.DUPLICATE_KEY, e));

        case "23506":
          return Either.left(new InsertionFailure(InsertionFailureReason.REFERENTIAL_INTEGRITY_VIOLATED, e));

        default:
          return createUnknownInsertionFailure(e);
      }
    }

    return createUnknownInsertionFailure(e);
  }

  private static <T> Either<InsertionFailure, T> createUnknownInsertionFailure(RuntimeException e) {
    return Either.left(new InsertionFailure(InsertionFailureReason.UNKNOWN, e));
  }

  private static Update getDeviceInfoUpdateBind(Update update, DeviceInfo deviceInfo, Instant lastUpdated) {
    Optional<SimInfo> optionalSimInfo = deviceInfo.getSimInfo();

    return update
        .bind("vpi", deviceInfo.getVpi().map(Vpi::toString).orElse(null))
        .bind("imsi", optionalSimInfo.flatMap(SimInfo::getImsi).map(Imsi::toLong).orElse(null))
        .bind("ipv4_address", optionalSimInfo.map(SimInfo::getIpv4Address).map(Ipv4Address::toString).orElse(null))
        .bind("ipv4_port", optionalSimInfo.map(SimInfo::getIpv4Port).map(Ipv4Port::toInt).orElse(null))
        .bind("mobile_network_operator", optionalSimInfo.map(SimInfo::getMobileNetworkOperator).map(MobileNetworkOperator::toString).orElse(null))
        .bind("msisdn", optionalSimInfo.map(SimInfo::getMsisdn).map(Msisdn::toString).orElse(null))
        .bind("handle", deviceInfo.getHandle().toString())
        .bind("satellite_id", deviceInfo.getSatelliteId().map(SatelliteId::toString).orElse(null))
        .bind("device_info_last_updated", lastUpdated);
  }

  private static Set<String> toStrings(Set<?> objects) {
    return objects.stream().map(Object::toString).collect(Collectors.toUnmodifiableSet());
  }

  @Override
  public void close() {
    try (jdbiHandle) {
      if (!jdbiHandle.isClosed() && jdbiHandle.isInTransaction()) {
        logger.warn("closing database connection before the transaction has been committed, the transaction will be rolled back");
        jdbiHandle.rollback();
      }
    }
  }

  @Override
  public void commitTransaction() {
    if (jdbiHandle.isClosed()) {
      throw new IllegalStateException("could not commit a transaction on a closed handle");
    }

    if (!jdbiHandle.isInTransaction()) {
      throw new IllegalStateException("transaction not started");
    }

    jdbiHandle.commit();
  }

  @Override
  public int countAllDevices() {
    try (Query query = jdbiHandle.createQuery("SELECT count(*) FROM device_info")) {
      return query.mapTo(Integer.class).one();
    }
  }

  @Override
  public int deleteDeviceInfoByHandle(com.wirelesscar.tcevce.wecu.device.info.database.model.Handle handle) {
    Validate.notNull(handle, "handle");

    try (Update update = jdbiHandle.createUpdate("DELETE FROM device_info WHERE handle = :handle")) {
      return update.bind("handle", handle.toString()).execute();
    }
  }

  @Override
  public int deleteDeviceInfoById(DeviceInfoId deviceInfoId) {
    Validate.notNull(deviceInfoId, "deviceInfoId");

    try (Update update = jdbiHandle.createUpdate("DELETE FROM device_info WHERE device_info_id = :device_info_id")) {
      return update.bind("device_info_id", deviceInfoId.toLong()).execute();
    }
  }

  @Override
  public int deleteDeviceInfoByVpi(Vpi vpi) {
    Validate.notNull(vpi, "vpi");

    try (Update update = jdbiHandle.createUpdate("DELETE FROM device_info WHERE vpi = :vpi")) {
      return update.bind("vpi", vpi.toString()).execute();
    }
  }

  @Override
  public int deleteDeviceSequenceByDeviceInfoId(DeviceInfoId deviceInfoId) {
    Validate.notNull(deviceInfoId, "deviceInfoId");

    try (Update update = jdbiHandle.createUpdate("DELETE FROM device_sequence WHERE device_sequence_device_info_id = :device_sequence_device_info_id")) {
      return update.bind("device_sequence_device_info_id", deviceInfoId.toLong()).execute();
    }
  }

  @Override
  public int deleteDeviceSequenceById(DeviceSequenceId deviceSequenceId) {
    Validate.notNull(deviceSequenceId, "deviceSequenceId");

    try (Update update = jdbiHandle.createUpdate("DELETE FROM device_sequence WHERE device_sequence_id = :device_sequence_id")) {
      return update.bind("device_sequence_id", deviceSequenceId.toLong()).execute();
    }
  }

  @Override
  public Optional<PersistedDeviceInfo> findAndLockByDeviceInfoId(DeviceInfoId deviceInfoId) {
    Validate.notNull(deviceInfoId, "deviceInfoId");

    try (Query query = jdbiHandle.createQuery("SELECT * FROM device_info WHERE device_info_id = :device_info_id FOR UPDATE")) {
      return query.bind("device_info_id", deviceInfoId.toLong()).map(rowMapper).findFirst();
    }
  }

  @Override
  public List<PersistedDeviceInfo> findAndLockByDeviceInfoUniqueKeys(UniqueVehicleIdentifier uniqueVehicleIdentifier) {
    Validate.notNull(uniqueVehicleIdentifier, "uniqueVehicleIdentifier");

    boolean isIpv4AddressesEmpty = uniqueVehicleIdentifier.getIpv4Addresses().isEmpty();
    boolean isMsisdnsEmpty = uniqueVehicleIdentifier.getMsisdns().isEmpty();
    boolean isSatelliteIdsEmpty = uniqueVehicleIdentifier.getSatelliteIds().isEmpty();
    boolean isVpisEmpty = uniqueVehicleIdentifier.getVpis().isEmpty();

    StringBuilder stringBuilder = new StringBuilder(120);

    stringBuilder.append("SELECT * FROM device_info WHERE device_info_id IN (SELECT device_info_id FROM device_info WHERE handle IN (<handle>)");
    if (!isMsisdnsEmpty) {
      stringBuilder.append(" UNION SELECT device_info_id FROM device_info WHERE msisdn IN (<msisdn>)");
    }
    if (!isIpv4AddressesEmpty) {
      stringBuilder.append(" UNION SELECT device_info_id FROM device_info WHERE ipv4_address IN (<ipv4_address>)");
    }
    if (!isSatelliteIdsEmpty) {
      stringBuilder.append(" UNION SELECT device_info_id FROM device_info WHERE satellite_id IN (<satellite_id>)");
    }
    if (!isVpisEmpty) {
      stringBuilder.append(" UNION SELECT device_info_id FROM device_info WHERE vpi IN (<vpi>)");
    }

    stringBuilder.append(") ORDER BY device_info_id FOR UPDATE");

    try (Query query = jdbiHandle.createQuery(stringBuilder.toString())) {
      query.bindList("handle", toStrings(uniqueVehicleIdentifier.getHandles()));

      if (!isIpv4AddressesEmpty) {
        query.bindList("ipv4_address", toStrings(uniqueVehicleIdentifier.getIpv4Addresses()));
      }
      if (!isMsisdnsEmpty) {
        query.bindList("msisdn", toStrings(uniqueVehicleIdentifier.getMsisdns()));
      }
      if (!isSatelliteIdsEmpty) {
        query.bindList("satellite_id", toStrings(uniqueVehicleIdentifier.getSatelliteIds()));
      }
      if (!isVpisEmpty) {
        query.bindList("vpi", toStrings(uniqueVehicleIdentifier.getVpis()));
      }

      return query.map(rowMapper).list();
    }
  }

  @Override
  public Optional<PersistedDeviceInfo> findDeviceInfoByDeviceInfoId(DeviceInfoId deviceInfoId) {
    Validate.notNull(deviceInfoId, "deviceInfoId");

    try (Query query = jdbiHandle.createQuery("SELECT * FROM device_info WHERE device_info_id = :device_info_id")) {
      return query.bind("device_info_id", deviceInfoId.toLong()).map(rowMapper).findFirst();
    }
  }

  @Override
  public Optional<PersistedDeviceInfo> findDeviceInfoByHandle(com.wirelesscar.tcevce.wecu.device.info.database.model.Handle handle) {
    Validate.notNull(handle, "handle");

    try (Query query = jdbiHandle.createQuery("SELECT * FROM device_info WHERE handle = :handle")) {
      return query.bind("handle", handle.toString()).map(rowMapper).findFirst();
    }
  }

  @Override
  public Optional<PersistedDeviceInfo> findDeviceInfoByIpv4Address(Ipv4Address ipv4Address) {
    Validate.notNull(ipv4Address, "ipv4Address");

    try (Query query = jdbiHandle.createQuery("SELECT * FROM device_info WHERE ipv4_address = :ipv4_address")) {
      return query.bind("ipv4_address", ipv4Address.toString()).map(rowMapper).findFirst();
    }
  }

  @Override
  public Optional<PersistedDeviceInfo> findDeviceInfoByMsisdn(Msisdn msisdn) {
    Validate.notNull(msisdn, "msisdn");

    try (Query query = jdbiHandle.createQuery("SELECT * FROM device_info WHERE msisdn = :msisdn")) {
      return query.bind("msisdn", msisdn.toString()).map(rowMapper).findFirst();
    }
  }

  @Override
  public Optional<PersistedDeviceInfo> findDeviceInfoBySatelliteId(SatelliteId satelliteId) {
    Validate.notNull(satelliteId, "satelliteId");

    try (Query query = jdbiHandle.createQuery("SELECT * FROM device_info WHERE satellite_id = :satellite_id")) {
      return query.bind("satellite_id", satelliteId.toString()).map(rowMapper).findFirst();
    }
  }

  @Override
  public Optional<PersistedDeviceInfo> findDeviceInfoByVpi(Vpi vpi) {
    Validate.notNull(vpi, "vpi");

    try (Query query = jdbiHandle.createQuery("SELECT * FROM device_info WHERE vpi = :vpi")) {
      return query.bind("vpi", vpi.toString()).map(rowMapper).findFirst();
    }
  }

  @Override
  public Optional<PersistedDeviceSequence> findDeviceSequenceByDeviceInfoId(DeviceInfoId deviceInfoId) {
    Validate.notNull(deviceInfoId, "deviceInfoId");

    try (Query query = jdbiHandle.createQuery("SELECT * FROM device_sequence WHERE device_sequence_device_info_id = :device_sequence_device_info_id")) {
      return query.bind("device_sequence_device_info_id", deviceInfoId.toLong()).map(DeviceSequenceRowMapper.INSTANCE).findFirst();
    }
  }

  @Override
  public List<PersistedDeviceInfo> getDeviceInfoWithWecuKeys(int page, int limit) {
    Validate.notNegative(page, "page");
    Validate.isPositive(limit, "limit");

    try (Query query = jdbiHandle.createQuery(
        "SELECT * FROM device_info WHERE HANDLE IN (SELECT handle FROM WECU_KEYS) OFFSET :offset ROWS FETCH NEXT :limit ROWS ONLY")) {
      return query.bind("offset", page * limit).bind("limit", limit).map(rowMapper).collectIntoList();
    }
  }

  @Override
  public Either<InsertionFailure, DeviceInfoId> insertDeviceInfo(DeviceInfo deviceInfo) {
    Validate.notNull(deviceInfo, "deviceInfo");

    try (Update update = jdbiHandle.createUpdate(
        "INSERT INTO device_info (vpi, imsi, ipv4_address, ipv4_port, msisdn, handle, mobile_network_operator, satellite_id, device_info_created, device_info_last_updated) VALUES (:vpi, :imsi, :ipv4_address, :ipv4_port, :msisdn, :handle, :mobile_network_operator, :satellite_id, :device_info_created, :device_info_last_updated)"
    )) {

      Instant now = clock.instant();
      Update updateBind = getDeviceInfoUpdateBind(update, deviceInfo, now)
          .bind("device_info_created", now);

      ResultBearing resultBearing = updateBind.executeAndReturnGeneratedKeys("device_info_id");

      return Either.right(DeviceInfoId.ofLong(resultBearing.mapTo(Long.class).one()));
    } catch (RuntimeException e) {
      return createInsertionFailure(e);
    }
  }

  @Override
  public Either<InsertionFailure, DeviceSequenceId> insertDeviceSequence(DeviceSequence deviceSequence) {
    Validate.notNull(deviceSequence, "deviceSequence");

    try (Update update = jdbiHandle.createUpdate(
        "INSERT INTO device_sequence (device_sequence_device_info_id, sequence_number, device_sequence_created, device_sequence_last_updated) VALUES (:device_sequence_device_info_id, :sequence_number, :device_sequence_created, :device_sequence_last_updated)"
    )) {
      Instant now = clock.instant();
      ResultBearing resultBearing = update
          .bind("device_sequence_device_info_id", deviceSequence.getDeviceInfoId().toLong())
          .bind("sequence_number", deviceSequence.getSequenceNumber().toByte())
          .bind("device_sequence_created", now)
          .bind("device_sequence_last_updated", now)
          .executeAndReturnGeneratedKeys("device_sequence_id");

      return Either.right(DeviceSequenceId.ofLong(resultBearing.mapTo(Long.class).one()));
    } catch (RuntimeException e) {
      return createInsertionFailure(e);
    }
  }

  @Override
  public void rollbackTransaction() {
    if (jdbiHandle.isClosed()) {
      throw new IllegalStateException("could not rollback a transaction on a closed handle");
    }

    if (!jdbiHandle.isInTransaction()) {
      throw new IllegalStateException("transaction not started");
    }

    jdbiHandle.rollback();
  }

  @Override
  public void startTransaction() {
    if (jdbiHandle.isClosed()) {
      throw new IllegalStateException("could not begin a transaction on a closed handle");
    }

    if (jdbiHandle.isInTransaction()) {
      throw new IllegalStateException("transaction already started");
    }

    jdbiHandle.begin();
  }

  @Override
  public int updateDeviceInfoByHandle(DeviceInfo deviceInfo) {
    Validate.notNull(deviceInfo, "deviceInfo");

    try (Update update = jdbiHandle.createUpdate(
        "UPDATE device_info SET ipv4_address = :ipv4_address"
            + ", ipv4_port = :ipv4_port"
            + ", msisdn = :msisdn"
            + ", vpi = :vpi"
            + ", mobile_network_operator = :mobile_network_operator"
            + ", satellite_id = :satellite_id"
            + ", device_info_last_updated = :device_info_last_updated "
            + ", imsi = :imsi "
            + "WHERE handle = :handle"
    )) {
      Update updateBind = getDeviceInfoUpdateBind(update, deviceInfo, clock.instant());
      return updateBind.execute();
    }
  }

  @Override
  public int updateDeviceSequence(DeviceSequence deviceSequence) {
    Validate.notNull(deviceSequence, "deviceSequence");

    try (Update update = jdbiHandle.createUpdate(
        "UPDATE device_sequence SET sequence_number = :sequence_number, device_sequence_last_updated = :device_sequence_last_updated WHERE device_sequence_device_info_id = :device_sequence_device_info_id"
    )) {
      return update
          .bind("sequence_number", deviceSequence.getSequenceNumber().toByte())
          .bind("device_sequence_last_updated", clock.instant())
          .bind("device_sequence_device_info_id", deviceSequence.getDeviceInfoId().toLong())
          .execute();
    }
  }
}
