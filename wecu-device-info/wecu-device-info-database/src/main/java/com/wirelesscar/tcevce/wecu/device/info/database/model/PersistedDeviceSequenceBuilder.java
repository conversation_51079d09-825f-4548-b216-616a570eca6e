package com.wirelesscar.tcevce.wecu.device.info.database.model;

import java.time.Instant;

import com.volvo.tisp.vc.main.utils.lib.Validate;

public class PersistedDeviceSequenceBuilder {
  private Instant created;
  private DeviceSequence deviceSequence;
  private DeviceSequenceId deviceSequenceId;
  private Instant lastUpdated;

  public PersistedDeviceSequence build() {
    Validate.notNull(created, "created");
    Validate.notNull(deviceSequence, "deviceSequence");
    Validate.notNull(deviceSequenceId, "deviceSequenceId");
    Validate.notNull(lastUpdated, "lastUpdated");

    return new PersistedDeviceSequence(this);
  }

  public Instant getCreated() {
    return created;
  }

  public DeviceSequence getDeviceSequence() {
    return deviceSequence;
  }

  public DeviceSequenceId getDeviceSequenceId() {
    return deviceSequenceId;
  }

  public Instant getLastUpdated() {
    return lastUpdated;
  }

  public PersistedDeviceSequenceBuilder setCreated(Instant created) {
    Validate.notNull(created, "created");

    this.created = created;
    return this;
  }

  public PersistedDeviceSequenceBuilder setDeviceSequence(DeviceSequence deviceSequence) {
    Validate.notNull(deviceSequence, "deviceSequence");

    this.deviceSequence = deviceSequence;
    return this;
  }

  public PersistedDeviceSequenceBuilder setDeviceSequenceId(DeviceSequenceId deviceSequenceId) {
    Validate.notNull(deviceSequenceId, "deviceSequenceId");

    this.deviceSequenceId = deviceSequenceId;
    return this;
  }

  public PersistedDeviceSequenceBuilder setLastUpdated(Instant lastUpdated) {
    Validate.notNull(lastUpdated, "lastUpdated");

    this.lastUpdated = lastUpdated;
    return this;
  }
}
