package com.wirelesscar.tcevce.wecu.device.info.database.database;

import java.sql.SQLException;
import java.time.Clock;
import java.time.Instant;
import java.util.Optional;

import org.jdbi.v3.core.Handle;
import org.jdbi.v3.core.mapper.RowMapper;
import org.jdbi.v3.core.result.ResultBearing;
import org.jdbi.v3.core.statement.Query;
import org.jdbi.v3.core.statement.Update;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.volvo.tisp.vc.common.dto.lib.vehicle.Msisdn;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.volvo.tisp.vc.main.utils.lib.type.Either;
import com.wirelesscar.tcevce.wecu.device.info.database.api.WecuKeyWriter;
import com.wirelesscar.tcevce.wecu.device.info.database.model.InsertionFailure;
import com.wirelesscar.tcevce.wecu.device.info.database.model.InsertionFailureReason;
import com.wirelesscar.tcevce.wecu.device.info.database.model.PersistedWecuKey;
import com.wirelesscar.tcevce.wecu.device.info.database.model.WecuKey;
import com.wirelesscar.tcevce.wecu.device.info.database.model.WecuKeyId;

public class WecuKeyWriterImpl implements WecuKeyWriter {
  private static final Logger logger = LoggerFactory.getLogger(WecuKeyWriterImpl.class);

  private final Clock clock;
  private final Handle jdbiHandle;
  private final RowMapper<PersistedWecuKey> rowMapper;

  private WecuKeyWriterImpl(Clock clock, Handle jdbiHandle, RowMapper<PersistedWecuKey> rowMapper) {
    this.clock = clock;
    this.jdbiHandle = jdbiHandle;
    this.rowMapper = rowMapper;
  }

  public static WecuKeyWriter create(Clock clock, Handle jdbiHandle, RowMapper<PersistedWecuKey> rowMapper) {
    Validate.notNull(clock, "clock");
    Validate.notNull(jdbiHandle, "jdbiHandle");
    Validate.notNull(rowMapper, "rowMapper");

    return new WecuKeyWriterImpl(clock, jdbiHandle, rowMapper);
  }

  private static <T> Either<InsertionFailure, T> createInsertionFailure(RuntimeException e) {
    if (e.getCause() instanceof SQLException) {
      SQLException sqlException = (SQLException) e.getCause();

      switch (sqlException.getSQLState()) {
        case "23505":
          return Either.left(new InsertionFailure(InsertionFailureReason.DUPLICATE_KEY, e));

        case "23506":
          return Either.left(new InsertionFailure(InsertionFailureReason.REFERENTIAL_INTEGRITY_VIOLATED, e));

        default:
          return createUnknownInsertionFailure(e);
      }
    }

    return createUnknownInsertionFailure(e);
  }

  private static <T> Either<InsertionFailure, T> createUnknownInsertionFailure(RuntimeException e) {
    return Either.left(new InsertionFailure(InsertionFailureReason.UNKNOWN, e));
  }

  private static Update getWecuKeyUpdateBind(Update update, WecuKey wecuKey, Instant lastUpdated) {
    return update
        .bind("vpi", wecuKey.getVpi().map(Vpi::toString).orElse(null))
        .bind("handle", wecuKey.getHandle().toString())
        .bind("msisdn", wecuKey.getMsisdn().toString())
        .bind("device_key_last_updated", lastUpdated);
  }

  @Override
  public void close() {
    try (jdbiHandle) {
      if (!jdbiHandle.isClosed() && jdbiHandle.isInTransaction()) {
        logger.warn("closing database connection before the transaction has been committed, the transaction will be rolled back");
        jdbiHandle.rollback();
      }
    }
  }

  @Override
  public void commitTransaction() {
    if (jdbiHandle.isClosed()) {
      throw new IllegalStateException("could not commit a transaction on a closed handle");
    }

    if (!jdbiHandle.isInTransaction()) {
      throw new IllegalStateException("transaction not started");
    }

    jdbiHandle.commit();
  }

  @Override
  public int countAllDevices() {
    try (Query query = jdbiHandle.createQuery("SELECT count(*) FROM wecu_keys")) {
      return query.mapTo(Integer.class).one();
    }
  }

  @Override
  public int deleteWecuKeyByHandle(com.wirelesscar.tcevce.wecu.device.info.database.model.Handle handle) {
    Validate.notNull(handle, "handle");

    try (Update update = jdbiHandle.createUpdate("DELETE FROM wecu_keys WHERE handle = :handle")) {
      return update.bind("handle", handle.toString()).execute();
    }
  }

  @Override
  public int deleteWecuKeyById(WecuKeyId wecuKeyId) {
    Validate.notNull(wecuKeyId, "wecuKeyId");

    try (Update update = jdbiHandle.createUpdate("DELETE FROM wecu_keys WHERE id = :device_key_id")) {
      return update.bind("device_key_id", wecuKeyId.toLong()).execute();
    }
  }

  @Override
  public int deleteWecuKeyByVpi(Vpi vpi) {
    Validate.notNull(vpi, "vpi");

    try (Update update = jdbiHandle.createUpdate("DELETE FROM wecu_keys WHERE vpi = :vpi")) {
      return update.bind("vpi", vpi.toString()).execute();
    }
  }

  @Override
  public Optional<PersistedWecuKey> findWecuKeyByHandle(com.wirelesscar.tcevce.wecu.device.info.database.model.Handle handle) {
    Validate.notNull(handle, "handle");

    try (Query query = jdbiHandle.createQuery("SELECT * FROM wecu_keys WHERE handle = :handle")) {
      return query.bind("handle", handle.toString()).map(rowMapper).findFirst();
    }
  }

  @Override
  public Optional<PersistedWecuKey> findWecuKeyByMsisdn(Msisdn msisdn) {
    Validate.notNull(msisdn, "msisdn");

    try (Query query = jdbiHandle.createQuery("SELECT * FROM wecu_keys WHERE msisdn = :msisdn")) {
      return query.bind("msisdn", msisdn.toString()).map(rowMapper).findFirst();
    }
  }

  @Override
  public Optional<PersistedWecuKey> findWecuKeyByVpi(Vpi vpi) {
    Validate.notNull(vpi, "vpi");

    try (Query query = jdbiHandle.createQuery("SELECT * FROM wecu_keys WHERE vpi = :vpi")) {
      return query.bind("vpi", vpi.toString()).map(rowMapper).findFirst();
    }
  }

  @Override
  public Optional<PersistedWecuKey> findWecuKeyByWecuKeyId(WecuKeyId wecuKeyId) {
    Validate.notNull(wecuKeyId, "wecuKeyId");

    try (Query query = jdbiHandle.createQuery("SELECT * FROM wecu_keys WHERE id = :device_key_id")) {
      return query.bind("device_key_id", wecuKeyId.toLong()).map(rowMapper).findFirst();
    }
  }

  @Override
  public Either<InsertionFailure, WecuKeyId> insertWecuKey(WecuKey wecuKey) {
    Validate.notNull(wecuKey, "wecuKey");

    try (Update update = jdbiHandle.createUpdate(
        "INSERT INTO wecu_keys (vpi, handle, msisdn, creation_time, update_time) VALUES (:vpi, :handle, :msisdn, :device_key_created, :device_key_last_updated)"
    )) {

      Instant now = clock.instant();
      Update updateBind = getWecuKeyUpdateBind(update, wecuKey, now)
          .bind("device_key_created", now);

      ResultBearing resultBearing = updateBind.executeAndReturnGeneratedKeys("id");

      return Either.right(WecuKeyId.ofLong(resultBearing.mapTo(Long.class).one()));
    } catch (RuntimeException e) {
      return createInsertionFailure(e);
    }
  }

  @Override
  public void rollbackTransaction() {
    if (jdbiHandle.isClosed()) {
      throw new IllegalStateException("could not rollback a transaction on a closed handle");
    }

    if (!jdbiHandle.isInTransaction()) {
      throw new IllegalStateException("transaction not started");
    }

    jdbiHandle.rollback();
  }

  @Override
  public void startTransaction() {
    if (jdbiHandle.isClosed()) {
      throw new IllegalStateException("could not begin a transaction on a closed handle");
    }

    if (jdbiHandle.isInTransaction()) {
      throw new IllegalStateException("transaction already started");
    }

    jdbiHandle.begin();
  }

  @Override
  public int updateWecuKey(WecuKey wecuKey, WecuKeyId wecuKeyId) {
    Validate.notNull(wecuKey, "wecuKey");
    Validate.notNull(wecuKey, "wecuKeyId");

    try (Update update = jdbiHandle.createUpdate(
        "UPDATE wecu_keys SET vpi = :vpi, handle = :handle, msisdn = :msisdn, update_time = :device_key_last_updated WHERE id = :device_key_id"
    )) {
      Instant now = clock.instant();
      return update.bind("vpi", wecuKey.getVpi().map(Vpi::toString).orElse(null))
          .bind("handle", wecuKey.getHandle().toString())
          .bind("msisdn", wecuKey.getMsisdn().toString())
          .bind("device_key_last_updated", now)
          .bind("device_key_id", wecuKeyId.toLong())
          .execute();
    }
  }
}
