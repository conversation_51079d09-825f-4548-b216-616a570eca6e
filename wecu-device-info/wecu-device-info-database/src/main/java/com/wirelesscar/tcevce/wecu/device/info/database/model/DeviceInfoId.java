package com.wirelesscar.tcevce.wecu.device.info.database.model;

public final class DeviceInfoId extends PrimaryKey {
  private DeviceInfoId(long id) {
    super(id);
  }

  public static DeviceInfoId ofLong(long id) {
    return new DeviceInfoId(id);
  }

  @Override
  public boolean equals(Object object) {
    if (this == object) {
      return true;
    } else if (object == null) {
      return false;
    } else if (getClass() != object.getClass()) {
      return false;
    }
    DeviceInfoId other = (DeviceInfoId) object;
    return id == other.id;
  }

  @Override
  public int hashCode() {
    return Long.hashCode(id);
  }
}
