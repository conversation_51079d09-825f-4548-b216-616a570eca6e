package com.wirelesscar.tcevce.wecu.device.info.database.database;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.Instant;
import java.util.Optional;

import org.jdbi.v3.core.mapper.RowMapper;
import org.jdbi.v3.core.statement.StatementContext;

import com.volvo.tisp.vc.common.dto.lib.vehicle.Msisdn;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.wirelesscar.tcevce.wecu.device.info.database.model.Handle;
import com.wirelesscar.tcevce.wecu.device.info.database.model.PersistedWecuKey;
import com.wirelesscar.tcevce.wecu.device.info.database.model.PersistedWecuKeyBuilder;
import com.wirelesscar.tcevce.wecu.device.info.database.model.WecuKey;
import com.wirelesscar.tcevce.wecu.device.info.database.model.WecuKeyBuilder;
import com.wirelesscar.tcevce.wecu.device.info.database.model.WecuKeyId;

public final class WecuKeyRowMapper implements RowMapper<PersistedWecuKey> {
  private WecuKeyRowMapper() {
  }

  public static RowMapper<PersistedWecuKey> create() {
    return new WecuKeyRowMapper();
  }

  private static Instant getCreated(ResultSet resultSet) throws SQLException {
    return getInstant(resultSet, "creation_time");
  }

  private static Optional<Handle> getHandle(ResultSet resultSet) throws SQLException {
    return Optional.ofNullable(resultSet.getString("handle")).map(Handle::ofString);
  }

  private static Instant getInstant(ResultSet resultSet, String columnName) throws SQLException {
    return resultSet.getTimestamp(columnName).toInstant();
  }

  private static Instant getLastUpdated(ResultSet resultSet) throws SQLException {
    return getInstant(resultSet, "update_time");
  }

  private static Optional<Msisdn> getMsisdn(ResultSet resultSet) throws SQLException {
    return Optional.ofNullable(resultSet.getString("msisdn")).map(Msisdn::ofString);
  }

  private static Optional<Vpi> getVpi(ResultSet resultSet) throws SQLException {
    return Optional.ofNullable(resultSet.getString("vpi")).map(Vpi::ofString);
  }

  private static WecuKeyId getWecuKeyId(ResultSet resultSet) throws SQLException {
    return WecuKeyId.ofLong(resultSet.getLong("id"));
  }

  private static WecuKey mapWecuKey(ResultSet resultSet) throws SQLException {
    return new WecuKeyBuilder()
        .setHandle(getHandle(resultSet).orElse(null))
        .setMsisdn(getMsisdn(resultSet).orElse(null))
        .setVpi(getVpi(resultSet).orElse(null))
        .build();
  }

  @Override
  public PersistedWecuKey map(ResultSet resultSet, StatementContext statementContext) throws SQLException {
    Validate.notNull(resultSet, "resultSet");

    return new PersistedWecuKeyBuilder()
        .setCreated(getCreated(resultSet))
        .setWecuKeyId(getWecuKeyId(resultSet))
        .setWecuKey(mapWecuKey(resultSet))
        .setLastUpdated(getLastUpdated(resultSet))
        .build();
  }
}
