package com.wirelesscar.tcevce.wecu.device.info.database.model;

import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.tisp.vc.main.utils.lib.Validate;

/**
 * Handle is the legacy device identifier from the CGW, the new vehicle identifier is the {@link Vpi
 * VPI}.
 */
public final class Handle implements Sizable {
  private final String handleString;

  private Handle(String handleString) {
    this.handleString = handleString;
  }

  /**
   * @throws IllegalArgumentException if {@code handleString} is null or empty.
   */
  public static Handle ofString(String handleString) {
    Validate.lengthBetween(handleString, 1, 200, "handleString");

    return new Handle(handleString);
  }

  @Override
  public int calculateSize() {
    return SizeUtil.SIZE_OF_OBJECT
        + SizeUtil.SIZE_OF_REFERENCE
        + SizeUtil.estimateSizeOfString(handleString);
  }

  @Override
  public boolean equals(Object object) {
    if (this == object) {
      return true;
    } else if (object == null) {
      return false;
    } else if (getClass() != object.getClass()) {
      return false;
    }
    Handle other = (Handle) object;
    return handleString.equals(other.handleString);
  }

  @Override
  public int hashCode() {
    return handleString.hashCode();
  }

  @Override
  public String toString() {
    return this.handleString;
  }
}
