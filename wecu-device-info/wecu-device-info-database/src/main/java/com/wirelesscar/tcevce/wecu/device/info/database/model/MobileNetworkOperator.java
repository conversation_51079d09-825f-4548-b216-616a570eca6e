package com.wirelesscar.tcevce.wecu.device.info.database.model;

import com.volvo.tisp.vc.main.utils.lib.Validate;

public final class MobileNetworkOperator implements Sizable {
  private final String mobileNetworkOperatorString;

  private MobileNetworkOperator(String mobileNetworkOperatorString) {
    this.mobileNetworkOperatorString = mobileNetworkOperatorString;
  }

  /**
   * @throws IllegalArgumentException if {@code mobileNetworkOperatorString} is {@link String#isEmpty() empty} or {@code null}.
   */
  public static MobileNetworkOperator ofString(String mobileNetworkOperatorString) {
    Validate.notEmpty(mobileNetworkOperatorString, "mobileNetworkOperatorString");

    return new MobileNetworkOperator(mobileNetworkOperatorString);
  }

  @Override
  public int calculateSize() {
    return SizeUtil.SIZE_OF_OBJECT
        + SizeUtil.SIZE_OF_REFERENCE
        + SizeUtil.estimateSizeOfString(mobileNetworkOperatorString);
  }

  @Override
  public boolean equals(Object object) {
    if (this == object) {
      return true;
    } else if (object == null) {
      return false;
    } else if (getClass() != object.getClass()) {
      return false;
    }
    MobileNetworkOperator other = (MobileNetworkOperator) object;
    return mobileNetworkOperatorString.equals(other.mobileNetworkOperatorString);
  }

  @Override
  public int hashCode() {
    return mobileNetworkOperatorString.hashCode();
  }

  @Override
  public String toString() {
    return mobileNetworkOperatorString;
  }
}
