package com.wirelesscar.tcevce.wecu.device.info.database.model;

import java.util.Optional;

import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.tisp.vc.main.utils.lib.Validate;

public class DeviceInfoBuilder {
  private Handle handle;
  private Optional<SatelliteId> satelliteId = Optional.empty();
  private Optional<SimInfo> simInfo = Optional.empty();
  private Optional<Vpi> vpi = Optional.empty();

  public static DeviceInfoBuilder from(DeviceInfo deviceInfo) {
    Validate.notNull(deviceInfo, "deviceInfo");

    return new DeviceInfoBuilder()
        .setHandle(deviceInfo.getHandle())
        .setSatelliteId(deviceInfo.getSatelliteId())
        .setSimInfo(deviceInfo.getSimInfo())
        .setVpi(deviceInfo.getVpi());
  }

  public DeviceInfo build() {
    Validate.notNull(handle, "handle");

    return new DeviceInfo(this);
  }

  public Handle getHandle() {
    return handle;
  }

  public Optional<SatelliteId> getSatelliteId() {
    return satelliteId;
  }

  public Optional<SimInfo> getSimInfo() {
    return simInfo;
  }

  public Optional<Vpi> getVpi() {
    return vpi;
  }

  public DeviceInfoBuilder setHandle(Handle handle) {
    Validate.notNull(handle, "handle");

    this.handle = handle;
    return this;
  }

  public DeviceInfoBuilder setSatelliteId(Optional<SatelliteId> satelliteId) {
    Validate.notNull(satelliteId, "satelliteId");

    this.satelliteId = satelliteId;
    return this;
  }

  public DeviceInfoBuilder setSimInfo(Optional<SimInfo> simInfo) {
    Validate.notNull(simInfo, "simInfo");

    this.simInfo = simInfo;
    return this;
  }

  public DeviceInfoBuilder setVpi(Optional<Vpi> vpi) {
    Validate.notNull(vpi, "vpi");

    this.vpi = vpi;
    return this;
  }
}
