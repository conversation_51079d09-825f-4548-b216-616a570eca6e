package com.wirelesscar.tcevce.wecu.device.info.database.model;

import java.util.Optional;
import java.util.function.ToIntFunction;

import com.volvo.tisp.vc.common.dto.lib.vehicle.Imsi;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Ipv4Address;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Msisdn;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.volvo.tisp.vc.main.utils.lib.type.ImmutableByteArray;

public final class SizeUtil {
  static final int SIZE_OF_ARRAY = 24;
  static final int SIZE_OF_OBJECT = 16;
  static final int SIZE_OF_REFERENCE = 8;
  private static final String OPTIONAL_VAR_NAME = "optional";

  private SizeUtil() {
    throw new IllegalStateException();
  }

  public static int estimateSizeOfImmutableByteArray(ImmutableByteArray immutableByteArray) {
    Validate.notNull(immutableByteArray, "immutableByteArray");

    return SIZE_OF_OBJECT
        + SIZE_OF_REFERENCE
        + SIZE_OF_ARRAY
        + Byte.BYTES * immutableByteArray.getLength();
  }

  public static int estimateSizeOfImsi(Imsi imsi) {
    Validate.notNull(imsi, "imsi");

    return SIZE_OF_OBJECT + SIZE_OF_REFERENCE + estimateSizeOfString(imsi.toString());
  }

  public static int estimateSizeOfInstant() {
    return SIZE_OF_OBJECT + Long.BYTES + Integer.BYTES;
  }

  public static int estimateSizeOfIpv4Address(Ipv4Address ipv4Address) {
    Validate.notNull(ipv4Address, "ipv4Address");

    return SIZE_OF_OBJECT + SIZE_OF_REFERENCE + estimateSizeOfString(ipv4Address.toString());
  }

  public static int estimateSizeOfMsisdn(Msisdn msisdn) {
    Validate.notNull(msisdn, "msisdn");

    return SIZE_OF_OBJECT + SIZE_OF_REFERENCE + estimateSizeOfString(msisdn.toString());
  }

  public static int estimateSizeOfOptional(Optional<? extends Sizable> optional) {
    Validate.notNull(optional, OPTIONAL_VAR_NAME);

    return estimateSizeOfOptional(optional, Sizable::calculateSize);
  }

  public static int estimateSizeOfOptionalEnum(Optional<? extends Enum<?>> optional) {
    Validate.notNull(optional, OPTIONAL_VAR_NAME);

    return estimateSizeOfOptional(optional, value -> 0);
  }

  public static int estimateSizeOfOptionalString(Optional<String> optional) {
    Validate.notNull(optional, OPTIONAL_VAR_NAME);

    return estimateSizeOfOptional(optional, SizeUtil::estimateSizeOfString);
  }

  public static int estimateSizeOfOptionalVpi(Optional<Vpi> vpiOptional) {
    Validate.notNull(vpiOptional, "vpiOptional");

    return estimateSizeOfOptional(vpiOptional, SizeUtil::estimateSizeOfVpi);
  }

  public static int estimateSizeOfString(String string) {
    Validate.notNull(string, "string");

    return SIZE_OF_OBJECT
        + SIZE_OF_REFERENCE
        + SIZE_OF_ARRAY
        + Byte.BYTES * string.length()
        + Byte.BYTES
        + Integer.BYTES;
  }

  public static int estimateSizeOfVpi(Vpi vpi) {
    Validate.notNull(vpi, "vpi");

    return SIZE_OF_OBJECT + SIZE_OF_REFERENCE + estimateSizeOfString(vpi.toString());
  }

  private static <T> int estimateSizeOfOptional(Optional<T> optional, ToIntFunction<T> toIntFunction) {
    if (optional.isPresent()) {
      int totalSize = SIZE_OF_OBJECT;
      totalSize += SIZE_OF_REFERENCE;
      totalSize += toIntFunction.applyAsInt(optional.get());

      return totalSize;
    }

    return 0;
  }
}
