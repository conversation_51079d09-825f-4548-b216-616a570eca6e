package com.wirelesscar.tcevce.wecu.device.info.database.model;

import java.util.Objects;
import java.util.Optional;

import com.volvo.tisp.vc.common.dto.lib.vehicle.Msisdn;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.tisp.vc.main.utils.lib.Validate;

public class Wecu<PERSON>ey implements Sizable {
  private final Handle handle;
  private final Msisdn msisdn;
  private final Optional<Vpi> vpi;

  WecuKey(WecuKeyBuilder wecuKeyBuilder) {
    Validate.notNull(wecuKeyBuilder, "wecuKeyBuilder");

    this.handle = wecuKeyBuilder.getHandle();
    this.msisdn = wecuKeyBuilder.getMsisdn();
    this.vpi = wecuKeyBuilder.getVpi();
  }

  @Override
  public int calculateSize() {
    return SizeUtil.SIZE_OF_OBJECT
        + SizeUtil.SIZE_OF_REFERENCE
        + handle.calculateSize()
        + SizeUtil.SIZE_OF_REFERENCE
        + SizeUtil.estimateSizeOfMsisdn(msisdn)
        + SizeUtil.SIZE_OF_REFERENCE
        + SizeUtil.estimateSizeOfOptionalVpi(vpi);
  }

  @Override
  public boolean equals(Object object) {
    if (this == object) {
      return true;
    }
    if (object == null || getClass() != object.getClass()) {
      return false;
    }
    WecuKey that = (WecuKey) object;
    return Objects.equals(handle, that.handle) &&
        Objects.equals(msisdn, that.msisdn) &&
        Objects.equals(vpi, that.vpi);
  }

  public Handle getHandle() {
    return handle;
  }

  public Msisdn getMsisdn() {
    return msisdn;
  }

  public Optional<Vpi> getVpi() {
    return vpi;
  }

  @Override
  public int hashCode() {
    return Objects.hash(handle, msisdn, vpi);
  }
}
