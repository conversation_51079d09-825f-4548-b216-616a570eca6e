package com.wirelesscar.tcevce.wecu.device.info.database.database;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.Instant;

import org.jdbi.v3.core.mapper.RowMapper;
import org.jdbi.v3.core.statement.StatementContext;

import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.wirelesscar.tcevce.wecu.device.info.database.model.DeviceInfoId;
import com.wirelesscar.tcevce.wecu.device.info.database.model.DeviceSequence;
import com.wirelesscar.tcevce.wecu.device.info.database.model.DeviceSequenceBuilder;
import com.wirelesscar.tcevce.wecu.device.info.database.model.DeviceSequenceId;
import com.wirelesscar.tcevce.wecu.device.info.database.model.PersistedDeviceSequence;
import com.wirelesscar.tcevce.wecu.device.info.database.model.PersistedDeviceSequenceBuilder;
import com.wirelesscar.tcevce.wecu.device.info.database.model.SequenceNumber;

final class DeviceSequenceRowMapper implements RowMapper<PersistedDeviceSequence> {
  static final RowMapper<PersistedDeviceSequence> INSTANCE = new DeviceSequenceRowMapper();

  private DeviceSequenceRowMapper() {
  }

  private static Instant getInstant(ResultSet resultSet, String columnName) throws SQLException {
    return resultSet.getTimestamp(columnName).toInstant();
  }

  private static DeviceSequence mapDeviceSequence(ResultSet resultSet) throws SQLException {
    return new DeviceSequenceBuilder()
        .setDeviceInfoId(DeviceInfoId.ofLong(resultSet.getLong("device_sequence_device_info_id")))
        .setSequenceNumber(SequenceNumber.ofByte(resultSet.getByte("sequence_number")))
        .build();
  }

  @Override
  public PersistedDeviceSequence map(ResultSet resultSet, StatementContext statementContext) throws SQLException {
    Validate.notNull(resultSet, "resultSet");

    return new PersistedDeviceSequenceBuilder()
        .setCreated(getInstant(resultSet, "device_sequence_created"))
        .setDeviceSequence(mapDeviceSequence(resultSet))
        .setDeviceSequenceId(DeviceSequenceId.ofLong(resultSet.getLong("device_sequence_id")))
        .setLastUpdated(getInstant(resultSet, "device_sequence_last_updated"))
        .build();
  }
}
