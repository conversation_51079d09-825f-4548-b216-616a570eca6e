package com.wirelesscar.tcevce.wecu.device.info.database.database;

import java.time.Clock;

import org.jdbi.v3.core.Jdbi;
import org.jdbi.v3.core.mapper.RowMapper;

import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.wirelesscar.tcevce.wecu.device.info.database.api.DeviceInfoReader;
import com.wirelesscar.tcevce.wecu.device.info.database.api.DeviceInfoReaderFactory;
import com.wirelesscar.tcevce.wecu.device.info.database.model.PersistedDeviceInfo;

public final class DeviceInfoReaderFactoryImpl implements DeviceInfoReaderFactory {
  private final Clock clock;
  private final Jdbi jdbi;
  private final RowMapper<PersistedDeviceInfo> rowMapper;

  private DeviceInfoReaderFactoryImpl(Clock clock, Jdbi jdbi, RowMapper<PersistedDeviceInfo> rowMapper) {
    this.clock = clock;
    this.jdbi = jdbi;
    this.rowMapper = rowMapper;
  }

  public static DeviceInfoReaderFactory create(Clock clock, Jdbi jdbi, RowMapper<PersistedDeviceInfo> rowMapper) {
    Validate.notNull(clock, "clock");
    Validate.notNull(jdbi, "jdbi");
    Validate.notNull(rowMapper, "rowMapper");

    return new DeviceInfoReaderFactoryImpl(clock, jdbi, rowMapper);
  }

  @Override
  public DeviceInfoReader create() {
    return DeviceInfoWriterImpl.create(clock, jdbi.open(), rowMapper);
  }
}
