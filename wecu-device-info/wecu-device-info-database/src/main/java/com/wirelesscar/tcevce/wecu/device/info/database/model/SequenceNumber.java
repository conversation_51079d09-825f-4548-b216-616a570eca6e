package com.wirelesscar.tcevce.wecu.device.info.database.model;

import com.volvo.tisp.vc.main.utils.lib.Validate;

public final class SequenceNumber {
  public static final byte MAX_SEQUENCE_NUMBER_VALUE = (byte) 15;
  public static final byte MIN_SEQUENCE_NUMBER_VALUE = (byte) 0;

  private final byte value;

  private SequenceNumber(byte value) {
    this.value = value;
  }

  public static SequenceNumber ofByte(byte value) {
    Validate.notNegativeAndNotGreaterThan(value, MAX_SEQUENCE_NUMBER_VALUE, "value");

    return new SequenceNumber(value);
  }

  @Override
  public boolean equals(Object object) {
    if (this == object) {
      return true;
    } else if (object == null) {
      return false;
    } else if (getClass() != object.getClass()) {
      return false;
    }
    SequenceNumber other = (SequenceNumber) object;
    return value == other.value;
  }

  @Override
  public int hashCode() {
    return Byte.hashCode(value);
  }

  public SequenceNumber next() {
    if (value == MAX_SEQUENCE_NUMBER_VALUE) {
      return SequenceNumber.ofByte(MIN_SEQUENCE_NUMBER_VALUE);
    }
    return SequenceNumber.ofByte((byte) (value + 1));
  }

  public byte toByte() {
    return value;
  }

  @Override
  public String toString() {
    return Byte.toString(value);
  }
}
