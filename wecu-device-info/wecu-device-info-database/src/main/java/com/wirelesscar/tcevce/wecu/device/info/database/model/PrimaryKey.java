package com.wirelesscar.tcevce.wecu.device.info.database.model;

import com.volvo.tisp.vc.main.utils.lib.Validate;

public abstract class PrimaryKey implements Sizable {
  protected final long id;

  protected PrimaryKey(long id) {
    Validate.isPositive(id, "id");

    this.id = id;
  }

  @Override
  public int calculateSize() {
    return SizeUtil.SIZE_OF_OBJECT + Long.BYTES;
  }

  public final long toLong() {
    return id;
  }

  @Override
  public String toString() {
    return Long.toString(id);
  }
}
