package com.wirelesscar.tcevce.wecu.device.info.database.model;

import com.volvo.tisp.vc.main.utils.lib.Validate;

public class DeviceSequenceBuilder {
  private DeviceInfoId deviceInfoId;
  private SequenceNumber sequenceNumber;

  public static DeviceSequenceBuilder from(DeviceSequence deviceSequence) {
    Validate.notNull(deviceSequence, "deviceSequence");

    return new DeviceSequenceBuilder()
        .setDeviceInfoId(deviceSequence.getDeviceInfoId())
        .setSequenceNumber(deviceSequence.getSequenceNumber());
  }

  public DeviceSequence build() {
    Validate.notNull(deviceInfoId, "deviceInfoId");
    Validate.notNull(sequenceNumber, "sequenceNumber");

    return new DeviceSequence(this);
  }

  public DeviceInfoId getDeviceInfoId() {
    return deviceInfoId;
  }

  public SequenceNumber getSequenceNumber() {
    return sequenceNumber;
  }

  public DeviceSequenceBuilder setDeviceInfoId(DeviceInfoId deviceInfoId) {
    Validate.notNull(deviceInfoId, "deviceInfoId");

    this.deviceInfoId = deviceInfoId;
    return this;
  }

  public DeviceSequenceBuilder setSequenceNumber(SequenceNumber sequenceNumber) {
    Validate.notNull(sequenceNumber, "sequenceNumber");

    this.sequenceNumber = sequenceNumber;
    return this;
  }
}
