package com.wirelesscar.tcevce.wecu.device.info.database.model;

import com.volvo.tisp.vc.main.utils.lib.Validate;

public final class InsertionFailure {
  private final InsertionFailureReason insertionFailureReason;
  private final RuntimeException runtimeException;

  public InsertionFailure(InsertionFailureReason insertionFailureReason, RuntimeException runtimeException) {
    Validate.notNull(insertionFailureReason, "insertionFailureReason");
    Validate.notNull(runtimeException, "runtimeException");

    this.insertionFailureReason = insertionFailureReason;
    this.runtimeException = runtimeException;
  }

  public InsertionFailureReason getInsertionFailureReason() {
    return insertionFailureReason;
  }

  public RuntimeException getRuntimeException() {
    return runtimeException;
  }

  @Override
  public String toString() {
    return new StringBuilder(100)
        .append("insertionFailureReason=")
        .append(insertionFailureReason)
        .append(", runtimeException=")
        .append(runtimeException)
        .toString();
  }
}
