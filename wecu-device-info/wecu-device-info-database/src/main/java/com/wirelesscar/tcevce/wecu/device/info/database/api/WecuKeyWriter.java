package com.wirelesscar.tcevce.wecu.device.info.database.api;

import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.tisp.vc.main.utils.lib.type.Either;
import com.wirelesscar.tcevce.wecu.device.info.database.model.Handle;
import com.wirelesscar.tcevce.wecu.device.info.database.model.InsertionFailure;
import com.wirelesscar.tcevce.wecu.device.info.database.model.WecuKey;
import com.wirelesscar.tcevce.wecu.device.info.database.model.WecuKeyId;

public interface WecuKeyWriter extends WecuKeyReader, Transaction {
  /**
   * @return the number of deleted rows.
   */
  int deleteWecuKeyByHandle(<PERSON>le handle);

  /**
   * @return the number of deleted rows.
   */
  int deleteWecuKeyById(WecuKeyId wecuKeyId);

  /**
   * @return the number of deleted rows.
   */
  int deleteWecuKeyByVpi(Vpi vpi);

  /**
   * @return the generated key for the inserted {@link WecuKey}.
   */
  Either<InsertionFailure, WecuKeyId> insertWecuKey(WecuKey wecuKey);

  /**
   * @return the number of updated rows.
   */
  int updateWecuKey(WecuKey wecuKey, WecuKeyId wecuKeyId);
}
