package com.wirelesscar.tcevce.wecu.device.info.database.database;

import java.time.Clock;

import org.jdbi.v3.core.Handle;
import org.jdbi.v3.core.Jdbi;
import org.jdbi.v3.core.mapper.RowMapper;
import org.jdbi.v3.core.transaction.TransactionIsolationLevel;

import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.wirelesscar.tcevce.wecu.device.info.database.api.WecuKeyWriter;
import com.wirelesscar.tcevce.wecu.device.info.database.api.WecuKeyWriterFactory;
import com.wirelesscar.tcevce.wecu.device.info.database.model.PersistedWecuKey;

public final class WecuKeyWriterFactoryImpl implements WecuKeyWriterFactory {
  private final Clock clock;
  private final Jdbi jdbi;
  private final RowMapper<PersistedWecuKey> rowMapper;

  private WecuKeyWriterFactoryImpl(Clock clock, Jdbi jdbi, RowMapper<PersistedWecuKey> rowMapper) {
    this.clock = clock;
    this.jdbi = jdbi;
    this.rowMapper = rowMapper;
  }

  public static WecuKeyWriterFactory create(Clock clock, Jdbi jdbi, RowMapper<PersistedWecuKey> rowMapper) {
    Validate.notNull(clock, "clock");
    Validate.notNull(jdbi, "jdbi");
    Validate.notNull(rowMapper, "rowMapper");

    return new WecuKeyWriterFactoryImpl(clock, jdbi, rowMapper);
  }

  @Override
  public WecuKeyWriter createReadCommitted() {
    return create(TransactionIsolationLevel.READ_COMMITTED);
  }

  @Override
  public WecuKeyWriter createSerializable() {
    return create(TransactionIsolationLevel.SERIALIZABLE);
  }

  private WecuKeyWriter create(TransactionIsolationLevel transactionIsolationLevel) {
    Handle handle = jdbi.open();
    handle.setTransactionIsolation(transactionIsolationLevel);

    return WecuKeyWriterImpl.create(clock, handle, rowMapper);
  }
}
