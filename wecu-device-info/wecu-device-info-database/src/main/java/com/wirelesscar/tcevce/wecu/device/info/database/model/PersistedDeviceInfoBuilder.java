package com.wirelesscar.tcevce.wecu.device.info.database.model;

import java.time.Instant;

import com.volvo.tisp.vc.main.utils.lib.Validate;

public class PersistedDeviceInfoBuilder {
  private Instant created;
  private DeviceInfo deviceInfo;
  private DeviceInfoId deviceInfoId;
  private Instant lastUpdated;

  public PersistedDeviceInfo build() {
    Validate.notNull(created, "created");
    Validate.notNull(deviceInfo, "deviceInfo");
    Validate.notNull(deviceInfoId, "deviceInfoId");
    Validate.notNull(lastUpdated, "lastUpdated");

    return new PersistedDeviceInfo(this);
  }

  public Instant getCreated() {
    return created;
  }

  public DeviceInfo getDeviceInfo() {
    return deviceInfo;
  }

  public DeviceInfoId getDeviceInfoId() {
    return deviceInfoId;
  }

  public Instant getLastUpdated() {
    return lastUpdated;
  }

  public PersistedDeviceInfoBuilder setCreated(Instant created) {
    Validate.notNull(created, "created");

    this.created = created;
    return this;
  }

  public PersistedDeviceInfoBuilder setDeviceInfo(DeviceInfo deviceInfo) {
    Validate.notNull(deviceInfo, "deviceInfo");

    this.deviceInfo = deviceInfo;
    return this;
  }

  public PersistedDeviceInfoBuilder setDeviceInfoId(DeviceInfoId deviceInfoId) {
    Validate.notNull(deviceInfoId, "deviceInfoId");

    this.deviceInfoId = deviceInfoId;
    return this;
  }

  public PersistedDeviceInfoBuilder setLastUpdated(Instant lastUpdated) {
    Validate.notNull(lastUpdated, "lastUpdated");

    this.lastUpdated = lastUpdated;
    return this;
  }
}
