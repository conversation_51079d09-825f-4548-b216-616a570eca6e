package com.wirelesscar.tcevce.wecu.device.info.database.model;

import com.volvo.tisp.vc.main.utils.lib.Validate;

public final class Ipv4Port implements Sizable {
  private final int ipv4PortValue;

  private Ipv4Port(int ipv4PortValue) {
    this.ipv4PortValue = ipv4PortValue;
  }

  public static Ipv4Port ofInt(int ipv4PortValue) {
    Validate.isPositivePortNumber(ipv4PortValue, "ipv4PortValue");

    return new Ipv4Port(ipv4PortValue);
  }

  @Override
  public int calculateSize() {
    return SizeUtil.SIZE_OF_OBJECT + Integer.BYTES;
  }

  @Override
  public boolean equals(Object object) {
    if (this == object) {
      return true;
    } else if (object == null) {
      return false;
    } else if (getClass() != object.getClass()) {
      return false;
    }
    Ipv4Port other = (Ipv4Port) object;
    return ipv4PortValue == other.ipv4PortValue;
  }

  @Override
  public int hashCode() {
    return Integer.hashCode(ipv4PortValue);
  }

  public int toInt() {
    return ipv4PortValue;
  }

  @Override
  public String toString() {
    return Integer.toString(ipv4PortValue);
  }
}
