CREATE TABLE device_info (
  device_info_id NUMBER(19, 0) GENERATED ALWAYS AS IDENTITY NOT NULL,
  device_info_created TIMESTAMP(3) NOT NULL,
  device_info_last_updated TIMESTAMP(3) NOT NULL,
  handle VARCHAR(200 CHAR) NOT NULL,
  vpi VARCHAR(32 CHAR) DEFAULT NULL,
  ipv4_address VARCHAR(15 CHAR) DEFAULT NULL,
  ipv4_port NUMBER(5, 0) DEFAULT NULL,
  mobile_network_operator VARCHAR(32 CHAR) DEFAULT NULL,
  msisdn VARCHAR(16 CHAR) DEFAULT NULL,
  satellite_id VARCHAR(17 CHAR) DEFAULT NULL,

  CONSTRAINT PK_di PRIMARY KEY (device_info_id),
  CONSTRAINT UK_di_handle UNIQUE (handle),
  CONSTRAINT UK_di_vpi UNIQUE (vpi),
  CONSTRAINT UK_di_ipv4_address UNIQUE (ipv4_address),
  CONSTRAINT UK_di_msisdn UNIQUE (msisdn),
  CONSTRAINT UK_di_satellite_id UNIQUE (satellite_id)
);

CREATE TABLE device_sequence (
  device_sequence_id NUMBER(19,0) GENERATED ALWAYS AS IDENTITY NOT NULL,
  device_sequence_created TIMESTAMP(3) NOT NULL,
  device_sequence_last_updated TIMESTAMP(3) NOT NULL,
  device_sequence_device_info_id NUMBER(19,0) NOT NULL,
  sequence_number NUMBER(2,0) NOT NULL,

  CONSTRAINT PK_ds PRIMARY KEY (device_sequence_id),
  CONSTRAINT FK_ds_device_sequence_device_info_id FOREIGN KEY (device_sequence_device_info_id) REFERENCES device_info (device_info_id) ON DELETE CASCADE,
  CONSTRAINT UK_ds_device_sequence_device_info_id UNIQUE (device_sequence_device_info_id)
);
