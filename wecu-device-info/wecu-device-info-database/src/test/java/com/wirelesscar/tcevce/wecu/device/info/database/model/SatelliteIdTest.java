package com.wirelesscar.tcevce.wecu.device.info.database.model;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.vc.test.utils.lib.AssertThrows;
import com.volvo.tisp.vc.test.utils.lib.AssertUtils;

class SatelliteIdTest {
  private static void verifyInvalidSatelliteId(String satelliteId) {
    String message = "satelliteIdString ";

    if (satelliteId == null) {
      message += "must not be null";
    } else {
      message += "is not a valid satellite id: " + satelliteId;
    }
    AssertThrows.illegalArgumentException(() -> SatelliteId.ofString(satelliteId), message);
  }

  @Test
  void calculateSizeTest() {
    Assertions.assertEquals(91, TestUtils.SATELLITE_ID.calculateSize());
  }

  @Test
  void equalsAndHashcodeTest() {
    AssertUtils.assertEqualsAndHashCode(TestUtils.SATELLITE_ID, SatelliteId.ofString("HQ1234567890x1"));
    Assertions.assertNotEquals(TestUtils.SATELLITE_ID, SatelliteId.ofString("HQ1234567890x2"));
  }

  @Test
  void ofStringInvalidTest() {
    verifyInvalidSatelliteId(null);
    verifyInvalidSatelliteId("");
    // starts with HQ, but 13 digits after
    verifyInvalidSatelliteId("HQ1234567890123X1");
    // starts with HQ, 10 digits, but no X then
    verifyInvalidSatelliteId("HQ1234567890s1");
    // starts with hq, 11 digits, but no X then
    verifyInvalidSatelliteId("hq12345678901s1");
    // starts with HQ, 12 digits, but no X then
    verifyInvalidSatelliteId("HQ123456789012s1");
    // starts with HQ, 10 digits, but there is a char between digits
    verifyInvalidSatelliteId("HQ123456x7890x1");
    // starts with HQ, 10 digits, x present, but version is absent
    verifyInvalidSatelliteId("HQ1234567890x");
    // starts with HQ, 10 digits, x present, but version consists of more than 2 digits
    verifyInvalidSatelliteId("HQ1234567890x123");
    // starts with HQ, 10 digits, x present, but version is a char
    verifyInvalidSatelliteId("HQ1234567890xa");
    // doesn't start with [Hh]
    verifyInvalidSatelliteId("a1234567890x1");
    // starts with "h", but no [Qq] then
    verifyInvalidSatelliteId("hS1234567890x1");
  }

  @Test
  void toStringTest() {
    Assertions.assertEquals("HQ1234567890x1", TestUtils.SATELLITE_ID.toString());
  }
}
