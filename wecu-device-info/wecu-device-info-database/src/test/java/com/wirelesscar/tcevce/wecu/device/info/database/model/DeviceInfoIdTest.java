package com.wirelesscar.tcevce.wecu.device.info.database.model;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.vc.test.utils.lib.AssertThrows;
import com.volvo.tisp.vc.test.utils.lib.AssertUtils;

class DeviceInfoIdTest {
  @Test
  void calculateSizeTest() {
    Assertions.assertEquals(24, DeviceInfoId.ofLong(1).calculateSize());
  }

  @Test
  void equalsAndHashcodeTest() {
    DeviceInfoId deviceInfoId = DeviceInfoId.ofLong(42);
    AssertUtils.assertEqualsAndHashCode(deviceInfoId, DeviceInfoId.ofLong(42));

    Assertions.assertNotEquals(deviceInfoId, DeviceInfoId.ofLong(43));
  }

  @Test
  void ofLongInvalidTest() {
    AssertThrows.illegalArgumentException(() -> DeviceInfoId.ofLong(-1), "id must be positive: -1");
    AssertThrows.illegalArgumentException(() -> DeviceInfoId.ofLong(0), "id must be positive: 0");
  }

  @Test
  void toLongTest() {
    Assertions.assertEquals(42, DeviceInfoId.ofLong(42).toLong());
  }

  @Test
  void toStringTest() {
    Assertions.assertEquals("42", DeviceInfoId.ofLong(42).toString());
  }
}
