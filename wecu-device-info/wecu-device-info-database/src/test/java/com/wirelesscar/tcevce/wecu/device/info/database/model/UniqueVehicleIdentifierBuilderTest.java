package com.wirelesscar.tcevce.wecu.device.info.database.model;

import java.util.Set;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

class UniqueVehicleIdentifierBuilderTest {
  private static void verifyEquals(UniqueVehicleIdentifierBuilder uniqueVehicleIdentifierBuilder, UniqueVehicleIdentifier uniqueVehicleIdentifier) {
    Assertions.assertEquals(uniqueVehicleIdentifierBuilder.getHandles(), uniqueVehicleIdentifier.getHandles());
    Assertions.assertEquals(uniqueVehicleIdentifierBuilder.getIpv4Addresses(), uniqueVehicleIdentifier.getIpv4Addresses());
    Assertions.assertEquals(uniqueVehicleIdentifierBuilder.getMsisdns(), uniqueVehicleIdentifier.getMsisdns());
    Assertions.assertEquals(uniqueVehicleIdentifierBuilder.getSatelliteIds(), uniqueVehicleIdentifier.getSatelliteIds());
    Assertions.assertEquals(uniqueVehicleIdentifierBuilder.getVpis(), uniqueVehicleIdentifier.getVpis());
  }

  @Test
  void buildTest() {
    UniqueVehicleIdentifierBuilder uniqueVehicleIdentifierBuilder = new UniqueVehicleIdentifierBuilder();
    AssertThrows.illegalArgumentException(uniqueVehicleIdentifierBuilder::build, "vpiSet must not be empty");

    uniqueVehicleIdentifierBuilder.addVpi(TestUtils.VPI);
    verifyEquals(uniqueVehicleIdentifierBuilder, uniqueVehicleIdentifierBuilder.build());
  }

  @Test
  void handlesTest() {
    UniqueVehicleIdentifierBuilder uniqueVehicleIdentifierBuilder = new UniqueVehicleIdentifierBuilder();
    Assertions.assertTrue(uniqueVehicleIdentifierBuilder.getHandles().isEmpty());

    uniqueVehicleIdentifierBuilder.addHandle(TestUtils.HANDLE);
    Assertions.assertEquals(Set.of(TestUtils.HANDLE), uniqueVehicleIdentifierBuilder.getHandles());

    AssertThrows.illegalArgumentException(() -> uniqueVehicleIdentifierBuilder.addHandle(null), "handle must not be null");
    Assertions.assertEquals(Set.of(TestUtils.HANDLE), uniqueVehicleIdentifierBuilder.getHandles());
  }

  @Test
  void ipv4AddressesTest() {
    UniqueVehicleIdentifierBuilder uniqueVehicleIdentifierBuilder = new UniqueVehicleIdentifierBuilder();
    Assertions.assertTrue(uniqueVehicleIdentifierBuilder.getIpv4Addresses().isEmpty());

    uniqueVehicleIdentifierBuilder.addIpv4Address(TestUtils.IPV4_ADDRESS);
    Assertions.assertEquals(Set.of(TestUtils.IPV4_ADDRESS), uniqueVehicleIdentifierBuilder.getIpv4Addresses());

    AssertThrows.illegalArgumentException(() -> uniqueVehicleIdentifierBuilder.addIpv4Address(null), "ipv4Address must not be null");
    Assertions.assertEquals(Set.of(TestUtils.IPV4_ADDRESS), uniqueVehicleIdentifierBuilder.getIpv4Addresses());
  }

  @Test
  void msisdnsTest() {
    UniqueVehicleIdentifierBuilder uniqueVehicleIdentifierBuilder = new UniqueVehicleIdentifierBuilder();
    Assertions.assertTrue(uniqueVehicleIdentifierBuilder.getMsisdns().isEmpty());

    uniqueVehicleIdentifierBuilder.addMsisdn(TestUtils.MSISDN);
    Assertions.assertEquals(Set.of(TestUtils.MSISDN), uniqueVehicleIdentifierBuilder.getMsisdns());

    AssertThrows.illegalArgumentException(() -> uniqueVehicleIdentifierBuilder.addMsisdn(null), "msisdn must not be null");
    Assertions.assertEquals(Set.of(TestUtils.MSISDN), uniqueVehicleIdentifierBuilder.getMsisdns());
  }

  @Test
  void satelliteIdsTest() {
    UniqueVehicleIdentifierBuilder uniqueVehicleIdentifierBuilder = new UniqueVehicleIdentifierBuilder();
    Assertions.assertTrue(uniqueVehicleIdentifierBuilder.getSatelliteIds().isEmpty());

    uniqueVehicleIdentifierBuilder.addSatelliteId(TestUtils.SATELLITE_ID);
    Assertions.assertEquals(Set.of(TestUtils.SATELLITE_ID), uniqueVehicleIdentifierBuilder.getSatelliteIds());

    AssertThrows.illegalArgumentException(() -> uniqueVehicleIdentifierBuilder.addSatelliteId(null), "satelliteId must not be null");
    Assertions.assertEquals(Set.of(TestUtils.SATELLITE_ID), uniqueVehicleIdentifierBuilder.getSatelliteIds());
  }

  @Test
  void vpisTest() {
    UniqueVehicleIdentifierBuilder uniqueVehicleIdentifierBuilder = new UniqueVehicleIdentifierBuilder();
    Assertions.assertTrue(uniqueVehicleIdentifierBuilder.getVpis().isEmpty());

    uniqueVehicleIdentifierBuilder.addVpi(TestUtils.VPI);
    Assertions.assertEquals(Set.of(TestUtils.VPI), uniqueVehicleIdentifierBuilder.getVpis());

    AssertThrows.illegalArgumentException(() -> uniqueVehicleIdentifierBuilder.addVpi(null), "vpi must not be null");
    Assertions.assertEquals(Set.of(TestUtils.VPI), uniqueVehicleIdentifierBuilder.getVpis());
  }
}
