package com.wirelesscar.tcevce.wecu.device.info.database.database;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;

import org.jdbi.v3.core.mapper.RowMapper;
import org.jdbi.v3.core.statement.StatementContext;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import com.volvo.tisp.vc.test.utils.lib.AssertThrows;
import com.wirelesscar.tcevce.wecu.device.info.database.model.PersistedWecuKey;
import com.wirelesscar.tcevce.wecu.device.info.database.model.TestUtils;

class WecuKeyRowMapperTest {
  private static RowMapper<PersistedWecuKey> createRowMapper() {
    return WecuKeyRowMapper.create();
  }

  private static void mockGetLong(ResultSet resultSet, String columnLabel, long returnValue) throws SQLException {
    Mockito.when(resultSet.getLong(columnLabel)).thenReturn(returnValue);
  }

  private static ResultSet mockResultSet() throws SQLException {
    ResultSet resultSet = Mockito.mock(ResultSet.class);

    mockGetLong(resultSet, "id", TestUtils.WECU_KEY_ID.toLong());
    Mockito.when(resultSet.getTimestamp("creation_time")).thenReturn(Timestamp.from(TestUtils.CREATED));
    Mockito.when(resultSet.getTimestamp("update_time")).thenReturn(Timestamp.from(TestUtils.LAST_UPDATED));
    Mockito.when(resultSet.getString("msisdn")).thenReturn(TestUtils.MSISDN.toString());
    Mockito.when(resultSet.getString("vpi")).thenReturn(TestUtils.VPI.toString());
    Mockito.when(resultSet.getString("handle")).thenReturn(TestUtils.HANDLE.toString());

    return resultSet;
  }

  @Test
  void createTest() {
    RowMapper<PersistedWecuKey> rowMapper = WecuKeyRowMapper.create();
    Assertions.assertTrue(rowMapper instanceof WecuKeyRowMapper);
  }

  @Test
  void mapNullResultSetTest() {
    RowMapper<PersistedWecuKey> rowMapper = createRowMapper();

    AssertThrows.illegalArgumentException(() -> rowMapper.map(null, Mockito.mock(StatementContext.class)), "resultSet must not be null");
  }

  @Test
  void mapTest() throws SQLException {
    ResultSet resultSet = mockResultSet();
    Mockito.when(resultSet.wasNull()).thenReturn(true);

    PersistedWecuKey persistedWecuKey = createRowMapper().map(resultSet, null);

    Assertions.assertEquals(TestUtils.CREATED, persistedWecuKey.getCreated());
    Assertions.assertEquals(TestUtils.WECU_KEY_ID, persistedWecuKey.getWecuKeyId());
    Assertions.assertEquals(TestUtils.LAST_UPDATED, persistedWecuKey.getLastUpdated());
    Assertions.assertEquals(TestUtils.MSISDN, persistedWecuKey.getWecuKey().getMsisdn());
    Assertions.assertEquals(TestUtils.VPI, persistedWecuKey.getWecuKey().getVpi().get());
    Assertions.assertEquals(TestUtils.HANDLE, persistedWecuKey.getWecuKey().getHandle());
  }
}
