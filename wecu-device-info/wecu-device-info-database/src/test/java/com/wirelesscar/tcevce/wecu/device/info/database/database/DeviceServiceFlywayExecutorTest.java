package com.wirelesscar.tcevce.wecu.device.info.database.database;

import javax.sql.DataSource;

import org.flywaydb.core.api.FlywayException;
import org.jdbi.v3.core.Handle;
import org.jdbi.v3.core.Jdbi;
import org.jdbi.v3.core.statement.Query;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.vc.test.utils.lib.AssertThrows;
import com.volvo.tisp.vc.test.utils.lib.UtilClassVerifier;

class DeviceServiceFlywayExecutorTest {
  private static void checkTables(DataSource dataSource) {
    Jdbi jdbi = Jdbi.create(dataSource);

    try (Handle handle = jdbi.open()) {
      try (Query query = handle.createQuery("SELECT count(*) FROM device_info")) {
        Integer result = query.mapTo(Integer.class).one();
        Assertions.assertEquals(0, result.intValue());
      }

      try (Query query = handle.createQuery("SELECT count(*) FROM device_service_schema_version")) {
        Integer result = query.mapTo(Integer.class).one();
        Assertions.assertEquals(4, result.intValue());
      }
    }
  }

  @Test
  void performDatabaseMigrationInvalidParametersTest() {
    AssertThrows.illegalArgumentException(() -> DeviceServiceFlywayExecutor.performDatabaseMigration(null), "dataSource must not be null");
  }

  @Test
  void performDatabaseMigrationMultipleTimesTest() {
    try (H2DatabaseWrapper h2DatabaseWrapper = H2DatabaseWrapper.createAndStart()) {
      Assertions.assertEquals(2, DeviceServiceFlywayExecutor.performDatabaseMigration(h2DatabaseWrapper.getDataSource()));
      checkTables(h2DatabaseWrapper.getDataSource());

      final String location = "classpath:db/migration/device_service_test";
      final String expectedMessage = "Validate failed: Migrations have failed validation\n"
          + "Migration checksum mismatch for migration version 2\n"
          + "-> Applied to database : -31130613\n"
          + "-> Resolved locally    : 1494905087\n"
          + "Either revert the changes to the migration, or run repair to update the schema history.\n"
          + "Migration checksum mismatch for migration version 3\n"
          + "-> Applied to database : -1731849362\n"
          + "-> Resolved locally    : -753405151\n"
          + "Either revert the changes to the migration, or run repair to update the schema history.\n"
          + "Need more flexibility with validation rules? Learn more: https://rd.gt/3AbJUZE";

      AssertThrows.exception(() -> DeviceServiceFlywayExecutor.performDatabaseMigration(h2DatabaseWrapper.getDataSource(), location), expectedMessage,
          FlywayException.class);

      AssertThrows.exception(() -> DeviceServiceFlywayExecutor.performDatabaseMigration(h2DatabaseWrapper.getDataSource(), location), expectedMessage,
          FlywayException.class);
    }
  }

  @Test
  void performDatabaseMigrationTest() {
    try (H2DatabaseWrapper h2DatabaseWrapper = H2DatabaseWrapper.createAndStart()) {
      Assertions.assertEquals(2, DeviceServiceFlywayExecutor.performDatabaseMigration(h2DatabaseWrapper.getDataSource()));
      checkTables(h2DatabaseWrapper.getDataSource());

      Assertions.assertEquals(0, DeviceServiceFlywayExecutor.performDatabaseMigration(h2DatabaseWrapper.getDataSource()));
      checkTables(h2DatabaseWrapper.getDataSource());
    }
  }

  @Test
  void verifyUtilClassTest() throws ReflectiveOperationException {
    UtilClassVerifier.verifyUtilClass(DeviceServiceFlywayExecutor.class);
  }
}
