package com.wirelesscar.tcevce.wecu.device.info.database.database;

import java.time.Clock;
import java.time.Instant;
import java.util.Optional;

import javax.sql.DataSource;

import org.jdbi.v3.core.Jdbi;
import org.jdbi.v3.core.mapper.RowMapper;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import com.volvo.tisp.vc.common.dto.lib.vehicle.Msisdn;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;
import com.wirelesscar.tcevce.wecu.device.info.database.api.WecuKeyWriter;
import com.wirelesscar.tcevce.wecu.device.info.database.api.WecuKeyWriterFactory;
import com.wirelesscar.tcevce.wecu.device.info.database.model.Handle;
import com.wirelesscar.tcevce.wecu.device.info.database.model.InsertionFailure;
import com.wirelesscar.tcevce.wecu.device.info.database.model.InsertionFailureReason;
import com.wirelesscar.tcevce.wecu.device.info.database.model.PersistedWecuKey;
import com.wirelesscar.tcevce.wecu.device.info.database.model.TestUtils;
import com.wirelesscar.tcevce.wecu.device.info.database.model.WecuKey;
import com.wirelesscar.tcevce.wecu.device.info.database.model.WecuKeyBuilder;
import com.wirelesscar.tcevce.wecu.device.info.database.model.WecuKeyId;

class WecuKeyWriterImplTest {
  private static final Handle HANDLE_1 = Handle.ofString("123456");
  private static final Handle HANDLE_2 = Handle.ofString("234567");
  private static final Handle HANDLE_3 = Handle.ofString("345678");
  private static final Instant INSTANT_1 = Instant.ofEpochSecond(42);
  private static final Instant INSTANT_2 = Instant.ofEpochSecond(43);
  private static final Instant INSTANT_3 = Instant.ofEpochSecond(44);
  private static final Instant INSTANT_4 = Instant.ofEpochSecond(45);
  private static final Instant INSTANT_5 = Instant.ofEpochSecond(46);
  private static final Msisdn MSISDN_1 = Msisdn.ofString("+11111");
  private static final Msisdn MSISDN_2 = Msisdn.ofString("+22222");
  private static final Msisdn MSISDN_3 = Msisdn.ofString("+33333");
  private static final Vpi VPI_1 = Vpi.ofString("11111111111111111111111111111111");
  private static final Vpi VPI_2 = Vpi.ofString("22222222222222222222222222222222");
  private static final Vpi VPI_3 = Vpi.ofString("33333333333333333333333333333333");

  private static void checkInsertionFailure(InsertionFailure insertionFailure, InsertionFailureReason expectedInsertionFailureReason, String containsString) {
    Assertions.assertEquals(expectedInsertionFailureReason, insertionFailure.getInsertionFailureReason());
    Assertions.assertTrue(insertionFailure.getRuntimeException().getMessage().contains(containsString), insertionFailure.getRuntimeException().getMessage());
  }

  private static void checkPersistedWecuKey(PersistedWecuKey persistedWecuKey, WecuKey expectedWecuKey, WecuKeyId expectedWecuKeyId,
      Instant expectedCreated, Instant expectedLastUpdated) {
    Assertions.assertEquals(expectedCreated, persistedWecuKey.getCreated());
    Assertions.assertEquals(expectedWecuKey, persistedWecuKey.getWecuKey());
    Assertions.assertEquals(expectedWecuKeyId, persistedWecuKey.getWecuKeyId());
    Assertions.assertEquals(expectedLastUpdated, persistedWecuKey.getLastUpdated());
  }

  private static WecuKey createAndInsertWecuKey1(WecuKeyWriter wecuKeyWriter) {
    WecuKey wecuKey = createWecuKey1();
    insertWecuKey(wecuKeyWriter, wecuKey);
    return wecuKey;
  }

  private static WecuKey createWecuKey1() {
    return createWecuKeyBuilder(VPI_1, MSISDN_1, HANDLE_1)
        .build();
  }

  private static WecuKey createWecuKey2() {
    return createWecuKeyBuilder(VPI_2, MSISDN_2, HANDLE_2)
        .build();
  }

  private static WecuKey createWecuKey3() {
    return createWecuKeyBuilder(VPI_3, MSISDN_3, HANDLE_3)
        .build();
  }

  private static WecuKeyBuilder createWecuKeyBuilder(Vpi vpi, Msisdn msisdn, Handle handle) {
    return new WecuKeyBuilder()
        .setHandle(handle)
        .setMsisdn(msisdn)
        .setVpi(vpi);
  }

  private static WecuKeyWriter createWecuKeyWriter(H2DatabaseWrapper h2DatabaseWrapper) {
    return runFlywayAndCreateWecuKeyWriterFactory(h2DatabaseWrapper.getDataSource())
        .createReadCommitted();
  }

  private static WecuKeyWriter createWecuKeyWriterImpl(org.jdbi.v3.core.Handle jdbiHandle) {
    return WecuKeyWriterImpl.create(mockClock(), jdbiHandle, WecuKeyRowMapper.create());
  }

  private static WecuKeyId insertWecuKey(WecuKeyWriter wecuKeyWriter, WecuKey wecuKey) {
    return wecuKeyWriter.insertWecuKey(wecuKey).getRight();
  }

  private static Clock mockClock() {
    Clock clock = Mockito.mock(Clock.class);
    Mockito.when(clock.instant()).thenReturn(INSTANT_1, INSTANT_2, INSTANT_3, INSTANT_4, INSTANT_5).thenAnswer(invocationOnMock -> Instant.now());
    return clock;
  }

  private static org.jdbi.v3.core.Handle mockJdbiHandle() {
    org.jdbi.v3.core.Handle jdbiHandle = Mockito.mock(org.jdbi.v3.core.Handle.class);
    Mockito.when(jdbiHandle.isClosed()).thenThrow(new IllegalStateException("foo"));
    return jdbiHandle;
  }

  private static Jdbi runFlyway(DataSource dataSource) {
    WecuKeyFlywayExecutor.performDatabaseMigration(dataSource, "classpath:db/migration/wecu_key_test");
    return Jdbi.create(dataSource);
  }

  private static WecuKeyWriterFactory runFlywayAndCreateWecuKeyWriterFactory(DataSource dataSource) {
    Jdbi jdbi = runFlyway(dataSource);
    RowMapper<PersistedWecuKey> rowMapper = WecuKeyRowMapper.create();

    return WecuKeyWriterFactoryImpl.create(mockClock(), jdbi, rowMapper);
  }

  private static void verifyInsertWecuKeyFail(WecuKeyWriter wecuKeyWriter, WecuKey wecuKey,
      InsertionFailureReason expectedInsertionFailureReason, String containsString) {
    InsertionFailure insertionFailure = wecuKeyWriter.insertWecuKey(wecuKey).getLeft();
    checkInsertionFailure(insertionFailure, expectedInsertionFailureReason, containsString);
  }

  @Test
  void closeIsClosedExceptionTest() {
    org.jdbi.v3.core.Handle jdbiHandle = mockJdbiHandle();

    WecuKeyWriter wecuKeyWriter = createWecuKeyWriterImpl(jdbiHandle);
    AssertThrows.illegalStateException(wecuKeyWriter::close, "foo");

    Mockito.verify(jdbiHandle).close();
  }

  @Test
  void closeThrowsExceptionTest() {
    org.jdbi.v3.core.Handle jdbiHandle = mockJdbiHandle();
    Mockito.doThrow(new IllegalArgumentException("bar")).when(jdbiHandle).close();

    WecuKeyWriter wecuKeyWriter = createWecuKeyWriterImpl(jdbiHandle);
    IllegalStateException illegalStateException = AssertThrows.illegalStateException(wecuKeyWriter::close, "foo");

    Assertions.assertEquals("bar", illegalStateException.getSuppressed()[0].getMessage());
    Mockito.verify(jdbiHandle).close();
  }

  @Test
  void commitTransactionTest() {
    try (H2DatabaseWrapper h2DatabaseWrapper = H2DatabaseWrapper.createAndStart();
        WecuKeyWriter wecuKeyWriter = createWecuKeyWriter(h2DatabaseWrapper)) {
      AssertThrows.illegalStateException(wecuKeyWriter::commitTransaction, "transaction not started");

      wecuKeyWriter.startTransaction();
      wecuKeyWriter.rollbackTransaction();

      AssertThrows.illegalStateException(wecuKeyWriter::commitTransaction, "transaction not started");

      wecuKeyWriter.startTransaction();
      wecuKeyWriter.commitTransaction();

      AssertThrows.illegalStateException(wecuKeyWriter::commitTransaction, "transaction not started");

      wecuKeyWriter.startTransaction();
      wecuKeyWriter.close();

      AssertThrows.illegalStateException(wecuKeyWriter::commitTransaction, "could not commit a transaction on a closed handle");
    }
  }

  @Test
  void countAllDevicesTest() {
    try (H2DatabaseWrapper h2DatabaseWrapper = H2DatabaseWrapper.createAndStart();
        WecuKeyWriter wecuKeyWriter = createWecuKeyWriter(h2DatabaseWrapper)) {
      Assertions.assertEquals(0, wecuKeyWriter.countAllDevices());

      insertWecuKey(wecuKeyWriter, createWecuKey1());
      Assertions.assertEquals(1, wecuKeyWriter.countAllDevices());

      insertWecuKey(wecuKeyWriter, createWecuKey2());
      Assertions.assertEquals(2, wecuKeyWriter.countAllDevices());

      insertWecuKey(wecuKeyWriter, createWecuKey3());
      Assertions.assertEquals(3, wecuKeyWriter.countAllDevices());
    }
  }

  @Test
  void createInvalidTest() {
    final Clock clock = Mockito.mock(Clock.class);
    final org.jdbi.v3.core.Handle jdbiHandle = Mockito.mock(org.jdbi.v3.core.Handle.class);
    final RowMapper<PersistedWecuKey> rowMapper = Mockito.mock(RowMapper.class);

    AssertThrows.illegalArgumentException(() -> WecuKeyWriterImpl.create(null, jdbiHandle, rowMapper), "clock must not be null");
    AssertThrows.illegalArgumentException(() -> WecuKeyWriterImpl.create(clock, null, rowMapper), "jdbiHandle must not be null");
    AssertThrows.illegalArgumentException(() -> WecuKeyWriterImpl.create(clock, jdbiHandle, null), "rowMapper must not be null");
  }

  @Test
  void deleteWecuKeyByHandleTest() {
    try (H2DatabaseWrapper h2DatabaseWrapper = H2DatabaseWrapper.createAndStart();
        WecuKeyWriter wecuKeyWriter = createWecuKeyWriter(h2DatabaseWrapper)) {
      AssertThrows.illegalArgumentException(() -> wecuKeyWriter.deleteWecuKeyByHandle(null), "handle must not be null");

      Assertions.assertEquals(0, wecuKeyWriter.deleteWecuKeyByHandle(TestUtils.HANDLE));

      WecuKey wecuKey = createAndInsertWecuKey1(wecuKeyWriter);
      Handle handle = wecuKey.getHandle();

      Assertions.assertTrue(wecuKeyWriter.findWecuKeyByHandle(handle).isPresent());
      Assertions.assertEquals(1, wecuKeyWriter.deleteWecuKeyByHandle(handle));

      Assertions.assertFalse(wecuKeyWriter.findWecuKeyByHandle(handle).isPresent());
      Assertions.assertEquals(0, wecuKeyWriter.deleteWecuKeyByHandle(handle));
    }
  }

  @Test
  void deleteWecuKeyByIdTest() {
    try (H2DatabaseWrapper h2DatabaseWrapper = H2DatabaseWrapper.createAndStart();
        WecuKeyWriter wecuKeyWriter = createWecuKeyWriter(h2DatabaseWrapper)) {
      AssertThrows.illegalArgumentException(() -> wecuKeyWriter.deleteWecuKeyById(null), "wecuKeyId must not be null");

      WecuKeyId wecuKeyId = WecuKeyId.ofLong(1L);

      Assertions.assertEquals(0, wecuKeyWriter.deleteWecuKeyById(wecuKeyId));

      createAndInsertWecuKey1(wecuKeyWriter);

      Assertions.assertTrue(wecuKeyWriter.findWecuKeyByWecuKeyId(wecuKeyId).isPresent());
      Assertions.assertEquals(1, wecuKeyWriter.deleteWecuKeyById(wecuKeyId));

      Assertions.assertFalse(wecuKeyWriter.findWecuKeyByWecuKeyId(wecuKeyId).isPresent());
      Assertions.assertEquals(0, wecuKeyWriter.deleteWecuKeyById(wecuKeyId));
    }
  }

  @Test
  void deleteWecuKeyByVpiTest() {
    try (H2DatabaseWrapper h2DatabaseWrapper = H2DatabaseWrapper.createAndStart();
        WecuKeyWriter wecuKeyWriter = createWecuKeyWriter(h2DatabaseWrapper)) {
      AssertThrows.illegalArgumentException(() -> wecuKeyWriter.deleteWecuKeyByVpi(null), "vpi must not be null");

      Assertions.assertEquals(0, wecuKeyWriter.deleteWecuKeyByVpi(TestUtils.VPI));

      WecuKey wecuKey = createAndInsertWecuKey1(wecuKeyWriter);
      Vpi vpi = wecuKey.getVpi().get();

      Assertions.assertTrue(wecuKeyWriter.findWecuKeyByVpi(vpi).isPresent());
      Assertions.assertEquals(1, wecuKeyWriter.deleteWecuKeyByVpi(vpi));

      Assertions.assertTrue(wecuKeyWriter.findWecuKeyByVpi(vpi).isEmpty());
      Assertions.assertEquals(0, wecuKeyWriter.deleteWecuKeyByVpi(vpi));
    }
  }

  @Test
  void findWecuKeyByHandleTest() {
    try (H2DatabaseWrapper h2DatabaseWrapper = H2DatabaseWrapper.createAndStart();
        WecuKeyWriter wecuKeyWriter = createWecuKeyWriter(h2DatabaseWrapper)) {
      AssertThrows.illegalArgumentException(() -> wecuKeyWriter.findWecuKeyByHandle(null), "handle must not be null");

      Assertions.assertTrue(wecuKeyWriter.findWecuKeyByHandle(TestUtils.HANDLE).isEmpty());

      WecuKey wecuKey = createAndInsertWecuKey1(wecuKeyWriter);

      Optional<PersistedWecuKey> optional = wecuKeyWriter.findWecuKeyByHandle(wecuKey.getHandle());
      Assertions.assertEquals(wecuKey, optional.get().getWecuKey());
    }
  }

  @Test
  void findWecuKeyByMsisdnTest() {
    try (H2DatabaseWrapper h2DatabaseWrapper = H2DatabaseWrapper.createAndStart();
        WecuKeyWriter wecuKeyWriter = createWecuKeyWriter(h2DatabaseWrapper)) {
      AssertThrows.illegalArgumentException(() -> wecuKeyWriter.findWecuKeyByMsisdn(null), "msisdn must not be null");

      Assertions.assertTrue(wecuKeyWriter.findWecuKeyByMsisdn(TestUtils.MSISDN).isEmpty());

      WecuKey wecuKey = createAndInsertWecuKey1(wecuKeyWriter);

      Optional<PersistedWecuKey> optional = wecuKeyWriter.findWecuKeyByMsisdn(wecuKey.getMsisdn());

      Assertions.assertEquals(wecuKey, optional.get().getWecuKey());
    }
  }

  @Test
  void findWecuKeyByVpiTest() {
    try (H2DatabaseWrapper h2DatabaseWrapper = H2DatabaseWrapper.createAndStart();
        WecuKeyWriter wecuKeyWriter = createWecuKeyWriter(h2DatabaseWrapper)) {
      AssertThrows.illegalArgumentException(() -> wecuKeyWriter.findWecuKeyByVpi(null), "vpi must not be null");

      Assertions.assertTrue(wecuKeyWriter.findWecuKeyByVpi(TestUtils.VPI).isEmpty());

      WecuKey wecuKey = createAndInsertWecuKey1(wecuKeyWriter);

      Optional<PersistedWecuKey> optional = wecuKeyWriter.findWecuKeyByVpi(wecuKey.getVpi().get());
      Assertions.assertEquals(wecuKey, optional.get().getWecuKey());
    }
  }

  @Test
  void findWecuKeyByWecuKeyIdTest() {
    try (H2DatabaseWrapper h2DatabaseWrapper = H2DatabaseWrapper.createAndStart();
        WecuKeyWriter wecuKeyWriter = createWecuKeyWriter(h2DatabaseWrapper)) {
      AssertThrows.illegalArgumentException(() -> wecuKeyWriter.findWecuKeyByWecuKeyId(null), "wecuKeyId must not be null");

      Assertions.assertTrue(wecuKeyWriter.findWecuKeyByWecuKeyId(TestUtils.WECU_KEY_ID).isEmpty());

      WecuKey wecuKey = createWecuKey1();
      WecuKeyId wecuKeyId = insertWecuKey(wecuKeyWriter, wecuKey);

      Optional<PersistedWecuKey> optional = wecuKeyWriter.findWecuKeyByWecuKeyId(wecuKeyId);
      Assertions.assertEquals(wecuKey, optional.get().getWecuKey());
    }
  }

  @Test
  void insertWecuKeyTest() {
    try (H2DatabaseWrapper h2DatabaseWrapper = H2DatabaseWrapper.createAndStart();
        WecuKeyWriter wecuKeyWriter = createWecuKeyWriter(h2DatabaseWrapper)) {
      AssertThrows.illegalArgumentException(() -> wecuKeyWriter.insertWecuKey(null), "wecuKey must not be null");

      WecuKey wecuKey1 = createWecuKeyBuilder(VPI_1, MSISDN_1, HANDLE_1).build();
      WecuKeyId wecuKeyId1 = insertWecuKey(wecuKeyWriter, wecuKey1);
      Assertions.assertEquals(1, wecuKeyId1.toLong());
      checkPersistedWecuKey(wecuKeyWriter.findWecuKeyByWecuKeyId(wecuKeyId1).get(), wecuKey1, wecuKeyId1, INSTANT_1, INSTANT_1);

      WecuKey wecuKey2 = createWecuKeyBuilder(VPI_2, MSISDN_2, HANDLE_2).build();
      WecuKeyId wecuKeyId2 = insertWecuKey(wecuKeyWriter, wecuKey2);
      Assertions.assertEquals(2, wecuKeyId2.toLong());
      checkPersistedWecuKey(wecuKeyWriter.findWecuKeyByWecuKeyId(wecuKeyId2).get(), wecuKey2, wecuKeyId2, INSTANT_2, INSTANT_2);

      WecuKey wecuKey3 = createWecuKeyBuilder(VPI_3, MSISDN_3, HANDLE_3).build();
      WecuKeyId wecuKeyId3 = insertWecuKey(wecuKeyWriter, wecuKey3);
      Assertions.assertEquals(3, wecuKeyId3.toLong());
      checkPersistedWecuKey(wecuKeyWriter.findWecuKeyByWecuKeyId(wecuKeyId3).get(), wecuKey3, wecuKeyId3, INSTANT_3, INSTANT_3);

      verifyInsertWecuKeyFail(wecuKeyWriter, wecuKey1, InsertionFailureReason.DUPLICATE_KEY, "Unique index or primary key violation");
    }
  }

  @Test
  void rollbackTransactionTest() {
    try (H2DatabaseWrapper h2DatabaseWrapper = H2DatabaseWrapper.createAndStart();
        WecuKeyWriter wecuKeyWriter = createWecuKeyWriter(h2DatabaseWrapper)) {
      AssertThrows.illegalStateException(wecuKeyWriter::rollbackTransaction, "transaction not started");

      wecuKeyWriter.startTransaction();
      wecuKeyWriter.commitTransaction();

      AssertThrows.illegalStateException(wecuKeyWriter::rollbackTransaction, "transaction not started");

      wecuKeyWriter.startTransaction();
      wecuKeyWriter.rollbackTransaction();

      AssertThrows.illegalStateException(wecuKeyWriter::rollbackTransaction, "transaction not started");

      wecuKeyWriter.startTransaction();
      wecuKeyWriter.close();

      AssertThrows.illegalStateException(wecuKeyWriter::rollbackTransaction, "could not rollback a transaction on a closed handle");
    }
  }

  @Test
  void startTransactionTest() {
    try (H2DatabaseWrapper h2DatabaseWrapper = H2DatabaseWrapper.createAndStart();
        WecuKeyWriter wecuKeyWriter = createWecuKeyWriter(h2DatabaseWrapper)) {
      wecuKeyWriter.startTransaction();

      AssertThrows.illegalStateException(wecuKeyWriter::startTransaction, "transaction already started");

      wecuKeyWriter.rollbackTransaction();
      wecuKeyWriter.startTransaction();

      AssertThrows.illegalStateException(wecuKeyWriter::startTransaction, "transaction already started");

      wecuKeyWriter.commitTransaction();
      wecuKeyWriter.startTransaction();

      AssertThrows.illegalStateException(wecuKeyWriter::startTransaction, "transaction already started");

      wecuKeyWriter.close();

      AssertThrows.illegalStateException(wecuKeyWriter::startTransaction, "could not begin a transaction on a closed handle");
    }
  }
}
