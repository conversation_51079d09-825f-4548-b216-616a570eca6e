package com.wirelesscar.tcevce.wecu.device.info.database.model;

import java.util.Collections;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

class UniqueVehicleIdentifierTest {
  private static UniqueVehicleIdentifierBuilder createUniqueVehicleIdentifierBuilder() {
    return new UniqueVehicleIdentifierBuilder().addVpi(TestUtils.VPI);
  }

  @Test
  void getHandleTest() {
    Assertions.assertEquals(Collections.singleton(TestUtils.HANDLE), createUniqueVehicleIdentifierBuilder().addHandle(TestUtils.HANDLE).build().getHandles());
  }

  @Test
  void getIpTest() {
    Assertions.assertEquals(Collections.singleton(TestUtils.IPV4_ADDRESS),
        createUniqueVehicleIdentifierBuilder().addIpv4Address(TestUtils.IPV4_ADDRESS).build().getIpv4Addresses());
  }

  @Test
  void getMsisdnsTest() {
    Assertions.assertEquals(Collections.singleton(TestUtils.MSISDN), createUniqueVehicleIdentifierBuilder().addMsisdn(TestUtils.MSISDN).build().getMsisdns());
  }

  @Test
  void getSatelliteIdsTest() {
    Assertions.assertEquals(Collections.singleton(TestUtils.SATELLITE_ID),
        createUniqueVehicleIdentifierBuilder().addSatelliteId(TestUtils.SATELLITE_ID).build().getSatelliteIds());
  }

  @Test
  void getVpisTest() {
    Assertions.assertEquals(Collections.singleton(TestUtils.VPI), createUniqueVehicleIdentifierBuilder().build().getVpis());
  }

  @Test
  void invalidConstructorTest() {
    AssertThrows.illegalArgumentException(() -> new UniqueVehicleIdentifier(null), "uniqueVehicleIdentifierBuilder must not be null");
  }
}
