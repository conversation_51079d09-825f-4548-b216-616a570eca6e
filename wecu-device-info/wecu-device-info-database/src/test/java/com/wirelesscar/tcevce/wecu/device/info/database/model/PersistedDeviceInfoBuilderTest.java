package com.wirelesscar.tcevce.wecu.device.info.database.model;

import java.time.Instant;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

class PersistedDeviceInfoBuilderTest {
  private static void verifyEquals(PersistedDeviceInfoBuilder persistedDeviceInfoBuilder, PersistedDeviceInfo persistedDeviceInfo) {
    Assertions.assertSame(persistedDeviceInfoBuilder.getCreated(), persistedDeviceInfo.getCreated());
    Assertions.assertSame(persistedDeviceInfoBuilder.getDeviceInfo(), persistedDeviceInfo.getDeviceInfo());
    Assertions.assertSame(persistedDeviceInfoBuilder.getDeviceInfoId(), persistedDeviceInfo.getDeviceInfoId());
    Assertions.assertSame(persistedDeviceInfoBuilder.getLastUpdated(), persistedDeviceInfo.getLastUpdated());
  }

  @Test
  void buildTest() {
    PersistedDeviceInfoBuilder persistedDeviceInfoBuilder = new PersistedDeviceInfoBuilder();
    AssertThrows.illegalArgumentException(persistedDeviceInfoBuilder::build, "created must not be null");

    persistedDeviceInfoBuilder.setCreated(TestUtils.CREATED);
    AssertThrows.illegalArgumentException(persistedDeviceInfoBuilder::build, "deviceInfo must not be null");

    persistedDeviceInfoBuilder.setDeviceInfo(TestUtils.createDeviceInfo());
    AssertThrows.illegalArgumentException(persistedDeviceInfoBuilder::build, "deviceInfoId must not be null");

    persistedDeviceInfoBuilder.setDeviceInfoId(TestUtils.DEVICE_INFO_ID);
    AssertThrows.illegalArgumentException(persistedDeviceInfoBuilder::build, "lastUpdated must not be null");

    persistedDeviceInfoBuilder.setLastUpdated(TestUtils.LAST_UPDATED);
    verifyEquals(persistedDeviceInfoBuilder, persistedDeviceInfoBuilder.build());
  }

  @Test
  void createdTest() {
    PersistedDeviceInfoBuilder persistedDeviceInfoBuilder = new PersistedDeviceInfoBuilder();
    Assertions.assertNull(persistedDeviceInfoBuilder.getCreated());

    final Instant created = TestUtils.CREATED;
    persistedDeviceInfoBuilder.setCreated(created);
    Assertions.assertSame(created, persistedDeviceInfoBuilder.getCreated());

    AssertThrows.illegalArgumentException(() -> persistedDeviceInfoBuilder.setCreated(null), "created must not be null");
    Assertions.assertSame(created, persistedDeviceInfoBuilder.getCreated());
  }

  @Test
  void deviceInfoIdTest() {
    PersistedDeviceInfoBuilder persistedDeviceInfoBuilder = new PersistedDeviceInfoBuilder();
    Assertions.assertNull(persistedDeviceInfoBuilder.getDeviceInfoId());

    final DeviceInfoId deviceInfoId = TestUtils.DEVICE_INFO_ID;
    persistedDeviceInfoBuilder.setDeviceInfoId(deviceInfoId);
    Assertions.assertSame(deviceInfoId, persistedDeviceInfoBuilder.getDeviceInfoId());

    AssertThrows.illegalArgumentException(() -> persistedDeviceInfoBuilder.setDeviceInfoId(null), "deviceInfoId must not be null");
    Assertions.assertSame(deviceInfoId, persistedDeviceInfoBuilder.getDeviceInfoId());
  }

  @Test
  void deviceInfoTest() {
    PersistedDeviceInfoBuilder persistedDeviceInfoBuilder = new PersistedDeviceInfoBuilder();
    Assertions.assertNull(persistedDeviceInfoBuilder.getDeviceInfo());

    final DeviceInfo deviceInfo = TestUtils.createDeviceInfo();
    persistedDeviceInfoBuilder.setDeviceInfo(deviceInfo);
    Assertions.assertSame(deviceInfo, persistedDeviceInfoBuilder.getDeviceInfo());

    AssertThrows.illegalArgumentException(() -> persistedDeviceInfoBuilder.setDeviceInfo(null), "deviceInfo must not be null");
    Assertions.assertSame(deviceInfo, persistedDeviceInfoBuilder.getDeviceInfo());
  }

  @Test
  void lastUpdatedTest() {
    PersistedDeviceInfoBuilder persistedDeviceInfoBuilder = new PersistedDeviceInfoBuilder();
    Assertions.assertNull(persistedDeviceInfoBuilder.getLastUpdated());

    final Instant lastUpdated = TestUtils.LAST_UPDATED;
    persistedDeviceInfoBuilder.setLastUpdated(lastUpdated);
    Assertions.assertSame(lastUpdated, persistedDeviceInfoBuilder.getLastUpdated());

    AssertThrows.illegalArgumentException(() -> persistedDeviceInfoBuilder.setLastUpdated(null), "lastUpdated must not be null");
    Assertions.assertSame(lastUpdated, persistedDeviceInfoBuilder.getLastUpdated());
  }
}
