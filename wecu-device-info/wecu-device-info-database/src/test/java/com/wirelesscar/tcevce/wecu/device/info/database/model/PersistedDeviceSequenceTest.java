package com.wirelesscar.tcevce.wecu.device.info.database.model;

import java.time.Instant;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.vc.test.utils.lib.AssertThrows;
import com.volvo.tisp.vc.test.utils.lib.AssertUtils;

class PersistedDeviceSequenceTest {
  @Test
  void equalsAndHashcodeTest() {
    PersistedDeviceSequence persistedDeviceSequence = TestUtils.createPersistedDeviceSequence();
    AssertUtils.assertEqualsAndHashCode(persistedDeviceSequence, TestUtils.createPersistedDeviceSequence());

    PersistedDeviceSequence persistedWithNewCreated = TestUtils.createPersistedDeviceSequenceBuilder()
        .setCreated(Instant.now())
        .build();
    Assertions.assertNotEquals(persistedDeviceSequence, persistedWithNewCreated);

    DeviceSequence deviceSequenceWithNewDeviceInfo = TestUtils.createDeviceSequenceBuilder()
        .setDeviceInfoId(DeviceInfoId.ofLong(42))
        .build();
    PersistedDeviceSequence persistedWithNewDeviceSequence = TestUtils.createPersistedDeviceSequenceBuilder()
        .setDeviceSequence(deviceSequenceWithNewDeviceInfo)
        .build();
    Assertions.assertNotEquals(persistedDeviceSequence, persistedWithNewDeviceSequence);

    PersistedDeviceSequence persistedWithNewDeviceSequenceId = TestUtils.createPersistedDeviceSequenceBuilder()
        .setDeviceSequenceId(DeviceSequenceId.ofLong(42))
        .build();
    Assertions.assertNotEquals(persistedDeviceSequence, persistedWithNewDeviceSequenceId);

    PersistedDeviceSequence persistedWithNewLastUpdated = TestUtils.createPersistedDeviceSequenceBuilder()
        .setLastUpdated(Instant.now())
        .build();
    Assertions.assertNotEquals(persistedDeviceSequence, persistedWithNewLastUpdated);
  }

  @Test
  void getCreatedTest() {
    Assertions.assertSame(TestUtils.CREATED, TestUtils.createPersistedDeviceSequence().getCreated());
  }

  @Test
  void getDeviceSequenceIdTest() {
    Assertions.assertSame(TestUtils.DEVICE_SEQUENCE_ID, TestUtils.createPersistedDeviceSequence().getDeviceSequenceId());
  }

  @Test
  void getDeviceSequenceTest() {
    DeviceSequence deviceSequence = TestUtils.createDeviceSequence();
    PersistedDeviceSequence persistedDeviceSequence = TestUtils.createPersistedDeviceSequenceBuilder()
        .setDeviceSequence(deviceSequence)
        .build();

    Assertions.assertSame(deviceSequence, persistedDeviceSequence.getDeviceSequence());
  }

  @Test
  void getLastUpdatedTest() {
    Assertions.assertSame(TestUtils.LAST_UPDATED, TestUtils.createPersistedDeviceSequence().getLastUpdated());
  }

  @Test
  void invalidConstructorTest() {
    AssertThrows.illegalArgumentException(() -> new PersistedDeviceSequence(null), "persistedDeviceSequenceBuilder must not be null");
  }

  @Test
  void toStringTest() {
    String expectedString = "created=1970-01-01T00:00:01Z, deviceSequence={deviceInfoId=1, sequenceNumber=0}, deviceSequenceId=1, lastUpdated=1970-01-01T00:00:02Z";
    Assertions.assertEquals(expectedString, TestUtils.createPersistedDeviceSequence().toString());
  }
}
