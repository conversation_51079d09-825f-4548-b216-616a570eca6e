package com.wirelesscar.tcevce.wecu.device.info.database.model;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.vc.test.utils.lib.AssertThrows;
import com.volvo.tisp.vc.test.utils.lib.AssertUtils;

class HandleTest {
  @Test
  void calculateSizeTest() {
    Assertions.assertEquals(83, TestUtils.HANDLE.calculateSize());
  }

  @Test
  void equalsAndHashcodeTest() {
    AssertUtils.assertEqualsAndHashCode(TestUtils.HANDLE, Handle.ofString("123456"));

    Assertions.assertNotEquals(TestUtils.HANDLE, Handle.ofString("123457"));
  }

  @Test
  void ofStringInvalidTest() {
    AssertThrows.illegalArgumentException(() -> Handle.ofString(null), "handleString must not be null");

    String invalidMinLengthValue = "";
    AssertThrows.illegalArgumentException(() -> Handle.ofString(invalidMinLengthValue),
        "handleString must have a minimum length of 1: " + invalidMinLengthValue);

    String invalidMaxLengthValue = "1".repeat(201);
    AssertThrows.illegalArgumentException(() -> Handle.ofString(invalidMaxLengthValue),
        "handleString must have a maximum length of 200: " + invalidMaxLengthValue);
  }

  @Test
  void toStringTest() {
    Assertions.assertEquals("123456", TestUtils.HANDLE.toString());
  }
}
