package com.wirelesscar.tcevce.wecu.device.info.database.model;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.vc.test.utils.lib.AssertThrows;
import com.volvo.tisp.vc.test.utils.lib.AssertUtils;

class Ipv4PortTest {
  @Test
  void calculateSizeIpv4Test() {
    Assertions.assertEquals(20, TestUtils.IPV4_PORT.calculateSize());
  }

  @Test
  void equalsAndHashcodeTest() {
    Ipv4Port ipv4Port = Ipv4Port.ofInt(42);
    AssertUtils.assertEqualsAndHashCode(ipv4Port, Ipv4Port.ofInt(42));

    Assertions.assertNotEquals(ipv4Port, Ipv4Port.ofInt(43));
  }

  @Test
  void ofIntInvalidTest() {
    AssertThrows.illegalArgumentException(() -> Ipv4Port.ofInt(0), "ipv4PortValue must be positive: 0");
    AssertThrows.illegalArgumentException(() -> Ipv4Port.ofInt(65_536), "ipv4PortValue must not be greater than 65535: 65536");
  }

  @Test
  void toIntTest() {
    Assertions.assertEquals(42, Ipv4Port.ofInt(42).toInt());
  }

  @Test
  void toStringTest() {
    Assertions.assertEquals("42", Ipv4Port.ofInt(42).toString());
  }
}
