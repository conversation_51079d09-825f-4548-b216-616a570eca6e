package com.wirelesscar.tcevce.wecu.device.info.database.database;

import java.time.Clock;

import org.jdbi.v3.core.Handle;
import org.jdbi.v3.core.Jdbi;
import org.jdbi.v3.core.mapper.RowMapper;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import com.volvo.tisp.vc.test.utils.lib.AssertThrows;
import com.wirelesscar.tcevce.wecu.device.info.database.api.DeviceInfoReaderFactory;
import com.wirelesscar.tcevce.wecu.device.info.database.model.PersistedDeviceInfo;

class DeviceInfoReaderFactoryImplTest {
  private static Jdbi mockJdbi() {
    Jdbi jdbi = Mockito.mock(Jdbi.class);
    Mockito.when(jdbi.open()).thenReturn(Mockito.mock(Handle.class));
    return jdbi;
  }

  private static RowMapper<PersistedDeviceInfo> mockRowMapper() {
    return Mockito.mock(RowMapper.class);
  }

  @Test
  void createAlwaysNewTest() {
    DeviceInfoReaderFactory deviceInfoReaderFactory = DeviceInfoReaderFactoryImpl.create(Mockito.mock(Clock.class), mockJdbi(), mockRowMapper());

    Assertions.assertNotSame(deviceInfoReaderFactory.create(), deviceInfoReaderFactory.create());
  }

  @Test
  void createInvalidTest() {
    final Clock clock = Mockito.mock(Clock.class);
    final Jdbi jdbi = mockJdbi();
    final RowMapper<PersistedDeviceInfo> rowMapper = mockRowMapper();

    AssertThrows.illegalArgumentException(() -> DeviceInfoReaderFactoryImpl.create(null, jdbi, rowMapper), "clock must not be null");
    AssertThrows.illegalArgumentException(() -> DeviceInfoReaderFactoryImpl.create(clock, null, rowMapper), "jdbi must not be null");
    AssertThrows.illegalArgumentException(() -> DeviceInfoReaderFactoryImpl.create(clock, jdbi, null), "rowMapper must not be null");
  }
}
