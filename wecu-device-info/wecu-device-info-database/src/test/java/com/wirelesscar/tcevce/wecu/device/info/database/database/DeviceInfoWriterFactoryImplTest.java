package com.wirelesscar.tcevce.wecu.device.info.database.database;

import java.time.Clock;
import java.util.function.Function;

import org.jdbi.v3.core.Handle;
import org.jdbi.v3.core.Jdbi;
import org.jdbi.v3.core.mapper.RowMapper;
import org.jdbi.v3.core.transaction.TransactionIsolationLevel;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import com.volvo.tisp.vc.test.utils.lib.AssertThrows;
import com.wirelesscar.tcevce.wecu.device.info.database.api.DeviceInfoWriter;
import com.wirelesscar.tcevce.wecu.device.info.database.api.DeviceInfoWriterFactory;
import com.wirelesscar.tcevce.wecu.device.info.database.model.PersistedDeviceInfo;

class DeviceInfoWriterFactoryImplTest {
  private static Jdbi mockJdbi(Handle handle) {
    Jdbi jdbi = Mockito.mock(Jdbi.class);
    Mockito.when(jdbi.open()).thenReturn(handle);
    return jdbi;
  }

  private static RowMapper<PersistedDeviceInfo> mockRowMapper() {
    return Mockito.mock(RowMapper.class);
  }

  private static void verifyCreate(Function<DeviceInfoWriterFactory, DeviceInfoWriter> function, TransactionIsolationLevel expectedTransactionIsolationLevel) {
    final Handle handle = Mockito.mock(Handle.class);

    DeviceInfoWriterFactory deviceInfoWriterFactory = DeviceInfoWriterFactoryImpl.create(Mockito.mock(Clock.class), mockJdbi(handle), mockRowMapper());

    try (DeviceInfoWriter deviceInfoWriter = function.apply(deviceInfoWriterFactory)) {
      Mockito.verify(handle).setTransactionIsolation(expectedTransactionIsolationLevel);
      Mockito.verifyNoMoreInteractions(handle);
    }
  }

  @Test
  void createAlwaysNewTest() {
    DeviceInfoWriterFactory deviceInfoWriterFactory = DeviceInfoWriterFactoryImpl.create(Mockito.mock(Clock.class), mockJdbi(Mockito.mock(Handle.class)),
        mockRowMapper());

    Assertions.assertNotSame(deviceInfoWriterFactory.createReadCommitted(), deviceInfoWriterFactory.createReadCommitted());
  }

  @Test
  void createInvalidTest() {
    final Clock clock = Mockito.mock(Clock.class);
    final Jdbi jdbi = Mockito.mock(Jdbi.class);
    final RowMapper<PersistedDeviceInfo> rowMapper = mockRowMapper();

    AssertThrows.illegalArgumentException(() -> DeviceInfoWriterFactoryImpl.create(null, jdbi, rowMapper), "clock must not be null");
    AssertThrows.illegalArgumentException(() -> DeviceInfoWriterFactoryImpl.create(clock, null, rowMapper), "jdbi must not be null");
    AssertThrows.illegalArgumentException(() -> DeviceInfoWriterFactoryImpl.create(clock, jdbi, null), "rowMapper must not be null");
  }

  @Test
  void createReadCommittedTest() {
    verifyCreate(DeviceInfoWriterFactory::createReadCommitted, TransactionIsolationLevel.READ_COMMITTED);
  }

  @Test
  void createSerializableTest() {
    verifyCreate(DeviceInfoWriterFactory::createSerializable, TransactionIsolationLevel.SERIALIZABLE);
  }
}
