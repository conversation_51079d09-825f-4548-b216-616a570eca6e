package com.wirelesscar.tcevce.wecu.device.info.database.database;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.time.Instant;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import com.volvo.tisp.vc.test.utils.lib.AssertThrows;
import com.wirelesscar.tcevce.wecu.device.info.database.model.DeviceSequence;
import com.wirelesscar.tcevce.wecu.device.info.database.model.PersistedDeviceSequence;
import com.wirelesscar.tcevce.wecu.device.info.database.model.TestUtils;

class DeviceSequenceRowMapperTest {
  private static void checkDeviceSequence(DeviceSequence deviceSequence) {
    Assertions.assertEquals(TestUtils.DEVICE_INFO_ID, deviceSequence.getDeviceInfoId());
    Assertions.assertEquals(TestUtils.SEQUENCE_NUMBER, deviceSequence.getSequenceNumber());
  }

  private static void checkPersistedDeviceSequence(PersistedDeviceSequence persistedDeviceSequence) {
    Assertions.assertEquals(TestUtils.CREATED, persistedDeviceSequence.getCreated());
    Assertions.assertEquals(TestUtils.DEVICE_SEQUENCE_ID, persistedDeviceSequence.getDeviceSequenceId());
    Assertions.assertEquals(TestUtils.LAST_UPDATED, persistedDeviceSequence.getLastUpdated());

    checkDeviceSequence(persistedDeviceSequence.getDeviceSequence());
  }

  private static void mockGetLong(ResultSet resultSet, String columnLabel, long returnValue) throws SQLException {
    Mockito.when(resultSet.getLong(columnLabel)).thenReturn(returnValue);
  }

  private static void mockGetTimestamp(ResultSet resultSet, String columnLabel, Instant returnValue) throws SQLException {
    Mockito.when(resultSet.getTimestamp(columnLabel)).thenReturn(Timestamp.from(returnValue));
  }

  private static ResultSet mockResultSet() throws SQLException {
    ResultSet resultSet = Mockito.mock(ResultSet.class);
    mockGetLong(resultSet, "device_sequence_device_info_id", TestUtils.DEVICE_INFO_ID.toLong());
    mockGetLong(resultSet, "device_sequence_id", TestUtils.DEVICE_SEQUENCE_ID.toLong());
    mockGetTimestamp(resultSet, "device_sequence_created", TestUtils.CREATED);
    mockGetTimestamp(resultSet, "device_sequence_last_updated", TestUtils.LAST_UPDATED);

    return resultSet;
  }

  @Test
  void mapResultSetNullTest() {
    AssertThrows.illegalArgumentException(() -> DeviceSequenceRowMapper.INSTANCE.map(null, null), "resultSet must not be null");
  }

  @Test
  void mapTest() throws SQLException {
    ResultSet resultSet = mockResultSet();

    checkPersistedDeviceSequence(DeviceSequenceRowMapper.INSTANCE.map(resultSet, null));
  }
}
