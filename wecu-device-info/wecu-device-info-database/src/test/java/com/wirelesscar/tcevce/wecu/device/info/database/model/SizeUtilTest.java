package com.wirelesscar.tcevce.wecu.device.info.database.model;

import java.util.Optional;
import java.util.concurrent.TimeUnit;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.vc.main.utils.lib.type.ImmutableByteArray;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;
import com.volvo.tisp.vc.test.utils.lib.UtilClassVerifier;

class SizeUtilTest {
  @Test
  void estimateSizeOfImmutableByteArrayInvalidTest() {
    AssertThrows.illegalArgumentException(() -> SizeUtil.estimateSizeOfImmutableByteArray(null), "immutableByteArray must not be null");
  }

  @Test
  void estimateSizeOfImmutableByteArrayTest() {
    Assertions.assertEquals(48, SizeUtil.estimateSizeOfImmutableByteArray(ImmutableByteArray.EMPTY));
  }

  @Test
  void estimateSizeOfInstantTest() {
    Assertions.assertEquals(28, SizeUtil.estimateSizeOfInstant());
  }

  @Test
  void estimateSizeOfIpv4AddressInvalidTest() {
    AssertThrows.illegalArgumentException(() -> SizeUtil.estimateSizeOfIpv4Address(null), "ipv4Address must not be null");
  }

  @Test
  void estimateSizeOfIpv4AddressTest() {
    Assertions.assertEquals(84, SizeUtil.estimateSizeOfIpv4Address(TestUtils.IPV4_ADDRESS));
  }

  @Test
  void estimateSizeOfMsisdnInvalidTest() {
    AssertThrows.illegalArgumentException(() -> SizeUtil.estimateSizeOfMsisdn(null), "msisdn must not be null");
  }

  @Test
  void estimateSizeOfMsisdnTest() {
    Assertions.assertEquals(90, SizeUtil.estimateSizeOfMsisdn(TestUtils.MSISDN));
  }

  @Test
  void estimateSizeOfOptionalEnumInvalidTest() {
    AssertThrows.illegalArgumentException(() -> SizeUtil.estimateSizeOfOptionalEnum(null), "optional must not be null");
  }

  @Test
  void estimateSizeOfOptionalEnumTest() {
    Assertions.assertEquals(0, SizeUtil.estimateSizeOfOptionalEnum(Optional.empty()));
    Assertions.assertEquals(24, SizeUtil.estimateSizeOfOptionalEnum(Optional.of(TimeUnit.DAYS)));
  }

  @Test
  void estimateSizeOfOptionalInvalidTest() {
    AssertThrows.illegalArgumentException(() -> SizeUtil.estimateSizeOfOptional(null), "optional must not be null");
  }

  @Test
  void estimateSizeOfOptionalStringInvalidTest() {
    AssertThrows.illegalArgumentException(() -> SizeUtil.estimateSizeOfOptionalString(null), "optional must not be null");
  }

  @Test
  void estimateSizeOfOptionalStringTest() {
    Assertions.assertEquals(0, SizeUtil.estimateSizeOfOptionalString(Optional.empty()));
    Assertions.assertEquals(80, SizeUtil.estimateSizeOfOptionalString(Optional.of("foo")));
  }

  @Test
  void estimateSizeOfOptionalTest() {
    Assertions.assertEquals(0, SizeUtil.estimateSizeOfOptional(Optional.empty()));
    Assertions.assertEquals(32, SizeUtil.estimateSizeOfOptional(Optional.of(() -> 8)));
  }

  @Test
  void estimateSizeOfOptionalVpiInvalidTest() {
    AssertThrows.illegalArgumentException(() -> SizeUtil.estimateSizeOfOptionalVpi(null), "vpiOptional must not be null");
  }

  @Test
  void estimateSizeOfOptionalVpiTest() {
    Assertions.assertEquals(133, SizeUtil.estimateSizeOfOptionalVpi(Optional.of(TestUtils.VPI)));
  }

  @Test
  void estimateSizeOfStringInvalidTest() {
    AssertThrows.illegalArgumentException(() -> SizeUtil.estimateSizeOfString(null), "string must not be null");
  }

  @Test
  void estimateSizeOfStringTest() {
    Assertions.assertEquals(53, SizeUtil.estimateSizeOfString(""));
    Assertions.assertEquals(56, SizeUtil.estimateSizeOfString("foo"));
  }

  @Test
  void estimateSizeOfVpiTest() {
    Assertions.assertEquals(109, SizeUtil.estimateSizeOfVpi(TestUtils.VPI));
  }

  @Test
  void estimateSizeOfVpiInvalidTest() {
    AssertThrows.illegalArgumentException(() -> SizeUtil.estimateSizeOfVpi(null), "vpi must not be null");
  }

  @Test
  void verifyUtilClassTest() throws ReflectiveOperationException {
    UtilClassVerifier.verifyUtilClass(SizeUtil.class);
  }
}
