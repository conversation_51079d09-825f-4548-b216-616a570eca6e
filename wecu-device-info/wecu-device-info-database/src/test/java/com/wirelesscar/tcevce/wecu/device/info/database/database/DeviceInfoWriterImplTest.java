package com.wirelesscar.tcevce.wecu.device.info.database.database;

import java.time.Clock;
import java.time.Instant;
import java.util.List;
import java.util.Optional;

import javax.sql.DataSource;

import org.jdbi.v3.core.Jdbi;
import org.jdbi.v3.core.mapper.RowMapper;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import com.volvo.tisp.vc.common.dto.lib.vehicle.Imsi;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Ipv4Address;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Msisdn;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;
import com.wirelesscar.tcevce.wecu.device.info.database.api.DeviceInfoWriter;
import com.wirelesscar.tcevce.wecu.device.info.database.api.DeviceInfoWriterFactory;
import com.wirelesscar.tcevce.wecu.device.info.database.model.DeviceInfo;
import com.wirelesscar.tcevce.wecu.device.info.database.model.DeviceInfoBuilder;
import com.wirelesscar.tcevce.wecu.device.info.database.model.DeviceInfoId;
import com.wirelesscar.tcevce.wecu.device.info.database.model.DeviceSequence;
import com.wirelesscar.tcevce.wecu.device.info.database.model.DeviceSequenceBuilder;
import com.wirelesscar.tcevce.wecu.device.info.database.model.DeviceSequenceId;
import com.wirelesscar.tcevce.wecu.device.info.database.model.Handle;
import com.wirelesscar.tcevce.wecu.device.info.database.model.InsertionFailure;
import com.wirelesscar.tcevce.wecu.device.info.database.model.InsertionFailureReason;
import com.wirelesscar.tcevce.wecu.device.info.database.model.PersistedDeviceInfo;
import com.wirelesscar.tcevce.wecu.device.info.database.model.PersistedDeviceSequence;
import com.wirelesscar.tcevce.wecu.device.info.database.model.SatelliteId;
import com.wirelesscar.tcevce.wecu.device.info.database.model.SequenceNumber;
import com.wirelesscar.tcevce.wecu.device.info.database.model.SimInfo;
import com.wirelesscar.tcevce.wecu.device.info.database.model.TestUtils;
import com.wirelesscar.tcevce.wecu.device.info.database.model.UniqueVehicleIdentifier;
import com.wirelesscar.tcevce.wecu.device.info.database.model.UniqueVehicleIdentifierBuilder;

class DeviceInfoWriterImplTest {
  private static final Handle HANDLE_1 = Handle.ofString("123456");
  private static final Handle HANDLE_2 = Handle.ofString("234567");
  private static final Handle HANDLE_3 = Handle.ofString("345678");
  private static final Imsi IMSI = Imsi.ofLong(1L);
  private static final Instant INSTANT_1 = Instant.ofEpochSecond(42);
  private static final Instant INSTANT_2 = Instant.ofEpochSecond(43);
  private static final Instant INSTANT_3 = Instant.ofEpochSecond(44);
  private static final Instant INSTANT_4 = Instant.ofEpochSecond(45);
  private static final Instant INSTANT_5 = Instant.ofEpochSecond(46);
  private static final Ipv4Address IPV4_ADDRESS_1 = Ipv4Address.ofString("*******");
  private static final Ipv4Address IPV4_ADDRESS_2 = Ipv4Address.ofString("*******");
  private static final Ipv4Address IPV4_ADDRESS_3 = Ipv4Address.ofString("*******");
  private static final Msisdn MSISDN_1 = Msisdn.ofString("+11111");
  private static final Msisdn MSISDN_2 = Msisdn.ofString("+22222");
  private static final Msisdn MSISDN_3 = Msisdn.ofString("+33333");
  private static final SatelliteId SATELLITE_ID_1 = SatelliteId.ofString("HQ1234567890x1");
  private static final SatelliteId SATELLITE_ID_2 = SatelliteId.ofString("HQ1234567891x1");
  private static final SatelliteId SATELLITE_ID_3 = SatelliteId.ofString("HQ1234567892x1");
  private static final Vpi VPI_1 = Vpi.ofString("11111111111111111111111111111111");
  private static final Vpi VPI_2 = Vpi.ofString("22222222222222222222222222222222");
  private static final Vpi VPI_3 = Vpi.ofString("33333333333333333333333333333333");

  private static void checkInsertionFailure(InsertionFailure insertionFailure, InsertionFailureReason expectedInsertionFailureReason, String containsString) {
    Assertions.assertEquals(expectedInsertionFailureReason, insertionFailure.getInsertionFailureReason());
    Assertions.assertTrue(insertionFailure.getRuntimeException().getMessage().contains(containsString), insertionFailure.getRuntimeException().getMessage());
  }

  private static void checkPersistedDeviceInfo(PersistedDeviceInfo persistedDeviceInfo, DeviceInfo expectedDeviceInfo, DeviceInfoId expectedDeviceInfoId,
      Instant expectedCreated, Instant expectedLastUpdated) {
    Assertions.assertEquals(expectedCreated, persistedDeviceInfo.getCreated());
    Assertions.assertEquals(expectedDeviceInfo, persistedDeviceInfo.getDeviceInfo());
    Assertions.assertEquals(expectedDeviceInfoId, persistedDeviceInfo.getDeviceInfoId());
    Assertions.assertEquals(expectedLastUpdated, persistedDeviceInfo.getLastUpdated());
  }

  private static void checkPersistedDeviceSequence(PersistedDeviceSequence persistedDeviceSequence, DeviceSequence expectedDeviceSequence,
      DeviceSequenceId expectedDeviceSequenceId, Instant expectedInstant) {
    Assertions.assertEquals(expectedInstant, persistedDeviceSequence.getCreated());
    Assertions.assertEquals(expectedDeviceSequence, persistedDeviceSequence.getDeviceSequence());
    Assertions.assertEquals(expectedDeviceSequenceId, persistedDeviceSequence.getDeviceSequenceId());
    Assertions.assertEquals(expectedInstant, persistedDeviceSequence.getLastUpdated());
  }

  private static DeviceInfo createAndInsertDeviceInfo1(DeviceInfoWriter deviceInfoWriter) {
    DeviceInfo deviceInfo = createDeviceInfo1();
    insertDeviceInfo(deviceInfoWriter, deviceInfo);
    return deviceInfo;
  }

  private static DeviceSequenceId createAndInsertDeviceSequence(DeviceInfoWriter deviceInfoWriter, DeviceInfoId deviceInfoId) {
    return insertDeviceSequence(deviceInfoWriter, createDeviceSequence(deviceInfoId));
  }

  private static DeviceInfo createDeviceInfo1() {
    return createDeviceInfoBuilder(VPI_1, HANDLE_1, IMSI)
        .setSatelliteId(Optional.of(SATELLITE_ID_1))
        .setSimInfo(Optional.of(createSimInfo(MSISDN_1, IPV4_ADDRESS_1)))
        .build();
  }

  private static DeviceInfo createDeviceInfo2() {
    return createDeviceInfoBuilder(VPI_2, HANDLE_2, IMSI)
        .setSatelliteId(Optional.of(SATELLITE_ID_2))
        .setSimInfo(Optional.of(createSimInfo(MSISDN_2, IPV4_ADDRESS_2)))
        .build();
  }

  private static DeviceInfo createDeviceInfo3() {
    return createDeviceInfoBuilder(VPI_3, HANDLE_3, IMSI)
        .setSatelliteId(Optional.of(SATELLITE_ID_3))
        .setSimInfo(Optional.of(createSimInfo(MSISDN_3, IPV4_ADDRESS_3)))
        .build();
  }

  private static DeviceInfoBuilder createDeviceInfoBuilder(Vpi vpi, Handle handle, Imsi imsi) {
    return TestUtils.createDeviceInfoBuilder().setHandle(handle).setVpi(Optional.of(vpi));
  }

  private static DeviceInfoWriter createDeviceInfoWriter(H2DatabaseWrapper h2DatabaseWrapper) {
    return runFlywayAndCreateDeviceInfoWriterFactory(h2DatabaseWrapper.getDataSource())
        .createReadCommitted();
  }

  private static DeviceInfoWriter createDeviceInfoWriterImpl(org.jdbi.v3.core.Handle jdbiHandle) {
    return DeviceInfoWriterImpl.create(mockClock(), jdbiHandle, DeviceInfoRowMapper.create());
  }

  private static DeviceSequence createDeviceSequence(DeviceInfoId deviceInfoId) {
    return TestUtils.createDeviceSequenceBuilder().setDeviceInfoId(deviceInfoId).build();
  }

  private static SimInfo createSimInfo(Msisdn msisdn, Ipv4Address ipv4Address) {
    return TestUtils.createSimInfoBuilder().setIpv4Address(ipv4Address).setMsisdn(msisdn).build();
  }

  private static UniqueVehicleIdentifier createUniqueVehicleIdentifier(DeviceInfo deviceInfo) {
    return new UniqueVehicleIdentifierBuilder()
        .addHandle(deviceInfo.getHandle())
        .addIpv4Address(deviceInfo.getSimInfo().get().getIpv4Address())
        .addMsisdn(deviceInfo.getSimInfo().get().getMsisdn())
        .addSatelliteId(deviceInfo.getSatelliteId().get())
        .addVpi(deviceInfo.getVpi().get())
        .build();
  }

  private static void findAndLockMultipleDevices(DeviceInfoWriter deviceInfoWriter, UniqueVehicleIdentifier uniqueVehicleIdentifier, DeviceInfo deviceInfo1,
      DeviceInfo deviceInfo2) {
    List<PersistedDeviceInfo> persistedDeviceInfos = deviceInfoWriter.findAndLockByDeviceInfoUniqueKeys(uniqueVehicleIdentifier);
    Assertions.assertEquals(2, persistedDeviceInfos.size());
    Assertions.assertEquals(deviceInfo1, persistedDeviceInfos.get(0).getDeviceInfo());
    Assertions.assertEquals(deviceInfo2, persistedDeviceInfos.get(1).getDeviceInfo());
  }

  private static void findAndLockSingleDevice(DeviceInfoWriter deviceInfoWriter, UniqueVehicleIdentifier uniqueVehicleIdentifier,
      DeviceInfo expectedDeviceInfo) {
    List<PersistedDeviceInfo> persistedDeviceInfos = deviceInfoWriter.findAndLockByDeviceInfoUniqueKeys(uniqueVehicleIdentifier);
    Assertions.assertEquals(1, persistedDeviceInfos.size());
    Assertions.assertEquals(expectedDeviceInfo, persistedDeviceInfos.get(0).getDeviceInfo());
  }

  private static byte getDeviceSequenceNumberValue(DeviceInfoWriter deviceInfoWriter, DeviceInfoId deviceInfoId) {
    return deviceInfoWriter
        .findDeviceSequenceByDeviceInfoId(deviceInfoId)
        .get()
        .getDeviceSequence()
        .getSequenceNumber()
        .toByte();
  }

  private static DeviceInfoId insertDeviceInfo(DeviceInfoWriter deviceInfoWriter, DeviceInfo deviceInfo) {
    return deviceInfoWriter.insertDeviceInfo(deviceInfo).getRight();
  }

  private static void insertDeviceInfos(DeviceInfoWriter deviceInfoWriter, Iterable<DeviceInfo> deviceInfos) {
    for (DeviceInfo deviceInfo : deviceInfos) {
      insertDeviceInfo(deviceInfoWriter, deviceInfo);
    }
  }

  private static DeviceSequenceId insertDeviceSequence(DeviceInfoWriter deviceInfoWriter, DeviceSequence deviceSequence) {
    return deviceInfoWriter.insertDeviceSequence(deviceSequence).getRight();
  }

  private static Clock mockClock() {
    Clock clock = Mockito.mock(Clock.class);
    Mockito.when(clock.instant()).thenReturn(INSTANT_1, INSTANT_2, INSTANT_3, INSTANT_4, INSTANT_5).thenAnswer(invocationOnMock -> Instant.now());
    return clock;
  }

  private static org.jdbi.v3.core.Handle mockJdbiHandle() {
    org.jdbi.v3.core.Handle jdbiHandle = Mockito.mock(org.jdbi.v3.core.Handle.class);
    Mockito.when(jdbiHandle.isClosed()).thenThrow(new IllegalStateException("foo"));
    return jdbiHandle;
  }

  private static Jdbi runFlyway(DataSource dataSource) {
    DeviceServiceFlywayExecutor.performDatabaseMigration(dataSource);
    return Jdbi.create(dataSource);
  }

  private static DeviceInfoWriterFactory runFlywayAndCreateDeviceInfoWriterFactory(DataSource dataSource) {
    Jdbi jdbi = runFlyway(dataSource);
    RowMapper<PersistedDeviceInfo> rowMapper = DeviceInfoRowMapper.create();

    return DeviceInfoWriterFactoryImpl.create(mockClock(), jdbi, rowMapper);
  }

  private static void verifyDeviceInfoUpdate(DeviceInfoId deviceInfoId, Instant expectedLastUpdated, DeviceInfo expectedDeviceInfo,
      DeviceInfoWriter deviceInfoWriter) {
    PersistedDeviceInfo persistedDeviceInfo = deviceInfoWriter.findDeviceInfoByHandle(expectedDeviceInfo.getHandle()).get();
    Assertions.assertEquals(INSTANT_1, persistedDeviceInfo.getCreated());
    Assertions.assertEquals(expectedDeviceInfo, persistedDeviceInfo.getDeviceInfo());
    Assertions.assertEquals(deviceInfoId, persistedDeviceInfo.getDeviceInfoId());
    Assertions.assertEquals(expectedLastUpdated, persistedDeviceInfo.getLastUpdated());
  }

  private static void verifyInsertDeviceInfoFail(DeviceInfoWriter deviceInfoWriter, DeviceInfo deviceInfo,
      InsertionFailureReason expectedInsertionFailureReason, String containsString) {
    InsertionFailure insertionFailure = deviceInfoWriter.insertDeviceInfo(deviceInfo).getLeft();
    checkInsertionFailure(insertionFailure, expectedInsertionFailureReason, containsString);
  }

  private static void verifyInsertDeviceSequenceFail(DeviceInfoWriter deviceInfoWriter, DeviceInfoId deviceInfoId,
      InsertionFailureReason expectedInsertionFailureReason, String containsString) {
    InsertionFailure insertionFailure = deviceInfoWriter.insertDeviceSequence(createDeviceSequence(deviceInfoId)).getLeft();
    checkInsertionFailure(insertionFailure, expectedInsertionFailureReason, containsString);
  }

  @Test
  void closeIsClosedExceptionTest() {
    org.jdbi.v3.core.Handle jdbiHandle = mockJdbiHandle();

    DeviceInfoWriter deviceInfoWriter = createDeviceInfoWriterImpl(jdbiHandle);
    AssertThrows.illegalStateException(deviceInfoWriter::close, "foo");

    Mockito.verify(jdbiHandle).close();
  }

  @Test
  void closeThrowsExceptionTest() {
    org.jdbi.v3.core.Handle jdbiHandle = mockJdbiHandle();
    Mockito.doThrow(new IllegalArgumentException("bar")).when(jdbiHandle).close();

    DeviceInfoWriter deviceInfoWriter = createDeviceInfoWriterImpl(jdbiHandle);
    IllegalStateException illegalStateException = AssertThrows.illegalStateException(deviceInfoWriter::close, "foo");

    Assertions.assertEquals("bar", illegalStateException.getSuppressed()[0].getMessage());
    Mockito.verify(jdbiHandle).close();
  }

  @Test
  void commitTransactionTest() {
    try (H2DatabaseWrapper h2DatabaseWrapper = H2DatabaseWrapper.createAndStart();
        DeviceInfoWriter deviceInfoWriter = createDeviceInfoWriter(h2DatabaseWrapper)) {
      AssertThrows.illegalStateException(deviceInfoWriter::commitTransaction, "transaction not started");

      deviceInfoWriter.startTransaction();
      deviceInfoWriter.rollbackTransaction();

      AssertThrows.illegalStateException(deviceInfoWriter::commitTransaction, "transaction not started");

      deviceInfoWriter.startTransaction();
      deviceInfoWriter.commitTransaction();

      AssertThrows.illegalStateException(deviceInfoWriter::commitTransaction, "transaction not started");

      deviceInfoWriter.startTransaction();
      deviceInfoWriter.close();

      AssertThrows.illegalStateException(deviceInfoWriter::commitTransaction, "could not commit a transaction on a closed handle");
    }
  }

  @Test
  void countAllDevicesTest() {
    try (H2DatabaseWrapper h2DatabaseWrapper = H2DatabaseWrapper.createAndStart();
        DeviceInfoWriter deviceInfoWriter = createDeviceInfoWriter(h2DatabaseWrapper)) {
      Assertions.assertEquals(0, deviceInfoWriter.countAllDevices());

      insertDeviceInfo(deviceInfoWriter, createDeviceInfo1());
      Assertions.assertEquals(1, deviceInfoWriter.countAllDevices());

      insertDeviceInfo(deviceInfoWriter, createDeviceInfo2());
      Assertions.assertEquals(2, deviceInfoWriter.countAllDevices());

      insertDeviceInfo(deviceInfoWriter, createDeviceInfo3());
      Assertions.assertEquals(3, deviceInfoWriter.countAllDevices());
    }
  }

  @Test
  void createInvalidTest() {
    final Clock clock = Mockito.mock(Clock.class);
    final org.jdbi.v3.core.Handle jdbiHandle = Mockito.mock(org.jdbi.v3.core.Handle.class);
    final RowMapper<PersistedDeviceInfo> rowMapper = Mockito.mock(RowMapper.class);

    AssertThrows.illegalArgumentException(() -> DeviceInfoWriterImpl.create(null, jdbiHandle, rowMapper), "clock must not be null");
    AssertThrows.illegalArgumentException(() -> DeviceInfoWriterImpl.create(clock, null, rowMapper), "jdbiHandle must not be null");
    AssertThrows.illegalArgumentException(() -> DeviceInfoWriterImpl.create(clock, jdbiHandle, null), "rowMapper must not be null");
  }

  @Test
  void deleteDeviceInfoByHandleTest() {
    try (H2DatabaseWrapper h2DatabaseWrapper = H2DatabaseWrapper.createAndStart();
        DeviceInfoWriter deviceInfoWriter = createDeviceInfoWriter(h2DatabaseWrapper)) {
      AssertThrows.illegalArgumentException(() -> deviceInfoWriter.deleteDeviceInfoByHandle(null), "handle must not be null");

      DeviceInfo deviceInfo = TestUtils.createDeviceInfo();

      Assertions.assertEquals(0, deviceInfoWriter.deleteDeviceInfoByHandle(deviceInfo.getHandle()));
      insertDeviceInfo(deviceInfoWriter, deviceInfo);
      Assertions.assertEquals(1, deviceInfoWriter.deleteDeviceInfoByHandle(deviceInfo.getHandle()));
      Assertions.assertEquals(0, deviceInfoWriter.deleteDeviceInfoByHandle(deviceInfo.getHandle()));
    }
  }

  @Test
  void deleteDeviceInfoByIdTest() {
    try (H2DatabaseWrapper h2DatabaseWrapper = H2DatabaseWrapper.createAndStart();
        DeviceInfoWriter deviceInfoWriter = createDeviceInfoWriter(h2DatabaseWrapper)) {
      AssertThrows.illegalArgumentException(() -> deviceInfoWriter.deleteDeviceInfoById(null), "deviceInfoId must not be null");

      DeviceInfoId deviceInfoId = DeviceInfoId.ofLong(1L);

      Assertions.assertEquals(0, deviceInfoWriter.deleteDeviceInfoById(deviceInfoId));

      createAndInsertDeviceInfo1(deviceInfoWriter);

      Assertions.assertTrue(deviceInfoWriter.findDeviceInfoByDeviceInfoId(deviceInfoId).isPresent());
      Assertions.assertEquals(1, deviceInfoWriter.deleteDeviceInfoById(deviceInfoId));

      Assertions.assertFalse(deviceInfoWriter.findDeviceInfoByDeviceInfoId(deviceInfoId).isPresent());
      Assertions.assertEquals(0, deviceInfoWriter.deleteDeviceInfoById(deviceInfoId));
    }
  }

  @Test
  void deleteDeviceInfoByVpiTest() {
    try (H2DatabaseWrapper h2DatabaseWrapper = H2DatabaseWrapper.createAndStart();
        DeviceInfoWriter deviceInfoWriter = createDeviceInfoWriter(h2DatabaseWrapper)) {
      AssertThrows.illegalArgumentException(() -> deviceInfoWriter.deleteDeviceInfoByVpi(null), "vpi must not be null");

      Assertions.assertEquals(0, deviceInfoWriter.deleteDeviceInfoByVpi(TestUtils.VPI));

      DeviceInfo deviceInfo = createAndInsertDeviceInfo1(deviceInfoWriter);
      Assertions.assertTrue(deviceInfoWriter.findDeviceInfoByVpi(deviceInfo.getVpi().get()).isPresent());

      Assertions.assertEquals(1, deviceInfoWriter.deleteDeviceInfoByVpi(deviceInfo.getVpi().get()));

      Assertions.assertTrue(deviceInfoWriter.findDeviceInfoByVpi(deviceInfo.getVpi().get()).isEmpty());

      Assertions.assertEquals(0, deviceInfoWriter.deleteDeviceInfoByVpi(deviceInfo.getVpi().get()));
    }
  }

  @Test
  void deleteDeviceSequenceByDeviceInfoIdTest() {
    try (H2DatabaseWrapper h2DatabaseWrapper = H2DatabaseWrapper.createAndStart();
        DeviceInfoWriter deviceInfoWriter = createDeviceInfoWriter(h2DatabaseWrapper)) {
      AssertThrows.illegalArgumentException(() -> deviceInfoWriter.deleteDeviceSequenceByDeviceInfoId(null), "deviceInfoId must not be null");

      Assertions.assertEquals(0, deviceInfoWriter.deleteDeviceSequenceByDeviceInfoId(TestUtils.DEVICE_INFO_ID));

      DeviceInfoId deviceInfoId = insertDeviceInfo(deviceInfoWriter, createDeviceInfo1());
      createAndInsertDeviceSequence(deviceInfoWriter, deviceInfoId);
      Assertions.assertTrue(deviceInfoWriter.findDeviceSequenceByDeviceInfoId(deviceInfoId).isPresent());

      Assertions.assertEquals(1, deviceInfoWriter.deleteDeviceSequenceByDeviceInfoId(deviceInfoId));

      Assertions.assertTrue(deviceInfoWriter.findDeviceSequenceByDeviceInfoId(deviceInfoId).isEmpty());

      Assertions.assertEquals(0, deviceInfoWriter.deleteDeviceSequenceByDeviceInfoId(deviceInfoId));
    }
  }

  @Test
  void deleteDeviceSequenceByIdTest() {
    try (H2DatabaseWrapper h2DatabaseWrapper = H2DatabaseWrapper.createAndStart();
        DeviceInfoWriter deviceInfoWriter = createDeviceInfoWriter(h2DatabaseWrapper)) {
      AssertThrows.illegalArgumentException(() -> deviceInfoWriter.deleteDeviceSequenceById(null), "deviceSequenceId must not be null");

      Assertions.assertEquals(0, deviceInfoWriter.deleteDeviceSequenceById(TestUtils.DEVICE_SEQUENCE_ID));

      DeviceInfoId deviceInfoId = insertDeviceInfo(deviceInfoWriter, createDeviceInfo1());
      DeviceSequenceId deviceSequenceId = createAndInsertDeviceSequence(deviceInfoWriter, deviceInfoId);
      Assertions.assertTrue(deviceInfoWriter.findDeviceSequenceByDeviceInfoId(deviceInfoId).isPresent());

      Assertions.assertEquals(1, deviceInfoWriter.deleteDeviceSequenceById(deviceSequenceId));

      Assertions.assertTrue(deviceInfoWriter.findDeviceSequenceByDeviceInfoId(deviceInfoId).isEmpty());

      Assertions.assertEquals(0, deviceInfoWriter.deleteDeviceSequenceById(deviceSequenceId));
    }
  }

  @Test
  void findAndLockByDeviceInfoIdTest() {
    try (H2DatabaseWrapper h2DatabaseWrapper = H2DatabaseWrapper.createAndStart();
        DeviceInfoWriter deviceInfoWriter = createDeviceInfoWriter(h2DatabaseWrapper)) {
      AssertThrows.illegalArgumentException(() -> deviceInfoWriter.findAndLockByDeviceInfoId(null), "deviceInfoId must not be null");

      Assertions.assertTrue(deviceInfoWriter.findAndLockByDeviceInfoId(TestUtils.DEVICE_INFO_ID).isEmpty());

      DeviceInfo deviceInfo = createDeviceInfo1();
      DeviceInfoId deviceInfoId = insertDeviceInfo(deviceInfoWriter, deviceInfo);

      Assertions.assertEquals(deviceInfo, deviceInfoWriter.findAndLockByDeviceInfoId(deviceInfoId).get().getDeviceInfo());
    }
  }

  @Test
  void findAndLockByDeviceInfoUniqueKeysTest() {
    try (H2DatabaseWrapper h2DatabaseWrapper = H2DatabaseWrapper.createAndStart();
        DeviceInfoWriter deviceInfoWriter = createDeviceInfoWriter(h2DatabaseWrapper)) {
      AssertThrows.illegalArgumentException(() -> deviceInfoWriter.findAndLockByDeviceInfoUniqueKeys(null), "uniqueVehicleIdentifier must not be null");

      DeviceInfo deviceInfo1 = createDeviceInfo1();
      DeviceInfo deviceInfo2 = createDeviceInfo2();
      DeviceInfo deviceInfo3 = createDeviceInfo3();

      UniqueVehicleIdentifier uniqueVehicleIdentifier1 = createUniqueVehicleIdentifier(deviceInfo1);
      UniqueVehicleIdentifier uniqueVehicleIdentifier2 = createUniqueVehicleIdentifier(deviceInfo2);

      Assertions.assertEquals(0, deviceInfoWriter.findAndLockByDeviceInfoUniqueKeys(uniqueVehicleIdentifier1).size());
      insertDeviceInfos(deviceInfoWriter, List.of(deviceInfo1, deviceInfo2));
      Assertions.assertEquals(0, deviceInfoWriter.findAndLockByDeviceInfoUniqueKeys(createUniqueVehicleIdentifier(deviceInfo3)).size());

      findAndLockSingleDevice(deviceInfoWriter, uniqueVehicleIdentifier1, deviceInfo1);
      findAndLockSingleDevice(deviceInfoWriter, uniqueVehicleIdentifier2, deviceInfo2);

      UniqueVehicleIdentifier uniqueVehicleIdentifier3 = createUniqueVehicleIdentifier(
          DeviceInfoBuilder.from(deviceInfo3).setHandle(deviceInfo1.getHandle()).build()
      );
      findAndLockSingleDevice(deviceInfoWriter, uniqueVehicleIdentifier3, deviceInfo1);

      UniqueVehicleIdentifier uniqueVehicleIdentifier4 = createUniqueVehicleIdentifier(
          DeviceInfoBuilder.from(deviceInfo3).setVpi(deviceInfo2.getVpi()).build()
      );
      findAndLockSingleDevice(deviceInfoWriter, uniqueVehicleIdentifier4, deviceInfo2);

      UniqueVehicleIdentifier uniqueVehicleIdentifier5 = createUniqueVehicleIdentifier(
          DeviceInfoBuilder.from(deviceInfo3).setSatelliteId(deviceInfo2.getSatelliteId()).build()
      );
      findAndLockSingleDevice(deviceInfoWriter, uniqueVehicleIdentifier5, deviceInfo2);

      UniqueVehicleIdentifier uniqueVehicleIdentifier6 = createUniqueVehicleIdentifier(
          DeviceInfoBuilder.from(deviceInfo3).setHandle(deviceInfo1.getHandle()).setVpi(deviceInfo2.getVpi()).build()
      );
      findAndLockMultipleDevices(deviceInfoWriter, uniqueVehicleIdentifier6, deviceInfo1, deviceInfo2);
    }
  }

  @Test
  void findDeviceInfoByDeviceInfoIdTest() {
    try (H2DatabaseWrapper h2DatabaseWrapper = H2DatabaseWrapper.createAndStart();
        DeviceInfoWriter deviceInfoWriter = createDeviceInfoWriter(h2DatabaseWrapper)) {
      AssertThrows.illegalArgumentException(() -> deviceInfoWriter.findDeviceInfoByDeviceInfoId(null), "deviceInfoId must not be null");

      Assertions.assertTrue(deviceInfoWriter.findDeviceInfoByDeviceInfoId(TestUtils.DEVICE_INFO_ID).isEmpty());

      DeviceInfo deviceInfo = createDeviceInfo1();
      DeviceInfoId deviceInfoId = insertDeviceInfo(deviceInfoWriter, deviceInfo);

      Optional<PersistedDeviceInfo> optional = deviceInfoWriter.findDeviceInfoByDeviceInfoId(deviceInfoId);
      Assertions.assertEquals(deviceInfo, optional.get().getDeviceInfo());
    }
  }

  @Test
  void findDeviceInfoByHandleTest() {
    try (H2DatabaseWrapper h2DatabaseWrapper = H2DatabaseWrapper.createAndStart();
        DeviceInfoWriter deviceInfoWriter = createDeviceInfoWriter(h2DatabaseWrapper)) {
      AssertThrows.illegalArgumentException(() -> deviceInfoWriter.findDeviceInfoByHandle(null), "handle must not be null");

      Assertions.assertTrue(deviceInfoWriter.findDeviceInfoByHandle(TestUtils.HANDLE).isEmpty());

      DeviceInfo deviceInfo = createAndInsertDeviceInfo1(deviceInfoWriter);

      Optional<PersistedDeviceInfo> optional = deviceInfoWriter.findDeviceInfoByHandle(deviceInfo.getHandle());
      Assertions.assertEquals(deviceInfo, optional.get().getDeviceInfo());
    }
  }

  @Test
  void findDeviceInfoByIpv4AddressTest() {
    try (H2DatabaseWrapper h2DatabaseWrapper = H2DatabaseWrapper.createAndStart();
        DeviceInfoWriter deviceInfoWriter = createDeviceInfoWriter(h2DatabaseWrapper)) {
      AssertThrows.illegalArgumentException(() -> deviceInfoWriter.findDeviceInfoByIpv4Address(null), "ipv4Address must not be null");

      Assertions.assertTrue(deviceInfoWriter.findDeviceInfoByIpv4Address(TestUtils.IPV4_ADDRESS).isEmpty());

      DeviceInfo deviceInfo = createAndInsertDeviceInfo1(deviceInfoWriter);

      Optional<PersistedDeviceInfo> optional = deviceInfo.getSimInfo()
          .map(SimInfo::getIpv4Address)
          .flatMap(deviceInfoWriter::findDeviceInfoByIpv4Address);
      Assertions.assertEquals(deviceInfo, optional.get().getDeviceInfo());
    }
  }

  @Test
  void findDeviceInfoByMsisdnTest() {
    try (H2DatabaseWrapper h2DatabaseWrapper = H2DatabaseWrapper.createAndStart();
        DeviceInfoWriter deviceInfoWriter = createDeviceInfoWriter(h2DatabaseWrapper)) {
      AssertThrows.illegalArgumentException(() -> deviceInfoWriter.findDeviceInfoByMsisdn(null), "msisdn must not be null");

      Assertions.assertTrue(deviceInfoWriter.findDeviceInfoByMsisdn(TestUtils.MSISDN).isEmpty());

      DeviceInfo deviceInfo = createAndInsertDeviceInfo1(deviceInfoWriter);

      Optional<PersistedDeviceInfo> optional = deviceInfo.getSimInfo()
          .map(SimInfo::getMsisdn)
          .flatMap(deviceInfoWriter::findDeviceInfoByMsisdn);

      Assertions.assertEquals(deviceInfo, optional.get().getDeviceInfo());
    }
  }

  @Test
  void findDeviceInfoBySatelliteIdTest() {
    try (H2DatabaseWrapper h2DatabaseWrapper = H2DatabaseWrapper.createAndStart();
        DeviceInfoWriter deviceInfoWriter = createDeviceInfoWriter(h2DatabaseWrapper)) {
      AssertThrows.illegalArgumentException(() -> deviceInfoWriter.findDeviceInfoBySatelliteId(null), "satelliteId must not be null");

      Assertions.assertTrue(deviceInfoWriter.findDeviceInfoBySatelliteId(TestUtils.SATELLITE_ID).isEmpty());

      DeviceInfo deviceInfo = createDeviceInfoBuilder(VPI_1, HANDLE_1, IMSI)
          .setSimInfo(Optional.ofNullable(createSimInfo(MSISDN_1, IPV4_ADDRESS_1)))
          .setSatelliteId(Optional.of(TestUtils.SATELLITE_ID))
          .build();

      insertDeviceInfo(deviceInfoWriter, deviceInfo);

      Optional<PersistedDeviceInfo> optional = deviceInfoWriter.findDeviceInfoBySatelliteId(TestUtils.SATELLITE_ID);
      Assertions.assertEquals(deviceInfo, optional.get().getDeviceInfo());
    }
  }

  @Test
  void findDeviceInfoByVpiTest() {
    try (H2DatabaseWrapper h2DatabaseWrapper = H2DatabaseWrapper.createAndStart();
        DeviceInfoWriter deviceInfoWriter = createDeviceInfoWriter(h2DatabaseWrapper)) {
      AssertThrows.illegalArgumentException(() -> deviceInfoWriter.findDeviceInfoByVpi(null), "vpi must not be null");

      Assertions.assertTrue(deviceInfoWriter.findDeviceInfoByVpi(TestUtils.VPI).isEmpty());

      DeviceInfo deviceInfo = createAndInsertDeviceInfo1(deviceInfoWriter);

      Optional<PersistedDeviceInfo> optional = deviceInfoWriter.findDeviceInfoByVpi(deviceInfo.getVpi().get());
      Assertions.assertEquals(deviceInfo, optional.get().getDeviceInfo());
    }
  }

  @Test
  void findDeviceSequenceByDeviceInfoIdTest() {
    try (H2DatabaseWrapper h2DatabaseWrapper = H2DatabaseWrapper.createAndStart();
        DeviceInfoWriter deviceInfoWriter = createDeviceInfoWriter(h2DatabaseWrapper)) {
      AssertThrows.illegalArgumentException(() -> deviceInfoWriter.findDeviceSequenceByDeviceInfoId(null), "deviceInfoId must not be null");

      Assertions.assertTrue(deviceInfoWriter.findDeviceSequenceByDeviceInfoId(TestUtils.DEVICE_INFO_ID).isEmpty());

      DeviceInfoId deviceInfoId = insertDeviceInfo(deviceInfoWriter, createDeviceInfo1());
      DeviceSequence deviceSequence = createDeviceSequence(deviceInfoId);
      DeviceSequenceId deviceSequenceId = insertDeviceSequence(deviceInfoWriter, deviceSequence);

      PersistedDeviceSequence persistedDeviceSequence = deviceInfoWriter.findDeviceSequenceByDeviceInfoId(deviceInfoId).get();
      Assertions.assertEquals(deviceSequence, persistedDeviceSequence.getDeviceSequence());
      Assertions.assertEquals(deviceSequenceId, persistedDeviceSequence.getDeviceSequenceId());
    }
  }

  @Test
  void insertDeviceInfoTest() {
    try (H2DatabaseWrapper h2DatabaseWrapper = H2DatabaseWrapper.createAndStart();
        DeviceInfoWriter deviceInfoWriter = createDeviceInfoWriter(h2DatabaseWrapper)) {
      AssertThrows.illegalArgumentException(() -> deviceInfoWriter.insertDeviceInfo(null), "deviceInfo must not be null");

      DeviceInfo deviceInfo1 = createDeviceInfoBuilder(VPI_1, HANDLE_1, IMSI).setSimInfo(Optional.empty()).build();
      DeviceInfoId deviceInfoId1 = insertDeviceInfo(deviceInfoWriter, deviceInfo1);
      Assertions.assertEquals(1, deviceInfoId1.toLong());
      checkPersistedDeviceInfo(deviceInfoWriter.findDeviceInfoByDeviceInfoId(deviceInfoId1).get(), deviceInfo1, deviceInfoId1, INSTANT_1, INSTANT_1);

      DeviceInfo deviceInfo2 = createDeviceInfo2();
      DeviceInfoId deviceInfoId2 = insertDeviceInfo(deviceInfoWriter, deviceInfo2);
      Assertions.assertEquals(2, deviceInfoId2.toLong());
      checkPersistedDeviceInfo(deviceInfoWriter.findDeviceInfoByDeviceInfoId(deviceInfoId2).get(), deviceInfo2, deviceInfoId2, INSTANT_2, INSTANT_2);

      DeviceInfo deviceInfo3 = createDeviceInfo3();
      DeviceInfoId deviceInfoId3 = insertDeviceInfo(deviceInfoWriter, deviceInfo3);
      Assertions.assertEquals(3, deviceInfoId3.toLong());
      checkPersistedDeviceInfo(deviceInfoWriter.findDeviceInfoByDeviceInfoId(deviceInfoId3).get(), deviceInfo3, deviceInfoId3, INSTANT_3, INSTANT_3);

      verifyInsertDeviceInfoFail(deviceInfoWriter, deviceInfo1, InsertionFailureReason.DUPLICATE_KEY, "Unique index or primary key violation");
    }
  }

  @Test
  void insertDeviceSequenceTest() {
    try (H2DatabaseWrapper h2DatabaseWrapper = H2DatabaseWrapper.createAndStart();
        DeviceInfoWriter deviceInfoWriter = createDeviceInfoWriter(h2DatabaseWrapper)) {
      AssertThrows.illegalArgumentException(() -> deviceInfoWriter.insertDeviceSequence(null), "deviceSequence must not be null");

      verifyInsertDeviceSequenceFail(deviceInfoWriter, TestUtils.DEVICE_INFO_ID, InsertionFailureReason.REFERENTIAL_INTEGRITY_VIOLATED,
          "Referential integrity constraint violation");

      DeviceInfoId deviceInfoId = insertDeviceInfo(deviceInfoWriter, createDeviceInfo1());

      DeviceSequence deviceSequence = createDeviceSequence(deviceInfoId);
      DeviceSequenceId deviceSequenceId = insertDeviceSequence(deviceInfoWriter, deviceSequence);
      Assertions.assertEquals(2, deviceSequenceId.toLong());

      PersistedDeviceSequence persistedDeviceEncryptionIv = deviceInfoWriter.findDeviceSequenceByDeviceInfoId(deviceInfoId).get();
      checkPersistedDeviceSequence(persistedDeviceEncryptionIv, deviceSequence, deviceSequenceId, INSTANT_3);

      verifyInsertDeviceSequenceFail(deviceInfoWriter, deviceInfoId, InsertionFailureReason.DUPLICATE_KEY, "Unique index or primary key violation");
    }
  }

  @Test
  void rollbackTransactionTest() {
    try (H2DatabaseWrapper h2DatabaseWrapper = H2DatabaseWrapper.createAndStart();
        DeviceInfoWriter deviceInfoWriter = createDeviceInfoWriter(h2DatabaseWrapper)) {
      AssertThrows.illegalStateException(deviceInfoWriter::rollbackTransaction, "transaction not started");

      deviceInfoWriter.startTransaction();
      deviceInfoWriter.commitTransaction();

      AssertThrows.illegalStateException(deviceInfoWriter::rollbackTransaction, "transaction not started");

      deviceInfoWriter.startTransaction();
      deviceInfoWriter.rollbackTransaction();

      AssertThrows.illegalStateException(deviceInfoWriter::rollbackTransaction, "transaction not started");

      deviceInfoWriter.startTransaction();
      deviceInfoWriter.close();

      AssertThrows.illegalStateException(deviceInfoWriter::rollbackTransaction, "could not rollback a transaction on a closed handle");
    }
  }

  @Test
  void startTransactionTest() {
    try (H2DatabaseWrapper h2DatabaseWrapper = H2DatabaseWrapper.createAndStart();
        DeviceInfoWriter deviceInfoWriter = createDeviceInfoWriter(h2DatabaseWrapper)) {
      deviceInfoWriter.startTransaction();

      AssertThrows.illegalStateException(deviceInfoWriter::startTransaction, "transaction already started");

      deviceInfoWriter.rollbackTransaction();
      deviceInfoWriter.startTransaction();

      AssertThrows.illegalStateException(deviceInfoWriter::startTransaction, "transaction already started");

      deviceInfoWriter.commitTransaction();
      deviceInfoWriter.startTransaction();

      AssertThrows.illegalStateException(deviceInfoWriter::startTransaction, "transaction already started");

      deviceInfoWriter.close();

      AssertThrows.illegalStateException(deviceInfoWriter::startTransaction, "could not begin a transaction on a closed handle");
    }
  }

  @Test
  void updateDeviceInfoByHandleTest() {
    try (H2DatabaseWrapper h2DatabaseWrapper = H2DatabaseWrapper.createAndStart();
        DeviceInfoWriter deviceInfoWriter = createDeviceInfoWriter(h2DatabaseWrapper)) {
      AssertThrows.illegalArgumentException(() -> deviceInfoWriter.updateDeviceInfoByHandle(null), "deviceInfo must not be null");

      Handle handle = TestUtils.HANDLE;
      DeviceInfo deviceInfo = new DeviceInfoBuilder()
          .setHandle(handle)
          .setSimInfo(Optional.of(createSimInfo(MSISDN_1, IPV4_ADDRESS_1)))
          .build();
      DeviceInfoId deviceInfoId = insertDeviceInfo(deviceInfoWriter, deviceInfo);

      DeviceInfo absentDeviceInfo = new DeviceInfoBuilder()
          .setHandle(HANDLE_2)
          .build();
      Assertions.assertEquals(0, deviceInfoWriter.updateDeviceInfoByHandle(absentDeviceInfo));

      DeviceInfo expectedDeviceInfo = DeviceInfoBuilder.from(deviceInfo)
          .setSatelliteId(Optional.of(TestUtils.SATELLITE_ID))
          .build();
      Assertions.assertEquals(1, deviceInfoWriter.updateDeviceInfoByHandle(expectedDeviceInfo));
      verifyDeviceInfoUpdate(deviceInfoId, INSTANT_3, expectedDeviceInfo, deviceInfoWriter);

      expectedDeviceInfo = DeviceInfoBuilder.from(expectedDeviceInfo)
          .setVpi(Optional.of(TestUtils.VPI))
          .build();
      Assertions.assertEquals(1, deviceInfoWriter.updateDeviceInfoByHandle(expectedDeviceInfo));
      verifyDeviceInfoUpdate(deviceInfoId, INSTANT_4, expectedDeviceInfo, deviceInfoWriter);

      expectedDeviceInfo = DeviceInfoBuilder.from(expectedDeviceInfo)
          .setSimInfo(Optional.of(TestUtils.createSimInfo()))
          .build();
      Assertions.assertEquals(1, deviceInfoWriter.updateDeviceInfoByHandle(expectedDeviceInfo));
      verifyDeviceInfoUpdate(deviceInfoId, INSTANT_5, expectedDeviceInfo, deviceInfoWriter);
    }
  }

  @Test
  void updateDeviceSequenceByDeviceInfoIdTest() {
    try (H2DatabaseWrapper h2DatabaseWrapper = H2DatabaseWrapper.createAndStart();
        DeviceInfoWriter deviceInfoWriter = createDeviceInfoWriter(h2DatabaseWrapper)) {
      AssertThrows.illegalArgumentException(() -> deviceInfoWriter.updateDeviceSequence(null), "deviceSequence must not be null");

      DeviceSequence deviceSequence = createDeviceSequence(TestUtils.DEVICE_INFO_ID);
      Assertions.assertEquals(0, deviceInfoWriter.updateDeviceSequence(deviceSequence));

      DeviceInfoId deviceInfoId = insertDeviceInfo(deviceInfoWriter, createDeviceInfo1());
      createAndInsertDeviceSequence(deviceInfoWriter, deviceInfoId);

      Optional<PersistedDeviceSequence> deviceSequenceFound = deviceInfoWriter.findDeviceSequenceByDeviceInfoId(deviceInfoId);
      Assertions.assertTrue(deviceSequenceFound.isPresent());

      DeviceSequence updateFrom = deviceSequenceFound.get().getDeviceSequence();

      DeviceSequence withNewSequenceNumberToUpdate = DeviceSequenceBuilder.from(updateFrom)
          .setSequenceNumber(SequenceNumber.ofByte((byte) 6))
          .build();
      Assertions.assertEquals(1, deviceInfoWriter.updateDeviceSequence(withNewSequenceNumberToUpdate));
      Assertions.assertEquals(6, getDeviceSequenceNumberValue(deviceInfoWriter, deviceInfoId));
    }
  }
}
