package com.wirelesscar.tcevce.wecu.device.info.database.database;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;

import org.jdbi.v3.core.mapper.RowMapper;
import org.jdbi.v3.core.statement.StatementContext;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import com.volvo.tisp.vc.test.utils.lib.AssertThrows;
import com.wirelesscar.tcevce.wecu.device.info.database.model.DeviceInfo;
import com.wirelesscar.tcevce.wecu.device.info.database.model.PersistedDeviceInfo;
import com.wirelesscar.tcevce.wecu.device.info.database.model.SimInfo;
import com.wirelesscar.tcevce.wecu.device.info.database.model.TestUtils;

class DeviceInfoRowMapperTest {
  private static void checkDeviceInfoFull(DeviceInfo deviceInfo) {
    Assertions.assertEquals(TestUtils.HANDLE, deviceInfo.getHandle());
    Assertions.assertEquals(TestUtils.SATELLITE_ID, deviceInfo.getSatelliteId().get());
    checkSimInfo(deviceInfo.getSimInfo().get());
    Assertions.assertEquals(TestUtils.VPI, deviceInfo.getVpi().get());
  }

  private static void checkDeviceInfoMin(DeviceInfo deviceInfo) {
    Assertions.assertEquals(TestUtils.HANDLE, deviceInfo.getHandle());
  }

  private static void checkPersistedDeviceInfoFull(PersistedDeviceInfo persistedDeviceInfo) {
    Assertions.assertEquals(TestUtils.CREATED, persistedDeviceInfo.getCreated());
    checkDeviceInfoFull(persistedDeviceInfo.getDeviceInfo());
    Assertions.assertEquals(TestUtils.DEVICE_INFO_ID, persistedDeviceInfo.getDeviceInfoId());
    Assertions.assertEquals(TestUtils.LAST_UPDATED, persistedDeviceInfo.getLastUpdated());
  }

  private static void checkPersistedDeviceInfoMin(PersistedDeviceInfo persistedDeviceInfo) {
    Assertions.assertEquals(TestUtils.CREATED, persistedDeviceInfo.getCreated());
    checkDeviceInfoMin(persistedDeviceInfo.getDeviceInfo());
    Assertions.assertEquals(TestUtils.DEVICE_INFO_ID, persistedDeviceInfo.getDeviceInfoId());
    Assertions.assertEquals(TestUtils.LAST_UPDATED, persistedDeviceInfo.getLastUpdated());
  }

  private static void checkSimInfo(SimInfo simInfo) {
    Assertions.assertEquals(TestUtils.IPV4_ADDRESS, simInfo.getIpv4Address());
    Assertions.assertEquals(TestUtils.IPV4_PORT, simInfo.getIpv4Port());
    Assertions.assertEquals(TestUtils.MOBILE_NETWORK_OPERATOR, simInfo.getMobileNetworkOperator());
    Assertions.assertEquals(TestUtils.MSISDN, simInfo.getMsisdn());
  }

  private static RowMapper<PersistedDeviceInfo> createRowMapper() {
    return DeviceInfoRowMapper.create();
  }

  private static void mockGetInt(ResultSet resultSet, String columnLabel, int returnValue) throws SQLException {
    Mockito.when(resultSet.getInt(columnLabel)).thenReturn(returnValue);
  }

  private static void mockGetLong(ResultSet resultSet, String columnLabel, long returnValue) throws SQLException {
    Mockito.when(resultSet.getLong(columnLabel)).thenReturn(returnValue);
  }

  private static ResultSet mockResultSetFull() throws SQLException {
    ResultSet resultSet = mockResultSetMin();

    Mockito.when(resultSet.getString("satellite_id")).thenReturn(TestUtils.SATELLITE_ID.toString());
    mockSimInfo(resultSet);
    Mockito.when(resultSet.getString("vpi")).thenReturn(TestUtils.VPI.toString());
    Mockito.when(resultSet.getLong("imsi")).thenReturn(TestUtils.IMSI.toLong());

    return resultSet;
  }

  private static ResultSet mockResultSetMin() throws SQLException {
    ResultSet resultSet = Mockito.mock(ResultSet.class);

    mockGetLong(resultSet, "device_info_id", TestUtils.DEVICE_INFO_ID.toLong());
    Mockito.when(resultSet.getTimestamp("device_info_created")).thenReturn(Timestamp.from(TestUtils.CREATED));
    Mockito.when(resultSet.getTimestamp("device_info_last_updated")).thenReturn(Timestamp.from(TestUtils.LAST_UPDATED));
    Mockito.when(resultSet.getString("handle")).thenReturn(TestUtils.HANDLE.toString());
    Mockito.when(resultSet.getLong("imsi")).thenReturn(TestUtils.IMSI.toLong());

    return resultSet;
  }

  private static void mockSimInfo(ResultSet resultSet) throws SQLException {
    Mockito.when(resultSet.getString("ipv4_address")).thenReturn(TestUtils.IPV4_ADDRESS.toString());
    mockGetInt(resultSet, "ipv4_port", TestUtils.IPV4_PORT.toInt());
    Mockito.when(resultSet.getString("msisdn")).thenReturn(TestUtils.MSISDN.toString());
    Mockito.when(resultSet.getString("mobile_network_operator")).thenReturn(TestUtils.MOBILE_NETWORK_OPERATOR.toString());
  }

  @Test
  void createTest() {
    RowMapper<PersistedDeviceInfo> rowMapper = DeviceInfoRowMapper.create();
    Assertions.assertTrue(rowMapper instanceof DeviceInfoRowMapper);
  }

  @Test
  void mapFullTest() throws SQLException {
    checkPersistedDeviceInfoFull(createRowMapper().map(mockResultSetFull(), null));
  }

  @Test
  void mapMinimalTest() throws SQLException {
    ResultSet resultSet = mockResultSetMin();
    Mockito.when(resultSet.wasNull()).thenReturn(true);

    checkPersistedDeviceInfoMin(createRowMapper().map(resultSet, null));
  }

  @Test
  void mapNullResultSetTest() {
    RowMapper<PersistedDeviceInfo> rowMapper = createRowMapper();

    AssertThrows.illegalArgumentException(() -> rowMapper.map(null, Mockito.mock(StatementContext.class)), "resultSet must not be null");
  }
}
