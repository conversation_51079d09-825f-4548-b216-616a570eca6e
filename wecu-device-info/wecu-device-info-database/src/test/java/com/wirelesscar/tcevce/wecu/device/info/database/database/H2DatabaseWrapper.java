package com.wirelesscar.tcevce.wecu.device.info.database.database;

import java.io.Closeable;

import javax.sql.DataSource;

import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;

final class H2DatabaseWrapper implements Closeable {
  private final HikariDataSource hikariDataSource;

  private H2DatabaseWrapper(HikariDataSource hikariDataSource) {
    this.hikariDataSource = hikariDataSource;
  }

  static H2DatabaseWrapper createAndStart() {
    return new H2DatabaseWrapper(new HikariDataSource(createHikariConfig()));
  }

  private static HikariConfig createHikariConfig() {
    HikariConfig hikariConfig = new HikariConfig();

    hikariConfig.setJdbcUrl("jdbc:h2:mem:testdb;MODE=Oracle");
    hikariConfig.setUsername("sa");
    hikariConfig.setPassword("");

    return hikariConfig;
  }

  @Override
  public void close() {
    hikariDataSource.close();
  }

  DataSource getDataSource() {
    return hikariDataSource;
  }
}
