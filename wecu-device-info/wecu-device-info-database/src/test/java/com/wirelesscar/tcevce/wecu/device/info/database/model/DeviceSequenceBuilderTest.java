package com.wirelesscar.tcevce.wecu.device.info.database.model;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

class DeviceSequenceBuilderTest {
  private static void verifyEquals(DeviceSequenceBuilder expectedDeviceSequenceBuilder, DeviceSequence actualDeviceSequence) {
    Assertions.assertSame(expectedDeviceSequenceBuilder.getDeviceInfoId(), actualDeviceSequence.getDeviceInfoId());
    Assertions.assertSame(expectedDeviceSequenceBuilder.getSequenceNumber(), actualDeviceSequence.getSequenceNumber());
  }

  @Test
  void buildTest() {
    DeviceSequenceBuilder deviceSequenceBuilder = new DeviceSequenceBuilder();
    AssertThrows.illegalArgumentException(deviceSequenceBuilder::build, "deviceInfoId must not be null");

    deviceSequenceBuilder.setDeviceInfoId(TestUtils.DEVICE_INFO_ID);
    AssertThrows.illegalArgumentException(deviceSequenceBuilder::build, "sequenceNumber must not be null");

    deviceSequenceBuilder.setSequenceNumber(TestUtils.SEQUENCE_NUMBER);
    verifyEquals(deviceSequenceBuilder, deviceSequenceBuilder.build());
  }

  @Test
  void deviceInfoIdTest() {
    DeviceSequenceBuilder deviceSequenceBuilder = new DeviceSequenceBuilder();
    Assertions.assertNull(deviceSequenceBuilder.getDeviceInfoId());

    final DeviceInfoId deviceInfoId = TestUtils.DEVICE_INFO_ID;
    deviceSequenceBuilder.setDeviceInfoId(deviceInfoId);
    Assertions.assertSame(deviceInfoId, deviceSequenceBuilder.getDeviceInfoId());

    AssertThrows.illegalArgumentException(() -> deviceSequenceBuilder.setDeviceInfoId(null), "deviceInfoId must not be null");
    Assertions.assertSame(deviceInfoId, deviceSequenceBuilder.getDeviceInfoId());
  }

  @Test
  void fromTest() {
    AssertThrows.illegalArgumentException(() -> DeviceSequenceBuilder.from(null), "deviceSequence must not be null");

    DeviceSequence expectedDeviceSequence = TestUtils.createDeviceSequence();
    DeviceSequence actualDeviceSequence = DeviceSequenceBuilder.from(expectedDeviceSequence).build();

    Assertions.assertEquals(expectedDeviceSequence, actualDeviceSequence);
  }

  @Test
  void sequenceNumberTest() {
    DeviceSequenceBuilder deviceSequenceBuilder = new DeviceSequenceBuilder();
    Assertions.assertNull(deviceSequenceBuilder.getSequenceNumber());

    final SequenceNumber sequenceNumber = TestUtils.SEQUENCE_NUMBER;
    deviceSequenceBuilder.setSequenceNumber(sequenceNumber);
    Assertions.assertSame(sequenceNumber, deviceSequenceBuilder.getSequenceNumber());

    AssertThrows.illegalArgumentException(() -> deviceSequenceBuilder.setSequenceNumber(null), "sequenceNumber must not be null");
    Assertions.assertSame(sequenceNumber, deviceSequenceBuilder.getSequenceNumber());
  }
}
