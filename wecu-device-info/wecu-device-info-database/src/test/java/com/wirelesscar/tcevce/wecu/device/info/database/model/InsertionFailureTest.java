package com.wirelesscar.tcevce.wecu.device.info.database.model;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

class InsertionFailureTest {
  private static final RuntimeException RUNTIME_EXCEPTION = new RuntimeException("test");

  @Test
  void insertionFailureReasonTest() {
    Assertions.assertSame(InsertionFailureReason.DUPLICATE_KEY,
        new InsertionFailure(InsertionFailureReason.DUPLICATE_KEY, RUNTIME_EXCEPTION).getInsertionFailureReason());
  }

  @Test
  void invalidConstructorTest() {
    AssertThrows.illegalArgumentException(() -> new InsertionFailure(null, RUNTIME_EXCEPTION), "insertionFailureReason must not be null");
    AssertThrows.illegalArgumentException(() -> new InsertionFailure(InsertionFailureReason.DUPLICATE_KEY, null), "runtimeException must not be null");
  }

  @Test
  void runtimeExceptionTest() {
    Assertions.assertSame(RUNTIME_EXCEPTION, new InsertionFailure(InsertionFailureReason.DUPLICATE_KEY, RUNTIME_EXCEPTION).getRuntimeException());
  }

  @Test
  void toStringTest() {
    String expectedString = "insertionFailureReason=DUPLICATE_KEY, runtimeException=java.lang.RuntimeException: test";
    Assertions.assertEquals(expectedString, new InsertionFailure(InsertionFailureReason.DUPLICATE_KEY, RUNTIME_EXCEPTION).toString());
  }
}
