package com.wirelesscar.tcevce.wecu.device.info.database.model;

import java.time.Instant;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

class PersistedDeviceSequenceBuilderTest {
  private static void verifyEquals(PersistedDeviceSequenceBuilder persistedDeviceSequenceBuilder, PersistedDeviceSequence persistedDeviceEncryptionIv) {
    Assertions.assertSame(persistedDeviceSequenceBuilder.getCreated(), persistedDeviceEncryptionIv.getCreated());
    Assertions.assertSame(persistedDeviceSequenceBuilder.getDeviceSequence(), persistedDeviceEncryptionIv.getDeviceSequence());
    Assertions.assertSame(persistedDeviceSequenceBuilder.getDeviceSequenceId(), persistedDeviceEncryptionIv.getDeviceSequenceId());
    Assertions.assertSame(persistedDeviceSequenceBuilder.getLastUpdated(), persistedDeviceEncryptionIv.getLastUpdated());
  }

  @Test
  void buildTest() {
    PersistedDeviceSequenceBuilder persistedDeviceEncryptionIvBuilder = new PersistedDeviceSequenceBuilder();
    AssertThrows.illegalArgumentException(persistedDeviceEncryptionIvBuilder::build, "created must not be null");

    persistedDeviceEncryptionIvBuilder.setCreated(TestUtils.CREATED);
    AssertThrows.illegalArgumentException(persistedDeviceEncryptionIvBuilder::build, "deviceSequence must not be null");

    persistedDeviceEncryptionIvBuilder.setDeviceSequence(TestUtils.createDeviceSequence());
    AssertThrows.illegalArgumentException(persistedDeviceEncryptionIvBuilder::build, "deviceSequenceId must not be null");

    persistedDeviceEncryptionIvBuilder.setDeviceSequenceId(TestUtils.DEVICE_SEQUENCE_ID);
    AssertThrows.illegalArgumentException(persistedDeviceEncryptionIvBuilder::build, "lastUpdated must not be null");

    persistedDeviceEncryptionIvBuilder.setLastUpdated(TestUtils.LAST_UPDATED);
    verifyEquals(persistedDeviceEncryptionIvBuilder, persistedDeviceEncryptionIvBuilder.build());
  }

  @Test
  void createdTest() {
    PersistedDeviceSequenceBuilder persistedDeviceSequenceBuilder = new PersistedDeviceSequenceBuilder();
    Assertions.assertNull(persistedDeviceSequenceBuilder.getCreated());

    final Instant created = TestUtils.CREATED;
    persistedDeviceSequenceBuilder.setCreated(created);
    Assertions.assertSame(created, persistedDeviceSequenceBuilder.getCreated());

    AssertThrows.illegalArgumentException(() -> persistedDeviceSequenceBuilder.setCreated(null), "created must not be null");
    Assertions.assertSame(created, persistedDeviceSequenceBuilder.getCreated());
  }

  @Test
  void deviceSequenceIdTest() {
    PersistedDeviceSequenceBuilder persistedDeviceSequenceBuilder = new PersistedDeviceSequenceBuilder();
    Assertions.assertNull(persistedDeviceSequenceBuilder.getDeviceSequenceId());

    final DeviceSequenceId deviceSequenceId = TestUtils.DEVICE_SEQUENCE_ID;
    persistedDeviceSequenceBuilder.setDeviceSequenceId(deviceSequenceId);
    Assertions.assertSame(deviceSequenceId, persistedDeviceSequenceBuilder.getDeviceSequenceId());

    AssertThrows.illegalArgumentException(() -> persistedDeviceSequenceBuilder.setDeviceSequenceId(null), "deviceSequenceId must not be null");
    Assertions.assertSame(deviceSequenceId, persistedDeviceSequenceBuilder.getDeviceSequenceId());
  }

  @Test
  void deviceSequenceTest() {
    PersistedDeviceSequenceBuilder persistedDeviceSequenceBuilder = new PersistedDeviceSequenceBuilder();
    Assertions.assertNull(persistedDeviceSequenceBuilder.getDeviceSequence());

    final DeviceSequence deviceSequence = TestUtils.createDeviceSequence();
    persistedDeviceSequenceBuilder.setDeviceSequence(deviceSequence);
    Assertions.assertSame(deviceSequence, persistedDeviceSequenceBuilder.getDeviceSequence());

    AssertThrows.illegalArgumentException(() -> persistedDeviceSequenceBuilder.setDeviceSequence(null), "deviceSequence must not be null");
    Assertions.assertSame(deviceSequence, persistedDeviceSequenceBuilder.getDeviceSequence());
  }

  @Test
  void lastUpdatedTest() {
    PersistedDeviceSequenceBuilder persistedDeviceSequenceBuilder = new PersistedDeviceSequenceBuilder();
    Assertions.assertNull(persistedDeviceSequenceBuilder.getLastUpdated());

    final Instant lastUpdated = TestUtils.LAST_UPDATED;
    persistedDeviceSequenceBuilder.setLastUpdated(lastUpdated);
    Assertions.assertSame(lastUpdated, persistedDeviceSequenceBuilder.getLastUpdated());

    AssertThrows.illegalArgumentException(() -> persistedDeviceSequenceBuilder.setLastUpdated(null), "lastUpdated must not be null");
    Assertions.assertSame(lastUpdated, persistedDeviceSequenceBuilder.getLastUpdated());
  }
}
