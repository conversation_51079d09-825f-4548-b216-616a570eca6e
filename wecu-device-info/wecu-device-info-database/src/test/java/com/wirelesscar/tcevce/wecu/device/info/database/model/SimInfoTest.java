package com.wirelesscar.tcevce.wecu.device.info.database.model;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.vc.common.dto.lib.vehicle.Ipv4Address;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Msisdn;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;
import com.volvo.tisp.vc.test.utils.lib.AssertUtils;

class SimInfoTest {
  @Test
  void calculateSizeTest() {
    Assertions.assertEquals(409, TestUtils.createSimInfo().calculateSize());
  }

  @Test
  void equalsAndHashcodeTest() {
    SimInfo simInfo = TestUtils.createSimInfo();
    AssertUtils.assertEqualsAndHashCode(simInfo, TestUtils.createSimInfo());

    Assertions.assertNotEquals(simInfo, TestUtils.createSimInfoBuilder().setIpv4Address(Ipv4Address.ofInt(42)).build());
    Assertions.assertNotEquals(simInfo, TestUtils.createSimInfoBuilder().setIpv4Port(Ipv4Port.ofInt(42)).build());
    Assertions.assertNotEquals(simInfo, TestUtils.createSimInfoBuilder().setMobileNetworkOperator(MobileNetworkOperator.ofString("x")).build());
    Assertions.assertNotEquals(simInfo, TestUtils.createSimInfoBuilder().setMsisdn(Msisdn.ofString("+12345")).build());
  }

  @Test
  void getIpv4AddressTest() {
    Assertions.assertEquals(TestUtils.IPV4_ADDRESS, TestUtils.createSimInfo().getIpv4Address());
  }

  @Test
  void getIpv4PortTest() {
    Assertions.assertEquals(TestUtils.IPV4_PORT, TestUtils.createSimInfo().getIpv4Port());
  }

  @Test
  void getMobileNetworkOperatorTest() {
    Assertions.assertEquals(TestUtils.MOBILE_NETWORK_OPERATOR, TestUtils.createSimInfo().getMobileNetworkOperator());
  }

  @Test
  void getMsisdnTest() {
    Assertions.assertEquals(TestUtils.MSISDN, TestUtils.createSimInfo().getMsisdn());
  }

  @Test
  void invalidConstructorTest() {
    AssertThrows.illegalArgumentException(() -> new SimInfo(null), "simInfoBuilder must not be null");
  }

  @Test
  void toStringTest() {
    String expectedString = "imsi=Optional[1], ipv4Address=*******, ipv4Port=9062, mobileNetworkOperator=testOperator, msisdn=+469123456789";
    Assertions.assertEquals(expectedString, TestUtils.createSimInfo().toString());
  }
}
