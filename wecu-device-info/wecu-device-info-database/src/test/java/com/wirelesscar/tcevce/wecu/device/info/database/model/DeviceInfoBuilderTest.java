package com.wirelesscar.tcevce.wecu.device.info.database.model;

import java.util.Optional;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

class DeviceInfoBuilderTest {
  private static void verifyEquals(DeviceInfoBuilder deviceInfoBuilder, DeviceInfo deviceInfo) {
    Assertions.assertSame(deviceInfoBuilder.getHandle(), deviceInfo.getHandle());
    Assertions.assertSame(deviceInfoBuilder.getSimInfo(), deviceInfo.getSimInfo());
    Assertions.assertSame(deviceInfoBuilder.getVpi(), deviceInfo.getVpi());
    Assertions.assertSame(deviceInfoBuilder.getSatelliteId(), deviceInfo.getSatelliteId());
  }

  @Test
  void buildTest() {
    DeviceInfoBuilder deviceInfoBuilder = new DeviceInfoBuilder();
    AssertThrows.illegalArgumentException(deviceInfoBuilder::build, "handle must not be null");

    deviceInfoBuilder.setHandle(TestUtils.HANDLE);
    verifyEquals(deviceInfoBuilder, deviceInfoBuilder.build());
  }

  @Test
  void fromTest() {
    DeviceInfoBuilder deviceInfoBuilder = TestUtils.createDeviceInfoBuilder();

    verifyEquals(deviceInfoBuilder, DeviceInfoBuilder.from(deviceInfoBuilder.build()).build());
  }

  @Test
  void handleTest() {
    DeviceInfoBuilder deviceInfoBuilder = new DeviceInfoBuilder();
    Assertions.assertNull(deviceInfoBuilder.getHandle());

    final Handle handle = TestUtils.HANDLE;
    deviceInfoBuilder.setHandle(handle);
    Assertions.assertSame(handle, deviceInfoBuilder.getHandle());

    AssertThrows.illegalArgumentException(() -> deviceInfoBuilder.setHandle(null), "handle must not be null");
    Assertions.assertSame(handle, deviceInfoBuilder.getHandle());
  }

  @Test
  void simInfoTest() {
    DeviceInfoBuilder deviceInfoBuilder = new DeviceInfoBuilder();
    Assertions.assertTrue(deviceInfoBuilder.getSimInfo().isEmpty());

    Optional<SimInfo> simInfoOptional = Optional.of(TestUtils.createSimInfo());
    deviceInfoBuilder.setSimInfo(simInfoOptional);
    Assertions.assertSame(simInfoOptional, deviceInfoBuilder.getSimInfo());

    AssertThrows.illegalArgumentException(() -> deviceInfoBuilder.setSimInfo(null), "simInfo must not be null");
    Assertions.assertSame(simInfoOptional, deviceInfoBuilder.getSimInfo());
  }

  @Test
  void vpiTest() {
    DeviceInfoBuilder deviceInfoBuilder = new DeviceInfoBuilder();
    Assertions.assertTrue(deviceInfoBuilder.getVpi().isEmpty());

    Optional<Vpi> vpiOptional = Optional.of(TestUtils.VPI);
    deviceInfoBuilder.setVpi(vpiOptional);
    Assertions.assertSame(vpiOptional, deviceInfoBuilder.getVpi());

    AssertThrows.illegalArgumentException(() -> deviceInfoBuilder.setVpi(null), "vpi must not be null");
    Assertions.assertSame(vpiOptional, deviceInfoBuilder.getVpi());
  }
}
