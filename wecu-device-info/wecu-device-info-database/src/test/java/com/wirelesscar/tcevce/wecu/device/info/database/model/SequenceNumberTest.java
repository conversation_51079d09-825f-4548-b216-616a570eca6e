package com.wirelesscar.tcevce.wecu.device.info.database.model;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.vc.test.utils.lib.AssertThrows;
import com.volvo.tisp.vc.test.utils.lib.AssertUtils;

class SequenceNumberTest {
  @Test
  void equalsAndHashcodeTest() {
    AssertUtils.assertEqualsAndHashCode(TestUtils.SEQUENCE_NUMBER, SequenceNumber.ofByte((byte) 0));

    Assertions.assertNotEquals(TestUtils.SEQUENCE_NUMBER, SequenceNumber.ofByte((byte) 1));
  }

  @Test
  void nextTest() {
    Assertions.assertEquals((byte) 1, TestUtils.SEQUENCE_NUMBER.next().toByte());
    Assertions.assertEquals((byte) 0, SequenceNumber.ofByte((byte) 15).next().toByte());
  }

  @Test
  void ofByteInvalidTest() {
    AssertThrows.illegalArgumentException(() -> SequenceNumber.ofByte((byte) -1), "value must not be negative: -1");
    AssertThrows.illegalArgumentException(() -> SequenceNumber.ofByte((byte) 16), "value must not be greater than 15: 16");
  }

  @Test
  void toByteTest() {
    Assertions.assertEquals((byte) 0, TestUtils.SEQUENCE_NUMBER.toByte());
  }

  @Test
  void toStringTest() {
    Assertions.assertEquals("0", TestUtils.SEQUENCE_NUMBER.toString());
  }
}
