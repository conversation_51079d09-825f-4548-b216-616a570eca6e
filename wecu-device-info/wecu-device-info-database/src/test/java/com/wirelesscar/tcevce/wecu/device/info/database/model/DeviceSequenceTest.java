package com.wirelesscar.tcevce.wecu.device.info.database.model;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.vc.test.utils.lib.AssertThrows;
import com.volvo.tisp.vc.test.utils.lib.AssertUtils;

class DeviceSequenceTest {
  @Test
  void equalsAndHashcodeTest() {
    DeviceSequence deviceSequence = TestUtils.createDeviceSequence();
    AssertUtils.assertEqualsAndHashCode(deviceSequence, TestUtils.createDeviceSequence());

    DeviceSequence withNewDeviceInfoId = TestUtils.createDeviceSequenceBuilder().setDeviceInfoId(DeviceInfoId.ofLong(42)).build();
    Assertions.assertNotEquals(deviceSequence, withNewDeviceInfoId);

    DeviceSequence withNewSequenceNumber = TestUtils.createDeviceSequenceBuilder().setSequenceNumber(SequenceNumber.ofByte((byte) 1)).build();
    Assertions.assertNotEquals(deviceSequence, withNewSequenceNumber);
  }

  @Test
  void getDeviceInfoIdTest() {
    Assertions.assertSame(TestUtils.DEVICE_INFO_ID, TestUtils.createDeviceSequence().getDeviceInfoId());
  }

  @Test
  void getSequenceTest() {
    Assertions.assertSame(TestUtils.SEQUENCE_NUMBER, TestUtils.createDeviceSequence().getSequenceNumber());
  }

  @Test
  void invalidConstructorTest() {
    AssertThrows.illegalArgumentException(() -> new DeviceSequence(null), "deviceSequenceBuilder must not be null");
  }

  @Test
  void toStringTest() {
    Assertions.assertEquals("deviceInfoId=1, sequenceNumber=0", TestUtils.createDeviceSequence().toString());
  }
}
