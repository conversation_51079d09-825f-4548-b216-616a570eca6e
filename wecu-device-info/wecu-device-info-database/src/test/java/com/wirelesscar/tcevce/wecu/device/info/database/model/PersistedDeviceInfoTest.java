package com.wirelesscar.tcevce.wecu.device.info.database.model;

import java.time.Instant;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.vc.test.utils.lib.AssertThrows;
import com.volvo.tisp.vc.test.utils.lib.AssertUtils;

class PersistedDeviceInfoTest {
  @Test
  void calculateSizeTest() {
    Assertions.assertEquals(825, TestUtils.createPersistedDeviceInfo().calculateSize());
  }

  @Test
  void equalsAndHashcodeTest() {
    PersistedDeviceInfo persistedDeviceInfo = TestUtils.createPersistedDeviceInfo();
    AssertUtils.assertEqualsAndHashCode(persistedDeviceInfo, TestUtils.createPersistedDeviceInfo());

    Assertions.assertNotEquals(persistedDeviceInfo, TestUtils.createPersistedDeviceInfoBuilder().setCreated(Instant.now()).build());
    Assertions.assertNotEquals(persistedDeviceInfo, TestUtils.createPersistedDeviceInfoBuilder().setDeviceInfoId(DeviceInfoId.ofLong(42)).build());
    Assertions.assertNotEquals(persistedDeviceInfo, TestUtils.createPersistedDeviceInfoBuilder().setLastUpdated(Instant.now()).build());
  }

  @Test
  void getCreatedTest() {
    Assertions.assertSame(TestUtils.CREATED, TestUtils.createPersistedDeviceInfo().getCreated());
  }

  @Test
  void getDeviceEncryptionIvIdTest() {
    Assertions.assertSame(TestUtils.DEVICE_INFO_ID, TestUtils.createPersistedDeviceInfo().getDeviceInfoId());
  }

  @Test
  void getDeviceInfoTest() {
    DeviceInfo deviceInfo = TestUtils.createDeviceInfo();
    Assertions.assertSame(deviceInfo, TestUtils.createPersistedDeviceInfoBuilder().setDeviceInfo(deviceInfo).build().getDeviceInfo());
  }

  @Test
  void getLastUpdatedTest() {
    Assertions.assertSame(TestUtils.LAST_UPDATED, TestUtils.createPersistedDeviceInfo().getLastUpdated());
  }

  @Test
  void invalidConstructorTest() {
    AssertThrows.illegalArgumentException(() -> new PersistedDeviceInfo(null), "persistedDeviceInfoBuilder must not be null");
  }

  @Test
  void toStringTest() {
    String expectedString = "created=1970-01-01T00:00:01Z, deviceInfo={handle=123456, satelliteId=Optional.empty, simInfo={Optional[imsi=Optional[1], ipv4Address=*******, ipv4Port=9062, mobileNetworkOperator=testOperator, msisdn=+469123456789]}, vpi=Optional[1234567890ABCDEF1234567890ABCDEF]}, deviceInfoId=1, lastUpdated=1970-01-01T00:00:02Z";
    Assertions.assertEquals(expectedString, TestUtils.createPersistedDeviceInfo().toString());
  }
}
