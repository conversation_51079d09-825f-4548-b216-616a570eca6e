package com.wirelesscar.tcevce.wecu.device.info.database.model;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.vc.test.utils.lib.AssertThrows;
import com.volvo.tisp.vc.test.utils.lib.AssertUtils;

class MobileNetworkOperatorTest {
  @Test
  void calculateSizeTest() {
    Assertions.assertEquals(80, MobileNetworkOperator.ofString("foo").calculateSize());
  }

  @Test
  void equalsAndHashcodeTest() {
    MobileNetworkOperator mobileNetworkOperator = MobileNetworkOperator.ofString("foo");
    AssertUtils.assertEqualsAndHashCode(mobileNetworkOperator, MobileNetworkOperator.ofString("foo"));

    Assertions.assertNotEquals(mobileNetworkOperator, MobileNetworkOperator.ofString("bar"));
  }

  @Test
  void ofStringInvalidTest() {
    AssertThrows.illegalArgumentException(() -> MobileNetworkOperator.ofString(null), "mobileNetworkOperatorString must not be null");
    AssertThrows.illegalArgumentException(() -> MobileNetworkOperator.ofString(""), "mobileNetworkOperatorString must not be empty");
  }

  @Test
  void toStringTest() {
    Assertions.assertEquals("foo", MobileNetworkOperator.ofString("foo").toString());
  }
}
