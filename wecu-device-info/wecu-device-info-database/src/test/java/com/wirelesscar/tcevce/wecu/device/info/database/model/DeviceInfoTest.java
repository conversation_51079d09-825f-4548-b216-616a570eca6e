package com.wirelesscar.tcevce.wecu.device.info.database.model;

import java.util.Optional;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;
import com.volvo.tisp.vc.test.utils.lib.AssertUtils;

class DeviceInfoTest {
  @Test
  void calculateSizeTest() {
    Assertions.assertEquals(697, TestUtils.createDeviceInfo().calculateSize());
  }

  @Test
  void equalsAndHashcodeTest() {
    DeviceInfo deviceInfo = TestUtils.createDeviceInfo();
    AssertUtils.assertEqualsAndHashCode(deviceInfo, TestUtils.createDeviceInfo());

    DeviceInfoBuilder deviceInfoBuilder = TestUtils.createDeviceInfoBuilder();

    Assertions.assertNotEquals(deviceInfo, deviceInfoBuilder.setHandle(Handle.ofString("234567")).build());

    SimInfo simInfo = TestUtils.createSimInfoBuilder().setIpv4Port(Ipv4Port.ofInt(43)).build();
    Assertions.assertNotEquals(deviceInfo, deviceInfoBuilder.setSimInfo(Optional.of(simInfo)).build());

    Assertions.assertNotEquals(deviceInfo, deviceInfoBuilder.setVpi(Optional.of(Vpi.ofString("00000000000000000000000000000000"))).build());
    Assertions.assertNotEquals(deviceInfo, deviceInfoBuilder.setSatelliteId(Optional.of(TestUtils.SATELLITE_ID)).build());
  }

  @Test
  void getHandleTest() {
    Assertions.assertSame(TestUtils.HANDLE, TestUtils.createDeviceInfo().getHandle());
  }

  @Test
  void getSatelliteIdTest() {
    Assertions.assertSame(Optional.empty(), TestUtils.createDeviceInfo().getSatelliteId());
  }

  @Test
  void getVpiTest() {
    Assertions.assertSame(TestUtils.VPI, TestUtils.createDeviceInfo().getVpi().get());
  }

  @Test
  void invalidConstructorTest() {
    AssertThrows.illegalArgumentException(() -> new DeviceInfo(null), "deviceInfoBuilder must not be null");
  }

  @Test
  void toStringTest() {
    String expectedString = "handle=123456, satelliteId=Optional.empty, simInfo={Optional[imsi=Optional[1], ipv4Address=*******, ipv4Port=9062, mobileNetworkOperator=testOperator, msisdn=+469123456789]}, vpi=Optional[1234567890ABCDEF1234567890ABCDEF]";
    Assertions.assertEquals(expectedString, TestUtils.createDeviceInfo().toString());
  }
}
