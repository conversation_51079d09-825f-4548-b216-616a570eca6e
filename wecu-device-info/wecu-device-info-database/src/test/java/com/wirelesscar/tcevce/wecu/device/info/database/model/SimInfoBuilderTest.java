package com.wirelesscar.tcevce.wecu.device.info.database.model;

import java.util.Optional;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.vc.common.dto.lib.vehicle.Imsi;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Ipv4Address;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Msisdn;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

class SimInfoBuilderTest {
  private static void verifyEquals(SimInfoBuilder simInfoBuilder, SimInfo simInfo) {
    Assertions.assertSame(simInfoBuilder.getIpv4Address(), simInfo.getIpv4Address());
    Assertions.assertEquals(simInfoBuilder.getIpv4Port(), simInfo.getIpv4Port());
    Assertions.assertSame(simInfoBuilder.getMobileNetworkOperator(), simInfo.getMobileNetworkOperator());
    Assertions.assertSame(simInfoBuilder.getMsisdn(), simInfo.getMsisdn());
  }

  @Test
  void buildTest() {
    SimInfoBuilder simInfoBuilder = new SimInfoBuilder();

    AssertThrows.illegalArgumentException(simInfoBuilder::build, "ipv4Address must not be null");
    simInfoBuilder.setIpv4Address(TestUtils.IPV4_ADDRESS);

    AssertThrows.illegalArgumentException(simInfoBuilder::build, "ipv4Port must not be null");
    simInfoBuilder.setIpv4Port(TestUtils.IPV4_PORT);

    AssertThrows.illegalArgumentException(simInfoBuilder::build, "mobileNetworkOperator must not be null");
    simInfoBuilder.setMobileNetworkOperator(TestUtils.MOBILE_NETWORK_OPERATOR);

    AssertThrows.illegalArgumentException(simInfoBuilder::build, "msisdn must not be null");
    simInfoBuilder.setMsisdn(TestUtils.MSISDN);

    Assertions.assertEquals(Optional.empty(), simInfoBuilder.build().getImsi());

    simInfoBuilder.setImsi(Imsi.ofLong(1L));
    verifyEquals(simInfoBuilder, simInfoBuilder.build());
  }

  @Test
  void ipv4AddressTest() {
    SimInfoBuilder simInfoBuilder = new SimInfoBuilder();
    Assertions.assertNull(simInfoBuilder.getIpv4Address());

    final Ipv4Address ipv4Address = TestUtils.IPV4_ADDRESS;
    simInfoBuilder.setIpv4Address(ipv4Address);
    Assertions.assertSame(ipv4Address, simInfoBuilder.getIpv4Address());

    AssertThrows.illegalArgumentException(() -> simInfoBuilder.setIpv4Address(null), "ipv4Address must not be null");
    Assertions.assertSame(ipv4Address, simInfoBuilder.getIpv4Address());
  }

  @Test
  void ipv4PortTest() {
    SimInfoBuilder simInfoBuilder = new SimInfoBuilder();
    Assertions.assertNull(simInfoBuilder.getIpv4Port());

    final Ipv4Port ipv4Port = TestUtils.IPV4_PORT;
    simInfoBuilder.setIpv4Port(ipv4Port);
    Assertions.assertEquals(ipv4Port, simInfoBuilder.getIpv4Port());

    AssertThrows.illegalArgumentException(() -> simInfoBuilder.setIpv4Port(null), "ipv4Port must not be null");
    Assertions.assertEquals(ipv4Port, simInfoBuilder.getIpv4Port());
  }

  @Test
  void mobileNetworkOperatorTest() {
    SimInfoBuilder simInfoBuilder = new SimInfoBuilder();
    Assertions.assertNull(simInfoBuilder.getMobileNetworkOperator());

    final MobileNetworkOperator mobileNetworkOperator = TestUtils.MOBILE_NETWORK_OPERATOR;
    simInfoBuilder.setMobileNetworkOperator(mobileNetworkOperator);
    Assertions.assertSame(mobileNetworkOperator, simInfoBuilder.getMobileNetworkOperator());

    AssertThrows.illegalArgumentException(() -> simInfoBuilder.setMobileNetworkOperator(null), "mobileNetworkOperator must not be null");
    Assertions.assertSame(mobileNetworkOperator, simInfoBuilder.getMobileNetworkOperator());
  }

  @Test
  void msisdnTest() {
    SimInfoBuilder simInfoBuilder = new SimInfoBuilder();
    Assertions.assertNull(simInfoBuilder.getMsisdn());

    final Msisdn msisdn = TestUtils.MSISDN;
    simInfoBuilder.setMsisdn(msisdn);
    Assertions.assertSame(msisdn, simInfoBuilder.getMsisdn());

    AssertThrows.illegalArgumentException(() -> simInfoBuilder.setMsisdn(null), "msisdn must not be null");
    Assertions.assertSame(msisdn, simInfoBuilder.getMsisdn());
  }
}
