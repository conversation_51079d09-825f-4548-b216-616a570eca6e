package com.wirelesscar.tcevce.wecu.device.info.database.model;

import java.time.Instant;
import java.util.Optional;

import com.volvo.tisp.vc.common.dto.lib.vehicle.Imsi;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Ipv4Address;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Msisdn;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;

public final class TestUtils {
  public static final Instant CREATED = Instant.ofEpochSecond(1);
  public static final DeviceInfoId DEVICE_INFO_ID = DeviceInfoId.ofLong(1);
  public static final DeviceSequenceId DEVICE_SEQUENCE_ID = DeviceSequenceId.ofLong(1);
  public static final Handle HANDLE = Handle.ofString("123456");
  public static final Imsi IMSI = Imsi.ofLong(1L);
  public static final Ipv4Address IPV4_ADDRESS = Ipv4Address.ofString("*******");
  public static final Ipv4Port IPV4_PORT = Ipv4Port.ofInt(9_062);
  public static final Instant LAST_UPDATED = Instant.ofEpochSecond(2);
  public static final MobileNetworkOperator MOBILE_NETWORK_OPERATOR = MobileNetworkOperator.ofString("testOperator");
  public static final Msisdn MSISDN = Msisdn.ofString("+469123456789");
  public static final SatelliteId SATELLITE_ID = SatelliteId.ofString("HQ1234567890x1");
  public static final SequenceNumber SEQUENCE_NUMBER = SequenceNumber.ofByte((byte) 0);
  public static final Vpi VPI = Vpi.ofString("1234567890ABCDEF1234567890ABCDEF");
  public static final WecuKeyId WECU_KEY_ID = WecuKeyId.ofLong(1);

  private TestUtils() {
    throw new IllegalStateException();
  }

  public static DeviceInfo createDeviceInfo() {
    return createDeviceInfoBuilder().build();
  }

  public static DeviceInfoBuilder createDeviceInfoBuilder() {
    return new DeviceInfoBuilder()
        .setHandle(HANDLE)
        .setSimInfo(Optional.of(createSimInfo()))
        .setVpi(Optional.of(VPI));
  }

  public static DeviceSequence createDeviceSequence() {
    return createDeviceSequenceBuilder().build();
  }

  public static DeviceSequenceBuilder createDeviceSequenceBuilder() {
    return new DeviceSequenceBuilder()
        .setDeviceInfoId(DEVICE_INFO_ID)
        .setSequenceNumber(SEQUENCE_NUMBER);
  }

  public static PersistedDeviceInfo createPersistedDeviceInfo() {
    return createPersistedDeviceInfoBuilder().build();
  }

  public static PersistedDeviceInfoBuilder createPersistedDeviceInfoBuilder() {
    return new PersistedDeviceInfoBuilder()
        .setCreated(CREATED)
        .setDeviceInfo(createDeviceInfo())
        .setDeviceInfoId(DEVICE_INFO_ID)
        .setLastUpdated(LAST_UPDATED);
  }

  public static PersistedDeviceSequence createPersistedDeviceSequence() {
    return createPersistedDeviceSequenceBuilder().build();
  }

  public static PersistedDeviceSequenceBuilder createPersistedDeviceSequenceBuilder() {
    return new PersistedDeviceSequenceBuilder()
        .setCreated(CREATED)
        .setDeviceSequence(createDeviceSequence())
        .setDeviceSequenceId(DEVICE_SEQUENCE_ID)
        .setLastUpdated(LAST_UPDATED);
  }

  public static SimInfo createSimInfo() {
    return createSimInfoBuilder().build();
  }

  public static SimInfoBuilder createSimInfoBuilder() {
    return new SimInfoBuilder()
        .setImsi(Imsi.ofLong(1L))
        .setIpv4Address(IPV4_ADDRESS)
        .setIpv4Port(IPV4_PORT)
        .setMobileNetworkOperator(MOBILE_NETWORK_OPERATOR)
        .setMsisdn(MSISDN);
  }
}
