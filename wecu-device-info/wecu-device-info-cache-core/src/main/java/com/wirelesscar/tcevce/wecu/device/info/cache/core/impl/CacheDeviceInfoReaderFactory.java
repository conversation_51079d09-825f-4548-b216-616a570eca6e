package com.wirelesscar.tcevce.wecu.device.info.cache.core.impl;

import java.util.Optional;
import java.util.function.ToIntFunction;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.Weigher;
import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.wirelesscar.tcevce.wecu.device.info.cache.core.api.CacheDeviceInfoReader;
import com.wirelesscar.tcevce.wecu.device.info.database.api.DeviceInfoReaderFactory;
import com.wirelesscar.tcevce.wecu.device.info.database.model.Handle;
import com.wirelesscar.tcevce.wecu.device.info.database.model.PersistedDeviceInfo;
import com.wirelesscar.tcevce.wecu.device.info.database.model.SatelliteId;
import com.wirelesscar.tcevce.wecu.device.info.database.model.SizeUtil;

public final class CacheDeviceInfoReaderFactory {
  private CacheDeviceInfoReaderFactory() {
    throw new IllegalStateException();
  }

  public static CacheDeviceInfoReader create(DeviceInfoReaderFactory deviceInfoReaderFactory, CacheDeviceInfoMetricReporter cacheDeviceInfoMetricReporter,
      long maximumCacheSizeInBytes) {
    Validate.notNull(deviceInfoReaderFactory, "deviceInfoReaderFactory");
    Validate.notNull(cacheDeviceInfoMetricReporter, "cacheDeviceInfoMetricReporter");
    Validate.notNegative(maximumCacheSizeInBytes, "maximumCacheSizeInBytes");

    CacheHolder cacheHolder = createCacheHolder(maximumCacheSizeInBytes);

    return CacheDeviceInfoReaderImpl.create(deviceInfoReaderFactory, cacheDeviceInfoMetricReporter, cacheHolder);
  }

  private static <T> Cache<T, Optional<PersistedDeviceInfo>> buildCache(long maximumWeight, Weigher<T, Optional<PersistedDeviceInfo>> weigher) {
    return CacheBuilder.newBuilder().maximumWeight(maximumWeight).weigher(weigher).build();
  }

  private static <T> Cache<T, Optional<PersistedDeviceInfo>> createCache(long maximumWeight, ToIntFunction<T> toIntFunction) {
    return buildCache(maximumWeight, PersistedDeviceInfoWeigher.create(toIntFunction));
  }

  private static CacheHolder createCacheHolder(long maximumWeight) {
    return new CacheHolderBuilder()
        .setHandleCache(createCache(maximumWeight, Handle::calculateSize))
        .setIpAddressCache(createCache(maximumWeight, SizeUtil::estimateSizeOfIpv4Address))
        .setMsisdnCache(createCache(maximumWeight, SizeUtil::estimateSizeOfMsisdn))
        .setSatelliteIdCache(createCache((maximumWeight), SatelliteId::calculateSize))
        .setVpiCache(createCache(maximumWeight, SizeUtil::estimateSizeOfVpi))
        .build();
  }
}
