package com.wirelesscar.tcevce.wecu.device.info.cache.core.impl;

import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.google.common.cache.Cache;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Ipv4Address;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Msisdn;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.wirelesscar.tcevce.wecu.device.info.cache.core.api.CacheDeviceInfoReader;
import com.wirelesscar.tcevce.wecu.device.info.database.api.DeviceInfoReader;
import com.wirelesscar.tcevce.wecu.device.info.database.api.DeviceInfoReaderFactory;
import com.wirelesscar.tcevce.wecu.device.info.database.model.DeviceInfo;
import com.wirelesscar.tcevce.wecu.device.info.database.model.DeviceInfoId;
import com.wirelesscar.tcevce.wecu.device.info.database.model.Handle;
import com.wirelesscar.tcevce.wecu.device.info.database.model.PersistedDeviceInfo;
import com.wirelesscar.tcevce.wecu.device.info.database.model.PersistedDeviceSequence;
import com.wirelesscar.tcevce.wecu.device.info.database.model.SatelliteId;

final class CacheDeviceInfoReaderImpl implements CacheDeviceInfoReader {
  private static final Logger logger = LoggerFactory.getLogger(CacheDeviceInfoReaderImpl.class);

  private final CacheDeviceInfoMetricReporter cacheDeviceInfoMetricReporter;
  private final CacheHolder cacheHolder;
  private final DeviceInfoReaderFactory deviceInfoReaderFactory;

  private CacheDeviceInfoReaderImpl(DeviceInfoReaderFactory deviceInfoReaderFactory, CacheDeviceInfoMetricReporter cacheDeviceInfoMetricReporter,
      CacheHolder cacheHolder) {
    this.deviceInfoReaderFactory = deviceInfoReaderFactory;
    this.cacheDeviceInfoMetricReporter = cacheDeviceInfoMetricReporter;
    this.cacheHolder = cacheHolder;
  }

  static CacheDeviceInfoReader create(DeviceInfoReaderFactory deviceInfoReaderFactory, CacheDeviceInfoMetricReporter cacheDeviceInfoMetricReporter,
      CacheHolder cacheHolder) {
    Validate.notNull(deviceInfoReaderFactory, "deviceInfoReaderFactory");
    Validate.notNull(cacheDeviceInfoMetricReporter, "cacheDeviceInfoMetricReporter");
    Validate.notNull(cacheHolder, "cacheHolder");

    return new CacheDeviceInfoReaderImpl(deviceInfoReaderFactory, cacheDeviceInfoMetricReporter, cacheHolder);
  }

  @Override
  public void close() {
    // do nothing
  }

  @Override
  public int countAllDevices() {
    try (DeviceInfoReader deviceInfoReader = deviceInfoReaderFactory.create()) {
      return deviceInfoReader.countAllDevices();
    }
  }

  @Override
  public Optional<PersistedDeviceInfo> findDeviceInfoByDeviceInfoId(DeviceInfoId deviceInfoId) {
    Validate.notNull(deviceInfoId, "deviceInfoId");

    try (DeviceInfoReader deviceInfoReader = deviceInfoReaderFactory.create()) {
      Optional<PersistedDeviceInfo> optional = deviceInfoReader.findDeviceInfoByDeviceInfoId(deviceInfoId);

      if (optional.isPresent()) {
        putInAllCaches(optional);
      }

      reportNumberOfEntriesInCache(cacheHolder.getVpiCache());
      return optional;
    }
  }

  @Override
  public Optional<PersistedDeviceInfo> findDeviceInfoByHandle(Handle handle) {
    Validate.notNull(handle, "handle");

    return findByKey(handle, cacheHolder.getHandleCache(), deviceInfoReader -> deviceInfoReader.findDeviceInfoByHandle(handle));
  }

  @Override
  public Optional<PersistedDeviceInfo> findDeviceInfoByIpv4Address(Ipv4Address ip4Address) {
    Validate.notNull(ip4Address, "ip4Address");

    return findByKey(ip4Address, cacheHolder.getIpAddressCache(), deviceInfoReader -> deviceInfoReader.findDeviceInfoByIpv4Address(ip4Address));
  }

  @Override
  public Optional<PersistedDeviceInfo> findDeviceInfoByMsisdn(Msisdn msisdn) {
    Validate.notNull(msisdn, "msisdn");

    return findByKey(msisdn, cacheHolder.getMsisdnCache(), deviceInfoReader -> deviceInfoReader.findDeviceInfoByMsisdn(msisdn));
  }

  @Override
  public Optional<PersistedDeviceInfo> findDeviceInfoBySatelliteId(SatelliteId satelliteId) {
    Validate.notNull(satelliteId, "satelliteId");

    return findByKey(satelliteId, cacheHolder.getSatelliteIdCache(), deviceInfoReader -> deviceInfoReader.findDeviceInfoBySatelliteId(satelliteId));
  }

  @Override
  public Optional<PersistedDeviceInfo> findDeviceInfoByVpi(Vpi vpi) {
    Validate.notNull(vpi, "vpi");

    return findByKey(vpi, cacheHolder.getVpiCache(), deviceInfoReader -> deviceInfoReader.findDeviceInfoByVpi(vpi));
  }

  @Override
  public Optional<PersistedDeviceSequence> findDeviceSequenceByDeviceInfoId(DeviceInfoId deviceInfoId) {
    Validate.notNull(deviceInfoId, "deviceInfoId");

    try (DeviceInfoReader deviceInfoReader = deviceInfoReaderFactory.create()) {
      return deviceInfoReader.findDeviceSequenceByDeviceInfoId(deviceInfoId);
    }
  }

  @Override
  public List<PersistedDeviceInfo> getDeviceInfoWithWecuKeys(int page, int limit) {
    return List.of();
  }

  @Override
  public void removeFromCache(Set<Handle> handles) {
    Validate.notEmpty(handles, "handles");

    Cache<Handle, Optional<PersistedDeviceInfo>> handleCache = cacheHolder.getHandleCache();

    for (Handle handle : handles) {
      logger.debug("Remove device with handle={} from device cache", handle);

      Optional<PersistedDeviceInfo> cacheOptional = handleCache.getIfPresent(handle);
      if (cacheOptional == null) {
        // do nothing
      } else if (cacheOptional.isEmpty()) {
        handleCache.invalidate(handle);
      } else {
        PersistedDeviceInfo persistedDeviceInfo = cacheOptional.get();
        invalidateAllCaches(persistedDeviceInfo.getDeviceInfo());
      }

      try (DeviceInfoReader deviceInfoReader = deviceInfoReaderFactory.create()) {
        Optional<PersistedDeviceInfo> databaseOptional = deviceInfoReader.findDeviceInfoByHandle(handle);

        databaseOptional.ifPresent(deviceInfo -> invalidateAllCaches(deviceInfo.getDeviceInfo()));
      }
    }

    reportNumberOfEntriesInCache(handleCache);
  }

  private <K> Optional<PersistedDeviceInfo> findByKey(K key, Cache<K, Optional<PersistedDeviceInfo>> cache,
      Function<DeviceInfoReader, Optional<PersistedDeviceInfo>> function) {
    Optional<PersistedDeviceInfo> cacheOptional = cache.getIfPresent(key);

    // Guava cache.getIfPresent(key) method returns null if there is no cached value for the key
    if (cacheOptional == null) {
      cacheDeviceInfoMetricReporter.onCacheMiss();

      try (DeviceInfoReader deviceInfoReader = deviceInfoReaderFactory.create()) {
        Optional<PersistedDeviceInfo> databaseOptional = function.apply(deviceInfoReader);

        if (databaseOptional.isPresent()) {
          putInAllCaches(databaseOptional);
        } else {
          cache.put(key, Optional.empty());
        }

        reportNumberOfEntriesInCache(cacheHolder.getVpiCache());
        return databaseOptional;
      }
    }

    cacheDeviceInfoMetricReporter.onCacheHit();
    return cacheOptional;
  }

  private void invalidateAllCaches(DeviceInfo deviceInfo) {
    cacheHolder.getHandleCache().invalidate(deviceInfo.getHandle());

    deviceInfo.getSatelliteId().ifPresent(satelliteId -> cacheHolder.getSatelliteIdCache().invalidate(satelliteId));
    deviceInfo.getSimInfo().ifPresent(simInfo -> {
      cacheHolder.getIpAddressCache().invalidate(simInfo.getIpv4Address());
      cacheHolder.getMsisdnCache().invalidate(simInfo.getMsisdn());
    });
    deviceInfo.getVpi().ifPresent(vpi -> cacheHolder.getVpiCache().invalidate(vpi));
  }

  private void putInAllCaches(Optional<PersistedDeviceInfo> optional) {
    DeviceInfo deviceInfo = optional.get().getDeviceInfo();

    cacheHolder.getHandleCache().put(deviceInfo.getHandle(), optional);

    deviceInfo.getSatelliteId().ifPresent(satelliteId -> cacheHolder.getSatelliteIdCache().put(satelliteId, optional));
    deviceInfo.getSimInfo().ifPresent(simInfo -> {
      cacheHolder.getIpAddressCache().put(simInfo.getIpv4Address(), optional);
      cacheHolder.getMsisdnCache().put(simInfo.getMsisdn(), optional);
    });
    deviceInfo.getVpi().ifPresent(vpi -> cacheHolder.getVpiCache().put(vpi, optional));
  }

  private void reportNumberOfEntriesInCache(Cache<?, ?> cache) {
    cacheDeviceInfoMetricReporter.onCurrentNumberOfEntries(cache.size());
  }
}
