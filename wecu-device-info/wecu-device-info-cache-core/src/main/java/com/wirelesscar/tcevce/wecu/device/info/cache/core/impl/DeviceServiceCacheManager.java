package com.wirelesscar.tcevce.wecu.device.info.cache.core.impl;

import java.util.Set;

import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.wirelesscar.tcevce.wecu.device.info.cache.core.api.CacheDeviceInfoReader;
import com.wirelesscar.tcevce.wecu.device.info.database.model.Handle;

public class DeviceServiceCacheManager {
  private final CacheDeviceInfoReader cacheDeviceInfoReader;

  public DeviceServiceCacheManager(CacheDeviceInfoReader cacheDeviceInfoReader) {
    Validate.notNull(cacheDeviceInfoReader, "cacheDeviceInfoReader");

    this.cacheDeviceInfoReader = cacheDeviceInfoReader;
  }

  public void invalidateCache(Set<Handle> handles) {
    Validate.notEmpty(handles, "handles");

    cacheDeviceInfoReader.removeFromCache(handles);
  }
}
