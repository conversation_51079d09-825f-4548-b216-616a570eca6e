package com.wirelesscar.tcevce.wecu.device.info.cache.core.impl;

import java.util.Optional;

import com.google.common.cache.Cache;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Ipv4Address;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Msisdn;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.wirelesscar.tcevce.wecu.device.info.database.model.Handle;
import com.wirelesscar.tcevce.wecu.device.info.database.model.PersistedDeviceInfo;
import com.wirelesscar.tcevce.wecu.device.info.database.model.SatelliteId;

class CacheHolderBuilder {
  private Cache<Handle, Optional<PersistedDeviceInfo>> handleCache;
  private Cache<Ipv4Address, Optional<PersistedDeviceInfo>> ipAddressCache;
  private Cache<Msisdn, Optional<PersistedDeviceInfo>> msisdnCache;
  private Cache<SatelliteId, Optional<PersistedDeviceInfo>> satelliteIdCache;
  private Cache<Vpi, Optional<PersistedDeviceInfo>> vpiCache;

  CacheHolder build() {
    Validate.notNull(handleCache, "handleCache");
    Validate.notNull(ipAddressCache, "ipAddressCache");
    Validate.notNull(msisdnCache, "msisdnCache");
    Validate.notNull(satelliteIdCache, "satelliteIdCache");
    Validate.notNull(vpiCache, "vpiCache");

    return new CacheHolder(this);
  }

  Cache<Handle, Optional<PersistedDeviceInfo>> getHandleCache() {
    return handleCache;
  }

  Cache<Ipv4Address, Optional<PersistedDeviceInfo>> getIpAddressCache() {
    return ipAddressCache;
  }

  Cache<Msisdn, Optional<PersistedDeviceInfo>> getMsisdnCache() {
    return msisdnCache;
  }

  Cache<SatelliteId, Optional<PersistedDeviceInfo>> getSatelliteIdCache() {
    return satelliteIdCache;
  }

  Cache<Vpi, Optional<PersistedDeviceInfo>> getVpiCache() {
    return vpiCache;
  }

  CacheHolderBuilder setHandleCache(Cache<Handle, Optional<PersistedDeviceInfo>> handleCache) {
    Validate.notNull(handleCache, "handleCache");

    this.handleCache = handleCache;
    return this;
  }

  CacheHolderBuilder setIpAddressCache(Cache<Ipv4Address, Optional<PersistedDeviceInfo>> ipAddressCache) {
    Validate.notNull(ipAddressCache, "ipAddressCache");

    this.ipAddressCache = ipAddressCache;
    return this;
  }

  CacheHolderBuilder setMsisdnCache(Cache<Msisdn, Optional<PersistedDeviceInfo>> msisdnCache) {
    Validate.notNull(msisdnCache, "msisdnCache");

    this.msisdnCache = msisdnCache;
    return this;
  }

  CacheHolderBuilder setSatelliteIdCache(Cache<SatelliteId, Optional<PersistedDeviceInfo>> satelliteIdCache) {
    Validate.notNull(handleCache, "satelliteIdCache");

    this.satelliteIdCache = satelliteIdCache;
    return this;
  }

  CacheHolderBuilder setVpiCache(Cache<Vpi, Optional<PersistedDeviceInfo>> vpiCache) {
    Validate.notNull(vpiCache, "vpiCache");

    this.vpiCache = vpiCache;
    return this;
  }
}