package com.wirelesscar.tcevce.wecu.device.info.cache.core.impl;

import java.util.Set;

import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.wirelesscar.tcevce.wecu.device.info.cache.core.api.CacheInvalidator;
import com.wirelesscar.tcevce.wecu.device.info.database.model.Handle;

public final class LocalCacheInvalidator implements CacheInvalidator {
  private final DeviceServiceCacheManager deviceServiceCacheManager;

  private LocalCacheInvalidator(DeviceServiceCacheManager deviceServiceCacheManager) {
    this.deviceServiceCacheManager = deviceServiceCacheManager;
  }

  public static CacheInvalidator create(DeviceServiceCacheManager deviceServiceCacheManager) {
    Validate.notNull(deviceServiceCacheManager, "deviceServiceCacheManager");

    return new LocalCacheInvalidator(deviceServiceCacheManager);
  }

  @Override
  public void invalidateCache(Set<Handle> handles) {
    Validate.notEmpty(handles, "handles");

    deviceServiceCacheManager.invalidateCache(handles);
  }
}
