package com.wirelesscar.tcevce.wecu.device.info.cache.core.impl;

import java.util.Optional;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.github.benmanes.caffeine.cache.Cache;
import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.volvo.vc.crypto.symmetric.key.SymmetricKey;
import com.wirelesscar.tcevce.wecu.device.info.cache.core.api.VehicleKeyCache;

public class VehicleKeyCacheImpl implements VehicleKeyCache {
  private static final String VEHICLE_ID_VAR = "vehicleId";
  private static final Logger logger = LoggerFactory.getLogger(VehicleKeyCacheImpl.class);

  private final Cache<String, SymmetricKey> cache;

  public VehicleKeyCacheImpl(Cache<String, SymmetricKey> cache) {
    this.cache = cache;
  }

  public static VehicleKeyCache create(Cache<String, SymmetricKey> cache) {
    return new VehicleKeyCacheImpl(cache);
  }

  @Override
  public Optional<SymmetricKey> getSymmetricKey(String vehicleId) {
    Validate.notNull(vehicleId, VEHICLE_ID_VAR);

    SymmetricKey symmetricKey = cache.getIfPresent(vehicleId);

    if (symmetricKey == null) {
      logger.debug("cache miss, vehicleId: {}", vehicleId);
      return Optional.empty();
    }

    return Optional.of(symmetricKey);
  }

  @Override
  public void putSymmetricKey(String vehicleId, SymmetricKey symmetricKey) {
    Validate.notNull(vehicleId, VEHICLE_ID_VAR);
    Validate.notNull(symmetricKey, "symmetricKey");

    cache.put(vehicleId, symmetricKey);
    logger.debug("cache put, vehicleId: {}, symmetricKey: {}", vehicleId, symmetricKey);
  }

  @Override
  public void removeSymmetricKey(String vehicleId) {
    Validate.notNull(vehicleId, VEHICLE_ID_VAR);

    SymmetricKey symmetricKey = cache.getIfPresent(vehicleId);

    if (symmetricKey == null) {
      logger.debug("cache miss, vehicleId: {}", vehicleId);
    } else {
      logger.debug("cache hit, vehicleId: {}, symmetricKey: {}", vehicleId, symmetricKey);
      cache.invalidate(vehicleId);
    }
  }
}
