package com.wirelesscar.tcevce.wecu.device.info.cache.core.impl;

import java.util.Optional;
import java.util.function.ToIntFunction;

import com.google.common.cache.Weigher;
import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.wirelesscar.tcevce.wecu.device.info.database.model.PersistedDeviceInfo;
import com.wirelesscar.tcevce.wecu.device.info.database.model.SizeUtil;

public final class PersistedDeviceInfoWeigher<T> implements Weigher<T, Optional<PersistedDeviceInfo>> {
  private final ToIntFunction<T> toIntFunction;

  private PersistedDeviceInfoWeigher(ToIntFunction<T> toIntFunction) {
    this.toIntFunction = toIntFunction;
  }

  public static <T> Weigher<T, Optional<PersistedDeviceInfo>> create(ToIntFunction<T> toIntFunction) {
    Validate.notNull(toIntFunction, "toIntFunction");

    return new PersistedDeviceInfoWeigher<>(toIntFunction);
  }

  @Override
  public int weigh(T key, Optional<PersistedDeviceInfo> optional) {
    Validate.notNull(key, "key");
    Validate.notNull(optional, "optional");

    int totalSize = 0;

    totalSize += toIntFunction.applyAsInt(key);
    totalSize += SizeUtil.estimateSizeOfOptional(optional);

    return totalSize;
  }
}
