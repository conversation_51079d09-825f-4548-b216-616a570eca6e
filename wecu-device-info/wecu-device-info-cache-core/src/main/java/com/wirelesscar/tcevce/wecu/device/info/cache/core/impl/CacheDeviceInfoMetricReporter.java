package com.wirelesscar.tcevce.wecu.device.info.cache.core.impl;

import com.volvo.tisp.vc.main.utils.lib.Validate;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;

public class CacheDeviceInfoMetricReporter {
  static final String CACHE = "cache";
  static final String CURRENT_NUMBER_OF_ENTRIES = "current-number-of-entries";
  static final String DEVICE_SERVICE = "device.service";
  static final String HIT = "hit";
  static final String MISS = "miss";

  private final Counter cacheHitCounter;
  private final Counter cacheMissCounter;
  private final Counter currentNumberOfEntriesCounter;

  public CacheDeviceInfoMetricReporter(MeterRegistry meterRegistry) {
    Validate.notNull(meterRegistry, "meterRegistry");

    cacheHitCounter = meterRegistry.counter(DEVICE_SERVICE, CACHE, HIT);
    cacheMissCounter = meterRegistry.counter(DEVICE_SERVICE, CACHE, MISS);
    currentNumberOfEntriesCounter = meterRegistry.counter(DEVICE_SERVICE, CACHE, CURRENT_NUMBER_OF_ENTRIES);
  }

  public void onCacheHit() {
    cacheHitCounter.increment();
  }

  public void onCacheMiss() {
    cacheMissCounter.increment();
  }

  public void onCurrentNumberOfEntries(long size) {
    Validate.notNegative(size, "size");

    currentNumberOfEntriesCounter.increment(size);
  }
}
