package com.wirelesscar.tcevce.wecu.device.info.cache.core.impl;

import java.util.Optional;

import com.google.common.cache.Cache;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Ipv4Address;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Msisdn;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.wirelesscar.tcevce.wecu.device.info.database.model.Handle;
import com.wirelesscar.tcevce.wecu.device.info.database.model.PersistedDeviceInfo;
import com.wirelesscar.tcevce.wecu.device.info.database.model.SatelliteId;

final class CacheHolder {
  private final Cache<Handle, Optional<PersistedDeviceInfo>> handleCache;
  private final Cache<Ipv4Address, Optional<PersistedDeviceInfo>> ipAddressCache;
  private final Cache<Msisdn, Optional<PersistedDeviceInfo>> msisdnCache;
  private final Cache<SatelliteId, Optional<PersistedDeviceInfo>> satelliteIdCache;
  private final Cache<Vpi, Optional<PersistedDeviceInfo>> vpiCache;

  CacheHolder(CacheHolderBuilder cacheHolderBuilder) {
    Validate.notNull(cacheHolderBuilder, "cacheHolderBuilder");

    handleCache = cacheHolderBuilder.getHandleCache();
    ipAddressCache = cacheHolderBuilder.getIpAddressCache();
    msisdnCache = cacheHolderBuilder.getMsisdnCache();
    satelliteIdCache = cacheHolderBuilder.getSatelliteIdCache();
    vpiCache = cacheHolderBuilder.getVpiCache();
  }

  Cache<Handle, Optional<PersistedDeviceInfo>> getHandleCache() {
    return handleCache;
  }

  Cache<Ipv4Address, Optional<PersistedDeviceInfo>> getIpAddressCache() {
    return ipAddressCache;
  }

  Cache<Msisdn, Optional<PersistedDeviceInfo>> getMsisdnCache() {
    return msisdnCache;
  }

  Cache<SatelliteId, Optional<PersistedDeviceInfo>> getSatelliteIdCache() {
    return satelliteIdCache;
  }

  Cache<Vpi, Optional<PersistedDeviceInfo>> getVpiCache() {
    return vpiCache;
  }
}
