package com.wirelesscar.tcevce.wecu.device.info.cache.core.impl;

import java.util.Set;

import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.wirelesscar.tcevce.wecu.device.info.cache.core.api.VehicleKeyCache;

public class VehicleKeyServiceCacheManager {
  private final VehicleKeyCache vehicleKeyCache;

  public VehicleKeyServiceCacheManager(VehicleKeyCache vehicleKeyCache) {
    Validate.notNull(vehicleKeyCache, "vehicleKeyCache");

    this.vehicleKeyCache = vehicleKeyCache;
  }

  public void invalidateCache(Set<String> vehicleIds) {
    Validate.notEmpty(vehicleIds, "vehicleIds");

    vehicleIds.forEach(vehicleKeyCache::removeSymmetricKey);
  }
}
