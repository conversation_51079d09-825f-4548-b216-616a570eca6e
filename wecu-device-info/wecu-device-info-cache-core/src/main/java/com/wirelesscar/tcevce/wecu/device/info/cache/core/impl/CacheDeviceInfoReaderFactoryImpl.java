package com.wirelesscar.tcevce.wecu.device.info.cache.core.impl;

import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.wirelesscar.tcevce.wecu.device.info.cache.core.api.CacheDeviceInfoReader;
import com.wirelesscar.tcevce.wecu.device.info.database.api.DeviceInfoReader;
import com.wirelesscar.tcevce.wecu.device.info.database.api.DeviceInfoReaderFactory;

public final class CacheDeviceInfoReaderFactoryImpl implements DeviceInfoReaderFactory {
  private final CacheDeviceInfoReader cacheDeviceInfoReader;

  private CacheDeviceInfoReaderFactoryImpl(CacheDeviceInfoReader cacheDeviceInfoReader) {
    this.cacheDeviceInfoReader = cacheDeviceInfoReader;
  }

  public static DeviceInfoReaderFactory create(CacheDeviceInfoReader cacheDeviceInfoReader) {
    Validate.notNull(cacheDeviceInfoReader, "cacheDeviceInfoReader");

    return new CacheDeviceInfoReaderFactoryImpl(cacheDeviceInfoReader);
  }

  @Override
  public DeviceInfoReader create() {
    return cacheDeviceInfoReader;
  }
}
