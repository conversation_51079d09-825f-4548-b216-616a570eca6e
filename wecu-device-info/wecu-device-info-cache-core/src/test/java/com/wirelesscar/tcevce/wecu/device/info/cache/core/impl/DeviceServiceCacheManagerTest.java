package com.wirelesscar.tcevce.wecu.device.info.cache.core.impl;

import java.util.Collections;

import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import com.volvo.tisp.vc.test.utils.lib.AssertThrows;
import com.wirelesscar.tcevce.wecu.device.info.cache.core.TestUtils;
import com.wirelesscar.tcevce.wecu.device.info.cache.core.api.CacheDeviceInfoReader;

class DeviceServiceCacheManagerTest {
  @Test
  void invalidConstructorTest() {
    AssertThrows.illegalArgumentException(() -> new DeviceServiceCacheManager(null), "cacheDeviceInfoReader must not be null");
  }

  @Test
  void invalidateCacheInvalidTest() {
    DeviceServiceCacheManager deviceServiceCacheManager = new DeviceServiceCacheManager(Mockito.mock(CacheDeviceInfoReader.class));

    AssertThrows.illegalArgumentException(() -> deviceServiceCacheManager.invalidateCache(null), "handles must not be null");
    AssertThrows.illegalArgumentException(() -> deviceServiceCacheManager.invalidateCache(Collections.emptySet()), "handles must not be empty");
  }

  @Test
  void invalidateCacheTest() {
    CacheDeviceInfoReader cacheDeviceInfoReader = Mockito.mock(CacheDeviceInfoReader.class);
    DeviceServiceCacheManager deviceServiceCacheManager = new DeviceServiceCacheManager(cacheDeviceInfoReader);

    deviceServiceCacheManager.invalidateCache(Collections.singleton(TestUtils.HANDLE_2));

    Mockito.verify(cacheDeviceInfoReader).removeFromCache(Collections.singleton(TestUtils.HANDLE_2));
    Mockito.verifyNoMoreInteractions(cacheDeviceInfoReader);
  }
}
