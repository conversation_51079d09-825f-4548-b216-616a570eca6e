package com.wirelesscar.tcevce.wecu.device.info.cache.core.impl;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import com.volvo.tisp.vc.test.utils.lib.AssertThrows;
import com.volvo.tisp.vc.test.utils.lib.UtilClassVerifier;
import com.wirelesscar.tcevce.wecu.device.info.database.api.DeviceInfoReaderFactory;

class CacheDeviceInfoReaderFactoryTest {
  @Test
  void createInvalidTest() {
    DeviceInfoReaderFactory deviceInfoReaderFactory = Mockito.mock(DeviceInfoReaderFactory.class);
    CacheDeviceInfoMetricReporter cacheDeviceInfoMetricReporter = Mockito.mock(CacheDeviceInfoMetricReporter.class);

    AssertThrows.illegalArgumentException(() -> CacheDeviceInfoReaderFactory.create(null, cacheDeviceInfoMetricReporter, 0),
        "deviceInfoReaderFactory must not be null");
    AssertThrows.illegalArgumentException(() -> CacheDeviceInfoReaderFactory.create(deviceInfoReaderFactory, null, 0),
        "cacheDeviceInfoMetricReporter must not be null");
    AssertThrows.illegalArgumentException(() -> CacheDeviceInfoReaderFactory.create(deviceInfoReaderFactory, cacheDeviceInfoMetricReporter, -1),
        "maximumCacheSizeInBytes must not be negative: -1");
  }

  @Test
  void createTest() {
    DeviceInfoReaderFactory deviceInfoReaderFactory = Mockito.mock(DeviceInfoReaderFactory.class);
    CacheDeviceInfoMetricReporter cacheDeviceInfoMetricReporter = Mockito.mock(CacheDeviceInfoMetricReporter.class);

    Assertions.assertNotNull(CacheDeviceInfoReaderFactory.create(deviceInfoReaderFactory, cacheDeviceInfoMetricReporter, 0));
  }

  @Test
  void verifyUtilClassTest() throws ReflectiveOperationException {
    UtilClassVerifier.verifyUtilClass(CacheDeviceInfoReaderFactory.class);
  }
}
