package com.wirelesscar.tcevce.wecu.device.info.cache.core.impl;

import java.util.Collections;
import java.util.Set;

import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import com.volvo.tisp.vc.test.utils.lib.AssertThrows;
import com.wirelesscar.tcevce.wecu.device.info.cache.core.TestUtils;
import com.wirelesscar.tcevce.wecu.device.info.database.model.Handle;

class LocalCacheInvalidatorTest {
  private static final Set<Handle> HANDLES = Set.of(TestUtils.HANDLE_1, TestUtils.HANDLE_2);

  @Test
  void createInvalidTest() {
    AssertThrows.illegalArgumentException(() -> LocalCacheInvalidator.create(null), "deviceServiceCacheManager must not be null");
  }

  @Test
  void emptySetTest() {
    DeviceServiceCacheManager deviceServiceCacheManager = Mockito.mock(DeviceServiceCacheManager.class);

    AssertThrows.illegalArgumentException(() -> LocalCacheInvalidator.create(deviceServiceCacheManager).invalidateCache(Collections.emptySet()),
        "handles must not be empty");

    Mockito.verifyNoMoreInteractions(deviceServiceCacheManager);
  }

  @Test
  void invalidateCacheTest() {
    DeviceServiceCacheManager deviceServiceCacheManager = Mockito.mock(DeviceServiceCacheManager.class);

    LocalCacheInvalidator.create(deviceServiceCacheManager).invalidateCache(HANDLES);

    Mockito.verify(deviceServiceCacheManager).invalidateCache(HANDLES);
    Mockito.verifyNoMoreInteractions(deviceServiceCacheManager);
  }
}
