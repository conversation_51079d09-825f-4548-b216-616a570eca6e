package com.wirelesscar.tcevce.wecu.device.info.cache.core.impl;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import com.volvo.tisp.vc.test.utils.lib.AssertThrows;
import com.wirelesscar.tcevce.wecu.device.info.cache.core.api.CacheDeviceInfoReader;
import com.wirelesscar.tcevce.wecu.device.info.database.api.DeviceInfoReaderFactory;

class CacheDeviceInfoReaderFactoryImplTest {
  @Test
  void createTest() {
    CacheDeviceInfoReader cacheDeviceInfoReader = Mockito.mock(CacheDeviceInfoReader.class);

    DeviceInfoReaderFactory deviceInfoReaderFactory = CacheDeviceInfoReaderFactoryImpl.create(cacheDeviceInfoReader);
    Assertions.assertSame(deviceInfoReaderFactory.create(), deviceInfoReaderFactory.create());
  }

  @Test
  void invalidConstructorTest() {
    AssertThrows.illegalArgumentException(() -> CacheDeviceInfoReaderFactoryImpl.create(null), "cacheDeviceInfoReader must not be null");
  }
}
