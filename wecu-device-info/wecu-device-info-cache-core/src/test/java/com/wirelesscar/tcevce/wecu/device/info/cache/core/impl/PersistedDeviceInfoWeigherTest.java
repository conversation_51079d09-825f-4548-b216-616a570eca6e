package com.wirelesscar.tcevce.wecu.device.info.cache.core.impl;

import java.util.Optional;
import java.util.function.ToIntFunction;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import com.google.common.cache.Weigher;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;
import com.wirelesscar.tcevce.wecu.device.info.database.model.PersistedDeviceInfo;

class PersistedDeviceInfoWeigherTest {
  @Test
  void createInvalidTest() {
    AssertThrows.illegalArgumentException(() -> PersistedDeviceInfoWeigher.create(null), "toIntFunction must not be null");
  }

  @Test
  void weighInvalidTest() {
    Weigher<String, Optional<PersistedDeviceInfo>> weigher = PersistedDeviceInfoWeigher.create(value -> 0);

    AssertThrows.illegalArgumentException(() -> weigher.weigh(null, Optional.empty()), "key must not be null");
    AssertThrows.illegalArgumentException(() -> weigher.weigh("foo", null), "optional must not be null");
  }

  @Test
  void weighTest() {
    ToIntFunction<String> toIntFunction = Mockito.mock(ToIntFunction.class);
    Mockito.when(toIntFunction.applyAsInt("foo")).thenReturn(3);

    Weigher<String, Optional<PersistedDeviceInfo>> weigher = PersistedDeviceInfoWeigher.create(toIntFunction);

    int weigh = weigher.weigh("foo", Optional.empty());
    Assertions.assertEquals(3, weigh);

    Mockito.verify(toIntFunction).applyAsInt("foo");
    Mockito.verifyNoMoreInteractions(toIntFunction);
  }
}
