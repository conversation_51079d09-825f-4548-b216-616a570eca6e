package com.wirelesscar.tcevce.wecu.device.info.cache.core;

import java.time.Instant;
import java.util.Optional;

import com.volvo.tisp.vc.common.dto.lib.vehicle.Imsi;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Ipv4Address;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Msisdn;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.wirelesscar.tcevce.wecu.device.info.database.model.DeviceInfo;
import com.wirelesscar.tcevce.wecu.device.info.database.model.DeviceInfoBuilder;
import com.wirelesscar.tcevce.wecu.device.info.database.model.DeviceInfoId;
import com.wirelesscar.tcevce.wecu.device.info.database.model.DeviceSequence;
import com.wirelesscar.tcevce.wecu.device.info.database.model.DeviceSequenceBuilder;
import com.wirelesscar.tcevce.wecu.device.info.database.model.DeviceSequenceId;
import com.wirelesscar.tcevce.wecu.device.info.database.model.Handle;
import com.wirelesscar.tcevce.wecu.device.info.database.model.Ipv4Port;
import com.wirelesscar.tcevce.wecu.device.info.database.model.MobileNetworkOperator;
import com.wirelesscar.tcevce.wecu.device.info.database.model.PersistedDeviceInfo;
import com.wirelesscar.tcevce.wecu.device.info.database.model.PersistedDeviceInfoBuilder;
import com.wirelesscar.tcevce.wecu.device.info.database.model.PersistedDeviceSequence;
import com.wirelesscar.tcevce.wecu.device.info.database.model.PersistedDeviceSequenceBuilder;
import com.wirelesscar.tcevce.wecu.device.info.database.model.SatelliteId;
import com.wirelesscar.tcevce.wecu.device.info.database.model.SequenceNumber;
import com.wirelesscar.tcevce.wecu.device.info.database.model.SimInfo;
import com.wirelesscar.tcevce.wecu.device.info.database.model.SimInfoBuilder;

public final class TestUtils {
  public static final DeviceInfoId DEVICE_INFO_ID = DeviceInfoId.ofLong(42);
  public static final Handle HANDLE_1 = Handle.ofString("1");
  public static final Handle HANDLE_2 = Handle.ofString("2");
  public static final Ipv4Address IPV4_ADDRESS = Ipv4Address.ofString("*******");
  public static final MobileNetworkOperator MOBILE_NETWORK_OPERATOR = MobileNetworkOperator.ofString("testOperator");
  public static final Msisdn MSISDN = Msisdn.ofString("+469123456789");
  public static final SatelliteId SATELLITE_ID = SatelliteId.ofString("HQ1234567890x1");
  public static final Vpi VPI = Vpi.ofString("1234567890ABCDEF1234567890ABCDEF");

  public TestUtils() {
    throw new IllegalStateException();
  }

  public static DeviceInfo createDeviceInfo() {
    return createDeviceInfoBuilder().build();
  }

  public static DeviceInfoBuilder createDeviceInfoBuilder() {
    return new DeviceInfoBuilder()
        .setHandle(HANDLE_1)
        .setSatelliteId(Optional.of(SATELLITE_ID))
        .setSimInfo(Optional.of(createSimInfo()))
        .setVpi(Optional.of(VPI));
  }

  public static DeviceSequence createDeviceSequence(DeviceInfoId deviceInfoId) {
    return new DeviceSequenceBuilder()
        .setDeviceInfoId(deviceInfoId)
        .setSequenceNumber(SequenceNumber.ofByte((byte) 0))
        .build();
  }

  public static PersistedDeviceInfo createPersistedDeviceInfo() {
    return createPersistedDeviceInfoBuilder().build();
  }

  public static PersistedDeviceInfoBuilder createPersistedDeviceInfoBuilder() {
    return new PersistedDeviceInfoBuilder()
        .setCreated(Instant.now())
        .setDeviceInfo(createDeviceInfo())
        .setDeviceInfoId(DEVICE_INFO_ID)
        .setLastUpdated(Instant.now());
  }

  public static PersistedDeviceSequence createPersistedDeviceSequence() {
    return new PersistedDeviceSequenceBuilder()
        .setCreated(Instant.now())
        .setDeviceSequence(createDeviceSequence(DEVICE_INFO_ID))
        .setDeviceSequenceId(DeviceSequenceId.ofLong(42))
        .setLastUpdated(Instant.now())
        .build();
  }

  public static SimInfo createSimInfo() {
    return new SimInfoBuilder()
        .setIpv4Address(IPV4_ADDRESS)
        .setIpv4Port(Ipv4Port.ofInt(42))
        .setMobileNetworkOperator(MOBILE_NETWORK_OPERATOR)
        .setMsisdn(MSISDN)
        .setImsi(Imsi.ofLong(1L))
        .build();
  }
}
