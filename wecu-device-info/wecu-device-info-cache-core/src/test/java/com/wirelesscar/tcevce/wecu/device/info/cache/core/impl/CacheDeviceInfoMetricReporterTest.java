package com.wirelesscar.tcevce.wecu.device.info.cache.core.impl;

import java.util.function.Consumer;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tags;
import io.micrometer.core.instrument.simple.SimpleMeterRegistry;

class CacheDeviceInfoMetricReporterTest {
  private static void checkMetricCount(Consumer<CacheDeviceInfoMetricReporter> consumer, int expectedCount, String metricName, Tags tags) {
    MeterRegistry meterRegistry = new SimpleMeterRegistry();
    CacheDeviceInfoMetricReporter cacheDeviceInfoMetricReporter = new CacheDeviceInfoMetricReporter(meterRegistry);

    consumer.accept(cacheDeviceInfoMetricReporter);

    Counter counter = meterRegistry.find(metricName).tags(tags).counter();
    Assertions.assertEquals(expectedCount, counter.count());
  }

  @Test
  void invalidConstructorTest() {
    AssertThrows.illegalArgumentException(() -> new CacheDeviceInfoMetricReporter(null), "meterRegistry must not be null");
  }

  @Test
  void onCacheHitTest() {
    checkMetricCount(CacheDeviceInfoMetricReporter::onCacheHit, 1, CacheDeviceInfoMetricReporter.DEVICE_SERVICE, Tags.of(CacheDeviceInfoMetricReporter.CACHE,
        CacheDeviceInfoMetricReporter.HIT));
  }

  @Test
  void onCacheMissTest() {
    checkMetricCount(CacheDeviceInfoMetricReporter::onCacheMiss, 1, CacheDeviceInfoMetricReporter.DEVICE_SERVICE, Tags.of(CacheDeviceInfoMetricReporter.CACHE,
        CacheDeviceInfoMetricReporter.MISS));
  }

  @Test
  void onCurrentNumberOfEntriesInvalidTest() {
    CacheDeviceInfoMetricReporter cacheDeviceInfoMetricReporter = new CacheDeviceInfoMetricReporter(new SimpleMeterRegistry());
    AssertThrows.illegalArgumentException(() -> cacheDeviceInfoMetricReporter.onCurrentNumberOfEntries(-1), "size must not be negative: -1");
  }

  @Test
  void onCurrentNumberOfEntriesTest() {
    checkMetricCount(cacheDeviceInfoMetricReporter -> cacheDeviceInfoMetricReporter.onCurrentNumberOfEntries(42),
        42, CacheDeviceInfoMetricReporter.DEVICE_SERVICE,
        Tags.of(CacheDeviceInfoMetricReporter.CACHE, CacheDeviceInfoMetricReporter.CURRENT_NUMBER_OF_ENTRIES));
  }
}
