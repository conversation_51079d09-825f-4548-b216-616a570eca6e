package com.wirelesscar.tcevce.wecu.device.info.cache.core.impl;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import com.google.common.cache.Cache;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

class CacheHolderBuilderTest {
  private static <K, V> Cache<K, V> mockCache() {
    return Mockito.mock(Cache.class);
  }

  private static void verifyEquals(CacheHolderBuilder cacheHolderBuilder, CacheHolder cacheHolder) {
    Assertions.assertSame(cacheHolderBuilder.getHandleCache(), cacheHolder.getHandleCache());
    Assertions.assertSame(cacheHolderBuilder.getIpAddressCache(), cacheHolder.getIpAddressCache());
    Assertions.assertSame(cacheHolderBuilder.getMsisdnCache(), cacheHolder.getMsisdnCache());
    Assertions.assertSame(cacheHolderBuilder.getSatelliteIdCache(), cacheHolder.getSatelliteIdCache());
    Assertions.assertSame(cacheHolderBuilder.getVpiCache(), cacheHolder.getVpiCache());
  }

  @Test
  void buildTest() {
    CacheHolderBuilder cacheHolderBuilder = new CacheHolderBuilder();
    AssertThrows.illegalArgumentException(cacheHolderBuilder::build, "handleCache must not be null");

    cacheHolderBuilder.setHandleCache(mockCache());
    AssertThrows.illegalArgumentException(cacheHolderBuilder::build, "ipAddressCache must not be null");

    cacheHolderBuilder.setIpAddressCache(mockCache());
    AssertThrows.illegalArgumentException(cacheHolderBuilder::build, "msisdnCache must not be null");

    cacheHolderBuilder.setMsisdnCache(mockCache());
    AssertThrows.illegalArgumentException(cacheHolderBuilder::build, "satelliteIdCache must not be null");

    cacheHolderBuilder.setSatelliteIdCache(mockCache());
    AssertThrows.illegalArgumentException(cacheHolderBuilder::build, "vpiCache must not be null");

    cacheHolderBuilder.setVpiCache(mockCache());
    verifyEquals(cacheHolderBuilder, cacheHolderBuilder.build());
  }
}
