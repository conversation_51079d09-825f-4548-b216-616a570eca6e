package com.wirelesscar.tcevce.wecu.device.info.cache.core.impl;

import java.util.Collections;
import java.util.Optional;
import java.util.function.Consumer;
import java.util.function.Function;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentMatchers;
import org.mockito.Mockito;

import com.google.common.cache.CacheBuilder;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Ipv4Address;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Msisdn;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;
import com.wirelesscar.tcevce.wecu.device.info.cache.core.TestUtils;
import com.wirelesscar.tcevce.wecu.device.info.cache.core.api.CacheDeviceInfoReader;
import com.wirelesscar.tcevce.wecu.device.info.database.api.DeviceInfoReader;
import com.wirelesscar.tcevce.wecu.device.info.database.api.DeviceInfoReaderFactory;
import com.wirelesscar.tcevce.wecu.device.info.database.model.DeviceInfo;
import com.wirelesscar.tcevce.wecu.device.info.database.model.DeviceInfoId;
import com.wirelesscar.tcevce.wecu.device.info.database.model.Handle;
import com.wirelesscar.tcevce.wecu.device.info.database.model.PersistedDeviceInfo;
import com.wirelesscar.tcevce.wecu.device.info.database.model.PersistedDeviceSequence;
import com.wirelesscar.tcevce.wecu.device.info.database.model.SatelliteId;

class CacheDeviceInfoReaderImplTest {

  private static void checkAllFindByMethods(CacheDeviceInfoReader cacheDeviceInfoReader, final Optional<PersistedDeviceInfo> optional,
      CacheDeviceInfoMetricReporter cacheDeviceInfoMetricReporter) {
    Assertions.assertSame(optional, cacheDeviceInfoReader.findDeviceInfoByHandle(TestUtils.HANDLE_1));
    Mockito.verify(cacheDeviceInfoMetricReporter, Mockito.times(2)).onCacheHit();
    Mockito.verify(cacheDeviceInfoMetricReporter, Mockito.times(3)).onCurrentNumberOfEntries(ArgumentMatchers.anyLong());

    Assertions.assertSame(optional, cacheDeviceInfoReader.findDeviceInfoByVpi(TestUtils.VPI));
    Assertions.assertSame(optional, cacheDeviceInfoReader.findDeviceInfoByIpv4Address(TestUtils.IPV4_ADDRESS));
    Assertions.assertSame(optional, cacheDeviceInfoReader.findDeviceInfoByMsisdn(TestUtils.MSISDN));
    Assertions.assertSame(optional, cacheDeviceInfoReader.findDeviceInfoBySatelliteId(TestUtils.SATELLITE_ID));
    Mockito.verify(cacheDeviceInfoMetricReporter, Mockito.times(6)).onCacheHit();

    Mockito.verifyNoMoreInteractions(cacheDeviceInfoMetricReporter);
  }

  private static void checkCacheMethod(Function<DeviceInfoReader, Optional<PersistedDeviceInfo>> function) {
    DeviceInfoReader deviceInfoReader = Mockito.mock(DeviceInfoReader.class);
    DeviceInfoReaderFactory deviceInfoReaderFactory = mockDeviceInfoReaderFactory(deviceInfoReader);

    CacheDeviceInfoMetricReporter cacheDeviceInfoMetricReporter = Mockito.mock(CacheDeviceInfoMetricReporter.class);

    try (CacheDeviceInfoReader cacheDeviceInfoReader = CacheDeviceInfoReaderImpl.create(deviceInfoReaderFactory, cacheDeviceInfoMetricReporter,
        createCacheHolder())) {
      verifyFirstCacheMiss(function, deviceInfoReader, cacheDeviceInfoMetricReporter, cacheDeviceInfoReader);
      verifyFirstCacheHit(function, deviceInfoReader, cacheDeviceInfoMetricReporter, cacheDeviceInfoReader);
      verifyFindByFunction(function, deviceInfoReaderFactory, cacheDeviceInfoMetricReporter, cacheDeviceInfoReader);
    }
  }

  private static CacheHolder createCacheHolder() {
    return new CacheHolderBuilder()
        .setHandleCache(CacheBuilder.newBuilder().build())
        .setIpAddressCache(CacheBuilder.newBuilder().build())
        .setMsisdnCache(CacheBuilder.newBuilder().build())
        .setSatelliteIdCache(CacheBuilder.newBuilder().build())
        .setVpiCache(CacheBuilder.newBuilder().build())
        .build();
  }

  private static DeviceInfoReader mockDeviceInfoReader(Optional<PersistedDeviceInfo> optional) {
    DeviceInfoReader deviceInfoReader = Mockito.mock(DeviceInfoReader.class);
    DeviceInfo deviceInfo = optional.get().getDeviceInfo();

    Mockito.when(deviceInfoReader.findDeviceInfoByHandle(deviceInfo.getHandle())).thenReturn(optional);
    Mockito.when(deviceInfoReader.findDeviceInfoByIpv4Address(deviceInfo.getSimInfo().get().getIpv4Address())).thenReturn(optional);
    Mockito.when(deviceInfoReader.findDeviceInfoByMsisdn(deviceInfo.getSimInfo().get().getMsisdn())).thenReturn(optional);
    Mockito.when(deviceInfoReader.findDeviceInfoBySatelliteId(deviceInfo.getSatelliteId().get())).thenReturn(optional);
    Mockito.when(deviceInfoReader.findDeviceInfoByVpi(deviceInfo.getVpi().get())).thenReturn(optional);

    return deviceInfoReader;
  }

  private static DeviceInfoReaderFactory mockDeviceInfoReaderFactory(DeviceInfoReader deviceInfoReader) {
    DeviceInfoReaderFactory deviceInfoReaderFactory = Mockito.mock(DeviceInfoReaderFactory.class);
    Mockito.when(deviceInfoReaderFactory.create()).thenReturn(deviceInfoReader);
    return deviceInfoReaderFactory;
  }

  private static void verifyCurrentNumberOfEntries(CacheDeviceInfoMetricReporter cacheDeviceInfoMetricReporter) {
    Mockito.verify(cacheDeviceInfoMetricReporter).onCurrentNumberOfEntries(ArgumentMatchers.anyLong());
  }

  private static void verifyFindByFunction(Function<DeviceInfoReader, Optional<PersistedDeviceInfo>> function, DeviceInfoReaderFactory deviceInfoReaderFactory,
      CacheDeviceInfoMetricReporter cacheDeviceInfoMetricReporter, CacheDeviceInfoReader cacheDeviceInfoReader) {
    Optional<PersistedDeviceInfo> persistedDeviceInfo = Optional.of(TestUtils.createPersistedDeviceInfo());
    DeviceInfoReader deviceInfoReader = mockDeviceInfoReader(persistedDeviceInfo);
    Mockito.when(deviceInfoReaderFactory.create()).thenReturn(deviceInfoReader);

    cacheDeviceInfoReader.removeFromCache(Collections.singleton(persistedDeviceInfo.get().getDeviceInfo().getHandle()));
    Mockito.verify(deviceInfoReader).findDeviceInfoByHandle(persistedDeviceInfo.get().getDeviceInfo().getHandle());
    Mockito.verify(deviceInfoReader).close();
    Mockito.verifyNoMoreInteractions(deviceInfoReader);

    deviceInfoReader = mockDeviceInfoReader(persistedDeviceInfo);
    Mockito.when(deviceInfoReaderFactory.create()).thenReturn(deviceInfoReader);
    Assertions.assertSame(persistedDeviceInfo, function.apply(cacheDeviceInfoReader));
    function.apply(Mockito.verify(deviceInfoReader));
    Mockito.verify(deviceInfoReader).close();
    Mockito.verify(cacheDeviceInfoMetricReporter, Mockito.times(2)).onCacheMiss();
    checkAllFindByMethods(cacheDeviceInfoReader, persistedDeviceInfo, cacheDeviceInfoMetricReporter);
    Mockito.verifyNoMoreInteractions(deviceInfoReader);
  }

  private static void verifyFirstCacheHit(Function<DeviceInfoReader, Optional<PersistedDeviceInfo>> function, DeviceInfoReader deviceInfoReader,
      CacheDeviceInfoMetricReporter cacheDeviceInfoMetricReporter, DeviceInfoReader cacheDeviceInfoReader) {
    Assertions.assertFalse(function.apply(cacheDeviceInfoReader).isPresent());

    Mockito.verifyNoMoreInteractions(deviceInfoReader);
    Mockito.verify(cacheDeviceInfoMetricReporter).onCacheHit();
    Mockito.verifyNoMoreInteractions(cacheDeviceInfoMetricReporter);
  }

  private static void verifyFirstCacheMiss(Function<DeviceInfoReader, Optional<PersistedDeviceInfo>> function, DeviceInfoReader deviceInfoReader,
      CacheDeviceInfoMetricReporter cacheDeviceInfoMetricReporter, DeviceInfoReader cacheDeviceInfoReader) {
    Assertions.assertFalse(function.apply(cacheDeviceInfoReader).isPresent());

    function.apply(Mockito.verify(deviceInfoReader));
    Mockito.verify(deviceInfoReader).close();
    Mockito.verifyNoMoreInteractions(deviceInfoReader);
    Mockito.verify(cacheDeviceInfoMetricReporter).onCacheMiss();
    verifyCurrentNumberOfEntries(cacheDeviceInfoMetricReporter);
    Mockito.verifyNoMoreInteractions(cacheDeviceInfoMetricReporter);
  }

  private static void verifyIllegalArgumentException(Consumer<CacheDeviceInfoReader> consumer, String expectedMessage) {
    try (CacheDeviceInfoReader cacheDeviceInfoReader = CacheDeviceInfoReaderImpl.create(Mockito.mock(DeviceInfoReaderFactory.class),
        Mockito.mock(CacheDeviceInfoMetricReporter.class), createCacheHolder())) {
      AssertThrows.illegalArgumentException(() -> consumer.accept(cacheDeviceInfoReader), expectedMessage);
    }
  }

  @Test
  void countAllDevicesTest() {
    DeviceInfoReader deviceInfoReader = Mockito.mock(DeviceInfoReader.class);
    DeviceInfoReaderFactory deviceInfoReaderFactory = mockDeviceInfoReaderFactory(deviceInfoReader);

    try (DeviceInfoReader cacheDeviceInfoReader = CacheDeviceInfoReaderImpl.create(deviceInfoReaderFactory, Mockito.mock(CacheDeviceInfoMetricReporter.class),
        createCacheHolder())) {
      Assertions.assertEquals(0, cacheDeviceInfoReader.countAllDevices());
      Mockito.verify(deviceInfoReader).countAllDevices();
      Mockito.verify(deviceInfoReader).close();

      Mockito.when(Integer.valueOf(deviceInfoReader.countAllDevices())).thenReturn(Integer.valueOf(42));

      Assertions.assertEquals(42, cacheDeviceInfoReader.countAllDevices());
      Mockito.verify(deviceInfoReader, Mockito.times(2)).countAllDevices();
      Mockito.verify(deviceInfoReader, Mockito.times(2)).close();
      Mockito.verifyNoMoreInteractions(deviceInfoReader);
    }
  }

  @Test
  void createInvalidTest() {
    DeviceInfoReaderFactory deviceInfoReaderFactory = Mockito.mock(DeviceInfoReaderFactory.class);
    CacheDeviceInfoMetricReporter cacheDeviceInfoMetricReporter = Mockito.mock(CacheDeviceInfoMetricReporter.class);
    CacheHolder cacheHolder = createCacheHolder();

    AssertThrows.illegalArgumentException(() -> CacheDeviceInfoReaderImpl.create(null, cacheDeviceInfoMetricReporter, cacheHolder),
        "deviceInfoReaderFactory must not be null");
    AssertThrows.illegalArgumentException(() -> CacheDeviceInfoReaderImpl.create(deviceInfoReaderFactory, null, cacheHolder),
        "cacheDeviceInfoMetricReporter must not be null");
    AssertThrows.illegalArgumentException(() -> CacheDeviceInfoReaderImpl.create(deviceInfoReaderFactory, cacheDeviceInfoMetricReporter, null),
        "cacheHolder must not be null");
  }

  @Test
  void findDeviceInfoByDeviceInfoIdTest() {
    verifyIllegalArgumentException(cacheDeviceInfoReader -> cacheDeviceInfoReader.findDeviceInfoByDeviceInfoId(null), "deviceInfoId must not be null");

    DeviceInfoReader deviceInfoReader = Mockito.mock(DeviceInfoReader.class);
    DeviceInfoReaderFactory deviceInfoReaderFactory = mockDeviceInfoReaderFactory(deviceInfoReader);

    CacheDeviceInfoMetricReporter cacheDeviceInfoMetricReporter = Mockito.mock(CacheDeviceInfoMetricReporter.class);

    try (DeviceInfoReader cacheDeviceInfoReader = CacheDeviceInfoReaderImpl.create(deviceInfoReaderFactory, cacheDeviceInfoMetricReporter,
        createCacheHolder())) {
      Assertions.assertFalse(cacheDeviceInfoReader.findDeviceInfoByDeviceInfoId(TestUtils.DEVICE_INFO_ID).isPresent());
      Mockito.verify(deviceInfoReader).findDeviceInfoByDeviceInfoId(TestUtils.DEVICE_INFO_ID);
      Mockito.verify(cacheDeviceInfoMetricReporter).onCurrentNumberOfEntries(0);
      Mockito.verifyNoMoreInteractions(cacheDeviceInfoMetricReporter);

      Optional<PersistedDeviceInfo> optional = Optional.of(TestUtils.createPersistedDeviceInfo());
      Mockito.when(deviceInfoReader.findDeviceInfoByDeviceInfoId(TestUtils.DEVICE_INFO_ID)).thenReturn(optional);

      Assertions.assertSame(optional, cacheDeviceInfoReader.findDeviceInfoByDeviceInfoId(TestUtils.DEVICE_INFO_ID));
      Mockito.verify(cacheDeviceInfoMetricReporter).onCurrentNumberOfEntries(1);
      Mockito.verifyNoMoreInteractions(cacheDeviceInfoMetricReporter);
    }
  }

  @Test
  void findDeviceInfoByHandleTest() {
    verifyIllegalArgumentException(cacheDeviceInfoReader -> cacheDeviceInfoReader.findDeviceInfoByHandle(null), "handle must not be null");

    checkCacheMethod(deviceInfoReader -> deviceInfoReader.findDeviceInfoByHandle(TestUtils.HANDLE_1));
  }

  @Test
  void findDeviceInfoByIpv4AddressTest() {
    verifyIllegalArgumentException(cacheDeviceInfoReader -> cacheDeviceInfoReader.findDeviceInfoByIpv4Address(null), "ip4Address must not be null");

    checkCacheMethod(deviceInfoReader -> deviceInfoReader.findDeviceInfoByIpv4Address(TestUtils.IPV4_ADDRESS));
  }

  @Test
  void findDeviceInfoByMsisdnTest() {
    verifyIllegalArgumentException(cacheDeviceInfoReader -> cacheDeviceInfoReader.findDeviceInfoByMsisdn(null), "msisdn must not be null");

    checkCacheMethod(deviceInfoReader -> deviceInfoReader.findDeviceInfoByMsisdn(TestUtils.MSISDN));
  }

  @Test
  void findDeviceInfoBySatelliteIdTest() {
    verifyIllegalArgumentException(cacheDeviceInfoReader -> cacheDeviceInfoReader.findDeviceInfoBySatelliteId(null), "satelliteId must not be null");

    checkCacheMethod(deviceInfoReader -> deviceInfoReader.findDeviceInfoBySatelliteId(TestUtils.SATELLITE_ID));
  }

  @Test
  void findDeviceInfoByVpiTest() {
    verifyIllegalArgumentException(cacheDeviceInfoReader -> cacheDeviceInfoReader.findDeviceInfoByVpi(null), "vpi must not be null");

    checkCacheMethod(deviceInfoReader -> deviceInfoReader.findDeviceInfoByVpi(TestUtils.VPI));
  }

  @Test
  void findDeviceSequenceByDeviceInfoIdTest() {
    verifyIllegalArgumentException(cacheDeviceInfoReader -> cacheDeviceInfoReader.findDeviceSequenceByDeviceInfoId(null), "deviceInfoId must not be null");

    DeviceInfoReader deviceInfoReader = Mockito.mock(DeviceInfoReader.class);
    DeviceInfoReaderFactory deviceInfoReaderFactory = mockDeviceInfoReaderFactory(deviceInfoReader);

    CacheDeviceInfoMetricReporter cacheDeviceInfoMetricReporter = Mockito.mock(CacheDeviceInfoMetricReporter.class);

    try (DeviceInfoReader cacheDeviceInfoReader = CacheDeviceInfoReaderImpl.create(deviceInfoReaderFactory, cacheDeviceInfoMetricReporter,
        createCacheHolder())) {
      DeviceInfoId deviceInfoId = TestUtils.DEVICE_INFO_ID;

      Assertions.assertFalse(cacheDeviceInfoReader.findDeviceSequenceByDeviceInfoId(deviceInfoId).isPresent());
      Mockito.verify(deviceInfoReader).findDeviceSequenceByDeviceInfoId(deviceInfoId);

      Assertions.assertFalse(cacheDeviceInfoReader.findDeviceSequenceByDeviceInfoId(deviceInfoId).isPresent());
      Mockito.verify(deviceInfoReader, Mockito.times(2)).findDeviceSequenceByDeviceInfoId(deviceInfoId);

      Optional<PersistedDeviceSequence> optional = Optional.of(TestUtils.createPersistedDeviceSequence());
      Mockito.when(deviceInfoReader.findDeviceSequenceByDeviceInfoId(deviceInfoId)).thenReturn(optional);

      Assertions.assertSame(optional, cacheDeviceInfoReader.findDeviceSequenceByDeviceInfoId(deviceInfoId));
      Mockito.verify(deviceInfoReader, Mockito.times(3)).findDeviceSequenceByDeviceInfoId(deviceInfoId);

      Assertions.assertSame(optional, cacheDeviceInfoReader.findDeviceSequenceByDeviceInfoId(deviceInfoId));
      Mockito.verify(deviceInfoReader, Mockito.times(4)).findDeviceSequenceByDeviceInfoId(deviceInfoId);

      Mockito.verifyNoInteractions(cacheDeviceInfoMetricReporter);
    }
  }

  @Test
  void removeFromCacheTest() {
    Handle handle = TestUtils.HANDLE_1;
    Ipv4Address ipv4Address = TestUtils.IPV4_ADDRESS;
    Msisdn msisdn = TestUtils.MSISDN;
    SatelliteId satelliteId = TestUtils.SATELLITE_ID;
    Vpi vpi = TestUtils.VPI;

    verifyIllegalArgumentException(cacheDeviceInfoReader -> cacheDeviceInfoReader.removeFromCache(null), "handles must not be null");

    DeviceInfoReader deviceInfoReader = Mockito.mock(DeviceInfoReader.class);
    DeviceInfoReaderFactory deviceInfoReaderFactory = mockDeviceInfoReaderFactory(deviceInfoReader);
    CacheDeviceInfoMetricReporter cacheDeviceInfoMetricReporter = Mockito.mock(CacheDeviceInfoMetricReporter.class);

    try (CacheDeviceInfoReader cacheDeviceInfoReader = CacheDeviceInfoReaderImpl.create(deviceInfoReaderFactory, cacheDeviceInfoMetricReporter,
        createCacheHolder())) {
      Optional<PersistedDeviceInfo> persistedDeviceInfo = Optional.of(TestUtils.createPersistedDeviceInfo());

      Mockito.when(deviceInfoReader.findDeviceInfoByHandle(handle)).thenReturn(persistedDeviceInfo);
      Assertions.assertSame(persistedDeviceInfo, cacheDeviceInfoReader.findDeviceInfoByHandle(handle));
      Mockito.verify(deviceInfoReader).findDeviceInfoByHandle(handle);

      cacheDeviceInfoReader.removeFromCache(Collections.singleton(handle));
      Mockito.verify(deviceInfoReader, Mockito.times(2)).findDeviceInfoByHandle(handle);

      Assertions.assertSame(Optional.empty(), cacheDeviceInfoReader.findDeviceInfoByIpv4Address(ipv4Address));
      Assertions.assertSame(Optional.empty(), cacheDeviceInfoReader.findDeviceInfoByMsisdn(msisdn));
      Assertions.assertSame(Optional.empty(), cacheDeviceInfoReader.findDeviceInfoBySatelliteId(satelliteId));
      Assertions.assertSame(Optional.empty(), cacheDeviceInfoReader.findDeviceInfoByVpi(vpi));

      Mockito.verify(deviceInfoReader).findDeviceInfoByIpv4Address(ipv4Address);
      Mockito.verify(deviceInfoReader).findDeviceInfoByMsisdn(msisdn);
      Mockito.verify(deviceInfoReader).findDeviceInfoBySatelliteId(satelliteId);
      Mockito.verify(deviceInfoReader).findDeviceInfoByVpi(vpi);

      Assertions.assertSame(persistedDeviceInfo, cacheDeviceInfoReader.findDeviceInfoByHandle(handle));
      Assertions.assertSame(persistedDeviceInfo, cacheDeviceInfoReader.findDeviceInfoByIpv4Address(ipv4Address));
      Assertions.assertSame(persistedDeviceInfo, cacheDeviceInfoReader.findDeviceInfoByMsisdn(msisdn));
      Assertions.assertSame(persistedDeviceInfo, cacheDeviceInfoReader.findDeviceInfoBySatelliteId(satelliteId));
      Assertions.assertSame(persistedDeviceInfo, cacheDeviceInfoReader.findDeviceInfoByVpi(vpi));

      Mockito.verify(deviceInfoReader, Mockito.times(3)).findDeviceInfoByHandle(handle);
      Mockito.verify(deviceInfoReader).findDeviceInfoByIpv4Address(ipv4Address);
      Mockito.verify(deviceInfoReader).findDeviceInfoBySatelliteId(satelliteId);
      Mockito.verify(deviceInfoReader).findDeviceInfoByMsisdn(msisdn);
      Mockito.verify(deviceInfoReader).findDeviceInfoByVpi(vpi);
    }
  }
}
