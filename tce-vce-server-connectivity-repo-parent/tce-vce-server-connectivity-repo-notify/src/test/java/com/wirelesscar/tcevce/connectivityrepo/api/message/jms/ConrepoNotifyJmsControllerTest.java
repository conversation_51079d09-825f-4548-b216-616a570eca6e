package com.wirelesscar.tcevce.connectivityrepo.api.message.jms;

import org.junit.jupiter.api.Test;
import org.mockito.InOrder;
import org.mockito.Mockito;

import com.volvo.tisp.framework.jms.JmsMessage;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;
import com.wirelesscar.conrepo.api.v1.ActivationNotifyEventMessage;
import com.wirelesscar.tcevce.connectivityrepo.api.message.domain.ConrepoNotifyDomainManager;

class ConrepoNotifyJmsControllerTest {

  private static JmsMessage<ActivationNotifyEventMessage> mockJmsMessage() {
    JmsMessage<ActivationNotifyEventMessage> jmsMessage = Mockito.mock(JmsMessage.class);
    Mockito.when(jmsMessage.payload()).thenReturn(new ActivationNotifyEventMessage());
    return jmsMessage;
  }

  @Test
  void constructorInvalidTest() {
    AssertThrows.illegalArgumentException(() -> new ConrepoNotifyJmsController(null), "conrepoNotifyDomainManager must not be null");
  }

  @Test
  void receiveMessageV1InvalidTest() {
    ConrepoNotifyDomainManager conrepoNotifyDomainManager = Mockito.mock(ConrepoNotifyDomainManager.class);

    ConrepoNotifyJmsController conrepoNotifyJmsController = new ConrepoNotifyJmsController(conrepoNotifyDomainManager);
    AssertThrows.illegalArgumentException(() -> conrepoNotifyJmsController.receiveMessageV1(null), "jmsMessage must not be null");

    Mockito.verifyNoInteractions(conrepoNotifyDomainManager);
  }

  @Test
  void receiveMessageV1Test() {
    ConrepoNotifyDomainManager conrepoNotifyDomainManager = Mockito.mock(ConrepoNotifyDomainManager.class);

    JmsMessage<ActivationNotifyEventMessage> jmsActivationEventMessage = mockJmsMessage();

    ConrepoNotifyJmsController conrepoNotifyJmsController = new ConrepoNotifyJmsController(conrepoNotifyDomainManager);
    conrepoNotifyJmsController.receiveMessageV1(jmsActivationEventMessage);

    InOrder inOrder = Mockito.inOrder(conrepoNotifyDomainManager);
    inOrder.verify(conrepoNotifyDomainManager).receiveMessageV1(jmsActivationEventMessage.payload());
    inOrder.verifyNoMoreInteractions();
  }
}
