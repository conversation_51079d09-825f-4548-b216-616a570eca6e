package com.wirelesscar.tcevce.connectivityrepo.api.message.domain;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.ArgumentMatchers;
import org.mockito.InOrder;
import org.mockito.Mockito;

import com.volvo.tisp.vc.common.dto.lib.vehicle.Imsi;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Ipv4Address;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Msisdn;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.tisp.vc.main.utils.lib.type.Either;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;
import com.wirelesscar.conrepo.api.v1.ActivationNotifyEvent;
import com.wirelesscar.conrepo.api.v1.ActivationNotifyEventMessage;
import com.wirelesscar.conrepo.api.v1.ChangeStatus;
import com.wirelesscar.conrepo.api.v1.DeviceDetailedEntry;
import com.wirelesscar.conrepo.api.v1.DeviceSim;
import com.wirelesscar.conrepo.api.v1.State;
import com.wirelesscar.tcevce.connectivityrepo.api.TestUtils;
import com.wirelesscar.tcevce.wecu.device.info.cache.core.api.VehicleKeyCacheInvalidator;
import com.wirelesscar.tcevce.wecu.device.info.database.api.DeviceInfoWriter;
import com.wirelesscar.tcevce.wecu.device.info.database.api.DeviceInfoWriterFactory;
import com.wirelesscar.tcevce.wecu.device.info.database.api.WecuKeyWriter;
import com.wirelesscar.tcevce.wecu.device.info.database.api.WecuKeyWriterFactory;
import com.wirelesscar.tcevce.wecu.device.info.database.model.DeviceInfo;
import com.wirelesscar.tcevce.wecu.device.info.database.model.DeviceInfoId;
import com.wirelesscar.tcevce.wecu.device.info.database.model.DeviceSequence;
import com.wirelesscar.tcevce.wecu.device.info.database.model.Handle;
import com.wirelesscar.tcevce.wecu.device.info.database.model.InsertionFailure;
import com.wirelesscar.tcevce.wecu.device.info.database.model.InsertionFailureReason;
import com.wirelesscar.tcevce.wecu.device.info.database.model.PersistedDeviceInfo;
import com.wirelesscar.tcevce.wecu.device.info.database.model.SatelliteId;
import com.wirelesscar.tcevce.wecu.device.info.database.model.SequenceNumber;
import com.wirelesscar.tcevce.wecu.device.info.database.model.SimInfo;
import com.wirelesscar.tcevce.wecu.device.info.database.model.UniqueVehicleIdentifier;

class NotifyRepositoryWorkerTest {
  private static final DeviceInfoId DEVICE_INFO_ID_1 = DeviceInfoId.ofLong(11);
  private static final DeviceInfoId DEVICE_INFO_ID_2 = DeviceInfoId.ofLong(22);
  private static final DeviceInfoId DEVICE_INFO_ID_3 = DeviceInfoId.ofLong(33);
  private static final DeviceInfoId DEVICE_INFO_ID_4 = DeviceInfoId.ofLong(44);
  private static final DeviceInfoId DEVICE_INFO_ID_5 = DeviceInfoId.ofLong(55);
  private static final Handle HANDLE_1 = Handle.ofString("1");
  private static final Handle HANDLE_2 = Handle.ofString("2");
  private static final Handle HANDLE_3 = Handle.ofString("3");
  private static final Handle HANDLE_4 = Handle.ofString("4");
  private static final Handle HANDLE_5 = Handle.ofString("5");
  private static final Ipv4Address IPV4_ADDRESS_1 = Ipv4Address.ofString("*******");
  private static final Ipv4Address IPV4_ADDRESS_2 = Ipv4Address.ofString("*******");
  private static final Ipv4Address IPV4_ADDRESS_3 = Ipv4Address.ofString("*******");
  private static final Ipv4Address IPV4_ADDRESS_4 = Ipv4Address.ofString("*******");
  private static final Msisdn MSISDN_1 = Msisdn.ofString("+12311");
  private static final Msisdn MSISDN_2 = Msisdn.ofString("+12322");
  private static final Msisdn MSISDN_3 = Msisdn.ofString("+12333");
  private static final Msisdn MSISDN_4 = Msisdn.ofString("+12344");
  private static final SatelliteId SATELLITE_ID_1 = SatelliteId.ofString("hq1234567890x1");
  private static final SatelliteId SATELLITE_ID_2 = SatelliteId.ofString("hq2234567890x1");
  private static final SatelliteId SATELLITE_ID_3 = SatelliteId.ofString("hq3234567890x1");
  private static final SatelliteId SATELLITE_ID_4 = SatelliteId.ofString("hq4234567890x1");
  private static final Vpi VPI_1 = Vpi.ofString("11111111111111111111111111111111");
  private static final Vpi VPI_2 = Vpi.ofString("22222222222222222222222222222222");
  private static final Vpi VPI_3 = Vpi.ofString("33333333333333333333333333333333");
  private static final Vpi VPI_4 = Vpi.ofString("44444444444444444444444444444444");
  private DeviceInfoWriter deviceInfoWriter;
  private DeviceInfoWriterFactory deviceInfoWriterFactory;
  private NotifyRepositoryWorker notifyRepositoryWorker;
  private VehicleKeyCacheInvalidator vehicleKeyCacheInvalidator;
  private WecuKeyWriter wecuKeyWriter;
  private WecuKeyWriterFactory wecuKeyWriterFactory;

  private static void assertEqualKeys(ActivationNotifyEvent activationNotifyEvent, DeviceInfo deviceInfo) {
    DeviceDetailedEntry deviceDetailedEntry = activationNotifyEvent.getDeviceDetail();

    Assertions.assertEquals(deviceDetailedEntry.getHandle(), deviceInfo.getHandle().toString());
    Assertions.assertEquals(deviceDetailedEntry.getVehiclePlatformId(), deviceInfo.getVpi().get().toString());
    Assertions.assertEquals(deviceDetailedEntry.getSatelliteId(), deviceInfo.getSatelliteId().get().toString());

    assertSimInfoEqualKeys(deviceInfo.getSimInfo().get(), deviceDetailedEntry.getSimEntry());
  }

  private static void assertSimInfoEqualKeys(SimInfo simInfo, DeviceSim deviceSim) {
    Assertions.assertEquals(deviceSim.getMsisdn(), simInfo.getMsisdn().toString());
    Assertions.assertEquals(deviceSim.getIp(), simInfo.getIpv4Address().toString());
  }

  private static ActivationNotifyEvent creatActivationNotifyEvent(Vpi vpi, Handle handle, Msisdn msisdn, Ipv4Address ipv4Address, SatelliteId satelliteId,
      State state, ChangeStatus changeStatus) {
    DeviceDetailedEntry deviceDetailedEntry = new DeviceDetailedEntry();
    deviceDetailedEntry.setHandle(handle.toString());
    deviceDetailedEntry.setSatelliteId(satelliteId.toString());
    deviceDetailedEntry.setState(state);
    deviceDetailedEntry.setVehiclePlatformId(vpi.toString());

    DeviceSim deviceSim = TestUtils.createDeviceSim();
    deviceSim.setMsisdn(msisdn.toString());
    deviceSim.setIp(ipv4Address.toString());

    deviceDetailedEntry.setSimEntry(deviceSim);

    ActivationNotifyEvent activationNotifyEvent = new ActivationNotifyEvent();
    activationNotifyEvent.setChangeStatus(changeStatus);
    activationNotifyEvent.setDeviceDetail(deviceDetailedEntry);

    return activationNotifyEvent;
  }

  private static ActivationNotifyEventMessage createActivationNotifyMessageExceedingMaxSqlLimit(int numberOfDevices) {
    List<ActivationNotifyEvent> activationNotifyEvents = IntStream.range(1, numberOfDevices)
        .mapToObj(i -> createDeviceDetailedEntryContinuous(i, createDeviceSimContinuous(i), State.ACTIVATED))
        .map(deviceDetailedEntry -> TestUtils.createActivationNotifyEvent(ChangeStatus.UPDATED, deviceDetailedEntry))
        .collect(Collectors.toList());

    ActivationNotifyEventMessage activationNotifyEventMessage = TestUtils.createActivationNotifyEventMessage();
    activationNotifyEventMessage.getActivationNotifyEvents().addAll(activationNotifyEvents);

    return activationNotifyEventMessage;
  }

  private static DeviceDetailedEntry createDeviceDetailedEntryContinuous(int i, DeviceSim deviceSim, State state) {
    Vpi vpi = Vpi.ofString(UUID.randomUUID().toString().replaceAll("-", "").toUpperCase());

    DeviceDetailedEntry deviceDetailedEntry = new DeviceDetailedEntry();
    deviceDetailedEntry.setHandle(Handle.ofString(Integer.toString(i)).toString());
    deviceDetailedEntry.setSatelliteId(SatelliteId.ofString("HQ" + (1_234_567_890 + i) + "x1").toString());
    deviceDetailedEntry.setSimEntry(deviceSim);
    deviceDetailedEntry.setState(state);
    deviceDetailedEntry.setVehiclePlatformId(vpi.toString());

    return deviceDetailedEntry;
  }

  private static DeviceSim createDeviceSimContinuous(int seed) {
    DeviceSim deviceSim = new DeviceSim();

    deviceSim.setImsi(Imsi.ofLong(1L).toString());
    deviceSim.setIp(Ipv4Address.ofInt(seed).toString());
    deviceSim.setMsisdn(Msisdn.ofString("+12345" + seed).toString());
    deviceSim.setPort(TestUtils.IPV4_PORT.toInt());
    deviceSim.setOperator(TestUtils.MOBILE_NETWORK_OPERATOR.toString());

    return deviceSim;
  }

  private static PersistedDeviceInfo createPersistedDeviceInfo(Vpi vpi, SatelliteId satelliteId, Handle handle, Msisdn msisdn, Ipv4Address ipv4Address,
      DeviceInfoId deviceInfoId) {
    SimInfo simInfo = TestUtils.createSimInfoBuilder()
        .setIpv4Address(ipv4Address)
        .setMsisdn(msisdn)
        .build();

    DeviceInfo deviceInfo = TestUtils.createDeviceInfoBuilder()
        .setHandle(handle)
        .setSimInfo(Optional.of(simInfo))
        .setVpi(Optional.of(vpi))
        .setSatelliteId(Optional.of(satelliteId))
        .build();

    return TestUtils.createPersistedDeviceInfoBuilder()
        .setDeviceInfo(deviceInfo)
        .setDeviceInfoId(deviceInfoId)
        .build();
  }

  private static void mockFindDeviceInfoByHandle(DeviceInfoWriter deviceInfoWriter) {
    SimInfo simInfo = TestUtils.createSimInfoBuilder()
        .setIpv4Address(IPV4_ADDRESS_4)
        .setMsisdn(MSISDN_3)
        .build();
    DeviceInfo deviceInfo = TestUtils.createDeviceInfoBuilder()
        .setSimInfo(Optional.of(simInfo))
        .setHandle(HANDLE_5)
        .build();
    PersistedDeviceInfo persistedDeviceInfo = TestUtils.createPersistedDeviceInfoBuilder()
        .setDeviceInfo(deviceInfo)
        .setDeviceInfoId(DEVICE_INFO_ID_5)
        .build();

    Mockito.when(deviceInfoWriter.findDeviceInfoByHandle(HANDLE_5)).thenReturn(Optional.of(persistedDeviceInfo));
  }

  private static <T> void verifyCaptorValuesSize(int expectedSize, ArgumentCaptor<T> argumentCaptor) {
    Assertions.assertEquals(expectedSize, argumentCaptor.getAllValues().size());
  }

  private static void verifyDeviceSequence(DeviceSequence deviceSequence) {
    Assertions.assertEquals(DEVICE_INFO_ID_5, deviceSequence.getDeviceInfoId());
    Assertions.assertEquals(SequenceNumber.ofByte((byte) 0), deviceSequence.getSequenceNumber());
  }

  private static void verifyKeysEquals(ActivationNotifyEventMessage activationNotifyEventMessage, UniqueVehicleIdentifier uniqueVehicleIdentifier) {
    verifyKeysEquals(activationNotifyEventMessage.getActivationNotifyEvents().get(0), uniqueVehicleIdentifier);
  }

  private static void verifyKeysEquals(ActivationNotifyEvent activationNotifyEvent, UniqueVehicleIdentifier actualUniqueVehicleIdentifier) {
    DeviceDetailedEntry deviceDetail = activationNotifyEvent.getDeviceDetail();

    DeviceSim simEntry = deviceDetail.getSimEntry();

    Assertions.assertEquals(Set.of(Handle.ofString(deviceDetail.getHandle())), actualUniqueVehicleIdentifier.getHandles());
    Assertions.assertEquals(Set.of(Ipv4Address.ofString(simEntry.getIp())), actualUniqueVehicleIdentifier.getIpv4Addresses());
    Assertions.assertEquals(Set.of(Msisdn.ofString(simEntry.getMsisdn())), actualUniqueVehicleIdentifier.getMsisdns());
    Assertions.assertEquals(Set.of(SatelliteId.ofString(deviceDetail.getSatelliteId())), actualUniqueVehicleIdentifier.getSatelliteIds());
    Assertions.assertEquals(Set.of(Vpi.ofString(deviceDetail.getVehiclePlatformId())), actualUniqueVehicleIdentifier.getVpis());
  }

  @Test
  void constructorInvalidTest() {
    AssertThrows.illegalArgumentException(() -> new NotifyRepositoryWorker(null, vehicleKeyCacheInvalidator, wecuKeyWriterFactory),
        "deviceInfoWriterFactory must not be null");
    AssertThrows.illegalArgumentException(() -> new NotifyRepositoryWorker(deviceInfoWriterFactory, null, wecuKeyWriterFactory),
        "vehicleKeyCacheInvalidator must not be null");
    AssertThrows.illegalArgumentException(() -> new NotifyRepositoryWorker(deviceInfoWriterFactory, vehicleKeyCacheInvalidator, null),
        "wecuKeyWriterFactory must not be null");
  }

  @Test
  void handleActivationNotifyEventMessageInvalidTest() {
    AssertThrows.illegalArgumentException(() -> notifyRepositoryWorker.handleActivationNotifyEventMessage(null),
        "activationNotifyEventMessage must not be null");

    Mockito.verifyNoInteractions(deviceInfoWriter);
  }

  @Test
  void handleActivationNotifyEventMessageWhenDeleteDeviceThenSuccessTest() {
    ActivationNotifyEventMessage activationNotifyEventMessage = TestUtils.createActivationNotifyEventMessage(
        List.of(TestUtils.createActivationNotifyEvent(ChangeStatus.UPDATED, State.DEACTIVATED)));
    handleActivationNotifyEventMessageWhenDeleteDevice(activationNotifyEventMessage, 1);
    handleActivationNotifyEventMessageWhenDeleteDevice(activationNotifyEventMessage, 2);

    activationNotifyEventMessage = TestUtils.createActivationNotifyEventMessage(
        List.of(TestUtils.createActivationNotifyEvent(ChangeStatus.SYNC, State.DEACTIVATED)));
    handleActivationNotifyEventMessageWhenDeleteDevice(activationNotifyEventMessage, 1);
    handleActivationNotifyEventMessageWhenDeleteDevice(activationNotifyEventMessage, 2);

    activationNotifyEventMessage = TestUtils.createActivationNotifyEventMessage(
        List.of(TestUtils.createActivationNotifyEvent(ChangeStatus.DELETED, State.DEACTIVATED))
    );
    handleActivationNotifyEventMessageWhenDeleteDevice(activationNotifyEventMessage, 1);
    handleActivationNotifyEventMessageWhenDeleteDevice(activationNotifyEventMessage, 2);
  }

  @Test
  void handleActivationNotifyEventMessageWhenDeviceUpdateFailedTest() {
    ActivationNotifyEventMessage activationNotifyEventMessage = TestUtils.createActivationNotifyEventMessage(
        List.of(TestUtils.createActivationNotifyEvent(ChangeStatus.UPDATED, State.ACTIVATED)));
    handleActivationNotifyEventMessageWhenDeviceUpdateSuccess(activationNotifyEventMessage);

    activationNotifyEventMessage = TestUtils.createActivationNotifyEventMessage(
        List.of(TestUtils.createActivationNotifyEvent(ChangeStatus.SYNC, State.ACTIVATED)));
    handleActivationNotifyEventMessageWhenDeviceUpdateSuccess(activationNotifyEventMessage);
  }

  @Test
  void handleActivationNotifyEventMessageWhenDeviceUpdateTest() {
    ActivationNotifyEventMessage activationNotifyEventMessage = TestUtils.createActivationNotifyEventMessage(
        List.of(TestUtils.createActivationNotifyEvent(ChangeStatus.UPDATED, State.ACTIVATED)));
    handleActivationNotifyEventMessageWhenDeviceUpdateSuccess(activationNotifyEventMessage);
    handleActivationNotifyEventMessageWhenDeviceUpdateFailed(activationNotifyEventMessage);

    activationNotifyEventMessage = TestUtils.createActivationNotifyEventMessage(
        List.of(TestUtils.createActivationNotifyEvent(ChangeStatus.SYNC, State.ACTIVATED)));
    handleActivationNotifyEventMessageWhenDeviceUpdateSuccess(activationNotifyEventMessage);
    handleActivationNotifyEventMessageWhenDeviceUpdateFailed(activationNotifyEventMessage);
  }

  @Test
  void handleActivationNotifyEventMessageWhenFailMaxSqlLimit() {
    final int limit = 1000;

    ActivationNotifyEventMessage activationNotifyEventMessage = createActivationNotifyMessageExceedingMaxSqlLimit(limit + 1);

    String expectedMessage = "Message contains 1001 VPIs, 1001 MSISDNs, 1001 Handles, 1001 Ipv4Addresses, 1001 SatelliteIds. The Oracle database JDBC driver doesn't support more than 1000 parameters.";
    AssertThrows.illegalStateException(() -> notifyRepositoryWorker.handleActivationNotifyEventMessage(activationNotifyEventMessage), expectedMessage);
  }

  @Test
  void handleActivationNotifyEventMessageWhenInsertDevicesWhereUniqueKeysExistsOnPersistedDevicesTest() {
    List<PersistedDeviceInfo> persistedDeviceInfos = List.of(
        createPersistedDeviceInfo(VPI_1, SATELLITE_ID_1, HANDLE_1, MSISDN_1, IPV4_ADDRESS_1, DEVICE_INFO_ID_1),
        createPersistedDeviceInfo(VPI_2, SATELLITE_ID_2, HANDLE_2, MSISDN_2, IPV4_ADDRESS_2, DEVICE_INFO_ID_2),
        createPersistedDeviceInfo(VPI_3, SATELLITE_ID_3, HANDLE_3, MSISDN_3, IPV4_ADDRESS_3, DEVICE_INFO_ID_3),
        createPersistedDeviceInfo(VPI_4, SATELLITE_ID_4, HANDLE_4, MSISDN_4, IPV4_ADDRESS_4, DEVICE_INFO_ID_4));
    ActivationNotifyEvent activationNotifyEvent = creatActivationNotifyEvent(VPI_2, HANDLE_5, MSISDN_3, IPV4_ADDRESS_4, SATELLITE_ID_1, State.ACTIVATED,
        ChangeStatus.UPDATED);
    ActivationNotifyEventMessage activationNotifyEventMessage = TestUtils.createActivationNotifyEventMessage(List.of(activationNotifyEvent));

    Mockito.when(deviceInfoWriter.findAndLockByDeviceInfoUniqueKeys(ArgumentMatchers.any(UniqueVehicleIdentifier.class)))
        .thenReturn(persistedDeviceInfos);
    Mockito.when(deviceInfoWriter.insertDeviceInfo(ArgumentMatchers.any(DeviceInfo.class))).thenReturn(Either.right(DEVICE_INFO_ID_5));
    Mockito.when(deviceInfoWriter.deleteDeviceInfoByHandle(ArgumentMatchers.any(Handle.class))).thenReturn(1);
    Mockito.when(deviceInfoWriter.findDeviceSequenceByDeviceInfoId(DEVICE_INFO_ID_5))
        .thenReturn(Optional.of(TestUtils.createPersistedDeviceSequence()));

    mockFindDeviceInfoByHandle(deviceInfoWriter);

    Set<Handle> handlesToBeInvalidateInCache = notifyRepositoryWorker.handleActivationNotifyEventMessage(activationNotifyEventMessage);
    Assertions.assertEquals(Set.of(HANDLE_1, HANDLE_2, HANDLE_3, HANDLE_4, HANDLE_5), handlesToBeInvalidateInCache);

    ArgumentCaptor<UniqueVehicleIdentifier> uniqueVehicleIdentifierArgumentCaptor = ArgumentCaptor.forClass(UniqueVehicleIdentifier.class);
    ArgumentCaptor<DeviceInfo> deviceInfoArgumentCaptor = ArgumentCaptor.forClass(DeviceInfo.class);
    ArgumentCaptor<Handle> handleArgumentCaptor = ArgumentCaptor.forClass(Handle.class);

    InOrder inOrder = Mockito.inOrder(deviceInfoWriter, deviceInfoWriterFactory);
    inOrder.verify(deviceInfoWriterFactory).createReadCommitted();
    inOrder.verify(deviceInfoWriter).startTransaction();
    inOrder.verify(deviceInfoWriter).findAndLockByDeviceInfoUniqueKeys(uniqueVehicleIdentifierArgumentCaptor.capture());
    inOrder.verify(deviceInfoWriter, Mockito.times(4)).deleteDeviceInfoByHandle(handleArgumentCaptor.capture());
    inOrder.verify(deviceInfoWriter).insertDeviceInfo(deviceInfoArgumentCaptor.capture());
    inOrder.verify(deviceInfoWriter).findDeviceSequenceByDeviceInfoId(DEVICE_INFO_ID_5);
    inOrder.verify(deviceInfoWriter).commitTransaction();
    inOrder.verify(deviceInfoWriter).close();
    inOrder.verifyNoMoreInteractions();

    Mockito.verify(vehicleKeyCacheInvalidator).invalidateCache(Mockito.anySet());

    verifyCaptorValuesSize(1, uniqueVehicleIdentifierArgumentCaptor);
    verifyKeysEquals(activationNotifyEventMessage, uniqueVehicleIdentifierArgumentCaptor.getValue());

    verifyCaptorValuesSize(1, deviceInfoArgumentCaptor);
    assertEqualKeys(activationNotifyEvent, deviceInfoArgumentCaptor.getValue());

    Assertions.assertEquals(List.of(HANDLE_1, HANDLE_2, HANDLE_3, HANDLE_4), handleArgumentCaptor.getAllValues());
  }

  @Test
  void handleActivationNotifyEventMessageWhenInsertNewDeviceSequenceFailTest() {
    List<PersistedDeviceInfo> persistedDeviceInfos = List.of(
        createPersistedDeviceInfo(VPI_1, SATELLITE_ID_1, HANDLE_1, MSISDN_1, IPV4_ADDRESS_1, DEVICE_INFO_ID_1));
    ActivationNotifyEvent activationNotifyEvent = creatActivationNotifyEvent(VPI_2, HANDLE_5, MSISDN_3, IPV4_ADDRESS_4, SATELLITE_ID_1, State.ACTIVATED,
        ChangeStatus.UPDATED);
    ActivationNotifyEventMessage activationNotifyEventMessage = TestUtils.createActivationNotifyEventMessage(List.of(activationNotifyEvent));

    Mockito.when(deviceInfoWriter.findAndLockByDeviceInfoUniqueKeys(ArgumentMatchers.any(UniqueVehicleIdentifier.class)))
        .thenReturn(persistedDeviceInfos);
    Mockito.when(deviceInfoWriter.insertDeviceInfo(ArgumentMatchers.any(DeviceInfo.class))).thenReturn(Either.right(DEVICE_INFO_ID_5));
    Mockito.when(deviceInfoWriter.findDeviceSequenceByDeviceInfoId(DEVICE_INFO_ID_5)).thenReturn(Optional.empty());
    mockFindDeviceInfoByHandle(deviceInfoWriter);

    InsertionFailure insertionFailure = new InsertionFailure(InsertionFailureReason.UNKNOWN, new RuntimeException());
    Mockito.when(deviceInfoWriter.insertDeviceSequence(ArgumentMatchers.any(DeviceSequence.class))).thenReturn(Either.left(insertionFailure));

    AssertThrows.illegalStateException(() -> notifyRepositoryWorker.handleActivationNotifyEventMessage(activationNotifyEventMessage),
        "java.lang.RuntimeException");

    ArgumentCaptor<UniqueVehicleIdentifier> uniqueVehicleIdentifierArgumentCaptor = ArgumentCaptor.forClass(UniqueVehicleIdentifier.class);
    ArgumentCaptor<DeviceInfo> deviceInfoArgumentCaptor = ArgumentCaptor.forClass(DeviceInfo.class);
    ArgumentCaptor<DeviceSequence> deviceSequenceArgumentCaptor = ArgumentCaptor.forClass(DeviceSequence.class);

    InOrder inOrder = Mockito.inOrder(deviceInfoWriter, deviceInfoWriterFactory);
    inOrder.verify(deviceInfoWriterFactory).createReadCommitted();
    inOrder.verify(deviceInfoWriter).startTransaction();
    inOrder.verify(deviceInfoWriter).findAndLockByDeviceInfoUniqueKeys(uniqueVehicleIdentifierArgumentCaptor.capture());
    inOrder.verify(deviceInfoWriter).insertDeviceInfo(deviceInfoArgumentCaptor.capture());
    inOrder.verify(deviceInfoWriter).findDeviceSequenceByDeviceInfoId(DEVICE_INFO_ID_5);
    inOrder.verify(deviceInfoWriter).insertDeviceSequence(deviceSequenceArgumentCaptor.capture());
    inOrder.verify(deviceInfoWriter).close();
    inOrder.verifyNoMoreInteractions();

    Mockito.verifyNoInteractions(vehicleKeyCacheInvalidator);

    verifyCaptorValuesSize(1, uniqueVehicleIdentifierArgumentCaptor);
    verifyKeysEquals(activationNotifyEventMessage, uniqueVehicleIdentifierArgumentCaptor.getValue());

    verifyCaptorValuesSize(1, deviceInfoArgumentCaptor);
    assertEqualKeys(activationNotifyEvent, deviceInfoArgumentCaptor.getValue());

    verifyCaptorValuesSize(1, deviceSequenceArgumentCaptor);
    verifyDeviceSequence(deviceSequenceArgumentCaptor.getValue());
  }

  @Test
  void handleActivationNotifyEventMessageWhenInsertNewDeviceSequenceSuccessTest() {
    List<PersistedDeviceInfo> persistedDeviceInfos = List.of(
        createPersistedDeviceInfo(VPI_1, SATELLITE_ID_1, HANDLE_1, MSISDN_1, IPV4_ADDRESS_1, DEVICE_INFO_ID_1));
    ActivationNotifyEvent activationNotifyEvent = creatActivationNotifyEvent(VPI_2, HANDLE_5, MSISDN_3, IPV4_ADDRESS_4, SATELLITE_ID_1, State.ACTIVATED,
        ChangeStatus.UPDATED);
    ActivationNotifyEventMessage activationNotifyEventMessage = TestUtils.createActivationNotifyEventMessage(List.of(activationNotifyEvent));

    Mockito.when(deviceInfoWriter.findAndLockByDeviceInfoUniqueKeys(ArgumentMatchers.any(UniqueVehicleIdentifier.class)))
        .thenReturn(persistedDeviceInfos);
    Mockito.when(deviceInfoWriter.insertDeviceInfo(ArgumentMatchers.any(DeviceInfo.class))).thenReturn(Either.right(DEVICE_INFO_ID_5));
    Mockito.when(deviceInfoWriter.findDeviceSequenceByDeviceInfoId(DEVICE_INFO_ID_5)).thenReturn(Optional.empty());
    mockFindDeviceInfoByHandle(deviceInfoWriter);

    Mockito.when(deviceInfoWriter.insertDeviceSequence(ArgumentMatchers.any(DeviceSequence.class)))
        .thenReturn(Either.right(TestUtils.DEVICE_SEQUENCE_ID));

    Set<Handle> handlesToBeInvalidateInCache = notifyRepositoryWorker.handleActivationNotifyEventMessage(activationNotifyEventMessage);
    Assertions.assertEquals(Set.of(HANDLE_1, HANDLE_5), handlesToBeInvalidateInCache);

    ArgumentCaptor<UniqueVehicleIdentifier> uniqueVehicleIdentifierArgumentCaptor = ArgumentCaptor.forClass(UniqueVehicleIdentifier.class);
    ArgumentCaptor<DeviceInfo> deviceInfoArgumentCaptor = ArgumentCaptor.forClass(DeviceInfo.class);
    ArgumentCaptor<DeviceSequence> deviceSequenceArgumentCaptor = ArgumentCaptor.forClass(DeviceSequence.class);

    InOrder inOrder = Mockito.inOrder(deviceInfoWriter, deviceInfoWriterFactory);
    inOrder.verify(deviceInfoWriterFactory).createReadCommitted();
    inOrder.verify(deviceInfoWriter).startTransaction();
    inOrder.verify(deviceInfoWriter).findAndLockByDeviceInfoUniqueKeys(uniqueVehicleIdentifierArgumentCaptor.capture());
    inOrder.verify(deviceInfoWriter).insertDeviceInfo(deviceInfoArgumentCaptor.capture());
    inOrder.verify(deviceInfoWriter).findDeviceSequenceByDeviceInfoId(DEVICE_INFO_ID_5);
    inOrder.verify(deviceInfoWriter).insertDeviceSequence(deviceSequenceArgumentCaptor.capture());
    inOrder.verify(deviceInfoWriter).commitTransaction();
    inOrder.verify(deviceInfoWriter).close();
    inOrder.verifyNoMoreInteractions();

    Mockito.verify(vehicleKeyCacheInvalidator).invalidateCache(Mockito.anySet());

    verifyCaptorValuesSize(1, uniqueVehicleIdentifierArgumentCaptor);
    verifyKeysEquals(activationNotifyEventMessage, uniqueVehicleIdentifierArgumentCaptor.getValue());

    verifyCaptorValuesSize(1, deviceInfoArgumentCaptor);
    assertEqualKeys(activationNotifyEvent, deviceInfoArgumentCaptor.getValue());

    verifyCaptorValuesSize(1, deviceSequenceArgumentCaptor);
    verifyDeviceSequence(deviceSequenceArgumentCaptor.getValue());
  }

  @Test
  void handleActivationNotifyEventMessageWhenInvalidChangeStatusOrStateTest() {
    handleActivationNotifyEventMessageWhenInvalidChangeStatusOrState(null, State.PAUSED, "changeStatus must not be null");

    handleActivationNotifyEventMessageWhenInvalidChangeStatusOrState(ChangeStatus.UPDATED, State.NEW, "unsupported state: " + State.NEW);
    handleActivationNotifyEventMessageWhenInvalidChangeStatusOrState(ChangeStatus.UPDATED, State.PAUSED, "unsupported state: " + State.PAUSED);
    handleActivationNotifyEventMessageWhenInvalidChangeStatusOrState(ChangeStatus.SYNC, State.NEW, "unsupported state: " + State.NEW);
    handleActivationNotifyEventMessageWhenInvalidChangeStatusOrState(ChangeStatus.SYNC, State.PAUSED, "unsupported state: " + State.PAUSED);
  }

  @Test
  void handleActivationNotifyEventMessageWhenMessageHasEmptyEventsTest() {
    ActivationNotifyEventMessage activationNotifyEventMessage = TestUtils.createActivationNotifyEventMessage(Collections.emptyList());

    Set<Handle> handlesToBeInvalidateInCache = notifyRepositoryWorker.handleActivationNotifyEventMessage(activationNotifyEventMessage);
    Assertions.assertTrue(handlesToBeInvalidateInCache.isEmpty());
  }

  @BeforeEach
  void setup() {
    deviceInfoWriter = Mockito.mock(DeviceInfoWriter.class);
    deviceInfoWriterFactory = Mockito.mock(DeviceInfoWriterFactory.class);
    Mockito.when(deviceInfoWriterFactory.createReadCommitted()).thenReturn(deviceInfoWriter);

    wecuKeyWriter = Mockito.mock(WecuKeyWriter.class);
    wecuKeyWriterFactory = Mockito.mock(WecuKeyWriterFactory.class);
    Mockito.when(wecuKeyWriterFactory.createReadCommitted()).thenReturn(wecuKeyWriter);

    vehicleKeyCacheInvalidator = Mockito.mock(VehicleKeyCacheInvalidator.class);

    notifyRepositoryWorker = new NotifyRepositoryWorker(deviceInfoWriterFactory, vehicleKeyCacheInvalidator, wecuKeyWriterFactory);
  }

  private void handleActivationNotifyEventMessageWhenDeleteDevice(ActivationNotifyEventMessage activationNotifyEventMessage, int deletedDevicesAmount) {
    Handle handle = TestUtils.HANDLE;

    PersistedDeviceInfo persistedDeviceInfo = TestUtils.createPersistedDeviceInfo();

    Mockito.when(deviceInfoWriter.findAndLockByDeviceInfoUniqueKeys(ArgumentMatchers.any(UniqueVehicleIdentifier.class)))
        .thenReturn(List.of(persistedDeviceInfo));
    Mockito.when(deviceInfoWriter.deleteDeviceInfoByHandle(handle)).thenReturn(deletedDevicesAmount);

    Set<Handle> handlesToBeInvalidateInCache = notifyRepositoryWorker.handleActivationNotifyEventMessage(activationNotifyEventMessage);
    Assertions.assertEquals(Set.of(handle), handlesToBeInvalidateInCache);

    ArgumentCaptor<UniqueVehicleIdentifier> uniqueVehicleIdentifierArgumentCaptor = ArgumentCaptor.forClass(UniqueVehicleIdentifier.class);

    InOrder inOrder = Mockito.inOrder(deviceInfoWriter, deviceInfoWriterFactory);
    inOrder.verify(deviceInfoWriterFactory).createReadCommitted();
    inOrder.verify(deviceInfoWriter).startTransaction();
    inOrder.verify(deviceInfoWriter).findAndLockByDeviceInfoUniqueKeys(uniqueVehicleIdentifierArgumentCaptor.capture());
    inOrder.verify(deviceInfoWriter).deleteDeviceInfoByHandle(handle);
    inOrder.verify(deviceInfoWriter).commitTransaction();
    inOrder.verify(deviceInfoWriter).close();
    inOrder.verifyNoMoreInteractions();

    ArgumentCaptor<Handle> handleArgumentCaptor = ArgumentCaptor.forClass(Handle.class);
    InOrder inOrderWecuKey = Mockito.inOrder(wecuKeyWriter, wecuKeyWriterFactory);
    inOrderWecuKey.verify(wecuKeyWriterFactory).createReadCommitted();
    inOrderWecuKey.verify(wecuKeyWriter).startTransaction();
    inOrderWecuKey.verify(wecuKeyWriter).deleteWecuKeyByHandle(handleArgumentCaptor.capture());
    //TODO - test update or delete
    inOrderWecuKey.verify(wecuKeyWriter).commitTransaction();
    inOrderWecuKey.verify(wecuKeyWriter).close();
    inOrderWecuKey.verifyNoMoreInteractions();

    Mockito.verify(vehicleKeyCacheInvalidator).invalidateCache(Mockito.anySet());

    Assertions.assertEquals(1, uniqueVehicleIdentifierArgumentCaptor.getAllValues().size());
    verifyKeysEquals(activationNotifyEventMessage, uniqueVehicleIdentifierArgumentCaptor.getValue());
    Mockito.clearInvocations(deviceInfoWriterFactory, deviceInfoWriter, wecuKeyWriterFactory, wecuKeyWriter, vehicleKeyCacheInvalidator);
  }

  private void handleActivationNotifyEventMessageWhenDeviceUpdateFailed(ActivationNotifyEventMessage activationNotifyEventMessage) {
    PersistedDeviceInfo persistedDeviceInfo = TestUtils.createPersistedDeviceInfo();

    Mockito.when(deviceInfoWriter.findAndLockByDeviceInfoUniqueKeys(ArgumentMatchers.any(UniqueVehicleIdentifier.class)))
        .thenReturn(List.of(persistedDeviceInfo));
    Mockito.when(deviceInfoWriter.updateDeviceInfoByHandle(persistedDeviceInfo.getDeviceInfo())).thenReturn(2);

    String expectedMessage = "Unable to update device with handle=" + persistedDeviceInfo.getDeviceInfo().getHandle() + " properly";
    AssertThrows.illegalStateException(() -> notifyRepositoryWorker.handleActivationNotifyEventMessage(activationNotifyEventMessage),
        expectedMessage);

    ArgumentCaptor<UniqueVehicleIdentifier> uniqueVehicleIdentifierArgumentCaptor = ArgumentCaptor.forClass(UniqueVehicleIdentifier.class);

    InOrder inOrder = Mockito.inOrder(deviceInfoWriter, deviceInfoWriterFactory);
    inOrder.verify(deviceInfoWriterFactory).createReadCommitted();
    inOrder.verify(deviceInfoWriter).startTransaction();
    inOrder.verify(deviceInfoWriter).findAndLockByDeviceInfoUniqueKeys(uniqueVehicleIdentifierArgumentCaptor.capture());
    inOrder.verify(deviceInfoWriter).updateDeviceInfoByHandle(persistedDeviceInfo.getDeviceInfo());
    inOrder.verify(deviceInfoWriter).close();
    inOrder.verifyNoMoreInteractions();

    ArgumentCaptor<Handle> handleArgumentCaptor = ArgumentCaptor.forClass(Handle.class);
    InOrder inOrderWecuKey = Mockito.inOrder(wecuKeyWriter, wecuKeyWriterFactory);
    inOrderWecuKey.verify(wecuKeyWriterFactory).createReadCommitted();
    inOrderWecuKey.verify(wecuKeyWriter).startTransaction();
    inOrderWecuKey.verify(wecuKeyWriter).close();

    Mockito.verifyNoInteractions(vehicleKeyCacheInvalidator);

    verifyCaptorValuesSize(1, uniqueVehicleIdentifierArgumentCaptor);
    verifyKeysEquals(activationNotifyEventMessage, uniqueVehicleIdentifierArgumentCaptor.getValue());
    Mockito.clearInvocations(deviceInfoWriterFactory, deviceInfoWriter, wecuKeyWriterFactory, wecuKeyWriter, vehicleKeyCacheInvalidator);
  }

  private void handleActivationNotifyEventMessageWhenDeviceUpdateSuccess(ActivationNotifyEventMessage activationNotifyEventMessage) {
    DeviceInfoId deviceInfoId = TestUtils.DEVICE_INFO_ID;

    PersistedDeviceInfo persistedDeviceInfo = TestUtils.createPersistedDeviceInfo();

    Mockito.when(deviceInfoWriter.findAndLockByDeviceInfoUniqueKeys(ArgumentMatchers.any(UniqueVehicleIdentifier.class)))
        .thenReturn(List.of(persistedDeviceInfo));
    Mockito.when(deviceInfoWriter.updateDeviceInfoByHandle(persistedDeviceInfo.getDeviceInfo())).thenReturn(1);
    Mockito.when(deviceInfoWriter.findDeviceInfoByHandle(persistedDeviceInfo.getDeviceInfo().getHandle()))
        .thenReturn(Optional.of(persistedDeviceInfo));
    Mockito.when(deviceInfoWriter.findDeviceSequenceByDeviceInfoId(deviceInfoId))
        .thenReturn(Optional.of(TestUtils.createPersistedDeviceSequence()));

    Set<Handle> handlesToBeInvalidateInCache = notifyRepositoryWorker.handleActivationNotifyEventMessage(activationNotifyEventMessage);
    Assertions.assertEquals(Set.of(TestUtils.HANDLE), handlesToBeInvalidateInCache);

    ArgumentCaptor<UniqueVehicleIdentifier> uniqueVehicleIdentifierArgumentCaptor = ArgumentCaptor.forClass(UniqueVehicleIdentifier.class);

    InOrder inOrder = Mockito.inOrder(deviceInfoWriter, deviceInfoWriterFactory);
    inOrder.verify(deviceInfoWriterFactory).createReadCommitted();
    inOrder.verify(deviceInfoWriter).startTransaction();
    inOrder.verify(deviceInfoWriter).findAndLockByDeviceInfoUniqueKeys(uniqueVehicleIdentifierArgumentCaptor.capture());
    inOrder.verify(deviceInfoWriter).updateDeviceInfoByHandle(persistedDeviceInfo.getDeviceInfo());
    inOrder.verify(deviceInfoWriter).findDeviceSequenceByDeviceInfoId(deviceInfoId);
    inOrder.verify(deviceInfoWriter).commitTransaction();
    inOrder.verify(deviceInfoWriter).close();
    inOrder.verifyNoMoreInteractions();

    ArgumentCaptor<Handle> handleArgumentCaptor = ArgumentCaptor.forClass(Handle.class);
    InOrder inOrderWecuKey = Mockito.inOrder(wecuKeyWriter, wecuKeyWriterFactory);
    inOrderWecuKey.verify(wecuKeyWriterFactory).createReadCommitted();
    inOrderWecuKey.verify(wecuKeyWriter).startTransaction();
    inOrderWecuKey.verify(wecuKeyWriter).findWecuKeyByHandle(handleArgumentCaptor.capture());
    //TODO - test update or delete
    inOrderWecuKey.verify(wecuKeyWriter).commitTransaction();
    inOrderWecuKey.verify(wecuKeyWriter).close();
    inOrderWecuKey.verifyNoMoreInteractions();

    Mockito.verify(vehicleKeyCacheInvalidator).invalidateCache(Mockito.anySet());

    verifyCaptorValuesSize(1, uniqueVehicleIdentifierArgumentCaptor);
    verifyKeysEquals(activationNotifyEventMessage, uniqueVehicleIdentifierArgumentCaptor.getValue());
    Mockito.clearInvocations(deviceInfoWriterFactory, deviceInfoWriter, wecuKeyWriterFactory, wecuKeyWriter, vehicleKeyCacheInvalidator);
  }

  private void handleActivationNotifyEventMessageWhenInvalidChangeStatusOrState(ChangeStatus changeStatus, State state, String expectedMessage) {
    ActivationNotifyEventMessage activationNotifyEventMessage = TestUtils.createActivationNotifyEventMessage(
        List.of(TestUtils.createActivationNotifyEvent(changeStatus, state)));

    PersistedDeviceInfo persistedDeviceInfo = TestUtils.createPersistedDeviceInfo();
    Mockito.when(deviceInfoWriter.findAndLockByDeviceInfoUniqueKeys(ArgumentMatchers.any(UniqueVehicleIdentifier.class)))
        .thenReturn(List.of(persistedDeviceInfo));

    AssertThrows.illegalArgumentException(() -> notifyRepositoryWorker.handleActivationNotifyEventMessage(activationNotifyEventMessage), expectedMessage);

    ArgumentCaptor<UniqueVehicleIdentifier> uniqueVehicleIdentifierArgumentCaptor = ArgumentCaptor.forClass(UniqueVehicleIdentifier.class);

    InOrder inOrder = Mockito.inOrder(deviceInfoWriter, deviceInfoWriterFactory);
    inOrder.verify(deviceInfoWriterFactory).createReadCommitted();
    inOrder.verify(deviceInfoWriter).startTransaction();
    inOrder.verify(deviceInfoWriter).findAndLockByDeviceInfoUniqueKeys(uniqueVehicleIdentifierArgumentCaptor.capture());
    inOrder.verify(deviceInfoWriter).close();
    inOrder.verifyNoMoreInteractions();

    InOrder inOrderWecuKey = Mockito.inOrder(wecuKeyWriter, wecuKeyWriterFactory);
    inOrderWecuKey.verify(wecuKeyWriterFactory).createReadCommitted();
    inOrderWecuKey.verify(wecuKeyWriter).startTransaction();
    inOrderWecuKey.verify(wecuKeyWriter).close();
    inOrderWecuKey.verifyNoMoreInteractions();

    Mockito.verifyNoInteractions(vehicleKeyCacheInvalidator);

    Assertions.assertEquals(1, uniqueVehicleIdentifierArgumentCaptor.getAllValues().size());
    verifyKeysEquals(activationNotifyEventMessage, uniqueVehicleIdentifierArgumentCaptor.getValue());
    Mockito.clearInvocations(deviceInfoWriterFactory, deviceInfoWriter, wecuKeyWriterFactory, wecuKeyWriter, vehicleKeyCacheInvalidator);
  }
}
