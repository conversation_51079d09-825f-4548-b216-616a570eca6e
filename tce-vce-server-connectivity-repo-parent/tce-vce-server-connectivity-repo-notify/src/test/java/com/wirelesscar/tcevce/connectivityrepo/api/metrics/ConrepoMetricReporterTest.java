package com.wirelesscar.tcevce.connectivityrepo.api.metrics;

import static com.wirelesscar.tcevce.connectivityrepo.api.metrics.ConrepoMetricReporter.FAIL;
import static com.wirelesscar.tcevce.connectivityrepo.api.metrics.ConrepoMetricReporter.SUCCESS;
import static com.wirelesscar.tcevce.connectivityrepo.api.metrics.ConrepoMetricReporter.TAG_ACTIVATION_STATUS;
import static com.wirelesscar.tcevce.connectivityrepo.api.metrics.ConrepoMetricReporter.TAG_CHANGE_STATUS;

import java.util.function.Consumer;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.vc.test.utils.lib.AssertThrows;
import com.wirelesscar.conrepo.api.v1.ChangeStatus;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tags;
import io.micrometer.core.instrument.simple.SimpleMeterRegistry;

class ConrepoMetricReporterTest {
  private static void checkCounter(MeterRegistry meterRegistry, int expectedCount, String name, Tags tags) {
    final Counter counter = meterRegistry.find(name).tags(tags).counter();

    Assertions.assertEquals(expectedCount, counter.count());
  }

  private static void initReporterAndTest(Consumer<ConrepoMetricReporter> conrepoMetricReporterConsumer, int expectedCount, String metricName, Tags tags) {
    MeterRegistry meterRegistry = new SimpleMeterRegistry();

    ConrepoMetricReporter conrepoMetricReporter = new ConrepoMetricReporter(meterRegistry);
    conrepoMetricReporterConsumer.accept(conrepoMetricReporter);

    checkCounter(meterRegistry, expectedCount, metricName, tags);
  }

  private static void onConrepoNotificationTest(ChangeStatus changeStatus) {
    initReporterAndTest(conrepoMetricReporter -> conrepoMetricReporter.onConrepoNotification(changeStatus), 1, ConrepoMetricReporter.CONREPO_NOTIFICATION,
        Tags.of(TAG_CHANGE_STATUS, changeStatus.name()));
  }

  @Test
  void onActivationFailTest() {
    initReporterAndTest(ConrepoMetricReporter::onActivationFail, 1, ConrepoMetricReporter.ACTIVATION_NOTIFY, Tags.of(TAG_ACTIVATION_STATUS, FAIL));
  }

  @Test
  void onActivationSuccessTest() {
    initReporterAndTest(ConrepoMetricReporter::onActivationSuccess, 1, ConrepoMetricReporter.ACTIVATION_NOTIFY, Tags.of(TAG_ACTIVATION_STATUS, SUCCESS));
  }

  @Test
  void onConrepoNotificationDeletedTest() {
    onConrepoNotificationTest(ChangeStatus.DELETED);
  }

  @Test
  void onConrepoNotificationUpdatedTest() {
    onConrepoNotificationTest(ChangeStatus.UPDATED);
  }

  @Test
  void onConrepoNotificationInvalidTest() {
    MeterRegistry meterRegistry = new SimpleMeterRegistry();
    ConrepoMetricReporter conrepoMetricReporter = new ConrepoMetricReporter(meterRegistry);

    AssertThrows.illegalArgumentException(() -> conrepoMetricReporter.onConrepoNotification(null), "changeStatus must not be null");
  }
}
