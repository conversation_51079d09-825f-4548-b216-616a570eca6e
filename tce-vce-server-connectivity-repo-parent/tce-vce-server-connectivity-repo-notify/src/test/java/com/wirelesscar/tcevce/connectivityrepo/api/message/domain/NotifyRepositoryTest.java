package com.wirelesscar.tcevce.connectivityrepo.api.message.domain;

import java.util.Set;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.InOrder;
import org.mockito.Mockito;

import com.volvo.tisp.vc.test.utils.lib.AssertThrows;
import com.wirelesscar.conrepo.api.v1.ActivationNotifyEventMessage;
import com.wirelesscar.tcevce.connectivityrepo.api.TestUtils;
import com.wirelesscar.tcevce.wecu.device.info.database.api.DeviceInfoWriterFactory;
import com.wirelesscar.tcevce.wecu.device.info.database.model.Handle;

class NotifyRepositoryTest {
  @Test
  void constructorInvalidTest() {
    DeviceInfoWriterFactory deviceInfoWriterFactory = Mockito.mock(DeviceInfoWriterFactory.class);

    AssertThrows.illegalArgumentException(() -> new NotifyRepository(null), "notifyRepositoryWorker must not be null");

    Mockito.verifyNoInteractions(deviceInfoWriterFactory);
  }

  @Test
  void handleActivationNotifyMessageInvalidTest() {
    NotifyRepositoryWorker notifyRepositoryWorker = Mockito.mock(NotifyRepositoryWorker.class);
    NotifyRepository notifyRepository = new NotifyRepository(notifyRepositoryWorker);

    AssertThrows.illegalArgumentException(() -> notifyRepository.handleActivationNotifyMessage(null), "activationNotifyEventMessage must not be null");

    Mockito.verifyNoInteractions(notifyRepositoryWorker);
  }

  @Test
  void handleActivationNotifyMessageTest() {
    NotifyRepositoryWorker notifyRepositoryWorker = Mockito.mock(NotifyRepositoryWorker.class);
    NotifyRepository notifyRepository = new NotifyRepository(notifyRepositoryWorker);

    ActivationNotifyEventMessage activationNotifyEventMessage = TestUtils.createActivationNotifyEventMessage();

    Set<Handle> expectedHandles = Set.of(TestUtils.HANDLE);
    Mockito.when(notifyRepositoryWorker.handleActivationNotifyEventMessage(activationNotifyEventMessage)).thenReturn(expectedHandles);

    Assertions.assertEquals(expectedHandles, notifyRepository.handleActivationNotifyMessage(activationNotifyEventMessage));

    InOrder inOrder = Mockito.inOrder(notifyRepositoryWorker);
    inOrder.verify(notifyRepositoryWorker).handleActivationNotifyEventMessage(activationNotifyEventMessage);
  }
}
