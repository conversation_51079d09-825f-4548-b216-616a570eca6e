package com.wirelesscar.tcevce.connectivityrepo.api.message.domain;

import org.junit.jupiter.api.Test;
import org.mockito.InOrder;
import org.mockito.Mockito;

import com.volvo.tisp.vc.test.utils.lib.AssertThrows;
import com.wirelesscar.conrepo.api.v1.ActivationNotifyEventMessage;
import com.wirelesscar.conrepo.api.v1.ChangeStatus;
import com.wirelesscar.tcevce.connectivityrepo.api.TestUtils;
import com.wirelesscar.tcevce.connectivityrepo.api.metrics.ConrepoMetricReporter;

class ConrepoNotifyDomainManagerTest {
  @Test
  void constructorInvalidTest() {
    ConrepoNotifyService conrepoNotifyService = Mockito.mock(ConrepoNotifyService.class);

    AssertThrows.illegalArgumentException(() -> new ConrepoNotifyDomainManager(null, null), "conrepoNotifyService must not be null");
    AssertThrows.illegalArgumentException(() -> new ConrepoNotifyDomainManager(conrepoNotifyService, null), "conrepoMetricReporter must not be null");

    Mockito.verifyNoInteractions(conrepoNotifyService);
  }

  @Test
  void receiveMessageV1InvalidTest() {
    ConrepoNotifyService conrepoNotifyService = Mockito.mock(ConrepoNotifyService.class);
    ConrepoMetricReporter conrepoMetricReporter = Mockito.mock(ConrepoMetricReporter.class);

    ConrepoNotifyDomainManager conrepoNotifyDomainManager = new ConrepoNotifyDomainManager(conrepoNotifyService, conrepoMetricReporter);
    AssertThrows.illegalArgumentException(() -> conrepoNotifyDomainManager.receiveMessageV1(null), "activationNotifyEventMessage must not be null");

    Mockito.verifyNoInteractions(conrepoNotifyService);
  }

  @Test
  void receiveMessageV1WhenActivationWasFailedTest() {
    ConrepoMetricReporter conrepoMetricReporter = Mockito.mock(ConrepoMetricReporter.class);
    ConrepoNotifyService conrepoNotifyService = Mockito.mock(ConrepoNotifyService.class);

    ActivationNotifyEventMessage activationNotifyEventMessage = TestUtils.createActivationNotifyEventMessage();

    Mockito.doThrow(RuntimeException.class).when(conrepoNotifyService).handleNotifyEventMessage(activationNotifyEventMessage);

    ConrepoNotifyDomainManager conrepoNotifyDomainManager = new ConrepoNotifyDomainManager(conrepoNotifyService, conrepoMetricReporter);
    conrepoNotifyDomainManager.receiveMessageV1(activationNotifyEventMessage);

    InOrder inOrder = Mockito.inOrder(conrepoNotifyService, conrepoMetricReporter);
    inOrder.verify(conrepoMetricReporter).onConrepoNotification(ChangeStatus.DELETED);
    inOrder.verify(conrepoNotifyService).handleNotifyEventMessage(activationNotifyEventMessage);
    inOrder.verify(conrepoMetricReporter).onActivationFail();
    inOrder.verifyNoMoreInteractions();
  }

  @Test
  void receiveMessageV1WhenActivationWasSuccessTest() {
    ConrepoMetricReporter conrepoMetricReporter = Mockito.mock(ConrepoMetricReporter.class);
    ConrepoNotifyService conrepoNotifyService = Mockito.mock(ConrepoNotifyService.class);

    ActivationNotifyEventMessage activationNotifyEventMessage = TestUtils.createActivationNotifyEventMessage();

    ConrepoNotifyDomainManager conrepoNotifyDomainManager = new ConrepoNotifyDomainManager(conrepoNotifyService, conrepoMetricReporter);
    conrepoNotifyDomainManager.receiveMessageV1(activationNotifyEventMessage);

    InOrder inOrder = Mockito.inOrder(conrepoNotifyService, conrepoMetricReporter);
    inOrder.verify(conrepoMetricReporter).onConrepoNotification(ChangeStatus.DELETED);
    inOrder.verify(conrepoNotifyService).handleNotifyEventMessage(activationNotifyEventMessage);
    inOrder.verify(conrepoMetricReporter).onActivationSuccess();
    inOrder.verifyNoMoreInteractions();
  }
}
