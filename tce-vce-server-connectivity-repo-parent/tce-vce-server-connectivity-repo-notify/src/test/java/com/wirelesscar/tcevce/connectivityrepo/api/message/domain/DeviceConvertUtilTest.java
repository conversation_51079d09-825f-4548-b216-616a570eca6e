package com.wirelesscar.tcevce.connectivityrepo.api.message.domain;

import java.util.List;
import java.util.Set;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.vc.test.utils.lib.AssertThrows;
import com.volvo.tisp.vc.test.utils.lib.UtilClassVerifier;
import com.wirelesscar.conrepo.api.v1.ActivationNotifyEvent;
import com.wirelesscar.conrepo.api.v1.DeviceDetailedEntry;
import com.wirelesscar.tcevce.connectivityrepo.api.TestUtils;
import com.wirelesscar.tcevce.wecu.device.info.database.model.UniqueVehicleIdentifier;

class DeviceConvertUtilTest {
  private static ActivationNotifyEvent createActivationNotifyEventFull() {
    ActivationNotifyEvent activationNotifyEvent = new ActivationNotifyEvent();
    activationNotifyEvent.setDeviceDetail(TestUtils.createDeviceDetailedEntry());

    return activationNotifyEvent;
  }

  private static ActivationNotifyEvent createActivationNotifyEventMin() {
    DeviceDetailedEntry deviceDetailedEntry = new DeviceDetailedEntry();
    deviceDetailedEntry.setVehiclePlatformId(TestUtils.VPI.toString());

    ActivationNotifyEvent activationNotifyEvent = new ActivationNotifyEvent();
    activationNotifyEvent.setDeviceDetail(deviceDetailedEntry);

    return activationNotifyEvent;
  }

  private static void verifyAllIdentifiers(UniqueVehicleIdentifier uniqueVehicleIdentifier) {
    Assertions.assertEquals(Set.of(TestUtils.HANDLE), uniqueVehicleIdentifier.getHandles());
    Assertions.assertEquals(Set.of(TestUtils.IPV4_ADDRESS), uniqueVehicleIdentifier.getIpv4Addresses());
    Assertions.assertEquals(Set.of(TestUtils.MSISDN), uniqueVehicleIdentifier.getMsisdns());
    Assertions.assertEquals(Set.of(TestUtils.VPI), uniqueVehicleIdentifier.getVpis());
  }

  private static void verifyOnlyVpi(UniqueVehicleIdentifier uniqueVehicleIdentifier) {
    Assertions.assertTrue(uniqueVehicleIdentifier.getHandles().isEmpty());
    Assertions.assertTrue(uniqueVehicleIdentifier.getIpv4Addresses().isEmpty());
    Assertions.assertTrue(uniqueVehicleIdentifier.getMsisdns().isEmpty());
    Assertions.assertEquals(Set.of(TestUtils.VPI), uniqueVehicleIdentifier.getVpis());
  }

  @Test
  void createActivationNotifyEventFullFullTest() {
    verifyAllIdentifiers(DeviceConvertUtil.createUniqueVehicleIdentifier(List.of(createActivationNotifyEventFull())));
  }

  @Test
  void createUniqueVehicleIdentifierInvalidTest() {
    AssertThrows.illegalArgumentException(() -> DeviceConvertUtil.createUniqueVehicleIdentifier(null),
        "activationNotifyEvents must not be null");
  }

  @Test
  void createUniqueVehicleIdentifierMinTest() {
    verifyOnlyVpi(DeviceConvertUtil.createUniqueVehicleIdentifier(List.of(createActivationNotifyEventMin())));
  }

  @Test
  void verifyUtilClassTest() throws ReflectiveOperationException {
    UtilClassVerifier.verifyUtilClass(DeviceConvertUtil.class);
  }
}
