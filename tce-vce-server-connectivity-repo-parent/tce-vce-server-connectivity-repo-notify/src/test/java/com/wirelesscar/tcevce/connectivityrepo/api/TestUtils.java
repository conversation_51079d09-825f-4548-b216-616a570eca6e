package com.wirelesscar.tcevce.connectivityrepo.api;

import java.time.Instant;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import com.volvo.tisp.vc.common.dto.lib.vehicle.Imsi;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Ipv4Address;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Msisdn;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.wirelesscar.conrepo.api.v1.ActivationNotifyEvent;
import com.wirelesscar.conrepo.api.v1.ActivationNotifyEventMessage;
import com.wirelesscar.conrepo.api.v1.ChangeStatus;
import com.wirelesscar.conrepo.api.v1.DeviceDetailedEntry;
import com.wirelesscar.conrepo.api.v1.DeviceSim;
import com.wirelesscar.conrepo.api.v1.State;
import com.wirelesscar.tcevce.wecu.device.info.database.model.DeviceInfo;
import com.wirelesscar.tcevce.wecu.device.info.database.model.DeviceInfoBuilder;
import com.wirelesscar.tcevce.wecu.device.info.database.model.DeviceInfoId;
import com.wirelesscar.tcevce.wecu.device.info.database.model.DeviceSequence;
import com.wirelesscar.tcevce.wecu.device.info.database.model.DeviceSequenceBuilder;
import com.wirelesscar.tcevce.wecu.device.info.database.model.DeviceSequenceId;
import com.wirelesscar.tcevce.wecu.device.info.database.model.Handle;
import com.wirelesscar.tcevce.wecu.device.info.database.model.Ipv4Port;
import com.wirelesscar.tcevce.wecu.device.info.database.model.MobileNetworkOperator;
import com.wirelesscar.tcevce.wecu.device.info.database.model.PersistedDeviceInfo;
import com.wirelesscar.tcevce.wecu.device.info.database.model.PersistedDeviceInfoBuilder;
import com.wirelesscar.tcevce.wecu.device.info.database.model.PersistedDeviceSequence;
import com.wirelesscar.tcevce.wecu.device.info.database.model.PersistedDeviceSequenceBuilder;
import com.wirelesscar.tcevce.wecu.device.info.database.model.SatelliteId;
import com.wirelesscar.tcevce.wecu.device.info.database.model.SequenceNumber;
import com.wirelesscar.tcevce.wecu.device.info.database.model.SimInfo;
import com.wirelesscar.tcevce.wecu.device.info.database.model.SimInfoBuilder;

public final class TestUtils {
  public static final Instant CREATED = Instant.ofEpochSecond(1);
  public static final DeviceInfoId DEVICE_INFO_ID = DeviceInfoId.ofLong(1);
  public static final DeviceSequenceId DEVICE_SEQUENCE_ID = DeviceSequenceId.ofLong(1);
  public static final Handle HANDLE = Handle.ofString("123456");
  public static final Ipv4Address IPV4_ADDRESS = Ipv4Address.ofString("*******");
  public static final Ipv4Port IPV4_PORT = Ipv4Port.ofInt(44);
  public static final Instant LAST_UPDATED = Instant.ofEpochSecond(2);
  public static final MobileNetworkOperator MOBILE_NETWORK_OPERATOR = MobileNetworkOperator.ofString("telenor");
  public static final Msisdn MSISDN = Msisdn.ofString("+4631888888");
  public static final SatelliteId SATELLITE_ID = SatelliteId.ofString("HQ1234567890x1");
  public static final SequenceNumber SEQUENCE_NUMBER = SequenceNumber.ofByte((byte) 0);
  public static final Vpi VPI = Vpi.ofString("1234567890ABCDEF1234567890ABCDEF");

  private TestUtils() {
    throw new IllegalStateException();
  }

  public static ActivationNotifyEvent createActivationNotifyEvent(ChangeStatus changeStatus, State state) {
    DeviceDetailedEntry deviceDetailedEntry = createDeviceDetailedEntry();
    deviceDetailedEntry.setState(state);

    return createActivationNotifyEvent(changeStatus, deviceDetailedEntry);
  }

  public static ActivationNotifyEvent createActivationNotifyEvent(ChangeStatus changeStatus, DeviceDetailedEntry deviceDetailedEntry) {
    ActivationNotifyEvent activationNotifyEvent = new ActivationNotifyEvent();

    activationNotifyEvent.setDeviceDetail(deviceDetailedEntry);
    activationNotifyEvent.setChangeStatus(changeStatus);
    return activationNotifyEvent;
  }

  public static ActivationNotifyEventMessage createActivationNotifyEventMessage(List<ActivationNotifyEvent> activationNotifyEventsList) {
    ActivationNotifyEventMessage activationNotifyEventMessage = new ActivationNotifyEventMessage();

    activationNotifyEventMessage.getActivationNotifyEvents().addAll(activationNotifyEventsList);
    return activationNotifyEventMessage;
  }

  public static ActivationNotifyEventMessage createActivationNotifyEventMessage() {
    return createActivationNotifyEventMessage(createActivationNotifyEvents());
  }

  public static List<ActivationNotifyEvent> createActivationNotifyEvents(ChangeStatus... changeStatuses) {
    return Arrays.stream(changeStatuses)
        .map(changeStatus -> createActivationNotifyEvent(changeStatus, createDeviceDetailedEntry()))
        .collect(Collectors.toList());
  }

  public static List<ActivationNotifyEvent> createActivationNotifyEvents() {
    return createActivationNotifyEvents(ChangeStatus.DELETED);
  }

  public static DeviceDetailedEntry createDeviceDetailedEntry() {
    DeviceDetailedEntry deviceDetailedEntry = new DeviceDetailedEntry();

    deviceDetailedEntry.setHandle(HANDLE.toString());
    deviceDetailedEntry.setSatelliteId(SATELLITE_ID.toString());
    deviceDetailedEntry.setSimEntry(createDeviceSim());
    deviceDetailedEntry.setVehiclePlatformId(VPI.toString());

    return deviceDetailedEntry;
  }

  public static DeviceInfo createDeviceInfo() {
    return createDeviceInfoBuilder().build();
  }

  public static DeviceInfoBuilder createDeviceInfoBuilder() {
    return new DeviceInfoBuilder()
        .setHandle(HANDLE)
        .setSatelliteId(Optional.of(SATELLITE_ID))
        .setSimInfo(Optional.of(createSimInfo()))
        .setVpi(Optional.of(VPI));
  }

  public static DeviceSequence createDeviceSequence() {
    return createDeviceSequenceBuilder().build();
  }

  public static DeviceSequenceBuilder createDeviceSequenceBuilder() {
    return new DeviceSequenceBuilder()
        .setDeviceInfoId(DEVICE_INFO_ID)
        .setSequenceNumber(SEQUENCE_NUMBER);
  }

  public static DeviceSim createDeviceSim() {
    DeviceSim deviceSim = new DeviceSim();

    deviceSim.setImsi(Imsi.ofLong(1L).toString());
    deviceSim.setIp(IPV4_ADDRESS.toString());
    deviceSim.setMsisdn(MSISDN.toString());
    deviceSim.setOperator(MOBILE_NETWORK_OPERATOR.toString());
    deviceSim.setPort(IPV4_PORT.toInt());

    return deviceSim;
  }

  public static PersistedDeviceInfo createPersistedDeviceInfo() {
    return createPersistedDeviceInfoBuilder().build();
  }

  public static PersistedDeviceInfoBuilder createPersistedDeviceInfoBuilder() {
    return new PersistedDeviceInfoBuilder()
        .setCreated(Instant.ofEpochSecond(1_000_000))
        .setDeviceInfo(createDeviceInfo())
        .setDeviceInfoId(DEVICE_INFO_ID)
        .setLastUpdated(Instant.ofEpochSecond(1_000_000));
  }

  public static PersistedDeviceSequence createPersistedDeviceSequence() {
    return createPersistedDeviceSequenceBuilder().build();
  }

  public static PersistedDeviceSequenceBuilder createPersistedDeviceSequenceBuilder() {
    return new PersistedDeviceSequenceBuilder()
        .setCreated(CREATED)
        .setDeviceSequence(createDeviceSequence())
        .setDeviceSequenceId(DEVICE_SEQUENCE_ID)
        .setLastUpdated(LAST_UPDATED);
  }

  public static SimInfo createSimInfo() {
    return createSimInfoBuilder().build();
  }

  public static SimInfoBuilder createSimInfoBuilder() {
    return new SimInfoBuilder()
        .setIpv4Address(IPV4_ADDRESS)
        .setIpv4Port(IPV4_PORT)
        .setImsi(Imsi.ofLong(1L))
        .setMobileNetworkOperator(MOBILE_NETWORK_OPERATOR)
        .setMsisdn(MSISDN);
  }
}
