package com.wirelesscar.tcevce.connectivityrepo.api.message.domain;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.wirelesscar.conrepo.api.v1.ActivationNotifyEvent;
import com.wirelesscar.conrepo.api.v1.ChangeStatus;
import com.wirelesscar.tcevce.connectivityrepo.api.TestUtils;

class ActivationNotifyEventComparatorTest {
  @Test
  void sortActivationNotifyEventsIsEmptyTest() {
    List<ActivationNotifyEvent> sortActivationNotifyEvents = new ArrayList<>();
    Collections.sort(sortActivationNotifyEvents, ActivationNotifyEventComparator.INSTANCE);

    Assertions.assertEquals(0, sortActivationNotifyEvents.size());
  }

  @Test
  void sortActivationNotifyEventsToHaveAllDeletedFirstTest() {
    List<ActivationNotifyEvent> sortActivationNotifyEvents = TestUtils.createActivationNotifyEvents(ChangeStatus.DELETED, ChangeStatus.UPDATED,
        ChangeStatus.DELETED);
    Collections.sort(sortActivationNotifyEvents, ActivationNotifyEventComparator.INSTANCE);

    Assertions.assertEquals(ChangeStatus.DELETED, sortActivationNotifyEvents.get(0).getChangeStatus());
    Assertions.assertEquals(ChangeStatus.DELETED, sortActivationNotifyEvents.get(1).getChangeStatus());
    Assertions.assertEquals(ChangeStatus.UPDATED, sortActivationNotifyEvents.get(2).getChangeStatus());
  }

  @Test
  void sortActivationNotifyEventsToHaveDeletedFirstTest() {
    List<ActivationNotifyEvent> sortActivationNotifyEvents = TestUtils.createActivationNotifyEvents(ChangeStatus.UPDATED, ChangeStatus.DELETED);
    Collections.sort(sortActivationNotifyEvents, ActivationNotifyEventComparator.INSTANCE);

    Assertions.assertEquals(ChangeStatus.DELETED, sortActivationNotifyEvents.get(0).getChangeStatus());
    Assertions.assertEquals(ChangeStatus.UPDATED, sortActivationNotifyEvents.get(1).getChangeStatus());
  }

  @Test
  void sortActivationNotifyEventsToHaveDeletedTest() {
    List<ActivationNotifyEvent> sortActivationNotifyEvents = TestUtils.createActivationNotifyEvents(ChangeStatus.DELETED);
    Collections.sort(sortActivationNotifyEvents, ActivationNotifyEventComparator.INSTANCE);

    Assertions.assertEquals(ChangeStatus.DELETED, sortActivationNotifyEvents.get(0).getChangeStatus());
  }

  @Test
  void sortActivationNotifyEventsToHaveUpdated() {
    List<ActivationNotifyEvent> sortActivationNotifyEvents = TestUtils.createActivationNotifyEvents(ChangeStatus.UPDATED);
    Collections.sort(sortActivationNotifyEvents, ActivationNotifyEventComparator.INSTANCE);

    Assertions.assertEquals(ChangeStatus.UPDATED, sortActivationNotifyEvents.get(0).getChangeStatus());
  }
}
