package com.wirelesscar.tcevce.connectivityrepo.api.message.domain;

import java.util.Collections;
import java.util.Set;

import org.junit.jupiter.api.Test;
import org.mockito.InOrder;
import org.mockito.Mockito;

import com.volvo.tisp.vc.test.utils.lib.AssertThrows;
import com.wirelesscar.conrepo.api.v1.ActivationNotifyEventMessage;
import com.wirelesscar.tcevce.connectivityrepo.api.TestUtils;
import com.wirelesscar.tcevce.wecu.device.info.cache.core.api.CacheInvalidator;
import com.wirelesscar.tcevce.wecu.device.info.database.model.Handle;

class ConrepoNotifyServiceTest {
  @Test
  void constructorInvalidTest() {
    CacheInvalidator cacheInvalidator = Mockito.mock(CacheInvalidator.class);

    AssertThrows.illegalArgumentException(() -> new ConrepoNotifyService(null, null), "cacheInvalidator must not be null");
    AssertThrows.illegalArgumentException(() -> new ConrepoNotifyService(cacheInvalidator, null), "notifyRepository must not be null");

    Mockito.verifyNoInteractions(cacheInvalidator);
  }

  @Test
  void handleNotifyEventMessageInvalidTest() {
    CacheInvalidator cacheInvalidator = Mockito.mock(CacheInvalidator.class);
    NotifyRepository notifyRepository = Mockito.mock(NotifyRepository.class);

    ConrepoNotifyService conrepoNotifyService = new ConrepoNotifyService(cacheInvalidator, notifyRepository);
    AssertThrows.illegalArgumentException(() -> conrepoNotifyService.handleNotifyEventMessage(null), "activationNotifyEventMessage must not be null");

    InOrder inOrder = Mockito.inOrder(cacheInvalidator, notifyRepository);
    inOrder.verifyNoMoreInteractions();
  }

  @Test
  void handleNotifyEventMessageWhenVpisToInvalidateAreAbsentTest() {
    CacheInvalidator cacheInvalidator = Mockito.mock(CacheInvalidator.class);
    NotifyRepository notifyRepository = Mockito.mock(NotifyRepository.class);

    ActivationNotifyEventMessage activationNotifyEventMessage = new ActivationNotifyEventMessage();

    Mockito.when(notifyRepository.handleActivationNotifyMessage(activationNotifyEventMessage)).thenReturn(Collections.emptySet());

    ConrepoNotifyService conrepoNotifyService = new ConrepoNotifyService(cacheInvalidator, notifyRepository);
    conrepoNotifyService.handleNotifyEventMessage(activationNotifyEventMessage);

    InOrder inOrder = Mockito.inOrder(cacheInvalidator, notifyRepository);
    inOrder.verify(notifyRepository).handleActivationNotifyMessage(activationNotifyEventMessage);
    inOrder.verifyNoMoreInteractions();
  }

  @Test
  void handleNotifyEventMessageWhenVpisToInvalidateArePresentTest() {
    CacheInvalidator cacheInvalidator = Mockito.mock(CacheInvalidator.class);
    NotifyRepository notifyRepository = Mockito.mock(NotifyRepository.class);

    ActivationNotifyEventMessage activationNotifyEventMessage = new ActivationNotifyEventMessage();

    Set<Handle> handlesToBeInvalidateInCache = Set.of(TestUtils.HANDLE);
    Mockito.when(notifyRepository.handleActivationNotifyMessage(activationNotifyEventMessage)).thenReturn(handlesToBeInvalidateInCache);

    ConrepoNotifyService conrepoNotifyService = new ConrepoNotifyService(cacheInvalidator, notifyRepository);
    conrepoNotifyService.handleNotifyEventMessage(activationNotifyEventMessage);

    InOrder inOrder = Mockito.inOrder(cacheInvalidator, notifyRepository);
    inOrder.verify(notifyRepository).handleActivationNotifyMessage(activationNotifyEventMessage);
    inOrder.verify(cacheInvalidator).invalidateCache(handlesToBeInvalidateInCache);
    inOrder.verifyNoMoreInteractions();
  }
}
