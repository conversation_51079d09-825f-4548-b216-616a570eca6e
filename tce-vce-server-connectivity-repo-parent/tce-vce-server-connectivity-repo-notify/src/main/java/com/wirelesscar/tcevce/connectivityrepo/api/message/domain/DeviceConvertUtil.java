package com.wirelesscar.tcevce.connectivityrepo.api.message.domain;

import java.util.List;

import com.volvo.tisp.vc.common.dto.lib.vehicle.Ipv4Address;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Msisdn;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.wirelesscar.conrepo.api.v1.ActivationNotifyEvent;
import com.wirelesscar.conrepo.api.v1.DeviceDetailedEntry;
import com.wirelesscar.conrepo.api.v1.DeviceSim;
import com.wirelesscar.tcevce.wecu.device.info.database.model.Handle;
import com.wirelesscar.tcevce.wecu.device.info.database.model.SatelliteId;
import com.wirelesscar.tcevce.wecu.device.info.database.model.UniqueVehicleIdentifier;
import com.wirelesscar.tcevce.wecu.device.info.database.model.UniqueVehicleIdentifierBuilder;

final class DeviceConvertUtil {
  private DeviceConvertUtil() {
    throw new IllegalStateException();
  }

  static UniqueVehicleIdentifier createUniqueVehicleIdentifier(List<ActivationNotifyEvent> activationNotifyEvents) {
    Validate.notEmpty(activationNotifyEvents, "activationNotifyEvents");

    UniqueVehicleIdentifierBuilder uniqueVehicleIdentifierBuilder = new UniqueVehicleIdentifierBuilder();

    for (ActivationNotifyEvent activationNotifyEvent : activationNotifyEvents) {
      DeviceDetailedEntry deviceDetailedEntry = activationNotifyEvent.getDeviceDetail();

      uniqueVehicleIdentifierBuilder.addVpi(Vpi.ofString(deviceDetailedEntry.getVehiclePlatformId()));

      if (deviceDetailedEntry.getHandle() != null) {
        uniqueVehicleIdentifierBuilder.addHandle(Handle.ofString(deviceDetailedEntry.getHandle()));
      }

      DeviceSim deviceSim = deviceDetailedEntry.getSimEntry();
      if (deviceSim != null) {
        uniqueVehicleIdentifierBuilder.addIpv4Address(Ipv4Address.ofString(deviceSim.getIp()));
        uniqueVehicleIdentifierBuilder.addMsisdn(Msisdn.ofString(deviceSim.getMsisdn()));
      }

      if (deviceDetailedEntry.getSatelliteId() != null) {
        uniqueVehicleIdentifierBuilder.addSatelliteId(SatelliteId.ofString(deviceDetailedEntry.getSatelliteId()));
      }
    }

    return uniqueVehicleIdentifierBuilder.build();
  }
}
