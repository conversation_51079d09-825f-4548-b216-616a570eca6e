package com.wirelesscar.tcevce.connectivityrepo.api.message.domain;

import java.util.Set;

import org.springframework.stereotype.Service;

import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.wirelesscar.conrepo.api.v1.ActivationNotifyEventMessage;
import com.wirelesscar.tcevce.wecu.device.info.cache.core.api.CacheInvalidator;
import com.wirelesscar.tcevce.wecu.device.info.database.model.Handle;

/**
 * Handles notification event from conrepo and notifies all TCEs about containing devices
 */
@Service
public class ConrepoNotifyService {
  private final CacheInvalidator cacheInvalidator;
  private final NotifyRepository notifyRepository;

  public ConrepoNotifyService(CacheInvalidator cacheInvalidator, NotifyRepository notifyRepository) {
    Validate.notNull(cacheInvalidator, "cacheInvalidator");
    Validate.notNull(notifyRepository, "notifyRepository");

    this.cacheInvalidator = cacheInvalidator;
    this.notifyRepository = notifyRepository;
  }

  public void handleNotifyEventMessage(ActivationNotifyEventMessage activationNotifyEventMessage) {
    Validate.notNull(activationNotifyEventMessage, "activationNotifyEventMessage");

    Set<Handle> handlesToBeInvalidateInCache = notifyRepository.handleActivationNotifyMessage(activationNotifyEventMessage);

    if (!handlesToBeInvalidateInCache.isEmpty()) {
      cacheInvalidator.invalidateCache(handlesToBeInvalidateInCache);
    }
  }
}
