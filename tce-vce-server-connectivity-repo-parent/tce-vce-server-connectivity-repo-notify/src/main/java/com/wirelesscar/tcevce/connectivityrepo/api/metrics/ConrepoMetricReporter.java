package com.wirelesscar.tcevce.connectivityrepo.api.metrics;

import org.springframework.stereotype.Component;

import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.wirelesscar.conrepo.api.v1.ChangeStatus;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;

@Component
public class ConrepoMetricReporter {
  static final String ACTIVATION_NOTIFY = "api.domain.conrepo.v1.activation.notify";
  static final String CONREPO_NOTIFICATION = "conrepo.notification";
  static final String FAIL = "fail";
  static final String SUCCESS = "success";
  static final String TAG_ACTIVATION_STATUS = "activation_status";
  static final String TAG_CHANGE_STATUS = "change_status";

  private final Counter activationFailCounter;
  private final Counter activationSuccessCounter;
  private final MeterRegistry meterRegistry;

  public ConrepoMetricReporter(MeterRegistry meterRegistry) {
    Validate.notNull(meterRegistry, "meterRegistry");

    this.meterRegistry = meterRegistry;

    activationFailCounter = meterRegistry.counter(ConrepoMetricReporter.ACTIVATION_NOTIFY, TAG_ACTIVATION_STATUS, FAIL);
    activationSuccessCounter = meterRegistry.counter(ConrepoMetricReporter.ACTIVATION_NOTIFY, TAG_ACTIVATION_STATUS, SUCCESS);
  }

  public void onActivationFail() {
    activationFailCounter.increment();
  }

  public void onActivationSuccess() {
    activationSuccessCounter.increment();
  }

  public void onConrepoNotification(ChangeStatus changeStatus) {
    Validate.notNull(changeStatus, "changeStatus");

    meterRegistry.counter(CONREPO_NOTIFICATION, TAG_CHANGE_STATUS, changeStatus.name()).increment();
  }
}
