package com.wirelesscar.tcevce.connectivityrepo.api.message.jms;

import com.volvo.tisp.framework.jms.JmsMessage;
import com.volvo.tisp.framework.jms.annotation.JmsController;
import com.volvo.tisp.framework.jms.annotation.JmsMessageMapping;
import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.wirelesscar.conrepo.MessageTypes;
import com.wirelesscar.conrepo.api.v1.ActivationNotifyEventMessage;
import com.wirelesscar.tcevce.connectivityrepo.api.message.domain.ConrepoNotifyDomainManager;

@JmsController(destination = ConrepoNotifyJmsController.CONREPO1_NOTIFY_IN_QUEUE)
public class ConrepoNotifyJmsController {
  public static final String CONREPO1_NOTIFY_IN_QUEUE = "DEVICE.NOTIFY.IN";

  private final ConrepoNotifyDomainManager conrepoNotifyDomainManager;

  public ConrepoNotifyJmsController(ConrepoNotifyDomainManager conrepoNotifyDomainManager) {
    Validate.notNull(conrepoNotifyDomainManager, "conrepoNotifyDomainManager");

    this.conrepoNotifyDomainManager = conrepoNotifyDomainManager;
  }

  @JmsMessageMapping(consumesType = MessageTypes.ACTIVATION_NOTIFY_MESSAGE_TYPE, consumesVersion = MessageTypes.VERSION_1_0)
  public void receiveMessageV1(final JmsMessage<ActivationNotifyEventMessage> jmsMessage) {
    Validate.notNull(jmsMessage, "jmsMessage");

    conrepoNotifyDomainManager.receiveMessageV1(jmsMessage.payload());
  }
}
