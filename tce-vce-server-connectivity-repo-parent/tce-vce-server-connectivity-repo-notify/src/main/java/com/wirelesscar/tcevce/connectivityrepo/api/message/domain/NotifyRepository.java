package com.wirelesscar.tcevce.connectivityrepo.api.message.domain;

import java.util.Set;

import org.springframework.stereotype.Component;

import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.wirelesscar.conrepo.api.v1.ActivationNotifyEventMessage;
import com.wirelesscar.tcevce.wecu.device.info.database.model.Handle;

@Component
public class NotifyRepository {
  private final NotifyRepositoryWorker notifyRepositoryWorker;

  public NotifyRepository(NotifyRepositoryWorker notifyRepositoryWorker) {
    Validate.notNull(notifyRepositoryWorker, "notifyRepositoryWorker");

    this.notifyRepositoryWorker = notifyRepositoryWorker;
  }

  public Set<Handle> handleActivationNotifyMessage(ActivationNotifyEventMessage activationNotifyEventMessage) {
    Validate.notNull(activationNotifyEventMessage, "activationNotifyEventMessage");

    return notifyRepositoryWorker.handleActivationNotifyEventMessage(activationNotifyEventMessage);
  }
}
