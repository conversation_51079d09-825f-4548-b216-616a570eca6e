package com.wirelesscar.tcevce.connectivityrepo.api.message.domain;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.wirelesscar.conrepo.api.v1.ActivationNotifyEvent;
import com.wirelesscar.conrepo.api.v1.ActivationNotifyEventMessage;
import com.wirelesscar.tcevce.connectivityrepo.api.metrics.ConrepoMetricReporter;

@Component
public class ConrepoNotifyDomainManager {
  private static final Logger logger = LoggerFactory.getLogger(ConrepoNotifyDomainManager.class);

  private final ConrepoMetricReporter conrepoMetricReporter;
  private final ConrepoNotifyService conrepoNotifyService;

  public ConrepoNotifyDomainManager(ConrepoNotifyService conrepoNotifyService, ConrepoMetricReporter conrepoMetricReporter) {
    Validate.notNull(conrepoNotifyService, "conrepoNotifyService");
    Validate.notNull(conrepoMetricReporter, "conrepoMetricReporter");

    this.conrepoNotifyService = conrepoNotifyService;
    this.conrepoMetricReporter = conrepoMetricReporter;
  }

  public void receiveMessageV1(ActivationNotifyEventMessage activationNotifyEventMessage) {
    Validate.notNull(activationNotifyEventMessage, "activationNotifyEventMessage");

    try {
      logger.debug("Received ActivationNotifyEventMessage, it contains {} messages", activationNotifyEventMessage.getActivationNotifyEvents().size());

      reportConrepoNotifications(activationNotifyEventMessage);
      conrepoNotifyService.handleNotifyEventMessage(activationNotifyEventMessage);

      conrepoMetricReporter.onActivationSuccess();
    } catch (RuntimeException e) {
      logger.warn("Failed to handle activation message request", e);
      conrepoMetricReporter.onActivationFail();
    }
  }

  private void reportConrepoNotifications(ActivationNotifyEventMessage activationNotifyEventMessage) {
    activationNotifyEventMessage.getActivationNotifyEvents()
        .stream()
        .map(ActivationNotifyEvent::getChangeStatus)
        .forEach(conrepoMetricReporter::onConrepoNotification);
  }
}
