package com.wirelesscar.tcevce.connectivityrepo.api.message.domain;

import java.util.Comparator;

import com.wirelesscar.conrepo.api.v1.ActivationNotifyEvent;

public final class ActivationNotifyEventComparator implements Comparator<ActivationNotifyEvent> {
  public static final Comparator<ActivationNotifyEvent> INSTANCE = new ActivationNotifyEventComparator();

  private ActivationNotifyEventComparator() {
    // do nothing
  }

  @Override
  public int compare(ActivationNotifyEvent activationNotifyEvent1, ActivationNotifyEvent activationNotifyEvent2) {
    return activationNotifyEvent1
        .getChangeStatus()
        .toString()
        .compareTo(activationNotifyEvent2.getChangeStatus().toString());
  }
}
