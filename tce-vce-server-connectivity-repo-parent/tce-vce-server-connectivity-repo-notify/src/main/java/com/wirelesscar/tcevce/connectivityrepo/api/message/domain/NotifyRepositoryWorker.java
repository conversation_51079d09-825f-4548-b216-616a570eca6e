package com.wirelesscar.tcevce.connectivityrepo.api.message.domain;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.volvo.tisp.vc.common.dto.lib.vehicle.Msisdn;
import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.volvo.tisp.vc.main.utils.lib.type.Either;
import com.wirelesscar.conrepo.api.v1.ActivationNotifyEvent;
import com.wirelesscar.conrepo.api.v1.ActivationNotifyEventMessage;
import com.wirelesscar.conrepo.api.v1.ChangeStatus;
import com.wirelesscar.conrepo.api.v1.DeviceDetailedEntry;
import com.wirelesscar.conrepo.api.v1.State;
import com.wirelesscar.tcevce.connectivityrepo.converter.DeviceDetailedEntryInputConverterFunction;
import com.wirelesscar.tcevce.wecu.device.info.cache.core.api.VehicleKeyCacheInvalidator;
import com.wirelesscar.tcevce.wecu.device.info.database.api.DeviceInfoWriter;
import com.wirelesscar.tcevce.wecu.device.info.database.api.DeviceInfoWriterFactory;
import com.wirelesscar.tcevce.wecu.device.info.database.api.WecuKeyWriter;
import com.wirelesscar.tcevce.wecu.device.info.database.api.WecuKeyWriterFactory;
import com.wirelesscar.tcevce.wecu.device.info.database.model.DeviceInfo;
import com.wirelesscar.tcevce.wecu.device.info.database.model.DeviceInfoId;
import com.wirelesscar.tcevce.wecu.device.info.database.model.DeviceSequence;
import com.wirelesscar.tcevce.wecu.device.info.database.model.DeviceSequenceBuilder;
import com.wirelesscar.tcevce.wecu.device.info.database.model.DeviceSequenceId;
import com.wirelesscar.tcevce.wecu.device.info.database.model.Handle;
import com.wirelesscar.tcevce.wecu.device.info.database.model.InsertionFailure;
import com.wirelesscar.tcevce.wecu.device.info.database.model.PersistedDeviceInfo;
import com.wirelesscar.tcevce.wecu.device.info.database.model.PersistedDeviceSequence;
import com.wirelesscar.tcevce.wecu.device.info.database.model.SequenceNumber;
import com.wirelesscar.tcevce.wecu.device.info.database.model.SimInfo;
import com.wirelesscar.tcevce.wecu.device.info.database.model.UniqueVehicleIdentifier;
import com.wirelesscar.tcevce.wecu.device.info.database.model.WecuKey;
import com.wirelesscar.tcevce.wecu.device.info.database.model.WecuKeyBuilder;
import com.wirelesscar.tcevce.wecu.device.info.database.model.WecuKeyId;

@Component
class NotifyRepositoryWorker {
  private static final int MAX_NO_OF_EXPRESSIONS = 1_000;
  private static final Logger logger = LoggerFactory.getLogger(NotifyRepositoryWorker.class);

  private final Map<Handle, PersistedDeviceInfo> affectedDeviceMap = new HashMap<>();
  private final DeviceInfoWriterFactory deviceInfoWriterFactory;
  private final VehicleKeyCacheInvalidator vehicleKeyCacheInvalidator;
  private final WecuKeyWriterFactory wecuKeyWriterFactory;

  NotifyRepositoryWorker(DeviceInfoWriterFactory deviceInfoWriterFactory, VehicleKeyCacheInvalidator vehicleKeyCacheInvalidator,
      WecuKeyWriterFactory wecuKeyWriterFactory) {
    Validate.notNull(deviceInfoWriterFactory, "deviceInfoWriterFactory");
    Validate.notNull(wecuKeyWriterFactory, "wecuKeyWriterFactory");
    Validate.notNull(vehicleKeyCacheInvalidator, "vehicleKeyCacheInvalidator");

    this.wecuKeyWriterFactory = wecuKeyWriterFactory;
    this.deviceInfoWriterFactory = deviceInfoWriterFactory;
    this.vehicleKeyCacheInvalidator = vehicleKeyCacheInvalidator;
  }

  private static DeviceInfo getDeviceInfo(DeviceDetailedEntry deviceDetailedEntry) {
    Either<IllegalArgumentException, DeviceInfo> deviceInfoEither = DeviceDetailedEntryInputConverterFunction.INSTANCE.apply(deviceDetailedEntry);

    if (deviceInfoEither.isLeft()) {
      throw deviceInfoEither.getLeft();
    }
    return deviceInfoEither.getRight();
  }

  /**
   * Check if the provided devices share any of the following unique identities: VPI, Msisdn, SatelliteId.
   *
   * @return true if one or more identity is equal, false otherwise.
   */
  private static boolean haveSameUniqueId(DeviceInfo deviceInfo1, DeviceInfo deviceInfo2) {
    return isSameUniqueId(deviceInfo1.getVpi(), deviceInfo2.getVpi())
        || isSameSimInfoId(deviceInfo1, deviceInfo2)
        || isSameUniqueId(deviceInfo1.getSatelliteId(), deviceInfo2.getSatelliteId());
  }

  /**
   * Insert Device Sequence if it does not already exists.
   */
  private static void insertDeviceSequenceIfNeeded(PersistedDeviceInfo persistedDeviceInfo, DeviceInfoWriter deviceInfoWriter) {
    Optional<PersistedDeviceSequence> optional = deviceInfoWriter.findDeviceSequenceByDeviceInfoId(persistedDeviceInfo.getDeviceInfoId());

    if (optional.isEmpty()) {
      DeviceSequence deviceSequence = new DeviceSequenceBuilder()
          .setDeviceInfoId(persistedDeviceInfo.getDeviceInfoId())
          .setSequenceNumber(SequenceNumber.ofByte((byte) 0))
          .build();

      Either<InsertionFailure, DeviceSequenceId> either = deviceInfoWriter.insertDeviceSequence(deviceSequence);

      if (either.isLeft()) {
        throw new IllegalStateException(either.getLeft().getRuntimeException());
      }
    }
  }

  private static boolean isSameSimInfoId(DeviceInfo deviceInfo1, DeviceInfo deviceInfo2) {
    Optional<SimInfo> optionalSimInfo1 = deviceInfo1.getSimInfo();
    Optional<SimInfo> optionalSimInfo2 = deviceInfo2.getSimInfo();

    if (optionalSimInfo1.isEmpty() || optionalSimInfo2.isEmpty()) {
      return false;
    }

    SimInfo simInfo1 = optionalSimInfo1.get();
    SimInfo simInfo2 = optionalSimInfo2.get();

    return Objects.equals(simInfo1.getIpv4Address(), simInfo2.getIpv4Address())
        || Objects.equals(simInfo1.getMsisdn(), simInfo2.getMsisdn());
  }

  private static <T> boolean isSameUniqueId(Optional<T> optional1, Optional<T> optional2) {
    if (optional1.isEmpty() || optional2.isEmpty()) {
      return false;
    }

    return optional1.equals(optional2);
  }

  private static void verifyUniqueVehicleIdentifierLimit(UniqueVehicleIdentifier uniqueVehicleIdentifier) {
    if (uniqueVehicleIdentifier.getVpis().size() > MAX_NO_OF_EXPRESSIONS
        || uniqueVehicleIdentifier.getMsisdns().size() > MAX_NO_OF_EXPRESSIONS
        || uniqueVehicleIdentifier.getHandles().size() > MAX_NO_OF_EXPRESSIONS
        || uniqueVehicleIdentifier.getIpv4Addresses().size() > MAX_NO_OF_EXPRESSIONS
        || uniqueVehicleIdentifier.getSatelliteIds().size() > MAX_NO_OF_EXPRESSIONS) {
      String message = new StringBuilder(200)
          .append(uniqueVehicleIdentifier.getVpis().size())
          .append(" VPIs, ")
          .append(uniqueVehicleIdentifier.getMsisdns().size())
          .append(" MSISDNs, ")
          .append(uniqueVehicleIdentifier.getHandles().size())
          .append(" Handles, ")
          .append(uniqueVehicleIdentifier.getIpv4Addresses().size())
          .append(" Ipv4Addresses, ")
          .append(uniqueVehicleIdentifier.getSatelliteIds().size())
          .append(" SatelliteIds")
          .toString();
      throw new IllegalStateException(
          String.format(Locale.ENGLISH, "Message contains %s. The Oracle database JDBC driver doesn't support more than 1000 parameters.", message));
    }
  }

  /**
   * Handle an activation notify event message.
   *
   * @return a set of VPIs that should be invalidated in the cache.
   */
  Set<Handle> handleActivationNotifyEventMessage(ActivationNotifyEventMessage activationNotifyEventMessage) {
    Validate.notNull(activationNotifyEventMessage, "activationNotifyEventMessage");

    try (DeviceInfoWriter deviceInfoWriter = deviceInfoWriterFactory.createReadCommitted();
        WecuKeyWriter wecuKeyWriter = wecuKeyWriterFactory.createReadCommitted()) {
      deviceInfoWriter.startTransaction();
      wecuKeyWriter.startTransaction();

      List<ActivationNotifyEvent> activationNotifyEvents = activationNotifyEventMessage.getActivationNotifyEvents();

      if (activationNotifyEvents.isEmpty()) {
        return Collections.emptySet();
      }

      Collections.sort(activationNotifyEvents, ActivationNotifyEventComparator.INSTANCE);
      findAndLockAffectedDevices(activationNotifyEvents, deviceInfoWriter);

      Set<Handle> handlesToBeInvalidateInCache = new HashSet<>(affectedDeviceMap.keySet());
      Set<String> vehicleKeyCacheToBeInvalidated = new HashSet<>(activationNotifyEvents.size());

      for (ActivationNotifyEvent activationNotifyEvent : activationNotifyEvents) {
        handlesToBeInvalidateInCache.add(Handle.ofString(activationNotifyEvent.getDeviceDetail().getHandle()));
        handleActivationNotifyEvent(activationNotifyEvent, deviceInfoWriter, wecuKeyWriter);
        vehicleKeyCacheToBeInvalidated.add(
            Optional.ofNullable(activationNotifyEvent.getDeviceDetail().getVehiclePlatformId()).orElse(activationNotifyEvent.getDeviceDetail().getHandle()));
      }
      wecuKeyWriter.commitTransaction();
      deviceInfoWriter.commitTransaction();

      vehicleKeyCacheInvalidator.invalidateCache(vehicleKeyCacheToBeInvalidated);
      return handlesToBeInvalidateInCache;
    }
  }

  /**
   * Locks all rows in database for devices holding same unique keys (VPI, MSISDN, Handle, Ipv4Address, ObsAlias) as the devices in provided activation notify
   * events and update the affectedDeviceMap.
   */
  private void findAndLockAffectedDevices(List<ActivationNotifyEvent> activationNotifyEvents, DeviceInfoWriter deviceInfoWriter) {
    UniqueVehicleIdentifier uniqueVehicleIdentifier = DeviceConvertUtil.createUniqueVehicleIdentifier(activationNotifyEvents);

    verifyUniqueVehicleIdentifierLimit(uniqueVehicleIdentifier);

    List<PersistedDeviceInfo> persistedDeviceInfos = deviceInfoWriter.findAndLockByDeviceInfoUniqueKeys(uniqueVehicleIdentifier);

    for (PersistedDeviceInfo persistedDeviceInfo : persistedDeviceInfos) {
      affectedDeviceMap.put(persistedDeviceInfo.getDeviceInfo().getHandle(), persistedDeviceInfo);
    }
  }

  /**
   * Handle an activation notification event. This function assumes that all devices with related unique keys are locked (for update) in database and populated
   * in the affectedDeviceMap.
   */
  private void handleActivationNotifyEvent(ActivationNotifyEvent activationNotifyEvent, DeviceInfoWriter deviceInfoWriter, WecuKeyWriter wecuKeyWriter) {
    DeviceDetailedEntry deviceDetailedEntry = activationNotifyEvent.getDeviceDetail();
    Handle handle = Handle.ofString(deviceDetailedEntry.getHandle());
    ChangeStatus changeStatus = activationNotifyEvent.getChangeStatus();
    logger.debug("Received ActivationNotifyEvent for handle: {}, changeStatus: {}", handle, changeStatus);

    if (changeStatus == null) {
      throw new IllegalArgumentException("changeStatus must not be null");
    }

    switch (changeStatus) {
      case UPDATED:
      case SYNC:
        handleState(deviceDetailedEntry, handle, deviceDetailedEntry.getState(), deviceInfoWriter, wecuKeyWriter);
        break;

      case DELETED:
        removeDevice(handle, deviceInfoWriter, wecuKeyWriter);
        break;

      default:
        throw new IllegalArgumentException("unknown changeStatus: " + changeStatus);
    }
  }

  private void handleState(DeviceDetailedEntry deviceDetailedEntry, Handle handle, State state, DeviceInfoWriter deviceInfoWriter,
      WecuKeyWriter wecuKeyWriter) {
    switch (state) {
      case ACTIVATED:
        DeviceInfo deviceInfo = getDeviceInfo(deviceDetailedEntry);
        removeDevicesWithClashingKeys(deviceInfo, deviceInfoWriter, wecuKeyWriter);
        updateOrInsertDevice(deviceInfo, deviceInfoWriter, wecuKeyWriter);
        break;

      case DEACTIVATED:
        removeDevice(handle, deviceInfoWriter, wecuKeyWriter);
        break;

      case NEW:
      case PAUSED:
      default:
        throw new IllegalArgumentException("unsupported state: " + state);
    }
  }

  /**
   * Remove specified persisted device in the database and affectedDeviceMap.
   */
  private void removeDevice(Handle handle, DeviceInfoWriter deviceInfoWriter, WecuKeyWriter wecuKeyWriter) {
    PersistedDeviceInfo persistedDeviceInfo = affectedDeviceMap.remove(handle);
    if (persistedDeviceInfo != null) {
      int numberOfDevicesDeleted = deviceInfoWriter.deleteDeviceInfoByHandle(handle);
      int numberOfWecuKeysDeleted = wecuKeyWriter.deleteWecuKeyByHandle(handle);

      if (numberOfDevicesDeleted != 1) {
        logger.warn("deleteDeviceInfoByVpi, handle: {}, numberOfDevicesDeleted: {}, numberOfWecuKeysDeleted: {}", handle, numberOfDevicesDeleted,
            numberOfWecuKeysDeleted);
      }
    }
  }

  /**
   * Remove devices in database and affectedDeviceMap that has clashing unique ids (MSISDN, Vpi, Ipv4Address). If there is a persisted device with the same
   * Handle as the supplied device info, it is not deleted.
   */
  private void removeDevicesWithClashingKeys(DeviceInfo deviceInfo, DeviceInfoWriter deviceInfoWriter, WecuKeyWriter wecuKeyWriter) {
    List<DeviceInfo> toRemoveDeviceInfos = new ArrayList<>(1);

    for (PersistedDeviceInfo itDeviceInfo : affectedDeviceMap.values()) {
      if (!deviceInfo.getHandle().equals(itDeviceInfo.getDeviceInfo().getHandle())
          && haveSameUniqueId(deviceInfo, itDeviceInfo.getDeviceInfo())) {
        toRemoveDeviceInfos.add(itDeviceInfo.getDeviceInfo());
      }
    }

    for (DeviceInfo deviceInfoToRemove : toRemoveDeviceInfos) {
      logger.debug("Removing device: {}, conflicts with update on device: {}", deviceInfoToRemove.getHandle(), deviceInfo.getHandle());
      removeDevice(deviceInfoToRemove.getHandle(), deviceInfoWriter, wecuKeyWriter);
    }
  }

  /**
   * Update or insert the provided device in the database and affectedDeviceMap.
   */
  private void updateOrInsertDevice(DeviceInfo deviceInfo, DeviceInfoWriter deviceInfoWriter, WecuKeyWriter wecuKeyWriter) {
    Handle handle = deviceInfo.getHandle();

    PersistedDeviceInfo persistedDeviceInfo = affectedDeviceMap.get(handle);

    if (persistedDeviceInfo != null) {
      logger.debug("Updating device with handle: {}", handle);
      int updatedRows = deviceInfoWriter.updateDeviceInfoByHandle(deviceInfo);

      if (updatedRows != 1) {
        throw new IllegalStateException(String.format(Locale.ENGLISH, "Unable to update device with handle=%s properly", handle));
      }
    } else {
      logger.debug("Inserting device with handle: {}", handle);
      Either<InsertionFailure, DeviceInfoId> either = deviceInfoWriter.insertDeviceInfo(deviceInfo);

      if (either.isLeft()) {
        throw new IllegalStateException(either.getLeft().getRuntimeException());
      }
    }

    wecuKeyWriter.findWecuKeyByHandle(handle).ifPresent(persistedWecuKey -> {
      WecuKey wecuKey = persistedWecuKey.getWecuKey();

      Optional<Msisdn> optionalDeviceMsisdn = deviceInfo.getSimInfo().map(SimInfo::getMsisdn);
      optionalDeviceMsisdn.ifPresentOrElse(deviceMsisdn -> {
        Msisdn wecuKeyMsisdn = wecuKey.getMsisdn();
        boolean isVpiEquals = Objects.equals(deviceInfo.getVpi().orElse(null), wecuKey.getVpi().orElse(null));
        if (!isVpiEquals || !wecuKey.getHandle().equals(handle) || !wecuKeyMsisdn.equals(deviceMsisdn)) {
          WecuKeyBuilder wecuKeyBuilder = new WecuKeyBuilder()
              .setHandle(handle)
              .setMsisdn(deviceMsisdn);
          deviceInfo.getVpi().ifPresent(wecuKeyBuilder::setVpi);

          WecuKeyId wecuKeyId = persistedWecuKey.getWecuKeyId();
          int updateWecuKey = wecuKeyWriter.updateWecuKey(wecuKeyBuilder.build(), wecuKeyId);
          logger.info("Update {} rows for wecuKeyId={}", updateWecuKey, wecuKeyId);
        }
      }, () -> wecuKeyWriter.deleteWecuKeyByHandle(handle));

    });

    persistedDeviceInfo = deviceInfoWriter.findDeviceInfoByHandle(handle).orElseThrow(IllegalStateException::new);
    insertDeviceSequenceIfNeeded(persistedDeviceInfo, deviceInfoWriter);
    affectedDeviceMap.put(handle, persistedDeviceInfo);
  }
}
