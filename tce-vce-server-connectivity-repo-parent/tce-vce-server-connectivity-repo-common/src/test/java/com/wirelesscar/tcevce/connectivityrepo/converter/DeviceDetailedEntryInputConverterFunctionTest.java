package com.wirelesscar.tcevce.connectivityrepo.converter;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.vc.main.utils.lib.type.Either;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;
import com.wirelesscar.conrepo.api.v1.DeviceDetailedEntry;
import com.wirelesscar.conrepo.api.v1.DeviceSim;
import com.wirelesscar.tcevce.wecu.device.info.database.model.DeviceInfo;
import com.wirelesscar.tcevce.wecu.device.info.database.model.SimInfo;

class DeviceDetailedEntryInputConverterFunctionTest {
  private static DeviceInfo convertToDeviceInfo(DeviceDetailedEntry deviceDetailedEntry) {
    return DeviceDetailedEntryInputConverterFunction.INSTANCE.apply(deviceDetailedEntry).getRight();
  }

  private static void verifyEqualsWithSatelliteId(DeviceDetailedEntry deviceDetailedEntry, DeviceInfo deviceInfo) {
    verifyHandleAndVpiAreEqual(deviceDetailedEntry, deviceInfo);
    Assertions.assertEquals(deviceDetailedEntry.getSatelliteId(), deviceInfo.getSatelliteId().get().toString());
  }

  private static void verifyEqualsWithoutSatelliteId(DeviceDetailedEntry deviceDetailedEntry, DeviceInfo deviceInfo) {
    verifyHandleAndVpiAreEqual(deviceDetailedEntry, deviceInfo);
    Assertions.assertTrue(deviceInfo.getSatelliteId().isEmpty());
  }

  private static void verifyHandleAndVpiAreEqual(DeviceDetailedEntry deviceDetailedEntry, DeviceInfo deviceInfo) {
    Assertions.assertEquals(deviceDetailedEntry.getHandle(), deviceInfo.getHandle().toString());
    Assertions.assertEquals(deviceDetailedEntry.getVehiclePlatformId(), deviceInfo.getVpi().get().toString());
  }

  private static void verifySimInfo(DeviceSim deviceSim, SimInfo simInfo) {
    Assertions.assertEquals(deviceSim.getIp(), simInfo.getIpv4Address().toString());
    Assertions.assertEquals(deviceSim.getPort(), simInfo.getIpv4Port().toInt());
    Assertions.assertEquals(deviceSim.getOperator(), simInfo.getMobileNetworkOperator().toString());
    Assertions.assertEquals(deviceSim.getMsisdn(), simInfo.getMsisdn().toString());
  }

  @Test
  void applyInvalidTest() {
    AssertThrows.illegalArgumentException(() -> DeviceDetailedEntryInputConverterFunction.INSTANCE.apply(null), "deviceDetailedEntry must not be null");
  }

  @Test
  void applyWhenMappingFailsTest() {
    DeviceDetailedEntry deviceDetailedEntry = TestUtils.createDeviceDetailedEntry();
    deviceDetailedEntry.setVehiclePlatformId("vpi");

    Either<IllegalArgumentException, DeviceInfo> either = DeviceDetailedEntryInputConverterFunction.INSTANCE.apply(deviceDetailedEntry);
    Assertions.assertEquals("vpiString must have a length of 32: vpi", either.getLeft().getMessage());
  }

  @Test
  void applyWithSatelliteIdTest() {
    DeviceDetailedEntry deviceDetailedEntry = TestUtils.createDeviceDetailedEntry();

    DeviceInfo deviceInfo = convertToDeviceInfo(deviceDetailedEntry);

    verifyEqualsWithSatelliteId(deviceDetailedEntry, deviceInfo);
    verifySimInfo(deviceDetailedEntry.getSimEntry(), deviceInfo.getSimInfo().get());
  }

  @Test
  void applyWithoutIpv4AddressTest() {
    DeviceDetailedEntry deviceDetailedEntry = TestUtils.createDeviceDetailedEntry();
    deviceDetailedEntry.getSimEntry().setIp(null);

    DeviceInfo deviceInfo = convertToDeviceInfo(deviceDetailedEntry);

    verifyEqualsWithSatelliteId(deviceDetailedEntry, deviceInfo);
    Assertions.assertTrue(deviceInfo.getSimInfo().isEmpty());
  }

  @Test
  void applyWithoutIpv4PortTest() {
    DeviceDetailedEntry deviceDetailedEntry = TestUtils.createDeviceDetailedEntry();
    deviceDetailedEntry.getSimEntry().setPort(null);

    DeviceInfo deviceInfo = convertToDeviceInfo(deviceDetailedEntry);

    verifyEqualsWithSatelliteId(deviceDetailedEntry, deviceInfo);
    Assertions.assertTrue(deviceInfo.getSimInfo().isEmpty());
  }

  @Test
  void applyWithoutMobileOperatorTest() {
    DeviceDetailedEntry deviceDetailedEntry = TestUtils.createDeviceDetailedEntry();
    deviceDetailedEntry.getSimEntry().setOperator(null);

    DeviceInfo deviceInfo = convertToDeviceInfo(deviceDetailedEntry);

    verifyEqualsWithSatelliteId(deviceDetailedEntry, deviceInfo);
    Assertions.assertTrue(deviceInfo.getSimInfo().isEmpty());
  }

  @Test
  void applyWithoutMsisdnTest() {
    DeviceDetailedEntry deviceDetailedEntry = TestUtils.createDeviceDetailedEntry();
    deviceDetailedEntry.getSimEntry().setMsisdn(null);

    DeviceInfo deviceInfo = convertToDeviceInfo(deviceDetailedEntry);

    verifyEqualsWithSatelliteId(deviceDetailedEntry, deviceInfo);
    Assertions.assertTrue(deviceInfo.getSimInfo().isEmpty());
  }

  @Test
  void applyWithoutSatelliteIdTest() {
    DeviceDetailedEntry deviceDetailedEntry = TestUtils.createDeviceDetailedEntry();
    deviceDetailedEntry.setSatelliteId(null);

    DeviceInfo deviceInfo = convertToDeviceInfo(deviceDetailedEntry);

    verifyEqualsWithoutSatelliteId(deviceDetailedEntry, deviceInfo);
    verifySimInfo(deviceDetailedEntry.getSimEntry(), deviceInfo.getSimInfo().get());
  }

  @Test
  void applyWithoutVpiTest() {
    DeviceDetailedEntry deviceDetailedEntry = TestUtils.createDeviceDetailedEntry();
    deviceDetailedEntry.setVehiclePlatformId(null);

    DeviceInfo deviceInfo = convertToDeviceInfo(deviceDetailedEntry);

    Assertions.assertEquals(deviceDetailedEntry.getSatelliteId(), deviceInfo.getSatelliteId().get().toString());
    Assertions.assertTrue(deviceInfo.getVpi().isEmpty());
    verifySimInfo(deviceDetailedEntry.getSimEntry(), deviceInfo.getSimInfo().get());
  }

  @Test
  void applyWithoutSimInfoTest() {
    DeviceDetailedEntry deviceDetailedEntry = TestUtils.createDeviceDetailedEntry();
    deviceDetailedEntry.setSimEntry(null);

    DeviceInfo deviceInfo = convertToDeviceInfo(deviceDetailedEntry);

    verifyEqualsWithSatelliteId(deviceDetailedEntry, deviceInfo);
    Assertions.assertTrue(deviceInfo.getSimInfo().isEmpty());
  }
}
