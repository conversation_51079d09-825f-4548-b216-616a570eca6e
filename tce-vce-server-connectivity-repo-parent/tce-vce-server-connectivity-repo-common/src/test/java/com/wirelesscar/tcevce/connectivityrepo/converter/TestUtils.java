package com.wirelesscar.tcevce.connectivityrepo.converter;

import com.volvo.tisp.vc.common.dto.lib.vehicle.Ipv4Address;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Msisdn;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.wirelesscar.conrepo.api.v1.DeviceDetailedEntry;
import com.wirelesscar.conrepo.api.v1.DeviceSim;
import com.wirelesscar.tcevce.wecu.device.info.database.model.Handle;
import com.wirelesscar.tcevce.wecu.device.info.database.model.Ipv4Port;
import com.wirelesscar.tcevce.wecu.device.info.database.model.MobileNetworkOperator;
import com.wirelesscar.tcevce.wecu.device.info.database.model.SatelliteId;

final class TestUtils {
  static final Handle HANDLE = Handle.ofString("123456");
  static final Ipv4Address IPV4_ADDRESS = Ipv4Address.ofString("*******");
  static final Ipv4Port IPV4_PORT = Ipv4Port.ofInt(44);
  static final MobileNetworkOperator MOBILE_NETWORK_OPERATOR = MobileNetworkOperator.ofString("telenor");
  static final Msisdn MSISDN = Msisdn.ofString("+4631888888");
  static final SatelliteId SATELLITE_ID = SatelliteId.ofString("HQ1234567890x1");
  static final Vpi VPI = Vpi.ofString("1234567890ABCDEF1234567890ABCDEF");

  private TestUtils() {
    throw new IllegalStateException();
  }

  static DeviceDetailedEntry createDeviceDetailedEntry() {
    DeviceDetailedEntry deviceDetailedEntry = new DeviceDetailedEntry();

    deviceDetailedEntry.setHandle(HANDLE.toString());
    deviceDetailedEntry.setSatelliteId(SATELLITE_ID.toString());
    deviceDetailedEntry.setSimEntry(createDeviceSim());
    deviceDetailedEntry.setVehiclePlatformId(VPI.toString());

    return deviceDetailedEntry;
  }

  static DeviceSim createDeviceSim() {
    DeviceSim deviceSim = new DeviceSim();

    deviceSim.setIp(IPV4_ADDRESS.toString());
    deviceSim.setMsisdn(MSISDN.toString());
    deviceSim.setOperator(MOBILE_NETWORK_OPERATOR.toString());
    deviceSim.setPort(IPV4_PORT.toInt());
    deviceSim.setImsi("1");

    return deviceSim;
  }
}
