package com.wirelesscar.tcevce.connectivityrepo.converter;

import java.util.Optional;
import java.util.function.Function;

import com.volvo.tisp.vc.common.dto.lib.vehicle.Imsi;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Ipv4Address;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Msisdn;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.volvo.tisp.vc.main.utils.lib.type.Either;
import com.wirelesscar.conrepo.api.v1.DeviceDetailedEntry;
import com.wirelesscar.conrepo.api.v1.DeviceSim;
import com.wirelesscar.tcevce.wecu.device.info.database.model.DeviceInfo;
import com.wirelesscar.tcevce.wecu.device.info.database.model.DeviceInfoBuilder;
import com.wirelesscar.tcevce.wecu.device.info.database.model.Handle;
import com.wirelesscar.tcevce.wecu.device.info.database.model.Ipv4Port;
import com.wirelesscar.tcevce.wecu.device.info.database.model.MobileNetworkOperator;
import com.wirelesscar.tcevce.wecu.device.info.database.model.SatelliteId;
import com.wirelesscar.tcevce.wecu.device.info.database.model.SimInfo;
import com.wirelesscar.tcevce.wecu.device.info.database.model.SimInfoBuilder;

public final class DeviceDetailedEntryInputConverterFunction implements Function<DeviceDetailedEntry, Either<IllegalArgumentException, DeviceInfo>> {
  public static final Function<DeviceDetailedEntry, Either<IllegalArgumentException, DeviceInfo>> INSTANCE = new DeviceDetailedEntryInputConverterFunction();

  private DeviceDetailedEntryInputConverterFunction() {
    //do nothing
  }

  private static DeviceInfo createDeviceInfo(DeviceDetailedEntry deviceDetailedEntry) {
    return new DeviceInfoBuilder()
        .setHandle(Handle.ofString(deviceDetailedEntry.getHandle()))
        .setSatelliteId(Optional.ofNullable(deviceDetailedEntry.getSatelliteId()).map(SatelliteId::ofString))
        .setSimInfo(Optional.ofNullable(deviceDetailedEntry.getSimEntry()).flatMap(DeviceDetailedEntryInputConverterFunction::mapToSimInfo))
        .setVpi(Optional.ofNullable(deviceDetailedEntry.getVehiclePlatformId()).map(Vpi::ofString))
        .build();
  }

  private static SimInfo createSimInfo(Ipv4Address ipv4Address, Ipv4Port ipv4Port, MobileNetworkOperator mobileNetworkOperator, Msisdn msisdn, Imsi imsi) {
    return new SimInfoBuilder()
        .setImsi(imsi)
        .setIpv4Address(ipv4Address)
        .setIpv4Port(ipv4Port)
        .setMobileNetworkOperator(mobileNetworkOperator)
        .setMsisdn(msisdn)
        .build();
  }

  private static Optional<SimInfo> mapToSimInfo(DeviceSim deviceSim) {
    Imsi imsi = Optional.ofNullable(deviceSim.getImsi()).map(stringImsi -> Imsi.ofLong(Long.parseLong(stringImsi))).orElse(null);
    Optional<Ipv4Address> optionalIpv4Address = Optional.ofNullable(deviceSim.getIp()).map(Ipv4Address::ofString);
    Optional<Ipv4Port> optionalIpv4Port = Optional.ofNullable(deviceSim.getPort()).map(Ipv4Port::ofInt);
    Optional<MobileNetworkOperator> optionalMobileNetworkOperator = Optional.ofNullable(deviceSim.getOperator()).map(MobileNetworkOperator::ofString);
    Optional<Msisdn> optionalMsisdn = Optional.ofNullable(deviceSim.getMsisdn()).map(Msisdn::ofString);

    if (optionalIpv4Address.isEmpty() || optionalIpv4Port.isEmpty() || optionalMobileNetworkOperator.isEmpty() || optionalMsisdn.isEmpty()) {
      return Optional.empty();
    }

    return Optional.of(
        createSimInfo(optionalIpv4Address.get(), optionalIpv4Port.get(), optionalMobileNetworkOperator.get(), optionalMsisdn.get(), imsi));
  }

  @Override
  public Either<IllegalArgumentException, DeviceInfo> apply(DeviceDetailedEntry deviceDetailedEntry) {
    Validate.notNull(deviceDetailedEntry, "deviceDetailedEntry");

    try {
      return Either.right(createDeviceInfo(deviceDetailedEntry));
    } catch (IllegalArgumentException e) {
      return Either.left(e);
    }
  }
}
