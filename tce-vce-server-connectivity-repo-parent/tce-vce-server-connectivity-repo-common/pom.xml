<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
     xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>com.volvo.tisp</groupId>
    <artifactId>tce-vce-server-connectivity-repo-parent</artifactId>
    <version>0-SNAPSHOT</version>
  </parent>

  <artifactId>tce-vce-server-connectivity-repo-common</artifactId>

  <dependencies>
    <dependency>
      <groupId>com.wirelesscar.conrepo</groupId>
      <artifactId>connectivity-repository-client-api</artifactId>
    </dependency>
    <dependency>
      <groupId>com.volvo.tisp</groupId>
      <artifactId>wecu-device-info-cache-core</artifactId>
      <version>${project.version}</version>
    </dependency>

    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter-engine</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>ch.qos.logback</groupId>
      <artifactId>logback-classic</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.volvo.tisp</groupId>
      <artifactId>test-utils-lib</artifactId>
      <scope>test</scope>
    </dependency>
  </dependencies>
</project>
