<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
     xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>com.volvo.tisp</groupId>
    <artifactId>tce-vce-server</artifactId>
    <version>0-SNAPSHOT</version>
  </parent>

  <artifactId>tce-vce-server-impl</artifactId>

  <dependencies>
    <dependency>
      <groupId>com.volvo.tisp.framework</groupId>
      <artifactId>tisp-framework-starter-web</artifactId>
    </dependency>
    <dependency>
      <groupId>com.volvo.tisp</groupId>
      <artifactId>tce-vce-server-message-jms</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>com.volvo.tisp</groupId>
      <artifactId>tce-vce-server-module-mo-throttling</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>com.volvo.tisp</groupId>
      <artifactId>tce-vce-server-module-mt-doorkeeper</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>com.volvo.tisp</groupId>
      <artifactId>tce-module-threadpool</artifactId>
    </dependency>
    <dependency>
      <groupId>com.volvo.tisp</groupId>
      <artifactId>tce-vce-server-module-split-ack</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>com.volvo.tisp</groupId>
      <artifactId>tce-vce-server-module-segmentation</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>com.volvo.tisp</groupId>
      <artifactId>tce-vce-server-module-logging</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>com.volvo.tisp</groupId>
      <artifactId>tce-vce-server-module-identify</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>com.volvo.tisp</groupId>
      <artifactId>tce-vce-server-module-router-subscription</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>com.volvo.tisp</groupId>
      <artifactId>tce-vce-server-module-schedule-selector</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>com.volvo.tisp</groupId>
      <artifactId>tce-module-udp</artifactId>
    </dependency>
    <dependency>
      <groupId>com.volvo.tisp</groupId>
      <artifactId>tce-module-tcpip</artifactId>
    </dependency>
    <dependency>
      <groupId>com.volvo.tisp</groupId>
      <artifactId>tce-module-shard-router</artifactId>
    </dependency>
    <dependency>
      <groupId>com.volvo.tisp</groupId>
      <artifactId>tce-module-scooter-agent</artifactId>
    </dependency>
    <dependency>
      <groupId>com.volvo.tisp</groupId>
      <artifactId>tce-core-legacy-ha</artifactId>
    </dependency>
    <dependency>
      <groupId>com.volvo.tisp</groupId>
      <artifactId>tce-vce-server-connectivity-repo-notify</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>com.volvo.tisp</groupId>
      <artifactId>tce-common-hazelcast</artifactId>
    </dependency>
    <dependency>
      <groupId>com.volvo.tisp</groupId>
      <artifactId>vc-influxdb-event-reporter-lib</artifactId>
    </dependency>
    <dependency>
      <groupId>com.volvo.tisp</groupId>
      <artifactId>vc-uncaught-exception-handler-lib</artifactId>
    </dependency>
    <dependency>
      <groupId>io.micrometer</groupId>
      <artifactId>micrometer-registry-influx</artifactId>
    </dependency>
    <dependency>
      <groupId>com.volvo.tisp</groupId>
      <artifactId>wecu-device-info-cache-notify</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>com.volvo.tisp</groupId>
      <artifactId>vc-crypto-symmetric</artifactId>
    </dependency>

    <dependency>
      <groupId>com.wirelesscar.config-lib</groupId>
      <artifactId>config-lib-impl-mock</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-core</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter-engine</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.volvo.tisp</groupId>
      <artifactId>test-utils-lib</artifactId>
      <scope>test</scope>
    </dependency>
  </dependencies>
</project>
