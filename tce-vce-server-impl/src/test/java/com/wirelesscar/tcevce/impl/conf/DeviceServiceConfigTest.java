package com.wirelesscar.tcevce.impl.conf;

import java.time.Clock;

import javax.sql.DataSource;

import org.jdbi.v3.core.Jdbi;
import org.jdbi.v3.core.mapper.RowMapper;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.InOrder;
import org.mockito.Mockito;

import com.hazelcast.core.HazelcastInstance;
import com.hazelcast.topic.ITopic;
import com.hazelcast.topic.MessageListener;
import com.wirelesscar.config.mock.MockConfiguration;
import com.wirelesscar.tce.db.source.TceDataSource;
import com.wirelesscar.tcevce.impl.TestUtils;
import com.wirelesscar.tcevce.wecu.device.info.cache.core.api.CacheDeviceInfoReader;
import com.wirelesscar.tcevce.wecu.device.info.cache.core.impl.CacheDeviceInfoMetricReporter;
import com.wirelesscar.tcevce.wecu.device.info.cache.core.impl.DeviceServiceCacheManager;
import com.wirelesscar.tcevce.wecu.device.info.cache.notify.api.DeviceNotificationPublisher;
import com.wirelesscar.tcevce.wecu.device.info.cache.notify.impl.DeviceNotification;
import com.wirelesscar.tcevce.wecu.device.info.cache.notify.metrics.NotifyMetricReporter;
import com.wirelesscar.tcevce.wecu.device.info.database.api.DeviceInfoReaderFactory;
import com.wirelesscar.tcevce.wecu.device.info.database.model.PersistedDeviceInfo;

import io.micrometer.core.instrument.MeterRegistry;

import static com.wirelesscar.tcevce.wecu.device.info.cache.notify.metrics.NotifyMetricReporter.CACHE;
import static com.wirelesscar.tcevce.wecu.device.info.cache.notify.metrics.NotifyMetricReporter.CACHE_DEVICE_INFO;
import static com.wirelesscar.tcevce.wecu.device.info.cache.notify.metrics.NotifyMetricReporter.CACHE_VEHICLE_KEY;
import static com.wirelesscar.tcevce.wecu.device.info.cache.notify.metrics.NotifyMetricReporter.METRIC_NAME_NOTIFY;
import static com.wirelesscar.tcevce.wecu.device.info.cache.notify.metrics.NotifyMetricReporter.OPERATION;
import static com.wirelesscar.tcevce.wecu.device.info.cache.notify.metrics.NotifyMetricReporter.OPERATION_PUBLISH;
import static com.wirelesscar.tcevce.wecu.device.info.cache.notify.metrics.NotifyMetricReporter.OPERATION_RECEIVE;

class DeviceServiceConfigTest {
  private static RowMapper<PersistedDeviceInfo> mockRowMapper() {
    return Mockito.mock(RowMapper.class);
  }

  @Test
  void createCacheDeviceInfoMetricReporterTest() {
    MeterRegistry meterRegistry = Mockito.mock(MeterRegistry.class);

    Assertions.assertNotNull(new DeviceServiceConfig().createCacheDeviceInfoMetricReporter(meterRegistry));

    String cache = "cache";
    String deviceService = "device.service";

    InOrder inOrder = Mockito.inOrder(meterRegistry);
    inOrder.verify(meterRegistry).counter(deviceService, cache, "hit");
    inOrder.verify(meterRegistry).counter(deviceService, cache, "miss");
    inOrder.verify(meterRegistry).counter(deviceService, cache, "current-number-of-entries");
    inOrder.verifyNoMoreInteractions();
  }

  @Test
  void createCacheDeviceInfoReaderTest() {
    DeviceInfoReaderFactory deviceInfoReaderFactory = Mockito.mock(DeviceInfoReaderFactory.class);
    CacheDeviceInfoMetricReporter cacheDeviceInfoMetricReporter = Mockito.mock(CacheDeviceInfoMetricReporter.class);

    MockConfiguration mockConfiguration = TestUtils.getEmptyMockConfiguration();
    mockConfiguration.setPropertySpecific(mockConfiguration.getComponentShortName(), ConfigLoader.DEVICE_SERVICE_MAXIMUM_CACHE_SIZE_IN_BYTES, "1");

    Assertions.assertNotNull(new DeviceServiceConfig().createCacheDeviceInfoReader(deviceInfoReaderFactory, cacheDeviceInfoMetricReporter, mockConfiguration));

    Mockito.verifyNoInteractions(deviceInfoReaderFactory, cacheDeviceInfoMetricReporter);
  }

  @Test
  void createCacheInvalidatorTest() {
    DeviceNotificationPublisher deviceNotificationPublisher = Mockito.mock(DeviceNotificationPublisher.class);

    Assertions.assertNotNull(new DeviceServiceConfig().createCacheInvalidator(deviceNotificationPublisher));

    Mockito.verifyNoInteractions(deviceNotificationPublisher);
  }

  @Test
  void createClockTest() {
    Assertions.assertEquals(Clock.systemUTC(), new DeviceServiceConfig().createClock());
  }

  @Test
  void createDataSourceTest() {
    MockConfiguration mockConfiguration = TestUtils.getEmptyMockConfiguration();
    mockConfiguration.setPropertySpecific(mockConfiguration.getComponentShortName(), TceDataSource.TEST_DB_PROP, "true");

    Assertions.assertNotNull(new DeviceServiceConfig().createDataSource());
  }

  @Test
  void createDeviceInfoReaderFactoryTest() {
    Clock clock = Mockito.mock(Clock.class);
    Jdbi jdbi = Mockito.mock(Jdbi.class);
    RowMapper<PersistedDeviceInfo> rowMapper = mockRowMapper();

    Assertions.assertNotNull(new DeviceServiceConfig().createDeviceInfoReaderFactory(clock, jdbi, rowMapper));

    Mockito.verifyNoInteractions(clock, jdbi, rowMapper);
  }

  @Test
  void createDeviceInfoWriterFactoryTest() {
    Clock clock = Mockito.mock(Clock.class);
    Jdbi jdbi = Mockito.mock(Jdbi.class);
    RowMapper<PersistedDeviceInfo> rowMapper = mockRowMapper();

    Assertions.assertNotNull(new DeviceServiceConfig().createDeviceInfoWriterFactory(clock, jdbi, rowMapper));

    Mockito.verifyNoInteractions(clock, jdbi, rowMapper);
  }

  @Test
  void createDeviceNotificationMessageListenerTest() {
    DeviceServiceCacheManager deviceServiceCacheManager = Mockito.mock(DeviceServiceCacheManager.class);
    NotifyMetricReporter notifyMetricReporter = Mockito.mock(NotifyMetricReporter.class);

    Assertions.assertNotNull(new DeviceServiceConfig().createDeviceNotificationMessageListener(deviceServiceCacheManager, notifyMetricReporter));

    Mockito.verifyNoInteractions(deviceServiceCacheManager, notifyMetricReporter);
  }

  @Test
  void createDeviceNotificationPublisherTest() {
    HazelcastInstance hazelcastInstance = Mockito.mock(HazelcastInstance.class);
    ITopic<DeviceNotification> iTopic = Mockito.mock(ITopic.class);
    MessageListener<DeviceNotification> deviceNotificationMessageListener = Mockito.mock(MessageListener.class);
    NotifyMetricReporter notifyMetricReporter = Mockito.mock(NotifyMetricReporter.class);

    String topic = "tce-device-notify";
    Mockito.when(hazelcastInstance.<DeviceNotification>getReliableTopic(topic)).thenReturn(iTopic);

    Assertions.assertNotNull(
        new DeviceServiceConfig().createDeviceNotificationPublisher(deviceNotificationMessageListener, hazelcastInstance, notifyMetricReporter));

    InOrder inOrder = Mockito.inOrder(hazelcastInstance, notifyMetricReporter, deviceNotificationMessageListener, iTopic);
    inOrder.verify(hazelcastInstance).getReliableTopic(topic);
    inOrder.verify(iTopic).addMessageListener(deviceNotificationMessageListener);
    inOrder.verifyNoMoreInteractions();
  }

  @Test
  void createDeviceServiceCacheManagerTest() {
    CacheDeviceInfoReader cacheDeviceInfoReader = Mockito.mock(CacheDeviceInfoReader.class);

    Assertions.assertNotNull(new DeviceServiceConfig().createDeviceServiceCacheManager(cacheDeviceInfoReader));

    Mockito.verifyNoInteractions(cacheDeviceInfoReader);
  }

  @Test
  void createJdbiTest() {
    DataSource dataSource = Mockito.mock(DataSource.class);

    Assertions.assertNotNull(new DeviceServiceConfig().createJdbi(dataSource));

    Mockito.verifyNoInteractions(dataSource);
  }

  @Test
  void createNotifyMetricReporterTest() {
    MeterRegistry meterRegistry = Mockito.mock(MeterRegistry.class);

    Assertions.assertNotNull(new DeviceServiceConfig().createNotifyMetricReporter(meterRegistry));

    InOrder inOrder = Mockito.inOrder(meterRegistry);
    inOrder.verify(meterRegistry).counter(METRIC_NAME_NOTIFY, OPERATION, OPERATION_RECEIVE, CACHE, CACHE_DEVICE_INFO);
    inOrder.verify(meterRegistry).counter(METRIC_NAME_NOTIFY, OPERATION, OPERATION_PUBLISH, CACHE, CACHE_DEVICE_INFO);
    inOrder.verify(meterRegistry).counter(METRIC_NAME_NOTIFY, OPERATION, OPERATION_RECEIVE, CACHE, CACHE_VEHICLE_KEY);
    inOrder.verify(meterRegistry).counter(METRIC_NAME_NOTIFY, OPERATION, OPERATION_PUBLISH, CACHE, CACHE_VEHICLE_KEY);
    inOrder.verifyNoMoreInteractions();
  }

  @Test
  void createPersistedDeviceInfoRowMapperTest() {
    Assertions.assertNotNull(new DeviceServiceConfig().createPersistedDeviceInfoRowMapper());
  }
}
