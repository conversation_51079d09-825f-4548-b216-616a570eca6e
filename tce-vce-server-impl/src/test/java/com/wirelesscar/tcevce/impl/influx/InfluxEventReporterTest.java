package com.wirelesscar.tcevce.impl.influx;

import java.util.function.Consumer;

import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import com.volvo.tisp.vc.influxdb.event.reporter.InfluxEventSender;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

class InfluxEventReporterTest {
  private static void onEventExceptionTest(Consumer<InfluxEventSender> exceptionMockConsumer, Consumer<InfluxEventReporter> consumer,
      Consumer<InfluxEventSender> verifyConsumer) {
    InfluxEventSender influxEventSender = Mockito.mock(InfluxEventSender.class);
    InfluxEventReporter influxEventReporter = new InfluxEventReporter(influxEventSender);

    exceptionMockConsumer.accept(influxEventSender);
    consumer.accept(influxEventReporter);

    verifyConsumer.accept(influxEventSender);
    Mockito.verifyNoMoreInteractions(influxEventSender);
  }

  private static void onEventTest(Consumer<InfluxEventReporter> consumer, Consumer<InfluxEventSender> verifyConsumer) {
    InfluxEventSender influxEventSender = Mockito.mock(InfluxEventSender.class);
    InfluxEventReporter influxEventReporter = new InfluxEventReporter(influxEventSender);

    consumer.accept(influxEventReporter);
    verifyConsumer.accept(influxEventSender);

    Mockito.verifyNoMoreInteractions(influxEventSender);
  }

  @Test
  void invalidConstructorTest() {
    AssertThrows.illegalArgumentException(() -> new InfluxEventReporter(null), "influxEventSender must not be null");
  }

  @Test
  void onApplicationReadyEventExceptionTest() {
    onEventExceptionTest(influxEventSender -> Mockito.doThrow(new IllegalStateException()).when(influxEventSender).sendStarted(),
        InfluxEventReporter::onApplicationReadyEvent, influxEventSender -> Mockito.verify(influxEventSender).sendStarted());
  }

  @Test
  void onApplicationReadyEventTest() {
    onEventTest(InfluxEventReporter::onApplicationReadyEvent, influxEventSender -> Mockito.verify(influxEventSender).sendStarted());
  }

  @Test
  void onContextClosedExceptionTest() {
    onEventExceptionTest(influxEventSender -> Mockito.doThrow(new IllegalStateException()).when(influxEventSender).sendStopped(),
        InfluxEventReporter::onContextClosed, influxEventSender -> Mockito.verify(influxEventSender).sendStopped());
  }

  @Test
  void onContextClosedTest() {
    onEventTest(InfluxEventReporter::onContextClosed, influxEventSender -> Mockito.verify(influxEventSender).sendStopped());
  }

  @Test
  void onServiceActivatedEventExceptionTest() {
    onEventExceptionTest(influxEventSender -> Mockito.doThrow(new IllegalStateException()).when(influxEventSender).sendActivated(),
        InfluxEventReporter::onServiceActivatedEvent, influxEventSender -> Mockito.verify(influxEventSender).sendActivated());
  }

  @Test
  void onServiceActivatedEventTest() {
    onEventTest(InfluxEventReporter::onServiceActivatedEvent, influxEventSender -> Mockito.verify(influxEventSender).sendActivated());
  }

  @Test
  void onServiceDeactivatedEventExceptionTest() {
    onEventExceptionTest(influxEventSender -> Mockito.doThrow(new IllegalStateException()).when(influxEventSender).sendDeactivated(),
        InfluxEventReporter::onServiceDeactivatedEvent, influxEventSender -> Mockito.verify(influxEventSender).sendDeactivated());
  }

  @Test
  void onServiceDeactivatedEventTest() {
    onEventTest(InfluxEventReporter::onServiceDeactivatedEvent, influxEventSender -> Mockito.verify(influxEventSender).sendDeactivated());
  }
}
