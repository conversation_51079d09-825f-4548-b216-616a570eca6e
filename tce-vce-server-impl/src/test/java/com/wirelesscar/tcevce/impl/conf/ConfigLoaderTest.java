package com.wirelesscar.tcevce.impl.conf;

import java.time.Duration;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.vc.influxdb.event.reporter.InfluxParameters;
import com.volvo.tisp.vc.influxdb.event.reporter.InfluxParametersBuilder;
import com.volvo.tisp.vc.influxdb.event.reporter.RetentionPolicy;
import com.volvo.tisp.vc.influxdb.event.reporter.RuntimeParameters;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;
import com.wirelesscar.config.mock.MockConfiguration;
import com.wirelesscar.tcevce.impl.TestUtils;

class ConfigLoaderTest {
  private static MockConfiguration getMockConfiguration() {
    MockConfiguration mockConfiguration = TestUtils.getEmptyMockConfiguration();
    String componentShortName = mockConfiguration.getComponentShortName();

    mockConfiguration.setPropertySpecific(componentShortName, "management.influx.metrics.export.db", "someDbName");
    mockConfiguration.setPlatformProperty("management.influx.metrics.export.uri", "someUri");
    mockConfiguration.setPropertySpecific(componentShortName, "management.influx.metrics.export.retention-policy", "30days");

    return mockConfiguration;
  }

  @Test
  void getConrepoBaseUrlInvalidTest() {
    AssertThrows.illegalArgumentException(() -> ConfigLoader.getConrepoBaseUrl(null), "config must not be null");
  }

  @Test
  void getConrepoBaseUrlTest() {
    String propertyName = "servicediscovery.conrepo";

    MockConfiguration mockConfiguration = getMockConfiguration();
    AssertThrows.illegalArgumentException(() -> ConfigLoader.getConrepoBaseUrl(mockConfiguration), "missing string config: " + propertyName);

    String expectedConrepoBaseUrl = "testUrl";
    mockConfiguration.setPropertySpecific(mockConfiguration.getComponentShortName(), propertyName, expectedConrepoBaseUrl);
    Assertions.assertEquals(expectedConrepoBaseUrl, ConfigLoader.getConrepoBaseUrl(mockConfiguration));
  }

  @Test
  void getInfluxParametersInvalidTest() {
    AssertThrows.illegalArgumentException(() -> ConfigLoader.getInfluxParameters(null), "config must not be null");

    MockConfiguration mockConfiguration = TestUtils.getEmptyMockConfiguration();
    AssertThrows.illegalArgumentException(() -> ConfigLoader.getInfluxParameters(mockConfiguration),
        "missing string config: management.influx.metrics.export.db");

    final String componentShortName = mockConfiguration.getComponentShortName();
    mockConfiguration.setPropertySpecific(componentShortName, "management.influx.metrics.export.db", "someDbName");
    AssertThrows.illegalArgumentException(() -> ConfigLoader.getInfluxParameters(mockConfiguration),
        "missing string config: management.influx.metrics.export.retention-policy");

    mockConfiguration.setPropertySpecific(componentShortName, "management.influx.metrics.export.retention-policy", "42days");
    AssertThrows.illegalArgumentException(() -> ConfigLoader.getInfluxParameters(mockConfiguration), "unknown RetentionPolicy: 42days");

    mockConfiguration.setPropertySpecific(componentShortName, "management.influx.metrics.export.retention-policy", "30days");
    AssertThrows.illegalArgumentException(() -> ConfigLoader.getInfluxParameters(mockConfiguration),
        "missing platform string config: management.influx.metrics.export.uri");
  }

  @Test
  void getInfluxParametersOverrideDefaultsTest() {
    MockConfiguration mockConfiguration = getMockConfiguration();
    mockConfiguration.setPropertySpecific(mockConfiguration.getComponentShortName(), "influx.connect.timeout", "PT0.042S");
    mockConfiguration.setPropertySpecific(mockConfiguration.getComponentShortName(), "influx.read.timeout", "PT0.043S");

    InfluxParameters influxParameters = ConfigLoader.getInfluxParameters(mockConfiguration);

    Assertions.assertEquals(Duration.ofMillis(42), influxParameters.getConnectTimeout());
    Assertions.assertEquals("someDbName", influxParameters.getDbName());
    Assertions.assertEquals(Duration.ofMillis(43), influxParameters.getReadTimeout());
    Assertions.assertEquals(RetentionPolicy.POLICY_30_DAYS, influxParameters.getRetentionPolicy());
    Assertions.assertEquals("someUri", influxParameters.getServerUrl());
  }

  @Test
  void getInfluxParametersWithDefaultsTest() {
    InfluxParameters influxParameters = ConfigLoader.getInfluxParameters(getMockConfiguration());

    Assertions.assertSame(InfluxParametersBuilder.DEFAULT_CONNECT_TIMEOUT, influxParameters.getConnectTimeout());
    Assertions.assertEquals("someDbName", influxParameters.getDbName());
    Assertions.assertSame(InfluxParametersBuilder.DEFAULT_READ_TIMEOUT, influxParameters.getReadTimeout());
    Assertions.assertSame(RetentionPolicy.POLICY_30_DAYS, influxParameters.getRetentionPolicy());
    Assertions.assertEquals("someUri", influxParameters.getServerUrl());
  }

  @Test
  void getMaximumCacheSizeInvalidTest() {
    AssertThrows.illegalArgumentException(() -> ConfigLoader.getMaximumCacheSize(null), "config must not be null");

    MockConfiguration mockConfiguration = TestUtils.getEmptyMockConfiguration();
    AssertThrows.illegalArgumentException(() -> ConfigLoader.getMaximumCacheSize(mockConfiguration),
        "missing long config: " + ConfigLoader.DEVICE_SERVICE_MAXIMUM_CACHE_SIZE_IN_BYTES);
  }

  @Test
  void getMaximumCacheSizeTest() {
    MockConfiguration mockConfiguration = TestUtils.getEmptyMockConfiguration();
    mockConfiguration.setPropertySpecific(mockConfiguration.getComponentShortName(), ConfigLoader.DEVICE_SERVICE_MAXIMUM_CACHE_SIZE_IN_BYTES, "1");

    Assertions.assertEquals(1L, ConfigLoader.getMaximumCacheSize(mockConfiguration));
  }

  @Test
  void getRuntimeParametersTest() {
    MockConfiguration mockConfiguration = TestUtils.getEmptyMockConfiguration();
    RuntimeParameters runtimeParameters = ConfigLoader.getRuntimeParameters(mockConfiguration);

    Assertions.assertEquals(mockConfiguration.getComponentShortName(), runtimeParameters.getComponentShortName());
    Assertions.assertEquals(mockConfiguration.getComponentVersion(), runtimeParameters.getComponentVersion());
    Assertions.assertEquals(mockConfiguration.getEnvironmentId(), runtimeParameters.getEnvironmentId());
    Assertions.assertEquals(mockConfiguration.getNodeId(), runtimeParameters.getNodeId());
    Assertions.assertEquals(mockConfiguration.getSite(), runtimeParameters.getSite());
    Assertions.assertEquals(mockConfiguration.getSolution(), runtimeParameters.getSolution());
  }
}
