package com.wirelesscar.tcevce.impl.conf;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.wirelesscar.config.Config;
import com.wirelesscar.config.ConfigFactory;
import com.wirelesscar.config.mock.MockConfiguration;

class AppConfigTest {
  private static MockConfiguration getEmptyMockConfiguration() {
    MockConfiguration mockConfiguration = MockConfiguration.getConfig();
    mockConfiguration.deleteAllProperties();
    return mockConfiguration;
  }

  @Test
  void createConfigTest() {
    Assertions.assertNotNull(new AppConfig().createConfig());
  }

  @Test
  void createInfluxEventReporterTest() {
    Config config = ConfigFactory.getConfig();

    MockConfiguration mockConfiguration = getEmptyMockConfiguration();
    mockConfiguration.setPropertySpecific(mockConfiguration.getComponentShortName(), "management.influx.metrics.export.db", "test");
    mockConfiguration.setPropertySpecific(mockConfiguration.getComponentShortName(), "management.influx.metrics.export.retention-policy", "30days");
    mockConfiguration.setPlatformProperty("management.influx.metrics.export.uri", "http://localhost:8086/");

    Assertions.assertNotNull(new AppConfig().createInfluxEventReporter(config));
  }

  @Test
  void createRestTemplateTest() {
    Assertions.assertNotNull(new AppConfig().createRestTemplate());
  }

  @Test
  void createSchedulerTest() {
    Assertions.assertNotNull(new AppConfig().createScheduler());
  }
}
