package com.wirelesscar.tcevce.impl.conf;

import java.util.Optional;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import com.hazelcast.core.HazelcastInstance;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;
import com.wirelesscar.config.mock.MockConfiguration;
import com.wirelesscar.tce.hazelcast.HazelcastInstanceProvider;
import com.wirelesscar.tce.hazelcast.reporter.HazelcastMetricReporter;
import com.wirelesscar.tcevce.impl.TestUtils;

import io.micrometer.core.instrument.MeterRegistry;

class HazelcastConfigTest {
  private static HazelcastInstanceProvider mockHazelcastInstanceProvider(Optional<HazelcastInstance> hazelcastInstanceOptional) {
    HazelcastInstanceProvider hazelcastInstanceProvider = Mockito.mock(HazelcastInstanceProvider.class);
    Mockito.when(hazelcastInstanceProvider.getHazelcastInstance()).thenReturn(hazelcastInstanceOptional);
    return hazelcastInstanceProvider;
  }

  @Test
  void createHazelcastInstanceInvalidTest() {
    HazelcastInstanceProvider hazelcastInstanceProvider = mockHazelcastInstanceProvider(Optional.empty());

    AssertThrows.illegalStateException(
        () -> new HazelcastConfig().createHazelcastInstance(hazelcastInstanceProvider),
        "Was not able to create a Hazelcast instance for tce-vce-server"
    );
  }

  @Test
  void createHazelcastInstanceProviderTest() {
    MockConfiguration mockConfiguration = TestUtils.getEmptyMockConfiguration();
    mockConfiguration.setPropertySpecific(mockConfiguration.getComponentShortName(), "hazelcast.enabled", Boolean.FALSE.toString());

    Assertions.assertNotNull(new HazelcastConfig().createHazelcastInstanceProvider(mockConfiguration));
  }

  @Test
  void createHazelcastInstanceTest() {
    HazelcastInstance hazelcastInstance = Mockito.mock(HazelcastInstance.class);
    HazelcastInstanceProvider hazelcastInstanceProvider = mockHazelcastInstanceProvider(Optional.of(hazelcastInstance));

    Assertions.assertEquals(hazelcastInstance, new HazelcastConfig().createHazelcastInstance(hazelcastInstanceProvider));
  }

  @Test
  void createHazelcastMetricReporterTest() {
    Assertions.assertNotNull(new HazelcastConfig().createHazelcastMetricReporter(Mockito.mock(MeterRegistry.class)));
  }

  @Test
  void createHazelcastStatisticsReporterTest() {
    HazelcastInstance hazelcastInstance = Mockito.mock(HazelcastInstance.class);
    HazelcastInstanceProvider hazelcastInstanceProvider = mockHazelcastInstanceProvider(Optional.of(hazelcastInstance));
    HazelcastMetricReporter hazelcastMetricReporter = Mockito.mock(HazelcastMetricReporter.class);

    Assertions.assertNotNull(
        new HazelcastConfig().createHazelcastStatisticsReporter(hazelcastInstanceProvider, hazelcastMetricReporter, TestUtils.getEmptyMockConfiguration())
    );
  }
}
