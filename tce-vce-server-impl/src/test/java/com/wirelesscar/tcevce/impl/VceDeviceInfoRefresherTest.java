package com.wirelesscar.tcevce.impl;

import java.util.Optional;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import com.volvo.tisp.framework.context.TispContext;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;
import com.wirelesscar.tce.module.api.Message;
import com.wirelesscar.tce.module.api.MetaData;
import com.wirelesscar.tcevce.wecu.device.info.database.api.DeviceInfoReader;
import com.wirelesscar.tcevce.wecu.device.info.database.api.DeviceInfoReaderFactory;
import com.wirelesscar.tcevce.wecu.device.info.database.model.DeviceInfo;
import com.wirelesscar.tcevce.wecu.device.info.database.model.PersistedDeviceInfo;
import com.wirelesscar.tcevce.wecu.device.info.database.model.SimInfo;

class VceDeviceInfoRefresherTest {
  private static final PersistedDeviceInfo PERSISTED_DEVICE_INFO = TestUtils.createPersistedDeviceInfo();

  private static DeviceInfoReaderFactory mockDeviceInfoReaderFactory(DeviceInfoReader deviceInfoReader, Optional<PersistedDeviceInfo> optional) {
    Mockito.when(deviceInfoReader.findDeviceInfoByVpi(TestUtils.VPI)).thenReturn(optional);
    DeviceInfoReaderFactory deviceInfoReaderFactory = Mockito.mock(DeviceInfoReaderFactory.class);
    Mockito.when(deviceInfoReaderFactory.create()).thenReturn(deviceInfoReader);
    return deviceInfoReaderFactory;
  }

  private static void performRefreshCommData(Optional<PersistedDeviceInfo> persistedDeviceInfoOptional, Optional<DeviceInfo> deviceInfoOptional) {
    Message message = TestUtils.createMessage();

    DeviceInfoReader deviceInfoReader = Mockito.mock(DeviceInfoReader.class);
    DeviceInfoReaderFactory deviceInfoReaderFactory = mockDeviceInfoReaderFactory(deviceInfoReader, persistedDeviceInfoOptional);
    VceDeviceInfoRefresher vceDeviceInfoRefresher = new VceDeviceInfoRefresher(deviceInfoReaderFactory);
    verifyMessage(message);

    TispContext.runInContext(() -> {
      vceDeviceInfoRefresher.refreshCommdata(message);
    });

    verifyMessage(message, deviceInfoOptional);
    verifyDeviceInfoReaderAndDeviceInfoReaderFactory(deviceInfoReader, deviceInfoReaderFactory);
  }

  private static void verifyDeviceInfoReaderAndDeviceInfoReaderFactory(DeviceInfoReader deviceInfoReader, DeviceInfoReaderFactory deviceInfoReaderFactory) {
    Mockito.verify(deviceInfoReader).findDeviceInfoByVpi(TestUtils.VPI);
    Mockito.verify(deviceInfoReader).close();
    Mockito.verify(deviceInfoReaderFactory).create();
    Mockito.verifyNoMoreInteractions(deviceInfoReader, deviceInfoReaderFactory);
  }

  private static void verifyMessage(Message message) {
    Assertions.assertNull(message.getProperty(MetaData.IP_DST_ADDRESS));
    Assertions.assertNull(message.getProperty(MetaData.IP_DST_PORT));
    Assertions.assertNull(message.getProperty(MetaData.SMPP_DEST_ADDRESS));
    Assertions.assertNull(message.getProperty(MetaData.SATELLITE_DEST_ADDRESS));
    Assertions.assertNull(message.getProperty(MetaData.OPERATOR));
  }

  private static void verifyMessage(Message message, Optional<DeviceInfo> optional) {
    if (optional.isEmpty()) {
      verifyMessage(message);
    } else {
      DeviceInfo deviceInfo = optional.get();
      SimInfo simInfo = deviceInfo.getSimInfo().get();
      Assertions.assertEquals(message.getProperty(MetaData.IP_DST_ADDRESS), simInfo.getIpv4Address().toString());
      Assertions.assertEquals(message.getProperty(MetaData.IP_DST_PORT), simInfo.getIpv4Port().toString());
      Assertions.assertEquals(message.getProperty(MetaData.SMPP_DEST_ADDRESS), simInfo.getMsisdn().toString());
      Assertions.assertEquals(message.getProperty(MetaData.SATELLITE_DEST_ADDRESS), deviceInfo.getSatelliteId().get().toString());
      Assertions.assertEquals(message.getProperty(MetaData.OPERATOR), simInfo.getMobileNetworkOperator().toString());
      Assertions.assertEquals(message.getProperty(MetaData.OPERATOR), simInfo.getMobileNetworkOperator().toString());
    }
  }

  @Test
  void refreshCommDataDeviceWithoutCommunicationCapablityTest() {
    performRefreshCommData(Optional.of(TestUtils.createPersistedDeviceInfoWithoutCommunicationCapability()), Optional.empty());
  }

  @Test
  void refreshCommDataInvalidTest() {
    DeviceInfoReaderFactory deviceInfoReaderFactory = Mockito.mock(DeviceInfoReaderFactory.class);

    VceDeviceInfoRefresher vceDeviceInfoRefresher = new VceDeviceInfoRefresher(deviceInfoReaderFactory);
    AssertThrows.illegalArgumentException(() -> vceDeviceInfoRefresher.refreshCommdata(null), "message must not be null");

    Mockito.verifyNoInteractions(deviceInfoReaderFactory);
  }

  @Test
  void refreshCommDataNoDeviceTest() {
    performRefreshCommData(Optional.empty(), Optional.empty());
  }

  @Test
  void refreshCommDataTest() {
    performRefreshCommData(Optional.of(PERSISTED_DEVICE_INFO), Optional.of(PERSISTED_DEVICE_INFO.getDeviceInfo()));
  }

  @Test
  void refreshCommdataWithHandleTest() {
    Message message = TestUtils.createMessageWithHandle();

    verifyMessage(message);

    DeviceInfoReader deviceInfoReader = Mockito.mock(DeviceInfoReader.class);
    DeviceInfoReaderFactory deviceInfoReaderFactory = Mockito.mock(DeviceInfoReaderFactory.class);
    Mockito.when(deviceInfoReaderFactory.create()).thenReturn(deviceInfoReader);
    Mockito.when(deviceInfoReader.findDeviceInfoByHandle(TestUtils.HANDLE)).thenReturn(Optional.of(PERSISTED_DEVICE_INFO));
    VceDeviceInfoRefresher vceDeviceInfoRefresher = new VceDeviceInfoRefresher(deviceInfoReaderFactory);
    TispContext.runInContext(() -> {
      vceDeviceInfoRefresher.refreshCommdata(message);
    });

    verifyMessage(message, Optional.of(PERSISTED_DEVICE_INFO.getDeviceInfo()));

    Mockito.verify(deviceInfoReaderFactory).create();
    Mockito.verify(deviceInfoReader).findDeviceInfoByHandle(TestUtils.HANDLE);
    Mockito.verify(deviceInfoReader).close();
    Mockito.verifyNoMoreInteractions(deviceInfoReader, deviceInfoReaderFactory);
  }

  @Test
  void refreshCommdataWithInvalidVehicleIdAndHandleTest() {
    Message message = TestUtils.createEmptyMessage();

    verifyMessage(message);

    DeviceInfoReader deviceInfoReader = Mockito.mock(DeviceInfoReader.class);
    DeviceInfoReaderFactory deviceInfoReaderFactory = Mockito.mock(DeviceInfoReaderFactory.class);
    Mockito.when(deviceInfoReaderFactory.create()).thenReturn(deviceInfoReader);
    VceDeviceInfoRefresher vceDeviceInfoRefresher = new VceDeviceInfoRefresher(deviceInfoReaderFactory);

    TispContext.runInContext(() -> vceDeviceInfoRefresher.refreshCommdata(message));

    verifyMessage(message);
    Mockito.verify(deviceInfoReader, Mockito.never()).findDeviceInfoByHandle(TestUtils.HANDLE);
    Mockito.verify(deviceInfoReader, Mockito.never()).findDeviceInfoByVpi(TestUtils.VPI);
    Mockito.verify(deviceInfoReaderFactory).create();
    Mockito.verify(deviceInfoReader).close();
    Mockito.verifyNoMoreInteractions(deviceInfoReader);
  }
}
