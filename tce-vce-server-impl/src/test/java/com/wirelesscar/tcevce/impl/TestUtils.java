package com.wirelesscar.tcevce.impl;

import java.time.Instant;
import java.util.Optional;

import com.volvo.tisp.vc.common.dto.lib.vehicle.Imsi;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Ipv4Address;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Msisdn;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.wirelesscar.config.mock.MockConfiguration;
import com.wirelesscar.tce.module.api.Message;
import com.wirelesscar.tcevce.wecu.device.info.database.model.DeviceInfo;
import com.wirelesscar.tcevce.wecu.device.info.database.model.DeviceInfoBuilder;
import com.wirelesscar.tcevce.wecu.device.info.database.model.DeviceInfoId;
import com.wirelesscar.tcevce.wecu.device.info.database.model.Handle;
import com.wirelesscar.tcevce.wecu.device.info.database.model.Ipv4Port;
import com.wirelesscar.tcevce.wecu.device.info.database.model.MobileNetworkOperator;
import com.wirelesscar.tcevce.wecu.device.info.database.model.PersistedDeviceInfo;
import com.wirelesscar.tcevce.wecu.device.info.database.model.PersistedDeviceInfoBuilder;
import com.wirelesscar.tcevce.wecu.device.info.database.model.SatelliteId;
import com.wirelesscar.tcevce.wecu.device.info.database.model.SimInfo;
import com.wirelesscar.tcevce.wecu.device.info.database.model.SimInfoBuilder;

public final class TestUtils {
  public static final Instant CREATED = Instant.ofEpochSecond(1);
  public static final DeviceInfoId DEVICE_INFO_ID = DeviceInfoId.ofLong(1);
  public static final Handle HANDLE = Handle.ofString("123456");
  public static final Ipv4Address IPV4_ADDRESS = Ipv4Address.ofString("*******");
  public static final Ipv4Port IPV4_PORT = Ipv4Port.ofInt(9_062);
  public static final Instant LAST_UPDATED = Instant.ofEpochSecond(2);
  public static final MobileNetworkOperator MOBILE_NETWORK_OPERATOR = MobileNetworkOperator.ofString("testOperator");
  public static final Msisdn MSISDN = Msisdn.ofString("+469123456789");
  public static final SatelliteId SATELLITE_ID = SatelliteId.ofString("HQ1234567890x1");
  public static final Vpi VPI = Vpi.ofString("1234567890ABCDEF1234567890ABCDEF");

  private TestUtils() {
    throw new IllegalStateException();
  }

  public static DeviceInfo createDeviceInfo() {
    return createDeviceInfoBuilder().build();
  }

  public static DeviceInfoBuilder createDeviceInfoBuilder() {
    return new DeviceInfoBuilder()
        .setHandle(HANDLE)
        .setSatelliteId(Optional.of(SATELLITE_ID))
        .setSimInfo(Optional.of(createSimInfo()))
        .setVpi(Optional.of(VPI));
  }

  public static Message createEmptyMessage() {
    return new Message();
  }

  public static Message createMessage() {
    Message message = new Message("1234567", new byte[42]);
    message.setVehicleID(VPI.toString());
    return message;
  }

  public static Message createMessageWithHandle() {
    Message message = new Message("1234567", new byte[42]);
    message.setVehicleID(HANDLE.toString());
    return message;
  }

  public static PersistedDeviceInfo createPersistedDeviceInfo() {
    return createPersistedDeviceInfoBuilder().build();
  }

  public static PersistedDeviceInfoBuilder createPersistedDeviceInfoBuilder() {
    return new PersistedDeviceInfoBuilder()
        .setCreated(CREATED)
        .setDeviceInfo(createDeviceInfo())
        .setDeviceInfoId(DEVICE_INFO_ID)
        .setLastUpdated(LAST_UPDATED);
  }

  public static PersistedDeviceInfo createPersistedDeviceInfoWithoutCommunicationCapability() {
    return createPersistedDeviceInfoBuilder()
        .setDeviceInfo(createDeviceInfoWithoutCommunicationCapability())
        .build();
  }

  public static SimInfo createSimInfo() {
    return createSimInfoBuilder().build();
  }

  public static SimInfoBuilder createSimInfoBuilder() {
    return new SimInfoBuilder()
        .setImsi(Imsi.ofLong(1L))
        .setIpv4Address(IPV4_ADDRESS)
        .setIpv4Port(IPV4_PORT)
        .setMobileNetworkOperator(MOBILE_NETWORK_OPERATOR)
        .setMsisdn(MSISDN);
  }

  public static MockConfiguration getEmptyMockConfiguration() {
    MockConfiguration mockConfiguration = MockConfiguration.getConfig();
    mockConfiguration.deleteAllProperties();
    return mockConfiguration;
  }

  private static DeviceInfo createDeviceInfoWithoutCommunicationCapability() {
    return createDeviceInfoBuilder()
        .setSatelliteId(Optional.empty())
        .setSimInfo(Optional.empty())
        .build();
  }
}
