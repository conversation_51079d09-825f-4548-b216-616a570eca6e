package com.wirelesscar.tcevce.impl;

import java.time.Instant;
import java.util.List;
import java.util.Optional;

import org.junit.jupiter.api.Test;
import org.mockito.ArgumentMatchers;
import org.mockito.Mockito;

import com.volvo.tisp.vc.common.dto.lib.vehicle.Imsi;
import com.wirelesscar.tcevce.module.segmentation.encryption.VceEncryptionHandler;
import com.wirelesscar.tcevce.wecu.device.info.database.api.DeviceInfoReader;
import com.wirelesscar.tcevce.wecu.device.info.database.api.DeviceInfoReaderFactory;
import com.wirelesscar.tcevce.wecu.device.info.database.model.DeviceInfo;
import com.wirelesscar.tcevce.wecu.device.info.database.model.DeviceInfoBuilder;
import com.wirelesscar.tcevce.wecu.device.info.database.model.Handle;
import com.wirelesscar.tcevce.wecu.device.info.database.model.PersistedDeviceInfo;
import com.wirelesscar.tcevce.wecu.device.info.database.model.PersistedDeviceInfoBuilder;
import com.wirelesscar.tcevce.wecu.device.info.database.model.SimInfo;
import com.wirelesscar.tcevce.wecu.device.info.database.model.SimInfoBuilder;

import static com.wirelesscar.tcevce.impl.TestUtils.DEVICE_INFO_ID;
import static com.wirelesscar.tcevce.impl.TestUtils.IPV4_ADDRESS;
import static com.wirelesscar.tcevce.impl.TestUtils.IPV4_PORT;
import static com.wirelesscar.tcevce.impl.TestUtils.MOBILE_NETWORK_OPERATOR;
import static com.wirelesscar.tcevce.impl.TestUtils.MSISDN;

class VehicleKeyCachePreheatServiceTest {
  private static final Handle HANDLE_1 = Handle.ofString("123456");
  private static final Handle HANDLE_2 = Handle.ofString("123451");
  private static final Imsi IMSI_1 = Imsi.ofLong(888880002);
  private static final Imsi IMSI_2 = Imsi.ofLong(888880012);

  @Test
  void postConstructTest() throws InterruptedException {
    VceEncryptionHandler vceEncryptionHandler = Mockito.mock(VceEncryptionHandler.class);
    DeviceInfoReaderFactory deviceInfoReaderFactory = Mockito.mock(DeviceInfoReaderFactory.class);
    DeviceInfoReader deviceInfoReader = Mockito.mock(DeviceInfoReader.class);
    VehicleKeyCachePreheatService vehicleKeyCachePreheatService = new VehicleKeyCachePreheatService(vceEncryptionHandler, deviceInfoReaderFactory);

    Mockito.when(deviceInfoReaderFactory.create()).thenReturn(deviceInfoReader);
    List<PersistedDeviceInfo> persistedDeviceInfos = List.of(
        createPersistedDeviceInfo(HANDLE_1, IMSI_1), createPersistedDeviceInfo(HANDLE_2, IMSI_2));
    Mockito.when(deviceInfoReader.getDeviceInfoWithWecuKeys(ArgumentMatchers.anyInt(), ArgumentMatchers.anyInt())).thenReturn(persistedDeviceInfos);

    vehicleKeyCachePreheatService.postConstruct();

    Thread.sleep(100);

    Mockito.verify(vceEncryptionHandler).createAndStoreSymmetricKey(HANDLE_1.toString(), IMSI_1);
    Mockito.verify(vceEncryptionHandler).createAndStoreSymmetricKey(HANDLE_2.toString(), IMSI_2);
    Mockito.verifyNoMoreInteractions(vceEncryptionHandler);
  }

  private PersistedDeviceInfo createPersistedDeviceInfo(Handle handle, Imsi imsi) {
    SimInfo simInfo = new SimInfoBuilder()
        .setImsi(imsi)
        .setIpv4Address(IPV4_ADDRESS)
        .setIpv4Port(IPV4_PORT)
        .setMobileNetworkOperator(MOBILE_NETWORK_OPERATOR)
        .setMsisdn(MSISDN)
        .build();

    DeviceInfo deviceInfo = new DeviceInfoBuilder()
        .setHandle(handle)
        .setSimInfo(Optional.of(simInfo))
        .build();

    return new PersistedDeviceInfoBuilder()
        .setCreated(Instant.ofEpochSecond(1_000_000))
        .setDeviceInfoId(DEVICE_INFO_ID)
        .setLastUpdated(Instant.ofEpochSecond(1_000_000))
        .setDeviceInfo(deviceInfo)
        .build();
  }
}
