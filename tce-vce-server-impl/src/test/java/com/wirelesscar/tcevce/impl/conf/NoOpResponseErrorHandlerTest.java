package com.wirelesscar.tcevce.impl.conf;

import java.io.IOException;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.http.client.ClientHttpResponse;

class NoOpResponseErrorHandlerTest {
  @Test
  void handleErrorTest() throws IOException {
    ClientHttpResponse clientHttpResponse = Mockito.mock(ClientHttpResponse.class);

    NoOpResponseErrorHandler.INSTANCE.handleError(clientHttpResponse);
    Mockito.verifyNoInteractions(clientHttpResponse);
  }

  @Test
  void instanceNotNullTest() {
    Assertions.assertNotNull(NoOpResponseErrorHandler.INSTANCE);
  }
}