package com.wirelesscar.tcevce.impl.conf;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.web.client.RestTemplate;

import com.volvo.tisp.vc.test.utils.lib.AssertThrows;
import com.wirelesscar.config.mock.MockConfiguration;
import com.wirelesscar.tcevce.impl.TestUtils;
import com.wirelesscar.tcevce.module.identify.metrics.ConrepoClientMetricReporter;

class ConrepoConfigTest {

  private static MockConfiguration prepareMockConfiguration() {
    MockConfiguration mockConfiguration = TestUtils.getEmptyMockConfiguration();
    mockConfiguration.setPropertySpecific(mockConfiguration.getComponentShortName(), "servicediscovery.conrepo", "testBaseUrl");
    return mockConfiguration;
  }

  @Test
  void createConrepoLookupClientInvalidTest() {
    MockConfiguration mockConfiguration = prepareMockConfiguration();

    ConrepoClientMetricReporter conrepoClientMetricReporter = Mockito.mock(ConrepoClientMetricReporter.class);
    RestTemplate restTemplate = Mockito.mock(RestTemplate.class);

    ConrepoConfig conrepoConfig = new ConrepoConfig();

    Assertions.assertAll(
        () -> AssertThrows.illegalArgumentException(() -> conrepoConfig.createConrepoLookupClient(null, conrepoClientMetricReporter, mockConfiguration),
            "restTemplate must not be null"),
        () -> AssertThrows.illegalArgumentException(() -> conrepoConfig.createConrepoLookupClient(restTemplate, null, mockConfiguration),
            "conrepoClientMetricReporter must not be null"),
        () -> AssertThrows.illegalArgumentException(() -> conrepoConfig.createConrepoLookupClient(restTemplate, conrepoClientMetricReporter, null),
            "config must not be null"));
  }

  @Test
  void createConrepoLookupClientTest() {
    Assertions.assertNotNull(new ConrepoConfig().createConrepoLookupClient(Mockito.mock(RestTemplate.class), Mockito.mock(ConrepoClientMetricReporter.class),
        prepareMockConfiguration()));
  }

}
