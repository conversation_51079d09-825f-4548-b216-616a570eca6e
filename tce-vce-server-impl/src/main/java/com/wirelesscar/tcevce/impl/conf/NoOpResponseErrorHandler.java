package com.wirelesscar.tcevce.impl.conf;

import org.springframework.http.client.ClientHttpResponse;
import org.springframework.web.client.DefaultResponseErrorHandler;
import org.springframework.web.client.ResponseErrorHandler;

class NoOpResponseErrorHandler extends DefaultResponseErrorHandler {
  static final ResponseErrorHandler INSTANCE = new NoOpResponseErrorHandler();

  private NoOpResponseErrorHandler() {
    // do nothing
  }

  @Override
  public void handleError(ClientHttpResponse clientHttpResponse) {
    // do nothing
  }
}
