package com.wirelesscar.tcevce.impl.conf;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Import;
import org.springframework.web.client.RestTemplate;

import com.github.benmanes.caffeine.cache.Scheduler;
import com.volvo.tisp.vc.influxdb.event.reporter.InfluxEventSender;
import com.volvo.tisp.vc.influxdb.event.reporter.InfluxParameters;
import com.volvo.tisp.vc.influxdb.event.reporter.RuntimeParameters;
import com.volvo.vc.crypto.symmetric.encryption.gcm.SymmetricAesGcmEncryptionService;
import com.volvo.vc.crypto.symmetric.encryption.gcm.SymmetricAesGcmEncryptionServiceImpl;
import com.wirelesscar.config.Config;
import com.wirelesscar.config.ConfigFactory;
import com.wirelesscar.tcevce.impl.influx.InfluxEventReporter;

@SpringBootApplication
@Import(com.wirelesscar.tce.core.conf.AppConfig.class)
@ComponentScan(basePackages = {"com.wirelesscar.tcevce", "com.volvo.tisp.vc.uncaught.exception.handler"})
public class AppConfig {
  private static final Logger logger = LoggerFactory.getLogger(AppConfig.class);

  public static void main(String[] args) {
    SpringApplication.run(AppConfig.class);
  }

  @Bean
  Config createConfig() {
    return ConfigFactory.getConfig();
  }

  @Bean
  @ConditionalOnProperty(prefix = "influx.event", name = "enabled", matchIfMissing = true)
  InfluxEventReporter createInfluxEventReporter(Config config) {
    RuntimeParameters runtimeParameters = ConfigLoader.getRuntimeParameters(config);
    InfluxParameters influxParameters = ConfigLoader.getInfluxParameters(config);
    logger.info("runtimeParameters: {}", runtimeParameters);
    logger.info("influxParameters: {}", influxParameters);
    InfluxEventSender influxEventSender = new InfluxEventSender(runtimeParameters, influxParameters);

    return new InfluxEventReporter(influxEventSender);
  }

  @Bean
  RestTemplate createRestTemplate() {
    RestTemplate restTemplate = new RestTemplate();
    restTemplate.setErrorHandler(NoOpResponseErrorHandler.INSTANCE);
    return restTemplate;
  }

  @Bean
  Scheduler createScheduler() {
    return Scheduler.systemScheduler();
  }

  @Bean
  SymmetricAesGcmEncryptionService createSymmetricAesGcmEncryptionService() {
    return SymmetricAesGcmEncryptionServiceImpl.INSTANCE;
  }
}
