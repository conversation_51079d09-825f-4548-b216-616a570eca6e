package com.wirelesscar.tcevce.impl;

import java.util.Optional;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.volvo.tisp.vc.main.utils.lib.type.Either;
import com.wirelesscar.tce.device.AbstractDeviceService;
import com.wirelesscar.tce.module.api.Message;
import com.wirelesscar.tce.module.api.MetaData;
import com.wirelesscar.tcevce.wecu.device.info.database.api.DeviceInfoReader;
import com.wirelesscar.tcevce.wecu.device.info.database.api.DeviceInfoReaderFactory;
import com.wirelesscar.tcevce.wecu.device.info.database.model.DeviceInfo;
import com.wirelesscar.tcevce.wecu.device.info.database.model.Handle;
import com.wirelesscar.tcevce.wecu.device.info.database.model.PersistedDeviceInfo;
import com.wirelesscar.tcevce.wecu.device.info.database.model.SatelliteId;
import com.wirelesscar.tcevce.wecu.device.info.database.model.SimInfo;

@Component
public class VceDeviceInfoRefresher extends AbstractDeviceService {
  private static final Logger logger = LoggerFactory.getLogger(VceDeviceInfoRefresher.class);

  private final DeviceInfoReaderFactory deviceInfoReaderFactory;

  public VceDeviceInfoRefresher(final DeviceInfoReaderFactory deviceInfoReaderFactory) {
    this.deviceInfoReaderFactory = deviceInfoReaderFactory;
  }

  private static Either<Vpi, Handle> createVpiOrHandle(String vehicleId) {
    try {
      return Either.left(Vpi.ofString(vehicleId));
    } catch (IllegalArgumentException e) {
      logger.debug("", e);
      return Either.right(Handle.ofString(vehicleId));
    }
  }

  private static Optional<PersistedDeviceInfo> findDeviceInfoByVehicleIdOrHandle(String vehicleId, DeviceInfoReader deviceInfoReader) {
    try {
      Either<Vpi, Handle> vpiHandleEither = createVpiOrHandle(vehicleId);
      if (vpiHandleEither.isLeft()) {
        logger.debug("Find device info by VPI: {}", vpiHandleEither);
        return deviceInfoReader.findDeviceInfoByVpi(vpiHandleEither.getLeft());
      }
      logger.debug("Find device info by handle: {}", vpiHandleEither);
      return deviceInfoReader.findDeviceInfoByHandle(vpiHandleEither.getRight());
    } catch (IllegalArgumentException e) {
      logger.debug("", e);
      return Optional.empty();
    }
  }

  @Override
  public void refreshCommdata(final Message message) {
    Validate.notNull(message, "message");

    logger.trace("commdata on vehicle before {}", message);

    try (DeviceInfoReader deviceInfoReader = deviceInfoReaderFactory.create()) {
      String vehicleID = message.getVehicleID();
      Optional<PersistedDeviceInfo> optional = findDeviceInfoByVehicleIdOrHandle(vehicleID, deviceInfoReader);

      if (optional.isEmpty()) {
        logger.error("ERROR: No device found with VPI or Handle: {}", vehicleID);
      } else {
        PersistedDeviceInfo persistedDeviceInfo = optional.get();
        DeviceInfo device = persistedDeviceInfo.getDeviceInfo();
        Optional<SimInfo> simInfoOptional = device.getSimInfo();
        Optional<SatelliteId> satelliteIdOptional = device.getSatelliteId();
        if (simInfoOptional.isEmpty() && satelliteIdOptional.isEmpty()) {
          logger.warn("ERROR: No Sim/Sat found for Device: {}", device);
          return;
        }
        simInfoOptional.ifPresent(simInfo -> {
          setProperty(message, MetaData.IP_DST_ADDRESS, simInfo.getIpv4Address().toString());
          setProperty(message, MetaData.IP_DST_PORT, simInfo.getIpv4Port().toString());
          setProperty(message, MetaData.SMPP_DEST_ADDRESS, simInfo.getMsisdn().toString());
          setProperty(message, MetaData.OPERATOR, simInfo.getMobileNetworkOperator().toString());
        });

        satelliteIdOptional.ifPresent(satelliteId -> setProperty(message, MetaData.SATELLITE_DEST_ADDRESS, satelliteId.toString()));
      }
    }

    logger.trace("commdata on vehicle after {}", message);
  }
}
