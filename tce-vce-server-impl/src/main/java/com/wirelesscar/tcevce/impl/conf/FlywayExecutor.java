package com.wirelesscar.tcevce.impl.conf;

import javax.sql.DataSource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.context.event.ApplicationStartedEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import com.wirelesscar.tcevce.wecu.device.info.database.database.DeviceServiceFlywayExecutor;
import com.wirelesscar.tcevce.wecu.device.info.database.database.WecuKeyFlywayExecutor;

@Component
class FlywayExecutor {
  private static final Logger logger = LoggerFactory.getLogger(FlywayExecutor.class);

  private final DataSource dataSource;

  FlywayExecutor(DataSource dataSource) {
    this.dataSource = dataSource;
  }

  @EventListener(ApplicationStartedEvent.class)
  void onApplicationStartedEvent() {
    logger.info("Running flyway database migration");

    DeviceServiceFlywayExecutor.performDatabaseMigration(dataSource);
    WecuKeyFlywayExecutor.performDatabaseMigration(dataSource);
  }
}
