package com.wirelesscar.tcevce.impl.conf;

import java.time.Clock;

import javax.sql.DataSource;

import org.jdbi.v3.core.Jdbi;
import org.jdbi.v3.core.mapper.RowMapper;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.hazelcast.core.HazelcastInstance;
import com.hazelcast.topic.MessageListener;
import com.wirelesscar.config.Config;
import com.wirelesscar.tce.db.source.TceDataSource;
import com.wirelesscar.tcevce.wecu.device.info.cache.core.api.CacheDeviceInfoReader;
import com.wirelesscar.tcevce.wecu.device.info.cache.core.api.CacheInvalidator;
import com.wirelesscar.tcevce.wecu.device.info.cache.core.impl.CacheDeviceInfoMetricReporter;
import com.wirelesscar.tcevce.wecu.device.info.cache.core.impl.CacheDeviceInfoReaderFactory;
import com.wirelesscar.tcevce.wecu.device.info.cache.core.impl.DeviceServiceCacheManager;
import com.wirelesscar.tcevce.wecu.device.info.cache.notify.api.DeviceNotificationPublisher;
import com.wirelesscar.tcevce.wecu.device.info.cache.notify.impl.DeviceNotification;
import com.wirelesscar.tcevce.wecu.device.info.cache.notify.impl.DeviceNotificationMessageListener;
import com.wirelesscar.tcevce.wecu.device.info.cache.notify.impl.DeviceNotificationPublisherImpl;
import com.wirelesscar.tcevce.wecu.device.info.cache.notify.impl.DistributedCacheInvalidator;
import com.wirelesscar.tcevce.wecu.device.info.cache.notify.metrics.NotifyMetricReporter;
import com.wirelesscar.tcevce.wecu.device.info.database.api.DeviceInfoReaderFactory;
import com.wirelesscar.tcevce.wecu.device.info.database.api.DeviceInfoWriterFactory;
import com.wirelesscar.tcevce.wecu.device.info.database.database.DeviceInfoReaderFactoryImpl;
import com.wirelesscar.tcevce.wecu.device.info.database.database.DeviceInfoRowMapper;
import com.wirelesscar.tcevce.wecu.device.info.database.database.DeviceInfoWriterFactoryImpl;
import com.wirelesscar.tcevce.wecu.device.info.database.model.PersistedDeviceInfo;

import io.micrometer.core.instrument.MeterRegistry;

@Configuration
class DeviceServiceConfig {
  @Bean
  CacheDeviceInfoMetricReporter createCacheDeviceInfoMetricReporter(MeterRegistry meterRegistry) {
    return new CacheDeviceInfoMetricReporter(meterRegistry);
  }

  @Bean
  CacheDeviceInfoReader createCacheDeviceInfoReader(@Qualifier(value = "deviceInfoReaderFactory") DeviceInfoReaderFactory deviceInfoReaderFactory,
      CacheDeviceInfoMetricReporter cacheDeviceInfoMetricReporter, Config config) {
    return CacheDeviceInfoReaderFactory.create(deviceInfoReaderFactory, cacheDeviceInfoMetricReporter, ConfigLoader.getMaximumCacheSize(config));
  }

  @Bean
  CacheInvalidator createCacheInvalidator(DeviceNotificationPublisher deviceNotificationPublisher) {
    return DistributedCacheInvalidator.create(deviceNotificationPublisher);
  }

  @Bean
  Clock createClock() {
    return Clock.systemUTC();
  }

  @Bean
  DataSource createDataSource() {
    return TceDataSource.getDataSource();
  }

  @Bean("deviceInfoReaderFactory")
  DeviceInfoReaderFactory createDeviceInfoReaderFactory(Clock clock, Jdbi jdbi, RowMapper<PersistedDeviceInfo> rowMapper) {
    return DeviceInfoReaderFactoryImpl.create(clock, jdbi, rowMapper);
  }

  @Bean
  DeviceInfoWriterFactory createDeviceInfoWriterFactory(Clock clock, Jdbi jdbi, RowMapper<PersistedDeviceInfo> rowMapper) {
    return DeviceInfoWriterFactoryImpl.create(clock, jdbi, rowMapper);
  }

  @Bean
  MessageListener<DeviceNotification> createDeviceNotificationMessageListener(DeviceServiceCacheManager deviceServiceCacheManager,
      NotifyMetricReporter notifyMetricReporter) {
    return DeviceNotificationMessageListener.create(deviceServiceCacheManager, notifyMetricReporter);
  }

  @Bean
  DeviceNotificationPublisher createDeviceNotificationPublisher(MessageListener<DeviceNotification> deviceNotificationMessageListener,
      HazelcastInstance hazelcastInstance, NotifyMetricReporter notifyMetricReporter) {
    return DeviceNotificationPublisherImpl.create(deviceNotificationMessageListener, hazelcastInstance, notifyMetricReporter);
  }

  @Bean
  DeviceServiceCacheManager createDeviceServiceCacheManager(CacheDeviceInfoReader cacheDeviceInfoReader) {
    return new DeviceServiceCacheManager(cacheDeviceInfoReader);
  }

  @Bean
  Jdbi createJdbi(DataSource dataSource) {
    return Jdbi.create(dataSource);
  }

  @Bean
  NotifyMetricReporter createNotifyMetricReporter(MeterRegistry meterRegistry) {
    return new NotifyMetricReporter(meterRegistry);
  }

  @Bean
  RowMapper<PersistedDeviceInfo> createPersistedDeviceInfoRowMapper() {
    return DeviceInfoRowMapper.create();
  }
}
