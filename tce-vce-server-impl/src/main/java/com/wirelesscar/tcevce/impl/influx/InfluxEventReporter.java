package com.wirelesscar.tcevce.impl.influx;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.ContextClosedEvent;
import org.springframework.context.event.EventListener;

import com.volvo.tisp.framework.servicediscovery.ServiceActivatedEvent;
import com.volvo.tisp.framework.servicediscovery.ServiceDeactivatedEvent;
import com.volvo.tisp.vc.influxdb.event.reporter.InfluxEventSender;
import com.volvo.tisp.vc.main.utils.lib.Validate;

public class InfluxEventReporter {
  private static final Logger logger = LoggerFactory.getLogger(InfluxEventReporter.class);

  private final InfluxEventSender influxEventSender;

  public InfluxEventReporter(InfluxEventSender influxEventSender) {
    Validate.notNull(influxEventSender, "influxEventSender");

    this.influxEventSender = influxEventSender;
  }

  @EventListener(ApplicationReadyEvent.class)
  public void onApplicationReadyEvent() {
    logger.info("Received ApplicationReadyEvent");
    try {
      influxEventSender.sendStarted();
    } catch (RuntimeException e) {
      logger.error("Could not report the start event", e);
    }
  }

  @EventListener(ContextClosedEvent.class)
  public void onContextClosed() {
    logger.info("Received ContextClosedEvent");
    try {
      influxEventSender.sendStopped();
    } catch (RuntimeException e) {
      logger.error("Could not report the stop event", e);
    }
  }

  @EventListener(ServiceActivatedEvent.class)
  public void onServiceActivatedEvent() {
    logger.info("Received ServiceActivatedEvent");
    try {
      influxEventSender.sendActivated();
    } catch (RuntimeException e) {
      logger.error("Could not report the activated event", e);
    }
  }

  @EventListener(ServiceDeactivatedEvent.class)
  public void onServiceDeactivatedEvent() {
    logger.info("Received ServiceDeactivatedEvent");
    try {
      influxEventSender.sendDeactivated();
    } catch (RuntimeException e) {
      logger.error("Could not report the deactivated event", e);
    }
  }
}
