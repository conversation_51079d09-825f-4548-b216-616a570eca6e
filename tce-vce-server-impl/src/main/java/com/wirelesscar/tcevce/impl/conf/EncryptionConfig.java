package com.wirelesscar.tcevce.impl.conf;

import java.time.Clock;

import org.jdbi.v3.core.Jdbi;
import org.jdbi.v3.core.mapper.RowMapper;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.wirelesscar.tcevce.wecu.device.info.database.api.WecuKeyWriterFactory;
import com.wirelesscar.tcevce.wecu.device.info.database.database.WecuKeyRowMapper;
import com.wirelesscar.tcevce.wecu.device.info.database.database.WecuKeyWriterFactoryImpl;
import com.wirelesscar.tcevce.wecu.device.info.database.model.PersistedWecuKey;

@Configuration
public class EncryptionConfig {

  @Bean("wecueyWriterFactory")
  public WecuKeyWriterFactory createWecuKeyWriterFactory(Clock clock, Jdbi jdbi, RowMapper<PersistedWecuKey> rowMapper) {
    return WecuKeyWriterFactoryImpl.create(clock, jdbi, rowMapper);
  }

  @Bean
  RowMapper<PersistedWecuKey> createWecuKeyInfoRowMapper() {
    return WecuKeyRowMapper.create();
  }
}
