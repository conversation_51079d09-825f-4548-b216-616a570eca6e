package com.wirelesscar.tcevce.impl.conf;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.client.RestTemplate;

import com.wirelesscar.config.Config;
import com.wirelesscar.tcevce.module.identify.ConrepoLookupClient;
import com.wirelesscar.tcevce.module.identify.metrics.ConrepoClientMetricReporter;

@Configuration
class ConrepoConfig {
  @Bean
  ConrepoLookupClient createConrepoLookupClient(RestTemplate restTemplate, ConrepoClientMetricReporter conrepoClientMetricReporter, Config config) {
    return new ConrepoLookupClient(restTemplate, conrepoClientMetricReporter, ConfigLoader.getConrepoBaseUrl(config));
  }
}
