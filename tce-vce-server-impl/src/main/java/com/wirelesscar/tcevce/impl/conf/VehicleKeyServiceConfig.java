package com.wirelesscar.tcevce.impl.conf;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.Scheduler;
import com.hazelcast.core.HazelcastInstance;
import com.hazelcast.topic.MessageListener;
import com.volvo.vc.crypto.symmetric.key.SymmetricKey;
import com.wirelesscar.tcevce.wecu.device.info.cache.core.api.VehicleKeyCache;
import com.wirelesscar.tcevce.wecu.device.info.cache.core.api.VehicleKeyCacheInvalidator;
import com.wirelesscar.tcevce.wecu.device.info.cache.core.impl.VehicleKeyCacheImpl;
import com.wirelesscar.tcevce.wecu.device.info.cache.core.impl.VehicleKeyServiceCacheManager;
import com.wirelesscar.tcevce.wecu.device.info.cache.notify.api.VehicleKeyNotificationPublisher;
import com.wirelesscar.tcevce.wecu.device.info.cache.notify.impl.DistributedVehicleKeyCacheInvalidator;
import com.wirelesscar.tcevce.wecu.device.info.cache.notify.impl.VehicleKeyNotification;
import com.wirelesscar.tcevce.wecu.device.info.cache.notify.impl.VehicleKeyNotificationMessageListener;
import com.wirelesscar.tcevce.wecu.device.info.cache.notify.impl.VehicleKeyNotificationPublisherImpl;
import com.wirelesscar.tcevce.wecu.device.info.cache.notify.metrics.NotifyMetricReporter;

@Configuration
public class VehicleKeyServiceConfig {

  @Bean
  Cache<String, SymmetricKey> createCaffeineCache(Scheduler scheduler) {
    return Caffeine.newBuilder()
        .scheduler(scheduler)
        .build();
  }

  @Bean
  VehicleKeyCache createVehicleKeyCache(Cache<String, SymmetricKey> cache) {
    return VehicleKeyCacheImpl.create(cache);
  }

  @Bean
  VehicleKeyCacheInvalidator createVehicleKeyCacheInvalidator(VehicleKeyNotificationPublisher vehicleKeyNotificationPublisher) {
    return DistributedVehicleKeyCacheInvalidator.create(vehicleKeyNotificationPublisher);
  }

  @Bean
  MessageListener<VehicleKeyNotification> createVehicleKeyNotificationMessageListener(VehicleKeyServiceCacheManager vehicleKeyServiceCacheManager,
      NotifyMetricReporter notifyMetricReporter) {
    return VehicleKeyNotificationMessageListener.create(vehicleKeyServiceCacheManager, notifyMetricReporter);
  }

  @Bean
  VehicleKeyNotificationPublisher createVehicleKeyNotificationPublisher(MessageListener<VehicleKeyNotification> vehicleKeyNotificationMessageListener,
      HazelcastInstance hazelcastInstance, NotifyMetricReporter notifyMetricReporter) {
    return VehicleKeyNotificationPublisherImpl.create(vehicleKeyNotificationMessageListener, hazelcastInstance, notifyMetricReporter);
  }

  @Bean
  VehicleKeyServiceCacheManager createVehicleKeyServiceCacheManager(VehicleKeyCache vehicleKeyCache) {
    return new VehicleKeyServiceCacheManager(vehicleKeyCache);
  }
}
