package com.wirelesscar.tcevce.impl.conf;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.wirelesscar.tcevce.module.scheduler.db.impl.ScheduledMessagePersisterImpl;
import com.wirelesscar.tcevce.module.split.ack.service.ScheduledMessageDatabaseService;

@Configuration
class MessagePersisterConfig {
  @Bean
  ScheduledMessageDatabaseService createScheduledMessageDatabaseService() {
    return new ScheduledMessageDatabaseService(new ScheduledMessagePersisterImpl());
  }
}
