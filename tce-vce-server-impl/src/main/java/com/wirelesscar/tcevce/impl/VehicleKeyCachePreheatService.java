package com.wirelesscar.tcevce.impl;

import java.util.List;
import java.util.Optional;

import jakarta.annotation.PostConstruct;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.wirelesscar.tcevce.module.segmentation.encryption.VceEncryptionHandler;
import com.wirelesscar.tcevce.wecu.device.info.database.api.DeviceInfoReader;
import com.wirelesscar.tcevce.wecu.device.info.database.api.DeviceInfoReaderFactory;
import com.wirelesscar.tcevce.wecu.device.info.database.model.Handle;
import com.wirelesscar.tcevce.wecu.device.info.database.model.PersistedDeviceInfo;

@Component
public class VehicleKeyCachePreheatService {
  private static final int BATCH_SIZE = 50;
  private static final Logger logger = LoggerFactory.getLogger(VehicleKeyCachePreheatService.class);
  private final DeviceInfoReaderFactory deviceInfoReaderFactory;
  private final VceEncryptionHandler vceEncryptionHandler;

  public VehicleKeyCachePreheatService(VceEncryptionHandler vceEncryptionHandler, DeviceInfoReaderFactory deviceInfoReaderFactory) {
    this.vceEncryptionHandler = vceEncryptionHandler;
    this.deviceInfoReaderFactory = deviceInfoReaderFactory;
  }

  @PostConstruct
  public void postConstruct() {
    new Thread(this::preheat).start();
  }

  private void preheat() {
    logger.info("Start wecu keys cache preheating.");

    try (DeviceInfoReader deviceInfoReader = deviceInfoReaderFactory.create()) {
      int page = 0;
      boolean hasMoreWecuKeys = true;

      while (hasMoreWecuKeys) {
        List<PersistedDeviceInfo> persistedWecuKeys = deviceInfoReader.getDeviceInfoWithWecuKeys(page, BATCH_SIZE);
        hasMoreWecuKeys = persistedWecuKeys.size() == BATCH_SIZE;
        page++;

        processWecuKeys(persistedWecuKeys);
      }
    } catch (Exception e) {
      logger.error("Error during wecu keys cache preheating", e);
    }

    logger.info("Finished wecu keys cache preheating.");
  }

  private void processWecuKeys(List<PersistedDeviceInfo> persistedDeviceInfos) {
    persistedDeviceInfos.stream()
        .map(PersistedDeviceInfo::getDeviceInfo)
        .filter(deviceInfo -> deviceInfo.getSimInfo().isPresent())
        .filter(deviceInfo -> deviceInfo.getSimInfo().get().getImsi().isPresent())
        .forEach(deviceInfo -> {
          Optional<Vpi> vpi = deviceInfo.getVpi();
          Handle handle = deviceInfo.getHandle();
          String vehicleId = vpi.map(Vpi::toString).orElse(handle.toString());

          vceEncryptionHandler.createAndStoreSymmetricKey(vehicleId,
              deviceInfo.getSimInfo().get().getImsi().get());
        });
  }
}
