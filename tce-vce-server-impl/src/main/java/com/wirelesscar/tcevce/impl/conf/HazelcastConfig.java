package com.wirelesscar.tcevce.impl.conf;

import java.util.Optional;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.hazelcast.core.HazelcastInstance;
import com.wirelesscar.config.Config;
import com.wirelesscar.tce.hazelcast.HazelcastConfigLoader;
import com.wirelesscar.tce.hazelcast.HazelcastInstanceProvider;
import com.wirelesscar.tce.hazelcast.HazelcastStatisticsReporter;
import com.wirelesscar.tce.hazelcast.reporter.HazelcastMetricReporter;

import io.micrometer.core.instrument.MeterRegistry;

@Configuration
class HazelcastConfig {
  @Bean(destroyMethod = "shutdown")
  HazelcastInstance createHazelcastInstance(HazelcastInstanceProvider hazelcastInstanceProvider) {
    return hazelcastInstanceProvider
        .getHazelcastInstance()
        .orElseThrow(() -> new IllegalStateException("Was not able to create a Hazelcast instance for tce-vce-server"));
  }

  @Bean(destroyMethod = "close")
  HazelcastInstanceProvider createHazelcastInstanceProvider(Config config) {
    Optional<com.hazelcast.config.Config> hazelcastConfig = HazelcastConfigLoader.createHazelcastConfig(config);
    return HazelcastInstanceProvider.create(hazelcastConfig);
  }

  @Bean
  HazelcastMetricReporter createHazelcastMetricReporter(MeterRegistry meterRegistry) {
    return new HazelcastMetricReporter(meterRegistry);
  }

  @Bean
  HazelcastStatisticsReporter createHazelcastStatisticsReporter(HazelcastInstanceProvider hazelcastInstanceProvider,
      HazelcastMetricReporter hazelcastMetricReporter, Config config) {
    return new HazelcastStatisticsReporter(hazelcastInstanceProvider, hazelcastMetricReporter, HazelcastConfigLoader.getReportDuration(config));
  }
}
