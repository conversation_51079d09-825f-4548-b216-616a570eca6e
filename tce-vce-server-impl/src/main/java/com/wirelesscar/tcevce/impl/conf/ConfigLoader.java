package com.wirelesscar.tcevce.impl.conf;

import java.time.Duration;

import com.volvo.tisp.vc.influxdb.event.reporter.InfluxParameters;
import com.volvo.tisp.vc.influxdb.event.reporter.InfluxParametersBuilder;
import com.volvo.tisp.vc.influxdb.event.reporter.RetentionPolicy;
import com.volvo.tisp.vc.influxdb.event.reporter.RuntimeParameters;
import com.volvo.tisp.vc.influxdb.event.reporter.RuntimeParametersBuilder;
import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.wirelesscar.config.Config;

class ConfigLoader {
  public static final String DEVICE_SERVICE_MAXIMUM_CACHE_SIZE_IN_BYTES = "device-service.maximum-cache-size-in-bytes";
  private static final String CONFIG_ARG_VARIABLE_NAME = "config";

  private ConfigLoader() {
    throw new IllegalStateException();
  }

  static String getConrepoBaseUrl(Config config) {
    Validate.notNull(config, CONFIG_ARG_VARIABLE_NAME);

    return getStringConfig(config, "servicediscovery.conrepo");
  }

  static InfluxParameters getInfluxParameters(Config config) {
    Validate.notNull(config, CONFIG_ARG_VARIABLE_NAME);

    return new InfluxParametersBuilder()
        .setConnectTimeout(getDurationConfig(config, "influx.connect.timeout", InfluxParametersBuilder.DEFAULT_CONNECT_TIMEOUT))
        .setDbName(getStringConfig(config, "management.influx.metrics.export.db"))
        .setReadTimeout(getDurationConfig(config, "influx.read.timeout", InfluxParametersBuilder.DEFAULT_READ_TIMEOUT))
        .setRetentionPolicy(getRetentionPolicy(config))
        .setServerUrl(getPlatformStringConfig(config, "management.influx.metrics.export.uri"))
        .build();
  }

  static long getMaximumCacheSize(Config config) {
    Validate.notNull(config, CONFIG_ARG_VARIABLE_NAME);

    return getLongConfig(config, DEVICE_SERVICE_MAXIMUM_CACHE_SIZE_IN_BYTES);
  }

  static RuntimeParameters getRuntimeParameters(Config config) {
    Validate.notNull(config, CONFIG_ARG_VARIABLE_NAME);

    return new RuntimeParametersBuilder()
        .setComponentShortName(config.getComponentShortName())
        .setComponentVersion(config.getComponentVersion())
        .setEnvironmentId(config.getEnvironmentId())
        .setNodeId(config.getNodeId())
        .setSite(config.getSite())
        .setSolution(config.getSolution())
        .build();
  }

  private static Duration getDurationConfig(Config config, String propertyName, Duration defaultValue) {
    return config.getString(propertyName).map(Duration::parse).orElse(defaultValue);
  }

  private static long getLongConfig(Config config, String propertyName) {
    return config.getLong(propertyName).orElseThrow(() -> new IllegalArgumentException("missing long config: " + propertyName));
  }

  private static String getPlatformStringConfig(Config config, String propertyName) {
    return config.getPlatformString(propertyName).orElseThrow(() -> new IllegalArgumentException("missing platform string config: " + propertyName));
  }

  private static RetentionPolicy getRetentionPolicy(Config config) {
    String string = getStringConfig(config, "management.influx.metrics.export.retention-policy");
    return RetentionPolicy.fromValue(string).orElseThrow(() -> new IllegalArgumentException("unknown RetentionPolicy: " + string));
  }

  private static String getStringConfig(Config config, String propertyName) {
    return config.getString(propertyName).orElseThrow(() -> new IllegalArgumentException("missing string config: " + propertyName));
  }
}
