package com.wirelesscar.tcevce.database.tools.metrics;

import java.time.Duration;

import org.junit.jupiter.api.Test;

import io.micrometer.core.instrument.Tags;

class DbMetricsReporterTest {
  @Test
  void logTableSizeTest() {
    MetricsReporterTestUtils.initReporterAndTest(DbMetricsReporter::new, (meterRegistry, dbMetricsReporter) -> {
      String tableTest = "tableTest";
      dbMetricsReporter.logTableSize(tableTest, 2);
      MetricsReporterTestUtils.checkGauge(meterRegistry, 2, DbMetricsReporter.DB_QUEUE_METRIC_NAME, Tags.of(DbMetricsReporter.TABLE_VAR_NAME, tableTest));
    });
  }

  @Test
  void onCleanExecutionStartTest() {
    MetricsReporterTestUtils.initReporterAndTest(DbMetricsReporter::new, (meterRegistry, dbMetricsReporter) -> {
      dbMetricsReporter.onCleanExecutionStart();
      MetricsReporterTestUtils.checkCounter(meterRegistry, 1, DbMetricsReporter.CLEAN_EXECUTION_START_METRIC_NAME);
    });
  }

  @Test
  void onCleanExecutionStopTest() {
    MetricsReporterTestUtils.initReporterAndTest(DbMetricsReporter::new, (meterRegistry, dbMetricsReporter) -> {
      dbMetricsReporter.onCleanExecutionStop(Duration.ofMillis(2));
      MetricsReporterTestUtils.checkTimer(meterRegistry, Duration.ofMillis(2), 1, DbMetricsReporter.CLEAN_EXECUTION_STOP_METRIC_NAME);
    });
  }
}
