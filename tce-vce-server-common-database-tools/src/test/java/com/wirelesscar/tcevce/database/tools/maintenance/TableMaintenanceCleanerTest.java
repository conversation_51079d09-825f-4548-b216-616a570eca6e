package com.wirelesscar.tcevce.database.tools.maintenance;

import java.nio.charset.StandardCharsets;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.Duration;
import java.time.Instant;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.Random;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentMatchers;
import org.mockito.Mockito;

import com.wirelesscar.config.mock.MockConfiguration;
import com.wirelesscar.tce.db.common.api.ActivityStatus;
import com.wirelesscar.tce.db.common.api.CacheUpdateEntry;
import com.wirelesscar.tce.db.common.db.api.CacheableObjectType;
import com.wirelesscar.tce.db.common.db.api.MessageFields;
import com.wirelesscar.tce.db.common.db.api.TableName;
import com.wirelesscar.tce.db.source.TceDataSource;
import com.wirelesscar.tce.db.standard.sql.impl.IdGenerator;
import com.wirelesscar.tce.db.standard.sql.impl.SqlPersistanceFactory;
import com.wirelesscar.tce.module.api.Message;
import com.wirelesscar.tce.utils.Hasher;
import com.wirelesscar.tcevce.database.tools.metrics.DbMetricsReporter;

class TableMaintenanceCleanerTest {
  private static final Random RANDOM = new Random(0);

  @BeforeAll
  static void beforeAll() {
    MockConfiguration mockConfiguration = MockConfiguration.getConfig();
    mockConfiguration.setPropertySpecific(mockConfiguration.getComponentShortName(), TceDataSource.TEST_DB_PROP, "true");
  }

  private static void cleanTable(Connection connection, TableName tableName) throws SQLException {
    String sqlString = "delete from " + tableName;

    try (PreparedStatement preparedStatement = connection.prepareStatement(sqlString)) {
      preparedStatement.execute();
    }
  }

  private static int countInTable(Connection connection, TableName tableName) throws SQLException {
    StringBuilder sqlString = new StringBuilder();
    sqlString.append("select count(*) from ");
    sqlString.append(tableName);

    try (PreparedStatement preparedStatement = connection.prepareStatement(sqlString.toString());
        ResultSet resultSet = preparedStatement.executeQuery()) {
      if (resultSet.next()) {
        return resultSet.getInt(1);
      }
      return 0;
    }
  }

  private static CacheUpdateEntry createCacheUpdateEntry() {
    return new CacheUpdateEntry(Integer.toString(RANDOM.nextInt()), CacheableObjectType.Device);
  }

  private static Message createMessage() {
    Message message = new Message();

    message.setPayload("payload".getBytes(StandardCharsets.UTF_8));
    message.setVehicleID(Integer.toString(RANDOM.nextInt()));
    message.setProperty(MessageFields.status.name(), ActivityStatus.a.name());

    return message;
  }

  /**
   * @return long - date as long format yyyyMMdd
   */
  private static long getDateLong(int daysBack) {
    Instant instant = Instant.now().minus(Duration.ofDays(daysBack));

    DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyyMMdd").withZone(ZoneId.from(ZoneOffset.UTC));
    String format = dateTimeFormatter.format(instant);

    return Long.parseLong(format);
  }

  private static String getInsertCacheUpdateSql() {
    StringBuilder stringBuilder = new StringBuilder();

    stringBuilder.append("INSERT INTO ");
    stringBuilder.append(TableName.CORE_CACHE_UPDATE);
    stringBuilder.append(" (");
    stringBuilder.append(MessageFields.id);
    stringBuilder.append(", ");
    stringBuilder.append(MessageFields.objectId);
    stringBuilder.append(", ");
    stringBuilder.append(MessageFields.objectType);
    stringBuilder.append(", ");
    stringBuilder.append(MessageFields.createTime);
    stringBuilder.append(", ");
    stringBuilder.append(MessageFields.lastUpdate);
    stringBuilder.append(", ");
    stringBuilder.append(MessageFields.partitionDate);

    stringBuilder.append(") VALUES (");
    stringBuilder.append("?");
    stringBuilder.append(", ?");
    stringBuilder.append(", ?");
    stringBuilder.append(", ?");
    stringBuilder.append(", ?");
    stringBuilder.append(", ?");
    stringBuilder.append(")");

    return stringBuilder.toString();
  }

  private static String getInsertMessageSql(TableName tableName) {
    StringBuilder stringBuilder = new StringBuilder();

    stringBuilder.append("INSERT INTO ");
    stringBuilder.append(tableName);
    stringBuilder.append(" (");
    stringBuilder.append(MessageFields.id);
    stringBuilder.append(", ");
    stringBuilder.append(MessageFields.hashID);
    stringBuilder.append(", ");
    stringBuilder.append(MessageFields.nextCheckTime);
    stringBuilder.append(", ");
    stringBuilder.append(MessageFields.createTime);
    stringBuilder.append(", ");
    stringBuilder.append(MessageFields.removeTime);
    stringBuilder.append(", ");
    stringBuilder.append(MessageFields.priority);
    stringBuilder.append(", ");
    stringBuilder.append(MessageFields.status);
    stringBuilder.append(", ");
    stringBuilder.append(MessageFields.vehicleID);
    stringBuilder.append(", ");
    stringBuilder.append(MessageFields.properties);
    stringBuilder.append(", ");
    stringBuilder.append(MessageFields.partitionDate);
    stringBuilder.append(", ");
    stringBuilder.append(MessageFields.payload);

    stringBuilder.append(") VALUES (");
    stringBuilder.append("?");
    stringBuilder.append(", ?");
    stringBuilder.append(", ?");
    stringBuilder.append(", ?");
    stringBuilder.append(", ?");
    stringBuilder.append(", ?");
    stringBuilder.append(", ?");
    stringBuilder.append(", ?");
    stringBuilder.append(", ?");
    stringBuilder.append(", ?");
    stringBuilder.append(", ?");
    stringBuilder.append(")");

    return stringBuilder.toString();
  }

  private static void insertCacheUpdate(Connection connection, CacheUpdateEntry cacheUpdateEntry, long partitionDate) throws SQLException {
    try (PreparedStatement preparedStatement = connection.prepareStatement(getInsertCacheUpdateSql())) {
      final long now = System.currentTimeMillis();

      int i = 0;
      preparedStatement.setString(++i, IdGenerator.newId());
      preparedStatement.setString(++i, cacheUpdateEntry.getObjectId());
      preparedStatement.setString(++i, cacheUpdateEntry.getObjectType().name());
      preparedStatement.setLong(++i, now);
      preparedStatement.setTimestamp(++i, new java.sql.Timestamp(now));
      preparedStatement.setLong(++i, partitionDate);

      preparedStatement.executeUpdate();
    }
  }

  private static void insertMessage(Connection connection, Message message, TableName tableName, long partitionDate, ActivityStatus activityStatus)
      throws SQLException {
    try (PreparedStatement preparedStatement = connection.prepareStatement(getInsertMessageSql(tableName))) {
      final long now = System.currentTimeMillis();

      int i = 0;
      preparedStatement.setString(++i, IdGenerator.newId());
      preparedStatement.setInt(++i, Hasher.getHash(message.getVehicleID()));
      preparedStatement.setLong(++i, now);
      preparedStatement.setLong(++i, now);
      preparedStatement.setLong(++i, now);
      preparedStatement.setInt(++i, 1);
      preparedStatement.setString(++i, activityStatus == null ? "" : activityStatus.name());
      preparedStatement.setString(++i, message.getVehicleID() != null ? message.getVehicleID() : "");
      preparedStatement.setString(++i, "");
      preparedStatement.setLong(++i, partitionDate);
      preparedStatement.setBytes(++i, message.getPayload() != null ? message.getPayload() : new byte[0]);

      preparedStatement.executeUpdate();
    }
  }

  @Test
  void testDeleteCacheUpdate() throws SQLException {
    try (Connection connection = SqlPersistanceFactory.getConnection()) {
      final TableName tableName = TableName.CORE_CACHE_UPDATE;
      final int cacheUpdatesToDelete = 2_042;
      final int cacheUpdatesYesterday = 20;
      final int cacheUpdatesToday = 30;

      cleanTable(connection, tableName);
      for (int i = 0; i < cacheUpdatesToDelete; ++i) {
        insertCacheUpdate(connection, createCacheUpdateEntry(), getDateLong(5));
      }
      Assertions.assertEquals(cacheUpdatesToDelete, countInTable(connection, tableName));

      for (int i = 0; i < cacheUpdatesYesterday; ++i) {
        insertCacheUpdate(connection, createCacheUpdateEntry(), getDateLong(1));
      }
      Assertions.assertEquals(cacheUpdatesToDelete + cacheUpdatesYesterday, countInTable(connection, tableName));

      for (int i = 0; i < cacheUpdatesToday; ++i) {
        insertCacheUpdate(connection, createCacheUpdateEntry(), getDateLong(0));
      }
      Assertions.assertEquals(cacheUpdatesToDelete + cacheUpdatesYesterday + cacheUpdatesToday, countInTable(connection, tableName));

      DbMetricsReporter dbMetricsReporter = Mockito.mock(DbMetricsReporter.class);
      TableMaintenanceCleaner tableMaintenanceCleaner = new TableMaintenanceCleaner(dbMetricsReporter);
      tableMaintenanceCleaner.cleanTables();

      Mockito.verify(dbMetricsReporter).onCleanExecutionStart();
      Mockito.verify(dbMetricsReporter).onCleanExecutionStop(ArgumentMatchers.any());

      Assertions.assertEquals(cacheUpdatesYesterday + cacheUpdatesToday, countInTable(connection, tableName)); // oldest deleted

      tableMaintenanceCleaner.cleanTables();
      Assertions.assertEquals(cacheUpdatesYesterday + cacheUpdatesToday, countInTable(connection, tableName));
    }
  }

  @Test
  void testDeletePersistMessage() throws SQLException {
    try (Connection connection = SqlPersistanceFactory.getConnection()) {
      final TableName tableName = TableName.PRST_PERSIST_MESSAGE;
      final int messagesToDelete = 2_042;
      final int messagesYesterday = 20;
      final int messagesToday = 30;
      final int messagesWithStatus = 40;

      cleanTable(connection, tableName);
      for (int i = 0; i < messagesToDelete; ++i) {
        insertMessage(connection, createMessage(), tableName, getDateLong(5), null);
      }
      Assertions.assertEquals(messagesToDelete, countInTable(connection, tableName));

      for (int i = 0; i < messagesWithStatus; ++i) {
        insertMessage(connection, createMessage(), tableName, getDateLong(5), ActivityStatus.a);
      }
      Assertions.assertEquals(messagesToDelete + messagesWithStatus, countInTable(connection, tableName));

      for (int i = 0; i < messagesYesterday; ++i) {
        insertMessage(connection, createMessage(), tableName, getDateLong(1), null);
      }
      Assertions.assertEquals(messagesToDelete + messagesWithStatus + messagesYesterday, countInTable(connection, tableName));

      for (int i = 0; i < messagesToday; ++i) {
        insertMessage(connection, createMessage(), tableName, getDateLong(0), null);
      }
      Assertions.assertEquals(messagesToDelete + messagesWithStatus + messagesYesterday + messagesToday, countInTable(connection, tableName));

      DbMetricsReporter dbMetricsReporter = Mockito.mock(DbMetricsReporter.class);
      TableMaintenanceCleaner tableMaintenanceCleaner = new TableMaintenanceCleaner(dbMetricsReporter);
      tableMaintenanceCleaner.cleanTables();

      Mockito.verify(dbMetricsReporter).onCleanExecutionStart();
      Mockito.verify(dbMetricsReporter).onCleanExecutionStop(ArgumentMatchers.any());

      Assertions.assertEquals(messagesWithStatus + messagesYesterday + messagesToday, countInTable(connection, tableName)); // oldest deleted

      tableMaintenanceCleaner.cleanTables();
      Assertions.assertEquals(messagesWithStatus + messagesYesterday + messagesToday, countInTable(connection, tableName));
    }
  }

  @Test
  void testDeleteScheduledMessage() throws SQLException {
    try (Connection connection = SqlPersistanceFactory.getConnection()) {
      final TableName tableName = TableName.SCHD_SCHEDULED_MESSAGE;
      final int messagesToDelete = 2_042;
      final int messagesYesterday = 20;
      final int messagesToday = 30;
      final int messagesWithStatus = 40;

      for (int i = 0; i < messagesToDelete; ++i) {
        insertMessage(connection, createMessage(), tableName, getDateLong(5), null);
      }
      Assertions.assertEquals(messagesToDelete, countInTable(connection, tableName));

      for (int i = 0; i < messagesWithStatus; ++i) {
        insertMessage(connection, createMessage(), tableName, getDateLong(5), ActivityStatus.a);
      }
      Assertions.assertEquals(messagesToDelete + messagesWithStatus, countInTable(connection, tableName));

      for (int i = 0; i < messagesYesterday; ++i) {
        insertMessage(connection, createMessage(), tableName, getDateLong(1), null);
      }
      Assertions.assertEquals(messagesToDelete + messagesWithStatus + messagesYesterday, countInTable(connection, tableName));

      for (int i = 0; i < messagesToday; ++i) {
        insertMessage(connection, createMessage(), tableName, getDateLong(0), null);
      }
      Assertions.assertEquals(messagesToDelete + messagesWithStatus + messagesYesterday + messagesToday, countInTable(connection, tableName));

      DbMetricsReporter dbMetricsReporter = Mockito.mock(DbMetricsReporter.class);
      TableMaintenanceCleaner tableMaintenanceCleaner = new TableMaintenanceCleaner(dbMetricsReporter);
      tableMaintenanceCleaner.cleanTables();

      Mockito.verify(dbMetricsReporter).onCleanExecutionStart();
      Mockito.verify(dbMetricsReporter).onCleanExecutionStop(ArgumentMatchers.any());

      Assertions.assertEquals(messagesWithStatus + messagesYesterday + messagesToday, countInTable(connection, tableName)); // oldest deleted

      tableMaintenanceCleaner.cleanTables();
      Assertions.assertEquals(messagesWithStatus + messagesYesterday + messagesToday, countInTable(connection, tableName));
    }
  }
}
