package com.wirelesscar.tcevce.database.tools.metrics;

import java.time.Duration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicLong;

import org.springframework.stereotype.Component;

import com.volvo.tisp.vc.main.utils.lib.Validate;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tag;
import io.micrometer.core.instrument.Timer;

@Component
public class DbMetricsReporter {
  static final String CLEAN_EXECUTION_START_METRIC_NAME = "db.table.maintenance.clean.execution.start";
  static final String CLEAN_EXECUTION_STOP_METRIC_NAME = "db.table.maintenance.clean.execution.stop";
  static final String DB_QUEUE_METRIC_NAME = "db.queue";
  static final String TABLE_VAR_NAME = "table";

  private final Counter cleanExecutionStartCounter;
  private final Timer cleanExecutionStopTimer;
  private final Map<String, AtomicLong> gaugeMap = new HashMap<>();
  private final MeterRegistry meterRegistry;

  public DbMetricsReporter(MeterRegistry meterRegistry) {
    this.meterRegistry = meterRegistry;

    cleanExecutionStartCounter = meterRegistry.counter(CLEAN_EXECUTION_START_METRIC_NAME);
    cleanExecutionStopTimer = meterRegistry.timer(CLEAN_EXECUTION_STOP_METRIC_NAME);
  }

  public void logTableSize(String table, long size) {
    Validate.notEmpty(table, TABLE_VAR_NAME);
    Validate.notNegative(size, "size");

    gaugeMap
        .computeIfAbsent(table, tableName -> meterRegistry.gauge(DB_QUEUE_METRIC_NAME, List.of(Tag.of(TABLE_VAR_NAME, table)), new AtomicLong()))
        .set(size);
  }

  public void onCleanExecutionStart() {
    cleanExecutionStartCounter.increment();
  }

  public void onCleanExecutionStop(Duration duration) {
    Validate.notNegative(duration, "duration");

    cleanExecutionStopTimer.record(duration);
  }
}
