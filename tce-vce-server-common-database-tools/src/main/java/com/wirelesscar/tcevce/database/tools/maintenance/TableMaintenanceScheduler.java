package com.wirelesscar.tcevce.database.tools.maintenance;

import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Conditional;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.wirelesscar.tcevce.database.tools.metrics.DbMetricsReporter;

@Component
@Conditional(TableMaintenanceSchedulerEnabledCondition.class)
public class TableMaintenanceScheduler {
  private static final Logger logger = LoggerFactory.getLogger(TableMaintenanceScheduler.class);

  private long currentScheduledExecutionId;
  private final Lock lock = new ReentrantLock();
  private final TableMaintenanceCleaner tableMaintenanceCleaner;

  public TableMaintenanceScheduler(DbMetricsReporter dbMetricsReporter) {
    tableMaintenanceCleaner = new TableMaintenanceCleaner(dbMetricsReporter);
  }

  /**
   * Requires a Spring @Configuration class with @EnableScheduling to be present.
   *
   * <p>
   * Ref: https://docs.spring.io/spring/docs/current/spring-framework-reference/html/scheduling.html
   *
   * <p>
   * Example cron expression: 0 5 * * * ? (fire 5 min past every whole hour). Note: do not fire at even hours, since log rotation typically happens at even
   * hours. Log rotation will make it harder to follow what's happening.
   *
   * <p>
   * Property substitution works in annotation since ConfigLib is hooked up as a Spring PropertySource, ref: <code>
   * com.wirelesscar.componentbaselib.springboot.ConfigLibPropertySourceInitializer</code>.
   */
  @Scheduled(cron = "${tce.db.table.maintenance.schedule.cron}")
  public void schedule() {
    // lock to make sure that only one cleanup is running at a time, if a cleanup takes longer than
    // the time between scheduled invocation we would otherwise
    // risk having multiple scheduled cleanups running at the same time
    if (lock.tryLock()) {
      try {
        currentScheduledExecutionId = System.currentTimeMillis();
        logger.info("scheduled: begin, id: {}", currentScheduledExecutionId);
        tableMaintenanceCleaner.cleanTables();
        logger.info("scheduled: end, id: {}", currentScheduledExecutionId);
      } catch (Exception e) {
        logger.error("scheduled execution failed, id: " + currentScheduledExecutionId, e);
      } finally {
        lock.unlock();
      }
    } else {
      logger.warn("skipped scheduled execution, previous execution still running, id: {}", currentScheduledExecutionId);
    }
  }
}
