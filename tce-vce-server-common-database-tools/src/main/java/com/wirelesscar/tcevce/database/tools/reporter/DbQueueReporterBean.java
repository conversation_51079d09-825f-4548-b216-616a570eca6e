package com.wirelesscar.tcevce.database.tools.reporter;

import java.util.Timer;
import java.util.TimerTask;

import jakarta.annotation.PostConstruct;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.wirelesscar.config.Config;
import com.wirelesscar.config.ConfigFactory;
import com.wirelesscar.tce.db.source.TceDataSource;
import com.wirelesscar.tcevce.database.tools.metrics.DbMetricsReporter;

@Component
public class DbQueueReporterBean {
  private static final String PROP_START_ENABLE = "tce.db.metrics.enable";
  private static final Logger logger = LoggerFactory.getLogger(DbQueueReporterBean.class);
  private final DbMetricsReporter dbMetricsReporter;
  private boolean enabled = true;

  public DbQueueReporterBean(DbMetricsReporter dbMetricsReporter) {
    this.dbMetricsReporter = dbMetricsReporter;
  }

  @PostConstruct
  public void start() {
    Config config = ConfigFactory.getConfig();
    enabled = config.getBoolean(PROP_START_ENABLE).orElse(enabled);
    boolean inmemory = TceDataSource.isH2();

    if (enabled && !inmemory) {
      logger.info("Will run continually, enable: {}", enabled);

      Timer timer = new Timer("DbQueueReporter");
      timer.schedule(new RunnerTask(dbMetricsReporter), 0, (10 * 1_000));
    } else {
      logger.info("Will not run, enabled: {}, inmemory: {}", enabled, inmemory);
    }
  }

  static class RunnerTask extends TimerTask {
    private final DbMetricsReporter dbMetricsReporter;

    RunnerTask(DbMetricsReporter dbMetricsReporter) {
      Validate.notNull(dbMetricsReporter, "dbMetricsReporter");

      this.dbMetricsReporter = dbMetricsReporter;
    }

    @Override
    public void run() {
      DbQueueReporter dbQueueReporter = new DbQueueReporter(dbMetricsReporter);
      dbQueueReporter.runOnce();
    }
  }
}
