package com.wirelesscar.tcevce.database.tools.maintenance;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Condition;
import org.springframework.context.annotation.ConditionContext;
import org.springframework.core.type.AnnotatedTypeMetadata;

import com.wirelesscar.config.Config;
import com.wirelesscar.config.ConfigFactory;

public class TableMaintenanceSchedulerEnabledCondition implements Condition {
  private static final Config config = ConfigFactory.getConfig();
  private static final Logger logger = LoggerFactory.getLogger(TableMaintenanceSchedulerEnabledCondition.class);

  @Override
  public boolean matches(ConditionContext context, AnnotatedTypeMetadata metadata) {
    boolean enabled = config.getBoolean("tce.db.table.maintenance.enable").orElse(false);
    logger.info("enabled: {}", enabled);
    return enabled;
  }
}
