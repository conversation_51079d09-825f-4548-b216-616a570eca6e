package com.wirelesscar.tcevce.database.tools.maintenance;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.Duration;
import java.time.Instant;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.wirelesscar.config.ConfigFactory;
import com.wirelesscar.tce.db.common.db.api.TableName;
import com.wirelesscar.tce.db.standard.sql.impl.SqlPersistanceFactory;
import com.wirelesscar.tcevce.database.tools.metrics.DbMetricsReporter;

class TableMaintenanceCleaner {
  private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd", Locale.ENGLISH);
  private static final Logger logger = LoggerFactory.getLogger(TableMaintenanceCleaner.class);
  private static final int MAX_DELETE_ROWS_PER_ITERATION = 1_000; // 1000 = max number of IN-clause parameters for Oracle

  private final DbMetricsReporter dbMetricsReporter;
  private final int retentionDays = ConfigFactory.getConfig().getInt("tce.db.maintenance.delay.delete.days").orElse(1);

  TableMaintenanceCleaner(DbMetricsReporter dbMetricsReporter) {
    Validate.notNull(dbMetricsReporter, "dbMetricsReporter");

    this.dbMetricsReporter = dbMetricsReporter;
  }

  private static long deleteFromTable(TableName tableName, long deleteToDate, boolean tableHasStatus) throws SQLException {
    long rowDeletedCount = 0;

    try (Connection connection = SqlPersistanceFactory.getConnection();
        PreparedStatement preparedStatement = connection.prepareStatement(getSqlSelectStatement(tableName, tableHasStatus))) {
      preparedStatement.setLong(1, deleteToDate);

      try (ResultSet resultSet = preparedStatement.executeQuery()) {
        List<String> idsToDelete = new ArrayList<>();

        while (resultSet.next()) {
          idsToDelete.add(resultSet.getString("id"));

          if (idsToDelete.size() >= MAX_DELETE_ROWS_PER_ITERATION) {
            rowDeletedCount += deleteRows(connection, tableName, idsToDelete);
            idsToDelete.clear();
          }
        }

        if (!idsToDelete.isEmpty()) {
          rowDeletedCount += deleteRows(connection, tableName, idsToDelete);
        }

        return rowDeletedCount;
      }
    }
  }

  private static int deleteRows(Connection connection, TableName tableName, List<String> idsToDelete) throws SQLException {
    try (PreparedStatement preparedStatement = connection.prepareStatement(getSqlDeleteStatement(tableName, idsToDelete.size()))) {
      int i = 0;
      for (String idToDelete : idsToDelete) {
        preparedStatement.setString(++i, idToDelete);
      }

      int deleteCount = preparedStatement.executeUpdate();

      if (deleteCount != idsToDelete.size()) {
        logger.warn("Diff in number of deleted rows: {}, vs expectedDeleted: {}", deleteCount, idsToDelete.size());
      }
      return deleteCount;
    }
  }

  private static String getSqlDeleteStatement(TableName tableName, int countIdsInStatement) {
    if (countIdsInStatement <= 0) {
      throw new IllegalArgumentException("countIdsInStatement must be positive: " + countIdsInStatement);
    }

    StringBuilder stringBuilder = new StringBuilder();

    stringBuilder.append("DELETE FROM ");
    stringBuilder.append(tableName);
    stringBuilder.append(" WHERE id IN (");

    for (int i = 0; i < countIdsInStatement; ++i) {
      stringBuilder.append("?,");
    }

    // remove the last comma character
    stringBuilder.setLength(stringBuilder.length() - 1);

    stringBuilder.append(')');
    return stringBuilder.toString();
  }

  private static String getSqlSelectStatement(TableName tableName, boolean tableHasStatus) {
    StringBuilder stringBuilder = new StringBuilder(128);

    stringBuilder.append("SELECT id FROM ");
    stringBuilder.append(tableName);
    stringBuilder.append(" WHERE partitiondate < ?");

    if (tableHasStatus) {
      stringBuilder.append(" AND status IS NULL");
    }

    return stringBuilder.toString();
  }

  void cleanTables() throws SQLException {
    logger.debug("begin clean tables");
    dbMetricsReporter.onCleanExecutionStart();
    Instant startTime = Instant.now();

    cleanTablesInternal();

    Duration elapsedDuration = Duration.between(startTime, Instant.now());
    logger.debug("end clean tables, took: {}", elapsedDuration);
    dbMetricsReporter.onCleanExecutionStop(elapsedDuration);
  }

  private void cleanTable(TableName tableName, boolean tableHasStatus) throws SQLException {
    long deleteToDate = getDeleteToDate();
    logger.debug("begin clean table: {}, for entries older than: {}", tableName, deleteToDate);

    Instant start = Instant.now();
    long rowDeletedCount = deleteFromTable(tableName, deleteToDate, tableHasStatus);
    Duration duration = Duration.between(start, Instant.now());

    logger.debug("end clean table: {}, # rows deleted: {}, took: {}", tableName, rowDeletedCount, duration);
  }

  private void cleanTablesInternal() throws SQLException {
    cleanTable(TableName.CORE_CACHE_UPDATE, false);

    cleanTable(TableName.SCHD_SCHEDULED_MESSAGE, true);
    cleanTable(TableName.PRST_PERSIST_MESSAGE, true);
  }

  private long getDeleteToDate() {
    OffsetDateTime daysAgo = OffsetDateTime.now(ZoneOffset.UTC).minusDays(retentionDays);

    return Long.parseLong(DATE_TIME_FORMATTER.format(daysAgo));
  }
}
