package com.wirelesscar.tcevce.database.tools.reporter;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.wirelesscar.tce.db.common.db.api.TableName;
import com.wirelesscar.tce.db.standard.sql.impl.SqlPersistanceFactory;
import com.wirelesscar.tcevce.database.tools.metrics.DbMetricsReporter;

public class DbQueueReporter {
  private static final Logger log = LoggerFactory.getLogger(DbQueueReporter.class);
  private static final TableName[] tablesWithState = new TableName[] {TableName.SCHD_SCHEDULED_MESSAGE, TableName.PRST_PERSIST_MESSAGE};

  private final DbMetricsReporter dbMetricsReporter;

  public DbQueueReporter(DbMetricsReporter dbMetricsReporter) {
    Validate.notNull(dbMetricsReporter, "dbMetricsReporter");

    this.dbMetricsReporter = dbMetricsReporter;
  }

  private static int getMetricDb(String sql) throws SQLException {
    try (Connection connection = SqlPersistanceFactory.getConnection();
        Statement statement = connection.createStatement();
        ResultSet resultSet = statement.executeQuery(sql)) {
      if (resultSet.next()) {
        return resultSet.getInt(1);
      }

      return 0;
    }
  }

  public void runOnce() {
    try {
      for (TableName tableName : tablesWithState) {
        StringBuilder stringBuilder = new StringBuilder(100);
        stringBuilder.append("SELECT count(*) FROM ");
        stringBuilder.append(tableName);
        stringBuilder.append(" WHERE status IS NOT NULL");

        sendMetricDb(tableName, getMetricDb(stringBuilder.toString()));
      }
    } catch (Exception e) {
      log.error("", e);
    }
  }

  private void sendMetricDb(TableName tableName, int count) {
    log.info("Database depth in table {} is: {}", tableName, count);
    dbMetricsReporter.logTableSize(tableName.name(), count);
  }
}
